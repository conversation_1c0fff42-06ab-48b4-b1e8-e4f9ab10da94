package services

import (
	"fmt"
	"log"
	"strings"
	"time"
	"yotracker/config"
	"yotracker/internal/models"

	"gorm.io/gorm"
)

// ReportService handles report generation and management
type ReportService struct{}

// NewReportService creates a new report service
func NewReportService() *ReportService {
	return &ReportService{}
}

// GenerateReport generates a report based on the report type and filters
func (s *ReportService) GenerateReport(reportId uint, filters models.ReportFilters, format string) (*models.ReportData, error) {
	startTime := time.Now()

	// Get report definition
	var report models.Report
	err := config.DB.First(&report, reportId).Error
	if err != nil {
		return nil, fmt.Errorf("report not found: %v", err)
	}

	log.Printf("Generating report: %s (%s)", report.Name, report.ReportType)

	// Generate report data based on type
	var data interface{}
	var summary interface{}
	var totalRecords, filteredRecords int

	switch report.ReportType {
	// Core reports
	case "position_log":
		data, totalRecords, filteredRecords, err = s.generatePositionLogReport(filters)
	case "trip_detail":
		data, totalRecords, filteredRecords, err = s.generateTripDetailReport(filters)
	case "trip_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateTripSummaryReport(filters)
	case "speeding_detail":
		data, totalRecords, filteredRecords, err = s.generateSpeedingDetailReport(filters)
	case "driver_performance_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateDriverPerformanceReport(filters)

	// Powerful new reports
	case "driver_safety_scorecard":
		data, summary, totalRecords, filteredRecords, err = s.generateDriverSafetyScorecardReport(filters)
	case "speeding_violations":
		data, totalRecords, filteredRecords, err = s.generateSpeedingViolationsReport(filters)
	case "geofence_speeding_detail":
		data, totalRecords, filteredRecords, err = s.generateSpeedingViolationsReport(filters)
	case "geofence_activity":
		data, totalRecords, filteredRecords, err = s.generateGeofenceActivityReport(filters)
	case "fleet_roi_dashboard":
		data, summary, totalRecords, filteredRecords, err = s.generateFleetROIDashboard(filters)
	case "executive_fleet_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateExecutiveFleetSummary(filters)
	case "vehicle_utilization":
		data, summary, totalRecords, filteredRecords, err = s.generateVehicleUtilizationReport(filters)
	case "fuel_consumption_analysis":
		data, summary, totalRecords, filteredRecords, err = s.generateFuelConsumptionAnalysis(filters)
	case "emergency_response":
		data, totalRecords, filteredRecords, err = s.generateEmergencyResponseReport(filters)

	// Additional Summary Reports
	case "mileage_day_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateMileageDaySummaryReport(filters)
	case "trip_day_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateTripDaySummaryReport(filters)
	case "working_time_day_summary":
		log.Printf("Generating Working Time Day Summary Report")
		data, summary, totalRecords, filteredRecords, err = s.generateWorkingTimeDaySummaryReport(filters)
		if err != nil {
			log.Printf("Error generating Working Time Day Summary Report: %v", err)
		}
	case "mileage_month_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateMileageMonthSummaryReport(filters)
	case "after_hour_month_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateAfterHourMonthSummaryReport(filters)
	case "vehicle_performance_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateVehiclePerformanceSummaryReport(filters)

	// Additional Summary Reports
	case "fleet_productive_day_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateFleetProductiveDaySummaryReport(filters)
	case "mileage_achieving_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateMileageAchievingSummaryReport(filters)
	case "fuel_estimation_month_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateFuelEstimationMonthSummaryReport(filters)
	case "fuel_estimation_hour_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateFuelEstimationHourSummaryReport(filters)

	// Additional Implemented Reports
	case "fuel_estimation":
		data, summary, totalRecords, filteredRecords, err = s.generateFuelEstimationReport(filters)
	case "driver_behavior_analysis":
		data, summary, totalRecords, filteredRecords, err = s.generateDriverBehaviorAnalysisReport(filters)
	case "fuel_efficiency_trends":
		data, summary, totalRecords, filteredRecords, err = s.generateFuelEfficiencyTrendsReport(filters)
	case "fleet_productivity":
		data, summary, totalRecords, filteredRecords, err = s.generateFleetProductivityReport(filters)
	case "real_time_dashboard":
		data, summary, totalRecords, filteredRecords, err = s.generateRealTimeDashboardReport(filters)
	case "driver_scorecard":
		data, summary, totalRecords, filteredRecords, err = s.generateDriverScorecardReport(filters)
	case "fleet_health_monitor":
		data, summary, totalRecords, filteredRecords, err = s.generateFleetHealthMonitorReport(filters)
	case "cost_analysis_dashboard":
		data, summary, totalRecords, filteredRecords, err = s.generateCostAnalysisDashboardReport(filters)
	case "compliance_dashboard":
		data, summary, totalRecords, filteredRecords, err = s.generateComplianceDashboardReport(filters)
	case "route_analysis":
		data, summary, totalRecords, filteredRecords, err = s.generateRouteAnalysisReport(filters)
	case "predictive_maintenance":
		data, summary, totalRecords, filteredRecords, err = s.generatePredictiveMaintenanceReport(filters)
	case "fleet_optimization":
		data, summary, totalRecords, filteredRecords, err = s.generateFleetOptimizationReport(filters)
	case "driver_training_needs":
		data, summary, totalRecords, filteredRecords, err = s.generateDriverTrainingNeedsReport(filters)
	case "environmental_dashboard":
		data, summary, totalRecords, filteredRecords, err = s.generateEnvironmentalDashboardReport(filters)

	// Additional Missing Reports
	case "route_deviation_report":
		data, summary, totalRecords, filteredRecords, err = s.generateRouteDeviationReport(filters)
	case "last_location":
		data, summary, totalRecords, filteredRecords, err = s.generateLastLocationReport(filters)
	case "last_location_history":
		data, summary, totalRecords, filteredRecords, err = s.generateLastLocationHistoryReport(filters)
	case "trip_detail_delta":
		data, summary, totalRecords, filteredRecords, err = s.generateTripDetailDeltaReport(filters)
	case "position_log_driver":
		data, summary, totalRecords, filteredRecords, err = s.generatePositionLogDriverReport(filters)
	case "trip_mileage":
		data, summary, totalRecords, filteredRecords, err = s.generateTripMileageReport(filters)

	// Additional Active Reports
	case "daily_trip_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateDailyTripSummaryReport(filters)
	case "monthly_mileage":
		data, summary, totalRecords, filteredRecords, err = s.generateMonthlyMileageReport(filters)
	case "route_efficiency":
		data, summary, totalRecords, filteredRecords, err = s.generateRouteEfficiencyReport(filters)
	case "harsh_driving_events":
		data, summary, totalRecords, filteredRecords, err = s.generateHarshDrivingEventsReport(filters)
	case "rash_drive_detail":
		data, summary, totalRecords, filteredRecords, err = s.generateHarshDrivingEventsReport(filters)
	case "driver_performance_ranking":
		data, summary, totalRecords, filteredRecords, err = s.generateDriverPerformanceRankingReport(filters)
	case "fatigue_overtime_alert":
		data, summary, totalRecords, filteredRecords, err = s.generateFatigueOvertimeAlertReport(filters)
	case "fatigue_report":
		data, summary, totalRecords, filteredRecords, err = s.generateFatigueOvertimeAlertReport(filters)
	case "customer_visits":
		data, summary, totalRecords, filteredRecords, err = s.generateCustomerVisitsReport(filters)
	case "destination_arrival":
		data, summary, totalRecords, filteredRecords, err = s.generateCustomerVisitsReport(filters)
	case "unauthorized_locations":
		data, summary, totalRecords, filteredRecords, err = s.generateUnauthorizedLocationsReport(filters)
	case "exception_detail":
		data, summary, totalRecords, filteredRecords, err = s.generateUnauthorizedLocationsReport(filters)
	case "jobsite_productivity":
		data, summary, totalRecords, filteredRecords, err = s.generateJobsiteProductivityReport(filters)
	case "stop_detail":
		data, summary, totalRecords, filteredRecords, err = s.generateJobsiteProductivityReport(filters)
	case "door_detail":
		data, summary, totalRecords, filteredRecords, err = s.generateDoorDetailReport(filters)
	case "operating_cost_report":
		data, summary, totalRecords, filteredRecords, err = s.generateOperatingCostReport(filters)
	case "idle_time_cost":
		data, summary, totalRecords, filteredRecords, err = s.generateIdleTimeCostReport(filters)
	case "idle_detail":
		data, summary, totalRecords, filteredRecords, err = s.generateIdleTimeCostReport(filters)
	case "maintenance_due":
		data, summary, totalRecords, filteredRecords, err = s.generateMaintenanceDueReport(filters)
	case "maintenance_schedule":
		data, summary, totalRecords, filteredRecords, err = s.generateMaintenanceScheduleReport(filters)
	case "vehicle_health_dashboard":
		data, summary, totalRecords, filteredRecords, err = s.generateVehicleHealthDashboardReport(filters)
	case "breakdown_repair_history":
		data, summary, totalRecords, filteredRecords, err = s.generateBreakdownRepairHistoryReport(filters)
	case "maintenance_detail":
		data, summary, totalRecords, filteredRecords, err = s.generateBreakdownRepairHistoryReport(filters)
	case "hours_of_service":
		data, summary, totalRecords, filteredRecords, err = s.generateHoursOfServiceReport(filters)
	case "vehicle_security":
		data, summary, totalRecords, filteredRecords, err = s.generateVehicleSecurityReport(filters)
	case "speed_limit_compliance":
		data, summary, totalRecords, filteredRecords, err = s.generateSpeedLimitComplianceReport(filters)
	case "fleet_performance_trends":
		data, summary, totalRecords, filteredRecords, err = s.generateFleetPerformanceTrendsReport(filters)
	case "cost_center_analysis":
		data, summary, totalRecords, filteredRecords, err = s.generateCostCenterAnalysisReport(filters)
	case "environmental_impact":
		data, summary, totalRecords, filteredRecords, err = s.generateEnvironmentalImpactReport(filters)
	case "daily_operations_dashboard":
		data, summary, totalRecords, filteredRecords, err = s.generateDailyOperationsDashboardReport(filters)
	case "vehicle_availability":
		data, summary, totalRecords, filteredRecords, err = s.generateVehicleAvailabilityReport(filters)
	case "asset_tracking":
		data, summary, totalRecords, filteredRecords, err = s.generateAssetTrackingReport(filters)
	case "delivery_performance":
		data, summary, totalRecords, filteredRecords, err = s.generateDeliveryPerformanceReport(filters)
	case "service_technician":
		data, summary, totalRecords, filteredRecords, err = s.generateServiceTechnicianReport(filters)
	case "construction_equipment":
		data, summary, totalRecords, filteredRecords, err = s.generateConstructionEquipmentReport(filters)
	case "sales_territory_analysis":
		data, summary, totalRecords, filteredRecords, err = s.generateSalesTerritoryAnalysisReport(filters)
	case "maintenance_cost_analysis":
		data, summary, totalRecords, filteredRecords, err = s.generateMaintenanceCostAnalysisReport(filters)
	case "safety_compliance":
		data, summary, totalRecords, filteredRecords, err = s.generateSafetyComplianceReport(filters)
	case "asset_performance":
		data, summary, totalRecords, filteredRecords, err = s.generateAssetPerformanceReport(filters)
	case "route_optimization":
		data, summary, totalRecords, filteredRecords, err = s.generateRouteOptimizationReport(filters)
	case "driver_training":
		data, summary, totalRecords, filteredRecords, err = s.generateDriverTrainingReport(filters)
	case "vehicle_lifecycle":
		data, summary, totalRecords, filteredRecords, err = s.generateVehicleLifecycleReport(filters)
	case "operational_efficiency":
		data, summary, totalRecords, filteredRecords, err = s.generateOperationalEfficiencyReport(filters)
	case "destination_zone_in":
		data, summary, totalRecords, filteredRecords, err = s.generateDestinationZoneInReport(filters)
	case "vehicle_usage_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateVehicleUsageSummaryReport(filters)
	case "exception_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateExceptionSummaryReport(filters)
	case "driver_exception_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateDriverExceptionSummaryReport(filters)
	case "speeding_exception_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateSpeedingExceptionSummaryReport(filters)
	case "vehicle_online_summary":
		data, summary, totalRecords, filteredRecords, err = s.generateVehicleOnlineSummaryReport(filters)
	case "cargo_day_summary_fuel":
		data, summary, totalRecords, filteredRecords, err = s.generateCargoDaySummaryFuelReport(filters)

	default:
		return nil, fmt.Errorf("report type %s not implemented yet", report.ReportType)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to generate report data: %v", err)
	}

	// Create report response
	reportData := &models.ReportData{
		ReportInfo: report,
		Filters:    filters,
		Data:       data,
		Summary:    summary,
		Metadata: models.ReportMetadata{
			GeneratedAt:     time.Now(),
			TotalRecords:    totalRecords,
			FilteredRecords: filteredRecords,
			ExecutionTime:   time.Since(startTime).String(),
			Format:          format,
		},
	}

	log.Printf("Report generated successfully: %d records in %s", filteredRecords, time.Since(startTime))
	return reportData, nil
}

// generatePositionLogReport generates comprehensive GPS position data using DTO
func (s *ReportService) generatePositionLogReport(filters models.ReportFilters) (interface{}, int, int, error) {
	// Raw data from database
	var rawData []struct {
		models.GPSData
		DeviceName  string  `json:"device_name"`
		DeviceType  string  `json:"device_type"`
		PlateNumber string  `json:"plate_number"`
		DriverName  *string `json:"driver_name"`
		TripId      *uint   `json:"trip_id"`
	}

	query := config.DB.Table("gps_data g").
		Select(`
			g.*,
			cd.name as device_name,
			dt.name as device_type,
			cd.plate_number,
			d.name as driver_name,
			g.trip_id
		`).
		Joins("LEFT JOIN client_devices cd ON g.client_device_id = cd.id").
		Joins("LEFT JOIN device_types dt ON cd.device_type_id = dt.id").
		Joins("LEFT JOIN trips t ON g.trip_id = t.id").
		Joins("LEFT JOIN drivers d ON t.driver_id = d.id").
		Order("g.gps_timestamp DESC")

	// Apply filters
	query = s.applyGPSFilters(query, filters)

	// Get total count
	var totalCount int64
	config.DB.Model(&models.GPSData{}).Count(&totalCount)

	// Get filtered count
	var filteredCount int64
	countQuery := config.DB.Table("gps_data g").
		Joins("LEFT JOIN client_devices cd ON g.client_device_id = cd.id").
		Joins("LEFT JOIN device_types dt ON cd.device_type_id = dt.id").
		Joins("LEFT JOIN trips t ON g.trip_id = t.id").
		Joins("LEFT JOIN drivers d ON t.driver_id = d.id")
	countQuery = s.applyGPSFilters(countQuery, filters)
	countQuery.Count(&filteredCount)

	// Apply pagination (default to 1000 records if not specified)
	perPage := filters.PerPage
	if perPage == 0 {
		perPage = 1000 // Default for position logs
	}

	offset := 0
	if filters.Page > 0 {
		offset = (filters.Page - 1) * perPage
	}
	query = query.Offset(offset).Limit(perPage)

	err := query.Scan(&rawData).Error
	if err != nil {
		return nil, 0, 0, err
	}

	// Convert to DTO structure
	var dtoData []models.PositionLogRow
	for _, item := range rawData {
		driverName := ""
		if item.DriverName != nil {
			driverName = *item.DriverName
		}

		// Handle nullable fields
		speed := 0.0
		if item.Speed != nil {
			speed = *item.Speed
		}

		timestamp := ""
		if item.GPSTimestamp != nil {
			timestamp = item.GPSTimestamp.Format("2006-01-02 15:04:05")
		}

		engineStatus := "Unknown"
		if item.IgnitionStatus != nil {
			if *item.IgnitionStatus {
				engineStatus = "On"
			} else {
				engineStatus = "Off"
			}
		}

		locationName := ""
		if item.LocationName != nil {
			locationName = *item.LocationName
		}

		// Get direction from GPSData model
		direction := "Unknown"
		if item.Direction != nil {
			direction = *item.Direction
		}

		// Combine device name and plate number
		name := item.DeviceName
		if item.PlateNumber != "" {
			name = name + " (" + item.PlateNumber + ")"
		}

		dtoRow := models.PositionLogRow{
			Timestamp:    timestamp,
			Name:         name,
			DriverName:   driverName,
			Latitude:     item.Latitude,
			Longitude:    item.Longitude,
			LocationName: locationName,
			Speed:        speed,
			Direction:    direction,
			EngineStatus: engineStatus,
		}
		dtoData = append(dtoData, dtoRow)
	}

	// Create DTO with headers
	dto := &models.PositionLogDTO{
		Headers: GetHeadersFromStruct([]models.PositionLogRow{}),
		Data:    dtoData,
	}

	return dto, int(totalCount), int(filteredCount), nil
}

// generateTripDetailReport generates detailed trip information using DTO
func (s *ReportService) generateTripDetailReport(filters models.ReportFilters) (interface{}, int, int, error) {
	// Use raw SQL with JOINs to get trip details with device and driver info
	query := `
		SELECT
			t.id as trip_id,
			COALESCE(cd.name, 'Unknown Device') as device_name,
			COALESCE(cd.plate_number, 'N/A') as plate_number,
			COALESCE(d.name, 'Unknown Driver') as driver_name,
			t.start_time,
			t.end_time,
			t.duration,
			t.distance,
			COALESCE(t.avg_speed, 0) as avg_speed,
			COALESCE(t.max_speed, 0) as max_speed,
			COALESCE(t.start_location, 'Unknown') as start_location,
			COALESCE(t.end_location, 'Unknown') as end_location,
			t.status
		FROM trips t
		LEFT JOIN client_devices cd ON t.client_device_id = cd.id
		LEFT JOIN drivers d ON t.driver_id = d.id
		JOIN client_devices cd2 ON t.client_device_id = cd2.id
		WHERE cd2.client_id = ?
	`

	args := []interface{}{filters.ClientId}

	// Apply date filters
	if filters.StartDate != nil {
		query += " AND t.start_time >= ?"
		args = append(args, *filters.StartDate)
	}
	if filters.EndDate != nil {
		query += " AND t.start_time <= ?"
		args = append(args, *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query += " AND t.client_device_id IN (?)"
		args = append(args, filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query += " AND t.driver_id IN (?)"
		args = append(args, filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		query += " AND cd2.fleet_id IN (?)"
		args = append(args, filters.FleetIds)
	}

	query += " ORDER BY t.start_time DESC"

	// Get total count
	var totalCount int64
	countQuery := `
		SELECT COUNT(*) 
		FROM trips t
		JOIN client_devices cd ON t.client_device_id = cd.id
		WHERE cd.client_id = ?
	`
	countArgs := []interface{}{filters.ClientId}
	if filters.StartDate != nil {
		countQuery += " AND t.start_time >= ?"
		countArgs = append(countArgs, *filters.StartDate)
	}
	if filters.EndDate != nil {
		countQuery += " AND t.start_time <= ?"
		countArgs = append(countArgs, *filters.EndDate)
	}
	config.DB.Raw(countQuery, countArgs...).Scan(&totalCount)

	// Apply pagination
	if filters.PerPage > 0 {
		query += " LIMIT ?"
		args = append(args, filters.PerPage)
		if filters.Page > 0 {
			query += " OFFSET ?"
			args = append(args, (filters.Page-1)*filters.PerPage)
		}
	}

	// Execute query
	var rawData []struct {
		TripID        uint       `json:"trip_id"`
		DeviceName    string     `json:"device_name"`
		PlateNumber   string     `json:"plate_number"`
		DriverName    string     `json:"driver_name"`
		StartTime     time.Time  `json:"start_time"`
		EndTime       *time.Time `json:"end_time"`
		Duration      *int       `json:"duration"`
		Distance      float64    `json:"distance"`
		AvgSpeed      float64    `json:"avg_speed"`
		MaxSpeed      float64    `json:"max_speed"`
		StartLocation string     `json:"start_location"`
		EndLocation   string     `json:"end_location"`
		Status        string     `json:"status"`
	}

	err := config.DB.Raw(query, args...).Scan(&rawData).Error
	if err != nil {
		return nil, 0, 0, err
	}

	// Convert to DTO structure
	var dtoData []models.TripDetailRow
	for _, trip := range rawData {
		endTime := ""
		if trip.EndTime != nil {
			endTime = trip.EndTime.Format("2006-01-02 15:04:05")
		}

		duration := ""
		if trip.Duration != nil {
			duration = FormatDuration(*trip.Duration)
		}

		dtoRow := models.TripDetailRow{
			TripID:        trip.TripID,
			DeviceName:    trip.DeviceName,
			PlateNumber:   trip.PlateNumber,
			DriverName:    trip.DriverName,
			StartTime:     trip.StartTime.Format("2006-01-02 15:04:05"),
			EndTime:       endTime,
			Duration:      duration,
			Distance:      trip.Distance,
			AvgSpeed:      trip.AvgSpeed,
			MaxSpeed:      trip.MaxSpeed,
			StartLocation: trip.StartLocation,
			EndLocation:   trip.EndLocation,
			FuelConsumed:  0.0, // Not available in Trip model
			FuelCost:      0.0, // Not available in Trip model
			Status:        trip.Status,
		}
		dtoData = append(dtoData, dtoRow)
	}

	// Create DTO with headers
	dto := &models.TripDetailDTO{
		Headers: GetHeadersFromStruct([]models.TripDetailRow{}),
		Data:    dtoData,
	}

	return dto, int(totalCount), len(dtoData), nil
}

// generateTripSummaryReport generates trip summary statistics
func (s *ReportService) generateTripSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Detailed data
	data, totalCount, filteredCount, err := s.generateTripDetailReport(filters)
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Summary statistics
	var summary struct {
		TotalTrips    int     `json:"total_trips"`
		TotalDistance float64 `json:"total_distance"`
		TotalDuration int     `json:"total_duration"`
		AvgDistance   float64 `json:"avg_distance"`
		AvgDuration   float64 `json:"avg_duration"`
		AvgSpeed      float64 `json:"avg_speed"`
	}

	query := config.DB.Model(&models.Trip{}).
		Joins("LEFT JOIN client_devices cd ON trips.client_device_id = cd.id")
	query = s.applyTripFilters(query, filters)

	err = query.Select(`
		COUNT(*) as total_trips,
		COALESCE(SUM(trips.distance), 0) as total_distance,
		COALESCE(SUM(trips.duration), 0) as total_duration,
		COALESCE(AVG(trips.distance), 0) as avg_distance,
		COALESCE(AVG(trips.duration), 0) as avg_duration,
		COALESCE(AVG(trips.avg_speed), 0) as avg_speed
	`).Scan(&summary).Error

	return data, summary, totalCount, filteredCount, err
}

// generateSpeedingDetailReport generates speeding events
func (s *ReportService) generateSpeedingDetailReport(filters models.ReportFilters) (interface{}, int, int, error) {
	query := config.DB.Model(&models.DrivingBehaviorEvent{}).
		Where("event_type = ?", models.EventTypeOverspeed).
		Preload("ClientDevice").
		Order("timestamp DESC")

	// Apply filters
	query = s.applyBehaviorFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.DrivingBehaviorEvent{}).Where("event_type = ?", models.EventTypeOverspeed).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []models.DrivingBehaviorEvent
	err := query.Find(&data).Error

	return data, int(totalCount), int(filteredCount), err
}

// generateDriverPerformanceReport generates driver performance summary
func (s *ReportService) generateDriverPerformanceReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Get driver performance data
	var data []struct {
		DriverId      *uint   `json:"driver_id"`
		DriverName    string  `json:"driver_name"`
		TotalTrips    int     `json:"total_trips"`
		TotalDistance float64 `json:"total_distance"`
		TotalEvents   int     `json:"total_events"`
		SafetyScore   float64 `json:"safety_score"`
		AvgSpeed      float64 `json:"avg_speed"`
	}

	query := `
		SELECT 
			t.driver_id,
			COALESCE(d.name, 'Unknown Driver') as driver_name,
			COUNT(DISTINCT t.id) as total_trips,
			COALESCE(SUM(t.distance), 0) as total_distance,
			COUNT(DISTINCT dbe.id) as total_events,
			GREATEST(0, 100 - (COUNT(DISTINCT dbe.id) * 2)) as safety_score,
			COALESCE(AVG(t.avg_speed), 0) as avg_speed
		FROM trips t
		LEFT JOIN drivers d ON t.driver_id = d.id
		LEFT JOIN driving_behavior_events dbe ON t.id = dbe.trip_id
		WHERE 1=1
	`

	// Add date filters
	args := []interface{}{}
	if filters.StartDate != nil {
		query += " AND t.start_time >= ?"
		args = append(args, *filters.StartDate)
	}
	if filters.EndDate != nil {
		query += " AND t.start_time <= ?"
		args = append(args, *filters.EndDate)
	}

	query += " GROUP BY t.driver_id, d.name ORDER BY safety_score DESC"

	err := config.DB.Raw(query, args...).Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Summary
	summary := struct {
		TotalDrivers   int     `json:"total_drivers"`
		AvgSafetyScore float64 `json:"avg_safety_score"`
	}{
		TotalDrivers: len(data),
	}

	if len(data) > 0 {
		var totalScore float64
		for _, d := range data {
			totalScore += d.SafetyScore
		}
		summary.AvgSafetyScore = totalScore / float64(len(data))
	}

	return data, summary, len(data), len(data), nil
}

// Helper functions for applying filters
func (s *ReportService) applyGPSFilters(query *gorm.DB, filters models.ReportFilters) *gorm.DB {
	// CRITICAL: Always filter by client_id for security
	// Note: client_devices cd is already joined in the main query
	query = query.Where("cd.client_id = ?", filters.ClientId)

	if filters.StartDate != nil {
		query = query.Where("g.gps_timestamp >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("g.gps_timestamp <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("g.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if filters.MinSpeed != nil {
		query = query.Where("g.speed >= ?", *filters.MinSpeed)
	}
	if filters.MaxSpeed != nil {
		query = query.Where("g.speed <= ?", *filters.MaxSpeed)
	}
	return query
}

func (s *ReportService) applyTripFilters(query *gorm.DB, filters models.ReportFilters) *gorm.DB {
	// CRITICAL: Always filter by client_id for security
	// Only add JOIN if it doesn't already exist (to avoid duplicate aliases)
	// The main query should already have the client_devices JOIN with alias 'cd'
	query = query.Where("cd.client_id = ?", filters.ClientId)

	if filters.StartDate != nil {
		query = query.Where("start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("start_time <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("driver_id IN ?", filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		query = query.Where("cd.fleet_id IN ?", filters.FleetIds)
	}
	if filters.MinDistance != nil {
		query = query.Where("distance >= ?", *filters.MinDistance)
	}
	if filters.MaxDistance != nil {
		query = query.Where("distance <= ?", *filters.MaxDistance)
	}
	return query
}

func (s *ReportService) applyBehaviorFilters(query *gorm.DB, filters models.ReportFilters) *gorm.DB {
	// CRITICAL: Always filter by client_id for security
	// Use the table alias that's already in the query (either 'dbe' or full table name)
	query = query.Joins("JOIN client_devices cd ON client_device_id = cd.id").
		Where("cd.client_id = ?", filters.ClientId)

	if filters.StartDate != nil {
		query = query.Where("timestamp >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("timestamp <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.EventTypes) > 0 {
		query = query.Where("event_type IN ?", filters.EventTypes)
	}
	if filters.MinSeverity != nil {
		query = query.Where("severity >= ?", *filters.MinSeverity)
	}
	if filters.MaxSeverity != nil {
		query = query.Where("severity <= ?", *filters.MaxSeverity)
	}
	return query
}

// === POWERFUL REPORT GENERATORS ===

// generateDriverSafetyScorecardReport generates comprehensive driver safety scoring using DTO
func (s *ReportService) generateDriverSafetyScorecardReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	var rawData []struct {
		DriverId       *uint   `json:"driver_id"`
		DriverName     string  `json:"driver_name"`
		TotalTrips     int     `json:"total_trips"`
		TotalDistance  float64 `json:"total_distance"`
		SpeedingEvents int     `json:"speeding_events"`
		HarshEvents    int     `json:"harsh_events"`
		SafetyScore    float64 `json:"safety_score"`
		RiskLevel      string  `json:"risk_level"`
	}

	query := `
		SELECT
			t.driver_id,
			COALESCE(d.name, 'Unknown Driver') as driver_name,
			COUNT(DISTINCT t.id) as total_trips,
			COALESCE(SUM(t.distance), 0) as total_distance,
			COUNT(CASE WHEN dbe.event_type = 'overspeed' THEN 1 END) as speeding_events,
			COUNT(CASE WHEN dbe.event_type IN ('harsh_braking', 'harsh_acceleration', 'harsh_cornering') THEN 1 END) as harsh_events,
			GREATEST(0, 100 - (COUNT(dbe.id) * 2)) as safety_score,
			CASE
				WHEN COUNT(dbe.id) = 0 THEN 'Excellent'
				WHEN COUNT(dbe.id) <= 5 THEN 'Good'
				WHEN COUNT(dbe.id) <= 15 THEN 'Fair'
				ELSE 'Critical'
			END as risk_level
		FROM trips t
		LEFT JOIN drivers d ON t.driver_id = d.id
		LEFT JOIN driving_behavior_events dbe ON t.id = dbe.trip_id
		JOIN client_devices cd ON t.client_device_id = cd.id
		WHERE cd.client_id = ?
	`

	args := []interface{}{filters.ClientId}
	if filters.StartDate != nil {
		query += " AND t.start_time >= ?"
		args = append(args, *filters.StartDate)
	}
	if filters.EndDate != nil {
		query += " AND t.start_time <= ?"
		args = append(args, *filters.EndDate)
	}

	query += " GROUP BY t.driver_id, d.name ORDER BY safety_score DESC"

	err := config.DB.Raw(query, args...).Scan(&rawData).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Convert to DTO structure
	var dtoData []models.DriverSafetyScoreRow
	for _, item := range rawData {
		dtoRow := models.DriverSafetyScoreRow{
			DriverName:        item.DriverName,
			DeviceName:        "", // Not available in this query
			PlateNumber:       "", // Not available in this query
			SafetyScore:       item.SafetyScore,
			SpeedingEvents:    item.SpeedingEvents,
			HarshBraking:      0,  // Not broken down in query
			HarshAcceleration: 0,  // Not broken down in query
			HarshCornering:    0,  // Not broken down in query
			IdleTime:          "", // Not available in this query
			TotalTrips:        item.TotalTrips,
			TotalDistance:     item.TotalDistance,
			AvgSpeed:          0.0, // Not available in this query
			RiskLevel:         item.RiskLevel,
		}
		dtoData = append(dtoData, dtoRow)
	}

	// Create summary
	summary := models.DriverSafetySummary{
		TotalDrivers:     len(dtoData),
		AvgSafetyScore:   0.0,
		HighRiskDrivers:  0,
		TotalViolations:  0,
		TotalSpeeding:    0,
		TotalHarshEvents: 0,
	}

	var totalScore float64
	for _, d := range dtoData {
		totalScore += d.SafetyScore
		summary.TotalSpeeding += d.SpeedingEvents
		summary.TotalHarshEvents += d.HarshBraking + d.HarshAcceleration + d.HarshCornering
		if d.RiskLevel == "Critical" || d.RiskLevel == "High" {
			summary.HighRiskDrivers++
		}
	}

	summary.TotalViolations = summary.TotalSpeeding + summary.TotalHarshEvents
	if len(dtoData) > 0 {
		summary.AvgSafetyScore = totalScore / float64(len(dtoData))
	}

	// Create DTO with headers
	dto := &models.DriverSafetyScorecardDTO{
		Headers: GetHeadersFromStruct([]models.DriverSafetyScoreRow{}),
		Data:    dtoData,
		Summary: summary,
	}

	return dto, summary, len(dtoData), len(dtoData), nil
}

// generateSpeedingViolationsReport generates detailed speeding violations
func (s *ReportService) generateSpeedingViolationsReport(filters models.ReportFilters) (interface{}, int, int, error) {
	query := config.DB.Table("driving_behavior_events dbe").
		Where("dbe.event_type = ?", "overspeed").
		Order("dbe.timestamp DESC")

	query = s.applyBehaviorFilters(query, filters)

	var totalCount, filteredCount int64
	config.DB.Table("driving_behavior_events dbe").Where("dbe.event_type = ?", "overspeed").Count(&totalCount)

	// Create a separate query for counting to avoid the column issue
	countQuery := config.DB.Table("driving_behavior_events dbe").Where("dbe.event_type = ?", "overspeed")
	countQuery = s.applyBehaviorFilters(countQuery, filters)
	countQuery.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []models.DrivingBehaviorEvent
	err := query.Find(&data).Error
	return data, int(totalCount), int(filteredCount), err
}

// Placeholder functions for other powerful reports
func (s *ReportService) generateGeofenceActivityReport(filters models.ReportFilters) (interface{}, int, int, error) {
	// TODO: Implement when geofence system is ready
	return []string{"Geofence activity report - coming soon"}, 0, 0, nil
}

func (s *ReportService) generateFleetROIDashboard(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// TODO: Implement ROI calculations
	summary := map[string]interface{}{
		"message":        "Fleet ROI dashboard - coming soon",
		"roi_percentage": 15.5,
		"cost_savings":   25000,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateExecutiveFleetSummary(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// TODO: Implement executive summary
	summary := map[string]interface{}{
		"message":          "Executive fleet summary - coming soon",
		"total_vehicles":   10,
		"total_distance":   50000,
		"avg_safety_score": 85.5,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateVehicleUtilizationReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// TODO: Implement utilization calculations
	summary := map[string]interface{}{
		"message":         "Vehicle utilization report - coming soon",
		"avg_utilization": 75.5,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateFuelConsumptionAnalysis(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// TODO: Implement fuel analysis
	summary := map[string]interface{}{
		"message":           "Fuel consumption analysis - coming soon",
		"total_fuel_cost":   15000,
		"efficiency_rating": "Good",
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateEmergencyResponseReport(filters models.ReportFilters) (interface{}, int, int, error) {
	// TODO: Implement emergency response tracking
	return []string{"Emergency response report - coming soon"}, 0, 0, nil
}

// === CRUCIAL MISSING REPORTS ===

func (s *ReportService) generateRealTimeDashboardReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Real-time fleet status with current locations and operational overview
	query := config.DB.Table("client_devices cd").
		Select(`
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			cd.status as device_status,
			g.gps_timestamp as last_seen,
			g.latitude as current_latitude,
			g.longitude as current_longitude,
			g.speed as current_speed,
			g.direction as current_heading,
			d.name as driver_name,
			t.id as current_trip_id,
			t.start_time as trip_start_time,
			t.distance as trip_distance,
			t.duration as trip_duration,
			CASE 
				WHEN g.gps_timestamp > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 'Online'
				WHEN g.gps_timestamp > DATE_SUB(NOW(), INTERVAL 30 MINUTE) THEN 'Offline'
				ELSE 'Inactive'
			END as connectivity_status
		`).
		Joins("LEFT JOIN drivers d ON cd.driver_id = d.id").
		Joins("LEFT JOIN trips t ON cd.id = t.client_device_id AND t.end_time IS NULL").
		Joins("LEFT JOIN (SELECT g1.* FROM gps_data g1 INNER JOIN (SELECT client_device_id, MAX(gps_timestamp) as max_timestamp FROM gps_data GROUP BY client_device_id) g2 ON g1.client_device_id = g2.client_device_id AND g1.gps_timestamp = g2.max_timestamp) g ON cd.id = g.client_device_id").
		Order("g.gps_timestamp DESC")

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("cd.id IN (?)", filters.ClientDeviceIds)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.ClientDevice{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		DeviceId           uint       `json:"device_id"`
		DeviceName         string     `json:"device_name"`
		PlateNumber        string     `json:"plate_number"`
		DeviceStatus       string     `json:"device_status"`
		LastSeen           *time.Time `json:"last_seen"`
		CurrentLatitude    *float64   `json:"current_latitude"`
		CurrentLongitude   *float64   `json:"current_longitude"`
		CurrentSpeed       *float64   `json:"current_speed"`
		CurrentHeading     *float64   `json:"current_heading"`
		DriverName         *string    `json:"driver_name"`
		CurrentTripId      *uint      `json:"current_trip_id"`
		TripStartTime      *time.Time `json:"trip_start_time"`
		TripDistance       *float64   `json:"trip_distance"`
		TripDuration       *int       `json:"trip_duration"`
		ConnectivityStatus string     `json:"connectivity_status"`
		AlertCount         int        `json:"alert_count"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate alert counts for each device
	for i := range data {
		var alertCount int64
		config.DB.Model(&models.DrivingBehaviorEvent{}).
			Where("client_device_id = ? AND timestamp > DATE_SUB(NOW(), INTERVAL 24 HOUR)", data[i].DeviceId).
			Count(&alertCount)
		data[i].AlertCount = int(alertCount)
	}

	// Summary statistics
	summary := struct {
		TotalVehicles    int     `json:"total_vehicles"`
		OnlineVehicles   int     `json:"online_vehicles"`
		ActiveTrips      int     `json:"active_trips"`
		TotalAlerts      int     `json:"total_alerts"`
		AvgSpeed         float64 `json:"avg_speed"`
		FleetUtilization float64 `json:"fleet_utilization"`
	}{
		TotalVehicles: len(data),
	}

	if len(data) > 0 {
		var totalSpeed float64
		var speedCount int
		var totalAlerts int

		for _, d := range data {
			if d.ConnectivityStatus == "Online" {
				summary.OnlineVehicles++
			}
			if d.CurrentTripId != nil {
				summary.ActiveTrips++
			}
			if d.CurrentSpeed != nil {
				totalSpeed += *d.CurrentSpeed
				speedCount++
			}
			totalAlerts += d.AlertCount
		}

		summary.TotalAlerts = totalAlerts
		if speedCount > 0 {
			summary.AvgSpeed = totalSpeed / float64(speedCount)
		}
		if summary.TotalVehicles > 0 {
			summary.FleetUtilization = float64(summary.ActiveTrips) / float64(summary.TotalVehicles) * 100
		}
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateDriverScorecardReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Comprehensive driver performance scoring
	query := config.DB.Table("drivers d").
		Select(`
			d.id as driver_id,
			d.name as driver_name,
			d.driver_license_no as license_number,
			d.phone_number as phone,
			COUNT(DISTINCT t.id) as total_trips,
			SUM(t.distance) as total_distance,
			SUM(t.duration) as total_duration,
			AVG(t.avg_speed) as avg_speed,
			COUNT(DISTINCT dbe.id) as total_violations,
			SUM(CASE WHEN dbe.event_type = 'overspeed' THEN 1 ELSE 0 END) as speeding_violations,
			SUM(CASE WHEN dbe.event_type IN ('harsh_acceleration', 'harsh_braking', 'harsh_cornering') THEN 1 ELSE 0 END) as harsh_driving_violations,
			SUM(CASE WHEN t.duration > 28800 THEN 1 ELSE 0 END) as overtime_trips
		`).
		Joins("LEFT JOIN trips t ON d.id = t.driver_id").
		Joins("LEFT JOIN driving_behavior_events dbe ON t.id = dbe.trip_id").
		Group("d.id").
		Order("COUNT(DISTINCT dbe.id) ASC, AVG(t.avg_speed) DESC")

	// Apply filters
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN (?)", filters.DriverIds)
	}
	if filters.StartDate != nil {
		query = query.Where("t.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("t.start_time <= ?", *filters.EndDate)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Driver{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		DriverId               uint    `json:"driver_id"`
		DriverName             string  `json:"driver_name"`
		LicenseNumber          string  `json:"license_number"`
		Phone                  string  `json:"phone"`
		TotalTrips             int     `json:"total_trips"`
		TotalDistance          float64 `json:"total_distance"`
		TotalDuration          int     `json:"total_duration"`
		AvgSpeed               float64 `json:"avg_speed"`
		TotalViolations        int     `json:"total_violations"`
		SpeedingViolations     int     `json:"speeding_violations"`
		HarshDrivingViolations int     `json:"harsh_driving_violations"`
		OvertimeTrips          int     `json:"overtime_trips"`
		SafetyScore            float64 `json:"safety_score"`
		EfficiencyScore        float64 `json:"efficiency_score"`
		ComplianceScore        float64 `json:"compliance_score"`
		OverallScore           float64 `json:"overall_score"`
		PerformanceGrade       string  `json:"performance_grade"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate performance scores
	for i := range data {
		// Safety Score (0-100) - based on violations
		safetyScore := 100.0
		if data[i].TotalTrips > 0 {
			violationRate := float64(data[i].TotalViolations) / float64(data[i].TotalTrips)
			safetyScore = 100.0 - (violationRate * 100.0)
			if safetyScore < 0 {
				safetyScore = 0
			}
		}

		// Efficiency Score (0-100) - based on speed and distance
		efficiencyScore := 0.0
		if data[i].TotalDistance > 0 && data[i].TotalDuration > 0 {
			avgSpeed := data[i].TotalDistance / (float64(data[i].TotalDuration) / 3600.0)
			if avgSpeed > 0 {
				efficiencyScore = (avgSpeed / 80.0) * 100.0 // Assuming 80 km/h is optimal
				if efficiencyScore > 100 {
					efficiencyScore = 100
				}
			}
		}

		// Compliance Score (0-100) - based on overtime
		complianceScore := 100.0
		if data[i].TotalTrips > 0 {
			overtimeRate := float64(data[i].OvertimeTrips) / float64(data[i].TotalTrips)
			complianceScore = 100.0 - (overtimeRate * 100.0)
			if complianceScore < 0 {
				complianceScore = 0
			}
		}

		// Overall Score (weighted average)
		overallScore := (safetyScore * 0.4) + (efficiencyScore * 0.3) + (complianceScore * 0.3)

		// Performance Grade
		var grade string
		switch {
		case overallScore >= 90:
			grade = "A"
		case overallScore >= 80:
			grade = "B"
		case overallScore >= 70:
			grade = "C"
		case overallScore >= 60:
			grade = "D"
		default:
			grade = "F"
		}

		data[i].SafetyScore = safetyScore
		data[i].EfficiencyScore = efficiencyScore
		data[i].ComplianceScore = complianceScore
		data[i].OverallScore = overallScore
		data[i].PerformanceGrade = grade
	}

	// Summary statistics
	summary := struct {
		TotalDrivers       int     `json:"total_drivers"`
		AvgSafetyScore     float64 `json:"avg_safety_score"`
		AvgEfficiencyScore float64 `json:"avg_efficiency_score"`
		AvgComplianceScore float64 `json:"avg_compliance_score"`
		AvgOverallScore    float64 `json:"avg_overall_score"`
		TopPerformer       string  `json:"top_performer"`
		NeedsImprovement   int     `json:"needs_improvement"`
	}{
		TotalDrivers: len(data),
	}

	if len(data) > 0 {
		var totalSafety, totalEfficiency, totalCompliance, totalOverall float64
		var topPerformer string
		var maxScore float64
		var needsImprovement int

		for _, d := range data {
			totalSafety += d.SafetyScore
			totalEfficiency += d.EfficiencyScore
			totalCompliance += d.ComplianceScore
			totalOverall += d.OverallScore

			if d.OverallScore > maxScore {
				maxScore = d.OverallScore
				topPerformer = d.DriverName
			}

			if d.PerformanceGrade == "D" || d.PerformanceGrade == "F" {
				needsImprovement++
			}
		}

		summary.AvgSafetyScore = totalSafety / float64(len(data))
		summary.AvgEfficiencyScore = totalEfficiency / float64(len(data))
		summary.AvgComplianceScore = totalCompliance / float64(len(data))
		summary.AvgOverallScore = totalOverall / float64(len(data))
		summary.TopPerformer = topPerformer
		summary.NeedsImprovement = needsImprovement
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateFleetHealthMonitorReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Overall fleet health status with maintenance alerts
	query := config.DB.Table("client_devices cd").
		Select(`
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			cd.status as device_status,
			MAX(g.gps_timestamp) as last_seen,
			MAX(g.latitude) as current_latitude,
			MAX(g.longitude) as current_longitude,
			SUM(t.distance) as total_mileage,
			COUNT(t.id) as total_trips,
			MAX(t.end_time) as last_trip_date,
			CASE 
				WHEN SUM(t.distance) >= 10000 THEN 'Due for major service'
				WHEN SUM(t.distance) >= 5000 THEN 'Due for minor service'
				ELSE 'No service required'
			END as maintenance_status,
			CASE 
				WHEN MAX(g.gps_timestamp) > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 'Online'
				WHEN MAX(g.gps_timestamp) > DATE_SUB(NOW(), INTERVAL 30 MINUTE) THEN 'Offline'
				ELSE 'Inactive'
			END as connectivity_status
		`).
		Joins("LEFT JOIN trips t ON cd.id = t.client_device_id").
		Joins("LEFT JOIN (SELECT g1.* FROM gps_data g1 INNER JOIN (SELECT client_device_id, MAX(gps_timestamp) as max_timestamp FROM gps_data GROUP BY client_device_id) g2 ON g1.client_device_id = g2.client_device_id AND g1.gps_timestamp = g2.max_timestamp) g ON cd.id = g.client_device_id").
		Group("cd.id").
		Order("CASE WHEN SUM(t.distance) >= 10000 THEN 3 WHEN SUM(t.distance) >= 5000 THEN 2 ELSE 1 END DESC, SUM(t.distance) DESC")

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("cd.id IN (?)", filters.ClientDeviceIds)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.ClientDevice{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		DeviceId           uint       `json:"device_id"`
		DeviceName         string     `json:"device_name"`
		PlateNumber        string     `json:"plate_number"`
		DeviceStatus       string     `json:"device_status"`
		LastSeen           *time.Time `json:"last_seen"`
		CurrentLatitude    *float64   `json:"current_latitude"`
		CurrentLongitude   *float64   `json:"current_longitude"`
		TotalMileage       float64    `json:"total_mileage"`
		TotalTrips         int        `json:"total_trips"`
		LastTripDate       *time.Time `json:"last_trip_date"`
		MaintenanceStatus  string     `json:"maintenance_status"`
		ConnectivityStatus string     `json:"connectivity_status"`
		HealthScore        float64    `json:"health_score"`
		AlertLevel         string     `json:"alert_level"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate health scores and alert levels
	for i := range data {
		healthScore := 100.0

		// Connectivity impact
		switch data[i].ConnectivityStatus {
		case "Online":
			healthScore += 0
		case "Offline":
			healthScore -= 20
		case "Inactive":
			healthScore -= 50
		}

		// Maintenance impact
		switch data[i].MaintenanceStatus {
		case "Due for major service":
			healthScore -= 30
		case "Due for minor service":
			healthScore -= 15
		}

		// Recent activity impact
		if data[i].LastTripDate != nil {
			daysSinceLastTrip := time.Since(*data[i].LastTripDate).Hours() / 24
			if daysSinceLastTrip > 7 {
				healthScore -= 10
			}
		}

		if healthScore < 0 {
			healthScore = 0
		}

		// Alert level
		var alertLevel string
		switch {
		case healthScore >= 80:
			alertLevel = "Good"
		case healthScore >= 60:
			alertLevel = "Warning"
		case healthScore >= 40:
			alertLevel = "Critical"
		default:
			alertLevel = "Emergency"
		}

		data[i].HealthScore = healthScore
		data[i].AlertLevel = alertLevel
	}

	// Summary statistics
	summary := struct {
		TotalVehicles   int     `json:"total_vehicles"`
		OnlineVehicles  int     `json:"online_vehicles"`
		MaintenanceDue  int     `json:"maintenance_due"`
		AvgHealthScore  float64 `json:"avg_health_score"`
		CriticalAlerts  int     `json:"critical_alerts"`
		EmergencyAlerts int     `json:"emergency_alerts"`
		FleetReadiness  float64 `json:"fleet_readiness"`
	}{
		TotalVehicles: len(data),
	}

	if len(data) > 0 {
		var totalHealth float64
		var criticalAlerts, emergencyAlerts int

		for _, d := range data {
			if d.ConnectivityStatus == "Online" {
				summary.OnlineVehicles++
			}
			if d.MaintenanceStatus != "No service required" {
				summary.MaintenanceDue++
			}
			totalHealth += d.HealthScore

			switch d.AlertLevel {
			case "Critical":
				criticalAlerts++
			case "Emergency":
				emergencyAlerts++
			}
		}

		summary.AvgHealthScore = totalHealth / float64(len(data))
		summary.CriticalAlerts = criticalAlerts
		summary.EmergencyAlerts = emergencyAlerts
		if summary.TotalVehicles > 0 {
			summary.FleetReadiness = float64(summary.OnlineVehicles) / float64(summary.TotalVehicles) * 100
		}
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateCostAnalysisDashboardReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Comprehensive cost analysis with fuel, maintenance, and operational costs
	query := config.DB.Table("trips t").
		Select(`
			DATE_FORMAT(t.start_time, '%Y-%m') as month,
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			SUM(t.distance) as total_distance,
			COUNT(t.id) as total_trips,
			SUM(t.duration) as total_duration,
			(SUM(t.distance) * 0.12) as estimated_fuel_cost,
			(SUM(t.distance) * 0.05) as estimated_maintenance_cost,
			(SUM(t.distance) * 0.03) as estimated_insurance_cost,
			(SUM(t.distance) * 0.02) as estimated_depreciation_cost,
			(SUM(t.distance) * 0.22) as total_operational_cost,
			(SUM(t.distance) * 0.22 / SUM(t.distance)) as cost_per_km
		`).
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m'), cd.id").
		Order("DATE_FORMAT(t.start_time, '%Y-%m') DESC, (SUM(t.distance) * 0.22) DESC")

	// Apply filters
	query = s.applyTripFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Month                     string  `json:"month"`
		DeviceId                  uint    `json:"device_id"`
		DeviceName                string  `json:"device_name"`
		PlateNumber               string  `json:"plate_number"`
		TotalDistance             float64 `json:"total_distance"`
		TotalTrips                int     `json:"total_trips"`
		TotalDuration             int     `json:"total_duration"`
		EstimatedFuelCost         float64 `json:"estimated_fuel_cost"`
		EstimatedMaintenanceCost  float64 `json:"estimated_maintenance_cost"`
		EstimatedInsuranceCost    float64 `json:"estimated_insurance_cost"`
		EstimatedDepreciationCost float64 `json:"estimated_depreciation_cost"`
		TotalOperationalCost      float64 `json:"total_operational_cost"`
		CostPerKm                 float64 `json:"cost_per_km"`
		CostEfficiency            string  `json:"cost_efficiency"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate cost efficiency ratings
	for i := range data {
		var efficiency string
		switch {
		case data[i].CostPerKm <= 0.15:
			efficiency = "Excellent"
		case data[i].CostPerKm <= 0.20:
			efficiency = "Good"
		case data[i].CostPerKm <= 0.25:
			efficiency = "Average"
		case data[i].CostPerKm <= 0.30:
			efficiency = "Poor"
		default:
			efficiency = "Very Poor"
		}
		data[i].CostEfficiency = efficiency
	}

	// Summary statistics
	summary := struct {
		TotalMonths           int     `json:"total_months"`
		TotalOperationalCost  float64 `json:"total_operational_cost"`
		TotalFuelCost         float64 `json:"total_fuel_cost"`
		TotalMaintenanceCost  float64 `json:"total_maintenance_cost"`
		TotalInsuranceCost    float64 `json:"total_insurance_cost"`
		TotalDepreciationCost float64 `json:"total_depreciation_cost"`
		AvgCostPerKm          float64 `json:"avg_cost_per_km"`
		MostExpensiveVehicle  string  `json:"most_expensive_vehicle"`
		MostEfficientVehicle  string  `json:"most_efficient_vehicle"`
	}{
		TotalMonths: len(data),
	}

	if len(data) > 0 {
		var totalOperational, totalFuel, totalMaintenance, totalInsurance, totalDepreciation, totalCostPerKm float64
		var mostExpensive, mostEfficient string
		var maxCost, minCostPerKm float64

		for _, d := range data {
			totalOperational += d.TotalOperationalCost
			totalFuel += d.EstimatedFuelCost
			totalMaintenance += d.EstimatedMaintenanceCost
			totalInsurance += d.EstimatedInsuranceCost
			totalDepreciation += d.EstimatedDepreciationCost
			totalCostPerKm += d.CostPerKm

			if d.TotalOperationalCost > maxCost {
				maxCost = d.TotalOperationalCost
				mostExpensive = d.DeviceName
			}
			if d.CostPerKm < minCostPerKm || minCostPerKm == 0 {
				minCostPerKm = d.CostPerKm
				mostEfficient = d.DeviceName
			}
		}

		summary.TotalOperationalCost = totalOperational
		summary.TotalFuelCost = totalFuel
		summary.TotalMaintenanceCost = totalMaintenance
		summary.TotalInsuranceCost = totalInsurance
		summary.TotalDepreciationCost = totalDepreciation
		summary.AvgCostPerKm = totalCostPerKm / float64(len(data))
		summary.MostExpensiveVehicle = mostExpensive
		summary.MostEfficientVehicle = mostEfficient
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateComplianceDashboardReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Regulatory compliance overview with hours of service and safety violations
	query := config.DB.Table("drivers d").
		Select(`
			DATE_FORMAT(t.start_time, '%Y-%m') as month,
			d.id as driver_id,
			d.name as driver_name,
			d.driver_license_no as license_number,
			COUNT(t.id) as total_trips,
			SUM(t.duration) as total_working_hours,
			SUM(CASE WHEN t.duration > 28800 THEN t.duration - 28800 ELSE 0 END) as overtime_hours,
			COUNT(CASE WHEN t.duration > 28800 THEN 1 END) as overtime_violations,
			COUNT(DISTINCT dbe.id) as total_safety_violations,
			SUM(CASE WHEN dbe.event_type = 'overspeed' THEN 1 ELSE 0 END) as speeding_violations,
			SUM(CASE WHEN dbe.event_type IN ('harsh_acceleration', 'harsh_braking', 'harsh_cornering') THEN 1 ELSE 0 END) as harsh_driving_violations,
			SUM(CASE WHEN dbe.event_type = 'geofence_violation' THEN 1 ELSE 0 END) as geofence_violations
		`).
		Joins("LEFT JOIN trips t ON d.id = t.driver_id").
		Joins("LEFT JOIN driving_behavior_events dbe ON t.id = dbe.trip_id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m'), d.id").
		Order("DATE_FORMAT(t.start_time, '%Y-%m') DESC, SUM(CASE WHEN dbe.event_type IN ('overspeed', 'harsh_braking', 'harsh_acceleration', 'harsh_cornering') THEN 1 ELSE 0 END) DESC")

	// Apply filters
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN (?)", filters.DriverIds)
	}
	if filters.StartDate != nil {
		query = query.Where("t.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("t.start_time <= ?", *filters.EndDate)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Driver{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Month                  string  `json:"month"`
		DriverId               uint    `json:"driver_id"`
		DriverName             string  `json:"driver_name"`
		LicenseNumber          string  `json:"license_number"`
		TotalTrips             int     `json:"total_trips"`
		TotalWorkingHours      int     `json:"total_working_hours"`
		OvertimeHours          int     `json:"overtime_hours"`
		OvertimeViolations     int     `json:"overtime_violations"`
		TotalSafetyViolations  int     `json:"total_safety_violations"`
		SpeedingViolations     int     `json:"speeding_violations"`
		HarshDrivingViolations int     `json:"harsh_driving_violations"`
		GeofenceViolations     int     `json:"geofence_violations"`
		ComplianceScore        float64 `json:"compliance_score"`
		RiskLevel              string  `json:"risk_level"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate compliance scores and risk levels
	for i := range data {
		complianceScore := 100.0

		// Hours of service compliance
		if data[i].TotalWorkingHours > 0 {
			overtimeRate := float64(data[i].OvertimeHours) / float64(data[i].TotalWorkingHours)
			complianceScore -= overtimeRate * 30 // Up to 30 points deduction for overtime
		}

		// Safety violations
		if data[i].TotalTrips > 0 {
			violationRate := float64(data[i].TotalSafetyViolations) / float64(data[i].TotalTrips)
			complianceScore -= violationRate * 50 // Up to 50 points deduction for violations
		}

		if complianceScore < 0 {
			complianceScore = 0
		}

		// Risk level
		var riskLevel string
		switch {
		case complianceScore >= 90:
			riskLevel = "Low"
		case complianceScore >= 70:
			riskLevel = "Medium"
		case complianceScore >= 50:
			riskLevel = "High"
		default:
			riskLevel = "Critical"
		}

		data[i].ComplianceScore = complianceScore
		data[i].RiskLevel = riskLevel
	}

	// Summary statistics
	summary := struct {
		TotalDrivers        int     `json:"total_drivers"`
		TotalViolations     int     `json:"total_violations"`
		TotalOvertimeHours  int     `json:"total_overtime_hours"`
		AvgComplianceScore  float64 `json:"avg_compliance_score"`
		HighRiskDrivers     int     `json:"high_risk_drivers"`
		CriticalRiskDrivers int     `json:"critical_risk_drivers"`
		ComplianceRate      float64 `json:"compliance_rate"`
	}{
		TotalDrivers: len(data),
	}

	if len(data) > 0 {
		var totalViolations, totalOvertime, totalCompliance float64
		var highRisk, criticalRisk int

		for _, d := range data {
			totalViolations += float64(d.TotalSafetyViolations)
			totalOvertime += float64(d.OvertimeHours)
			totalCompliance += d.ComplianceScore

			switch d.RiskLevel {
			case "High":
				highRisk++
			case "Critical":
				criticalRisk++
			}
		}

		summary.TotalViolations = int(totalViolations)
		summary.TotalOvertimeHours = int(totalOvertime)
		summary.AvgComplianceScore = totalCompliance / float64(len(data))
		summary.HighRiskDrivers = highRisk
		summary.CriticalRiskDrivers = criticalRisk
		if summary.TotalDrivers > 0 {
			summary.ComplianceRate = float64(summary.TotalDrivers-criticalRisk-highRisk) / float64(summary.TotalDrivers) * 100
		}
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// === ADDITIONAL MISSING REPORTS ===

func (s *ReportService) generateMileageDaySummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Daily mileage summary with aggregation
	query := config.DB.Table("trips t").
		Select(`
			DATE_FORMAT(t.start_time, '%Y-%m-%d') as date,
			cd.name as device_name,
			cd.plate_number,
			SUM(t.distance) as total_distance,
			SUM(t.duration) as total_duration,
			COUNT(*) as trip_count,
			AVG(t.avg_speed) as avg_speed,
			(SUM(t.distance) / 1000.0) as distance_km
		`).
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m-%d'), cd.id").
		Order("date DESC, total_distance DESC")

	// Apply filters
	query = s.applyTripFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)

	// Create a separate count query without ORDER BY to avoid column reference issues
	countQuery := config.DB.Table("trips t").
		Select("COUNT(*)").
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m-%d'), cd.id")
	countQuery = s.applyTripFilters(countQuery, filters)
	countQuery.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Date          string  `json:"date"`
		DeviceName    string  `json:"device_name"`
		PlateNumber   string  `json:"plate_number"`
		TotalDistance float64 `json:"total_distance"`
		TotalDuration int     `json:"total_duration"`
		TripCount     int     `json:"trip_count"`
		AvgSpeed      float64 `json:"avg_speed"`
		DistanceKm    float64 `json:"distance_km"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Summary statistics
	summary := struct {
		TotalDays        int     `json:"total_days"`
		TotalDistance    float64 `json:"total_distance"`
		TotalTrips       int     `json:"total_trips"`
		AvgDailyDistance float64 `json:"avg_daily_distance"`
		MostActiveDay    string  `json:"most_active_day"`
	}{
		TotalDays: len(data),
	}

	if len(data) > 0 {
		var totalDistance float64
		var totalTrips int
		var mostActiveDay string
		var maxDistance float64

		for _, d := range data {
			totalDistance += d.TotalDistance
			totalTrips += d.TripCount
			if d.TotalDistance > maxDistance {
				maxDistance = d.TotalDistance
				mostActiveDay = d.Date
			}
		}

		summary.TotalDistance = totalDistance
		summary.TotalTrips = totalTrips
		summary.AvgDailyDistance = totalDistance / float64(len(data))
		summary.MostActiveDay = mostActiveDay
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateTripDaySummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Daily trip summary with trip count and metrics
	query := config.DB.Table("trips").
		Select(`
			DATE_FORMAT(trips.start_time, '%Y-%m-%d') as date,
			cd.name as device_name,
			cd.plate_number,
			COUNT(*) as trip_count,
			SUM(trips.distance) as total_distance,
			SUM(trips.duration) as total_duration,
			AVG(trips.avg_speed) as avg_speed,
			AVG(trips.distance) as avg_trip_distance
		`).
		Joins("LEFT JOIN client_devices cd ON trips.client_device_id = cd.id").
		Group("DATE_FORMAT(trips.start_time, '%Y-%m-%d'), cd.id").
		Order("DATE_FORMAT(trips.start_time, '%Y-%m-%d') DESC, COUNT(*) DESC")

	// Apply filters manually to avoid duplicate table aliases
	if filters.StartDate != nil {
		query = query.Where("trips.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("trips.start_time <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("trips.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("trips.driver_id IN ?", filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		query = query.Where("cd.fleet_id IN ?", filters.FleetIds)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Date            string  `json:"date"`
		DeviceName      string  `json:"device_name"`
		PlateNumber     string  `json:"plate_number"`
		TripCount       int     `json:"trip_count"`
		TotalDistance   float64 `json:"total_distance"`
		TotalDuration   int     `json:"total_duration"`
		AvgSpeed        float64 `json:"avg_speed"`
		AvgTripDistance float64 `json:"avg_trip_distance"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Summary statistics
	summary := struct {
		TotalDays      int     `json:"total_days"`
		TotalTrips     int     `json:"total_trips"`
		TotalDistance  float64 `json:"total_distance"`
		AvgTripsPerDay float64 `json:"avg_trips_per_day"`
		BusiestDay     string  `json:"busiest_day"`
	}{
		TotalDays: len(data),
	}

	if len(data) > 0 {
		var totalTrips int
		var totalDistance float64
		var busiestDay string
		var maxTrips int

		for _, d := range data {
			totalTrips += d.TripCount
			totalDistance += d.TotalDistance
			if d.TripCount > maxTrips {
				maxTrips = d.TripCount
				busiestDay = d.Date
			}
		}

		summary.TotalTrips = totalTrips
		summary.TotalDistance = totalDistance
		summary.AvgTripsPerDay = float64(totalTrips) / float64(len(data))
		summary.BusiestDay = busiestDay
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateWorkingTimeDaySummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	log.Printf("Starting generateWorkingTimeDaySummaryReport")
	// Daily working hours per driver with overtime analysis
	query := config.DB.Table("trips").
		Select(`
			DATE_FORMAT(trips.start_time, '%Y-%m-%d') as date,
			COALESCE(d.name, 'Unknown Driver') as driver_name,
			d.id as driver_id,
			COALESCE(SUM(trips.duration), 0) as total_working_time,
			COUNT(*) as trip_count,
			COALESCE(SUM(trips.distance), 0) as total_distance,
			CASE WHEN SUM(trips.duration) > 28800 THEN 'Overtime' ELSE 'Normal' END as work_status
		`).
		Joins("LEFT JOIN drivers d ON trips.driver_id = d.id").
		Joins("LEFT JOIN client_devices cd ON trips.client_device_id = cd.id").
		Where("cd.client_id = ?", filters.ClientId).
		Group("DATE_FORMAT(trips.start_time, '%Y-%m-%d'), d.id").
		Order("DATE_FORMAT(trips.start_time, '%Y-%m-%d') DESC, total_working_time DESC")

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("trips.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("trips.start_time <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("trips.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN ?", filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		query = query.Where("cd.fleet_id IN ?", filters.FleetIds)
	}

	// Get total count - need to count distinct combinations
	var totalCount int64
	countQuery := config.DB.Table("trips").
		Select("COUNT(DISTINCT CONCAT(DATE_FORMAT(trips.start_time, '%Y-%m-%d'), '-', d.id))").
		Joins("LEFT JOIN drivers d ON trips.driver_id = d.id").
		Joins("LEFT JOIN client_devices cd ON trips.client_device_id = cd.id").
		Where("cd.client_id = ?", filters.ClientId)

	if filters.StartDate != nil {
		countQuery = countQuery.Where("trips.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		countQuery = countQuery.Where("trips.start_time <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		countQuery = countQuery.Where("trips.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		countQuery = countQuery.Where("d.id IN ?", filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		countQuery = countQuery.Where("cd.fleet_id IN ?", filters.FleetIds)
	}

	countQuery.Count(&totalCount)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	var data []struct {
		Date             string  `json:"date"`
		DriverName       string  `json:"driver_name"`
		DriverId         uint    `json:"driver_id"`
		TotalWorkingTime int     `json:"total_working_time"`
		TripCount        int     `json:"trip_count"`
		TotalDistance    float64 `json:"total_distance"`
		WorkStatus       string  `json:"work_status"`
		OvertimeHours    float64 `json:"overtime_hours"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		log.Printf("Error in generateWorkingTimeDaySummaryReport: %v", err)
		return nil, nil, 0, 0, err
	}
	log.Printf("Successfully generated working time day summary report with %d records", len(data))

	// Calculate overtime hours
	for i := range data {
		if data[i].TotalWorkingTime > 28800 { // 8 hours in seconds
			data[i].OvertimeHours = float64(data[i].TotalWorkingTime-28800) / 3600.0
		}
	}

	// Summary statistics
	summary := struct {
		TotalDays        int     `json:"total_days"`
		TotalDrivers     int     `json:"total_divers"`
		TotalWorkingTime int     `json:"total_working_time"`
		OvertimeDays     int     `json:"overtime_days"`
		AvgWorkingTime   float64 `json:"avg_working_time"`
	}{
		TotalDays: len(data),
	}

	if len(data) > 0 {
		var totalWorkingTime int
		var overtimeDays int
		driverMap := make(map[uint]bool)

		for _, d := range data {
			totalWorkingTime += d.TotalWorkingTime
			driverMap[d.DriverId] = true
			if d.WorkStatus == "Overtime" {
				overtimeDays++
			}
		}

		summary.TotalDrivers = len(driverMap)
		summary.TotalWorkingTime = totalWorkingTime
		summary.OvertimeDays = overtimeDays
		summary.AvgWorkingTime = float64(totalWorkingTime) / float64(len(data))
	}

	return data, summary, int(totalCount), len(data), nil
}

func (s *ReportService) generateMileageMonthSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Monthly mileage summary with cost analysis
	query := `
		SELECT
			DATE_FORMAT(t.start_time, '%Y-%m') as month,
			COALESCE(cd.name, 'Unknown Device') as device_name,
			COALESCE(cd.plate_number, 'N/A') as plate_number,
			COALESCE(SUM(t.distance), 0) as total_distance,
			COUNT(*) as trip_count,
			COALESCE(AVG(t.avg_speed), 0) as avg_speed,
			COALESCE(SUM(t.distance) * 0.12, 0) as estimated_fuel_consumption,
			COALESCE(SUM(t.distance) * 0.12 * 1.5, 0) as estimated_fuel_cost
		FROM trips t
		LEFT JOIN client_devices cd ON t.client_device_id = cd.id
		WHERE cd.client_id = ?
	`

	args := []interface{}{filters.ClientId}

	// Apply filters
	if filters.StartDate != nil {
		query += " AND t.start_time >= ?"
		args = append(args, *filters.StartDate)
	}
	if filters.EndDate != nil {
		query += " AND t.start_time <= ?"
		args = append(args, *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query += " AND t.client_device_id IN (?)"
		args = append(args, filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query += " AND t.driver_id IN (?)"
		args = append(args, filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		query += " AND cd.fleet_id IN (?)"
		args = append(args, filters.FleetIds)
	}

	query += " GROUP BY DATE_FORMAT(t.start_time, '%Y-%m'), cd.id ORDER BY month DESC, total_distance DESC"

	// Get total count
	var totalCount int64
	countQuery := `
		SELECT COUNT(DISTINCT CONCAT(DATE_FORMAT(t.start_time, '%Y-%m'), '-', cd.id)) 
		FROM trips t
		LEFT JOIN client_devices cd ON t.client_device_id = cd.id
		WHERE cd.client_id = ?
	`
	countArgs := []interface{}{filters.ClientId}
	if filters.StartDate != nil {
		countQuery += " AND t.start_time >= ?"
		countArgs = append(countArgs, *filters.StartDate)
	}
	if filters.EndDate != nil {
		countQuery += " AND t.start_time <= ?"
		countArgs = append(countArgs, *filters.EndDate)
	}
	config.DB.Raw(countQuery, countArgs...).Scan(&totalCount)

	// Apply pagination
	if filters.PerPage > 0 {
		query += " LIMIT ?"
		args = append(args, filters.PerPage)
		if filters.Page > 0 {
			query += " OFFSET ?"
			args = append(args, (filters.Page-1)*filters.PerPage)
		}
	}

	var data []struct {
		Month                    string  `json:"month"`
		DeviceName               string  `json:"device_name"`
		PlateNumber              string  `json:"plate_number"`
		TotalDistance            float64 `json:"total_distance"`
		TripCount                int     `json:"trip_count"`
		AvgSpeed                 float64 `json:"avg_speed"`
		EstimatedFuelConsumption float64 `json:"estimated_fuel_consumption"`
		EstimatedFuelCost        float64 `json:"estimated_fuel_cost"`
	}

	err := config.DB.Raw(query, args...).Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Summary statistics
	summary := struct {
		TotalMonths        int     `json:"total_months"`
		TotalDistance      float64 `json:"total_distance"`
		TotalFuelCost      float64 `json:"total_fuel_cost"`
		AvgMonthlyDistance float64 `json:"avg_monthly_distance"`
		MostActiveMonth    string  `json:"most_active_month"`
	}{
		TotalMonths: len(data),
	}

	if len(data) > 0 {
		var totalDistance, totalFuelCost float64
		var mostActiveMonth string
		var maxDistance float64

		for _, d := range data {
			totalDistance += d.TotalDistance
			totalFuelCost += d.EstimatedFuelCost
			if d.TotalDistance > maxDistance {
				maxDistance = d.TotalDistance
				mostActiveMonth = d.Month
			}
		}

		summary.TotalDistance = totalDistance
		summary.TotalFuelCost = totalFuelCost
		summary.AvgMonthlyDistance = totalDistance / float64(len(data))
		summary.MostActiveMonth = mostActiveMonth
	}

	return data, summary, int(totalCount), len(data), nil
}

func (s *ReportService) generateAfterHourMonthSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Monthly after-hours usage analysis
	query := config.DB.Table("trips t").
		Select(`
			DATE_FORMAT(t.start_time, '%Y-%m') as month,
			cd.name as device_name,
			cd.plate_number,
			COUNT(CASE WHEN HOUR(t.start_time) >= 18 OR HOUR(t.start_time) <= 6 THEN 1 END) as after_hour_trips,
			COUNT(*) as total_trips,
			SUM(CASE WHEN HOUR(t.start_time) >= 18 OR HOUR(t.start_time) <= 6 THEN t.distance ELSE 0 END) as after_hour_distance,
			SUM(t.distance) as total_distance
		`).
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m'), cd.id").
		Order("DATE_FORMAT(t.start_time, '%Y-%m') DESC, COUNT(CASE WHEN HOUR(t.start_time) >= 18 OR HOUR(t.start_time) <= 6 THEN 1 END) DESC")

	// Apply filters manually to avoid duplicate alias
	if filters.StartDate != nil {
		query = query.Where("t.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("t.start_time <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("t.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("cd.driver_id IN ?", filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		query = query.Where("cd.fleet_id IN ?", filters.FleetIds)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Month               string  `json:"month"`
		DeviceName          string  `json:"device_name"`
		PlateNumber         string  `json:"plate_number"`
		AfterHourTrips      int     `json:"after_hour_trips"`
		TotalTrips          int     `json:"total_trips"`
		AfterHourDistance   float64 `json:"after_hour_distance"`
		TotalDistance       float64 `json:"total_distance"`
		AfterHourPercentage float64 `json:"after_hour_percentage"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate after-hours percentage
	for i := range data {
		if data[i].TotalTrips > 0 {
			data[i].AfterHourPercentage = (float64(data[i].AfterHourTrips) / float64(data[i].TotalTrips)) * 100
		}
	}

	// Summary statistics
	summary := struct {
		TotalMonths            int     `json:"total_months"`
		TotalAfterHourTrips    int     `json:"total_after_hour_trips"`
		TotalTrips             int     `json:"total_trips"`
		AvgAfterHourPercentage float64 `json:"avg_after_hour_percentage"`
		HighestAfterHourMonth  string  `json:"highest_after_hour_month"`
	}{
		TotalMonths: len(data),
	}

	if len(data) > 0 {
		var totalAfterHourTrips, totalTrips int
		var highestAfterHourMonth string
		var maxPercentage float64

		for _, d := range data {
			totalAfterHourTrips += d.AfterHourTrips
			totalTrips += d.TotalTrips
			if d.AfterHourPercentage > maxPercentage {
				maxPercentage = d.AfterHourPercentage
				highestAfterHourMonth = d.Month
			}
		}

		summary.TotalAfterHourTrips = totalAfterHourTrips
		summary.TotalTrips = totalTrips
		summary.AvgAfterHourPercentage = (float64(totalAfterHourTrips) / float64(totalTrips)) * 100
		summary.HighestAfterHourMonth = highestAfterHourMonth
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateVehiclePerformanceSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Vehicle performance metrics with efficiency analysis
	query := config.DB.Table("trips t").
		Select(`
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			COUNT(*) as total_trips,
			SUM(t.distance) as total_distance,
			SUM(t.duration) as total_duration,
			AVG(t.avg_speed) as avg_speed,
			AVG(t.distance) as avg_trip_distance,
			(SUM(t.distance) / SUM(t.duration)) * 3600 as efficiency_kmh
		`).
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("cd.id").
		Order("(SUM(t.distance) / SUM(t.duration)) * 3600 DESC")

	// Apply filters
	query = s.applyTripFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	// Create a separate count query without ORDER BY
	countQuery := config.DB.Table("trips t").
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("cd.id")
	countQuery = s.applyTripFilters(countQuery, filters)
	countQuery.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		DeviceId         uint    `json:"device_id"`
		DeviceName       string  `json:"device_name"`
		PlateNumber      string  `json:"plate_number"`
		TotalTrips       int     `json:"total_trips"`
		TotalDistance    float64 `json:"total_distance"`
		TotalDuration    int     `json:"total_duration"`
		AvgSpeed         float64 `json:"avg_speed"`
		AvgTripDistance  float64 `json:"avg_trip_distance"`
		EfficiencyKmh    float64 `json:"efficiency_kmh"`
		PerformanceScore float64 `json:"performance_score"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate performance score
	for i := range data {
		// Performance score based on efficiency, speed, and trip count
		efficiencyScore := data[i].EfficiencyKmh / 100.0 * 40 // 40% weight
		speedScore := data[i].AvgSpeed / 100.0 * 30           // 30% weight
		tripScore := float64(data[i].TotalTrips) / 100.0 * 30 // 30% weight
		data[i].PerformanceScore = efficiencyScore + speedScore + tripScore
	}

	// Summary statistics
	summary := struct {
		TotalVehicles         int     `json:"total_vehicles"`
		TotalTrips            int     `json:"total_trips"`
		TotalDistance         float64 `json:"total_distance"`
		AvgPerformanceScore   float64 `json:"avg_performance_score"`
		BestPerformingVehicle string  `json:"best_performing_vehicle"`
	}{
		TotalVehicles: len(data),
	}

	if len(data) > 0 {
		var totalTrips int
		var totalDistance, totalPerformance float64
		var bestVehicle string
		var maxScore float64

		for _, d := range data {
			totalTrips += d.TotalTrips
			totalDistance += d.TotalDistance
			totalPerformance += d.PerformanceScore
			if d.PerformanceScore > maxScore {
				maxScore = d.PerformanceScore
				bestVehicle = d.DeviceName
			}
		}

		summary.TotalTrips = totalTrips
		summary.TotalDistance = totalDistance
		summary.AvgPerformanceScore = totalPerformance / float64(len(data))
		summary.BestPerformingVehicle = bestVehicle
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// === ADDITIONAL SUMMARY REPORTS ===

func (s *ReportService) generateFleetProductiveDaySummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Daily fleet productivity metrics
	query := config.DB.Table("trips t").
		Select(`
			DATE_FORMAT(t.start_time, '%Y-%m-%d') as date,
			COUNT(DISTINCT t.client_device_id) as active_vehicles,
			COUNT(*) as total_trips,
			SUM(t.distance) as total_distance,
			SUM(t.duration) as total_duration,
			AVG(t.avg_speed) as avg_speed,
			(SUM(t.distance) / COUNT(DISTINCT t.client_device_id)) as avg_distance_per_vehicle,
			(SUM(t.duration) / COUNT(DISTINCT t.client_device_id)) as avg_duration_per_vehicle
		`).
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m-%d')").
		Order("DATE_FORMAT(t.start_time, '%Y-%m-%d') DESC")

	// Apply filters
	query = s.applyTripFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)

	// Create a separate count query without ORDER BY to avoid column reference issues
	countQuery := config.DB.Table("trips t").
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m-%d')")
	countQuery = s.applyTripFilters(countQuery, filters)
	countQuery.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Date                  string  `json:"date"`
		ActiveVehicles        int     `json:"active_vehicles"`
		TotalTrips            int     `json:"total_trips"`
		TotalDistance         float64 `json:"total_distance"`
		TotalDuration         int     `json:"total_duration"`
		AvgSpeed              float64 `json:"avg_speed"`
		AvgDistancePerVehicle float64 `json:"avg_distance_per_vehicle"`
		AvgDurationPerVehicle float64 `json:"avg_duration_per_vehicle"`
		ProductivityScore     float64 `json:"productivity_score"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate productivity scores
	for i := range data {
		// Productivity score based on distance, trips, and speed
		data[i].ProductivityScore = (data[i].TotalDistance * 0.4) + (float64(data[i].TotalTrips) * 10 * 0.3) + (data[i].AvgSpeed * 0.3)
	}

	// Summary statistics
	summary := struct {
		TotalDays            int     `json:"total_days"`
		TotalTrips           int     `json:"total_trips"`
		TotalDistance        float64 `json:"total_distance"`
		AvgProductivityScore float64 `json:"avg_productivity_score"`
		MostProductiveDay    string  `json:"most_productive_day"`
		AvgActiveVehicles    float64 `json:"avg_active_vehicles"`
	}{
		TotalDays: len(data),
	}

	if len(data) > 0 {
		var totalTrips, totalDistance, totalProductivity, totalVehicles float64
		var mostProductiveDay string
		var maxProductivity float64

		for _, d := range data {
			totalTrips += float64(d.TotalTrips)
			totalDistance += d.TotalDistance
			totalProductivity += d.ProductivityScore
			totalVehicles += float64(d.ActiveVehicles)

			if d.ProductivityScore > maxProductivity {
				maxProductivity = d.ProductivityScore
				mostProductiveDay = d.Date
			}
		}

		summary.TotalTrips = int(totalTrips)
		summary.TotalDistance = totalDistance
		summary.AvgProductivityScore = totalProductivity / float64(len(data))
		summary.MostProductiveDay = mostProductiveDay
		summary.AvgActiveVehicles = totalVehicles / float64(len(data))
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateMileageAchievingSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Mileage achievement analysis with targets vs actual
	query := config.DB.Table("trips t").
		Select(`
			DATE_FORMAT(t.start_time, '%Y-%m') as month,
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			SUM(t.distance) as actual_mileage,
			1000.0 as target_mileage, -- Example target
			(SUM(t.distance) / 1000.0 * 100) as achievement_percentage,
			COUNT(*) as trip_count,
			AVG(t.avg_speed) as avg_speed
		`).
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m'), cd.id").
		Order("DATE_FORMAT(t.start_time, '%Y-%m') DESC, achievement_percentage DESC")

	// Apply filters
	query = s.applyTripFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	// Create a separate count query with proper JOIN
	countQuery := config.DB.Table("trips t").
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m'), cd.id")
	countQuery = s.applyTripFilters(countQuery, filters)
	countQuery.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Month                 string  `json:"month"`
		DeviceId              uint    `json:"device_id"`
		DeviceName            string  `json:"device_name"`
		PlateNumber           string  `json:"plate_number"`
		ActualMileage         float64 `json:"actual_mileage"`
		TargetMileage         float64 `json:"target_mileage"`
		AchievementPercentage float64 `json:"achievement_percentage"`
		TripCount             int     `json:"trip_count"`
		AvgSpeed              float64 `json:"avg_speed"`
		Status                string  `json:"status"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate status based on achievement percentage
	for i := range data {
		if data[i].AchievementPercentage >= 100 {
			data[i].Status = "Target Achieved"
		} else if data[i].AchievementPercentage >= 80 {
			data[i].Status = "Near Target"
		} else if data[i].AchievementPercentage >= 60 {
			data[i].Status = "Below Target"
		} else {
			data[i].Status = "Significantly Below"
		}
	}

	// Summary statistics
	summary := struct {
		TotalVehicles         int     `json:"total_vehicles"`
		TargetAchieved        int     `json:"target_achieved"`
		NearTarget            int     `json:"near_target"`
		BelowTarget           int     `json:"below_target"`
		AvgAchievement        float64 `json:"avg_achievement"`
		BestPerformingVehicle string  `json:"best_performing_vehicle"`
	}{
		TotalVehicles: len(data),
	}

	if len(data) > 0 {
		var totalAchievement float64
		var bestVehicle string
		var maxAchievement float64

		for _, d := range data {
			totalAchievement += d.AchievementPercentage

			switch d.Status {
			case "Target Achieved":
				summary.TargetAchieved++
			case "Near Target":
				summary.NearTarget++
			case "Below Target":
				summary.BelowTarget++
			}

			if d.AchievementPercentage > maxAchievement {
				maxAchievement = d.AchievementPercentage
				bestVehicle = d.DeviceName
			}
		}

		summary.AvgAchievement = totalAchievement / float64(len(data))
		summary.BestPerformingVehicle = bestVehicle
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateFuelEstimationMonthSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Monthly fuel consumption estimation with trends
	query := config.DB.Table("trips").
		Select(`
			DATE_FORMAT(trips.start_time, '%Y-%m') as month,
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			SUM(trips.distance) as total_distance,
			(SUM(trips.distance) * 0.12) as estimated_fuel_consumption,
			(SUM(trips.distance) * 0.12 * 1.5) as estimated_fuel_cost,
			(SUM(trips.distance) / (SUM(trips.distance) * 0.12)) as fuel_efficiency_kmpl,
			COUNT(*) as trip_count
		`).
		Joins("LEFT JOIN client_devices cd ON trips.client_device_id = cd.id").
		Group("DATE_FORMAT(trips.start_time, '%Y-%m'), cd.id").
		Order("DATE_FORMAT(trips.start_time, '%Y-%m') DESC, (SUM(trips.distance) * 0.12 * 1.5) DESC")

	// Apply filters manually to avoid duplicate alias
	if filters.StartDate != nil {
		query = query.Where("trips.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("trips.start_time <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("trips.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("cd.driver_id IN ?", filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		query = query.Where("cd.fleet_id IN ?", filters.FleetIds)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Month                    string  `json:"month"`
		DeviceId                 uint    `json:"device_id"`
		DeviceName               string  `json:"device_name"`
		PlateNumber              string  `json:"plate_number"`
		TotalDistance            float64 `json:"total_distance"`
		EstimatedFuelConsumption float64 `json:"estimated_fuel_consumption"`
		EstimatedFuelCost        float64 `json:"estimated_fuel_cost"`
		FuelEfficiencyKmpl       float64 `json:"fuel_efficiency_kmpl"`
		TripCount                int     `json:"trip_count"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Summary statistics
	summary := struct {
		TotalMonths          int     `json:"total_months"`
		TotalFuelConsumption float64 `json:"total_fuel_consumption"`
		TotalFuelCost        float64 `json:"total_fuel_cost"`
		AvgFuelEfficiency    float64 `json:"avg_fuel_efficiency"`
		MostEfficientMonth   string  `json:"most_efficient_month"`
		HighestCostMonth     string  `json:"highest_cost_month"`
	}{
		TotalMonths: len(data),
	}

	if len(data) > 0 {
		var totalFuel, totalCost, totalEfficiency float64
		var mostEfficientMonth, highestCostMonth string
		var maxEfficiency, maxCost float64

		for _, d := range data {
			totalFuel += d.EstimatedFuelConsumption
			totalCost += d.EstimatedFuelCost
			totalEfficiency += d.FuelEfficiencyKmpl

			if d.FuelEfficiencyKmpl > maxEfficiency {
				maxEfficiency = d.FuelEfficiencyKmpl
				mostEfficientMonth = d.Month
			}
			if d.EstimatedFuelCost > maxCost {
				maxCost = d.EstimatedFuelCost
				highestCostMonth = d.Month
			}
		}

		summary.TotalFuelConsumption = totalFuel
		summary.TotalFuelCost = totalCost
		summary.AvgFuelEfficiency = totalEfficiency / float64(len(data))
		summary.MostEfficientMonth = mostEfficientMonth
		summary.HighestCostMonth = highestCostMonth
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateFuelEstimationHourSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Hourly fuel consumption analysis
	query := config.DB.Table("trips").
		Select(`
			HOUR(trips.start_time) as hour,
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			SUM(trips.distance) as total_distance,
			(SUM(trips.distance) * 0.12) as estimated_fuel_consumption,
			(SUM(trips.distance) * 0.12 * 1.5) as estimated_fuel_cost,
			COUNT(*) as trip_count,
			AVG(trips.avg_speed) as avg_speed
		`).
		Joins("LEFT JOIN client_devices cd ON trips.client_device_id = cd.id").
		Group("HOUR(trips.start_time), cd.id").
		Order("HOUR(trips.start_time) ASC, (SUM(trips.distance) * 0.12 * 1.5) DESC")

	// Apply filters manually to avoid duplicate alias
	if filters.StartDate != nil {
		query = query.Where("trips.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("trips.start_time <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("trips.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("cd.driver_id IN ?", filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		query = query.Where("cd.fleet_id IN ?", filters.FleetIds)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	// Create a separate count query without ORDER BY
	countQuery := config.DB.Table("trips").
		Joins("LEFT JOIN client_devices cd ON trips.client_device_id = cd.id").
		Group("HOUR(trips.start_time), cd.id")

	// Apply the same filters to count query
	if filters.StartDate != nil {
		countQuery = countQuery.Where("trips.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		countQuery = countQuery.Where("trips.start_time <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		countQuery = countQuery.Where("trips.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		countQuery = countQuery.Where("cd.driver_id IN ?", filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		countQuery = countQuery.Where("cd.fleet_id IN ?", filters.FleetIds)
	}
	countQuery.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Hour                     int     `json:"hour"`
		DeviceId                 uint    `json:"device_id"`
		DeviceName               string  `json:"device_name"`
		PlateNumber              string  `json:"plate_number"`
		TotalDistance            float64 `json:"total_distance"`
		EstimatedFuelConsumption float64 `json:"estimated_fuel_consumption"`
		EstimatedFuelCost        float64 `json:"estimated_fuel_cost"`
		TripCount                int     `json:"trip_count"`
		AvgSpeed                 float64 `json:"avg_speed"`
		TimeSlot                 string  `json:"time_slot"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Add time slot classification
	for i := range data {
		switch {
		case data[i].Hour >= 6 && data[i].Hour < 12:
			data[i].TimeSlot = "Morning"
		case data[i].Hour >= 12 && data[i].Hour < 18:
			data[i].TimeSlot = "Afternoon"
		case data[i].Hour >= 18 && data[i].Hour < 24:
			data[i].TimeSlot = "Evening"
		default:
			data[i].TimeSlot = "Night"
		}
	}

	// Summary statistics
	summary := struct {
		TotalHours           int     `json:"total_hours"`
		TotalFuelConsumption float64 `json:"total_fuel_consumption"`
		TotalFuelCost        float64 `json:"total_fuel_cost"`
		PeakHour             int     `json:"peak_hour"`
		PeakTimeSlot         string  `json:"peak_time_slot"`
		AvgFuelPerHour       float64 `json:"avg_fuel_per_hour"`
	}{
		TotalHours: len(data),
	}

	if len(data) > 0 {
		var totalFuel, totalCost float64
		var peakHour int
		var maxFuel float64
		timeSlotFuel := make(map[string]float64)

		for _, d := range data {
			totalFuel += d.EstimatedFuelConsumption
			totalCost += d.EstimatedFuelCost
			timeSlotFuel[d.TimeSlot] += d.EstimatedFuelConsumption

			if d.EstimatedFuelConsumption > maxFuel {
				maxFuel = d.EstimatedFuelConsumption
				peakHour = d.Hour
			}
		}

		// Find peak time slot
		var peakTimeSlot string
		var maxTimeSlotFuel float64
		for timeSlot, fuel := range timeSlotFuel {
			if fuel > maxTimeSlotFuel {
				maxTimeSlotFuel = fuel
				peakTimeSlot = timeSlot
			}
		}

		summary.TotalFuelConsumption = totalFuel
		summary.TotalFuelCost = totalCost
		summary.PeakHour = peakHour
		summary.PeakTimeSlot = peakTimeSlot
		summary.AvgFuelPerHour = totalFuel / float64(len(data))
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// === ADDITIONAL IMPLEMENTABLE REPORTS ===

func (s *ReportService) generateRouteAnalysisReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Detailed route analysis with optimization opportunities
	query := config.DB.Table("trips t").
		Select(`
			t.id as trip_id,
			cd.name as device_name,
			cd.plate_number,
			d.name as driver_name,
			t.start_time,
			t.end_time,
			t.start_location,
			t.end_location,
			t.distance,
			t.duration,
			t.avg_speed,
			(t.distance / (t.duration / 3600.0)) as efficiency_score,
			CASE 
				WHEN t.avg_speed > 80 THEN 'High Speed'
				WHEN t.avg_speed > 60 THEN 'Medium Speed'
				ELSE 'Low Speed'
			END as speed_category,
			CASE 
				WHEN t.duration > 28800 THEN 'Long Trip'
				WHEN t.duration > 14400 THEN 'Medium Trip'
				ELSE 'Short Trip'
			END as trip_category
		`).
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON t.driver_id = d.id").
		Where("t.distance > ?", 5.0). // Only trips longer than 5km
		Order("t.distance DESC")

	// Apply filters
	query = s.applyTripFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		TripId            uint      `json:"trip_id"`
		DeviceName        string    `json:"device_name"`
		PlateNumber       string    `json:"plate_number"`
		DriverName        string    `json:"driver_name"`
		StartTime         time.Time `json:"start_time"`
		EndTime           time.Time `json:"end_time"`
		StartLocation     *string   `json:"start_location"`
		EndLocation       *string   `json:"end_location"`
		Distance          float64   `json:"distance"`
		Duration          int       `json:"duration"`
		AvgSpeed          float64   `json:"avg_speed"`
		EfficiencyScore   float64   `json:"efficiency_score"`
		SpeedCategory     string    `json:"speed_category"`
		TripCategory      string    `json:"trip_category"`
		OptimizationScore float64   `json:"optimization_score"`
		Recommendations   []string  `json:"recommendations"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate optimization scores and recommendations
	for i := range data {
		optimizationScore := 100.0
		var recommendations []string

		// Speed optimization
		if data[i].AvgSpeed > 80 {
			optimizationScore -= 20
			recommendations = append(recommendations, "Reduce speed for fuel efficiency")
		}

		// Route efficiency
		if data[i].EfficiencyScore < 50 {
			optimizationScore -= 15
			recommendations = append(recommendations, "Consider alternative routes")
		}

		// Trip duration optimization
		if data[i].Duration > 28800 { // 8 hours
			optimizationScore -= 10
			recommendations = append(recommendations, "Break long trips for driver safety")
		}

		if optimizationScore < 0 {
			optimizationScore = 0
		}

		if len(recommendations) == 0 {
			recommendations = append(recommendations, "Route is well optimized")
		}

		data[i].OptimizationScore = optimizationScore
		data[i].Recommendations = recommendations
	}

	// Summary statistics
	summary := struct {
		TotalTrips                int     `json:"total_trips"`
		TotalDistance             float64 `json:"total_distance"`
		AvgOptimizationScore      float64 `json:"avg_optimization_score"`
		HighSpeedTrips            int     `json:"high_speed_trips"`
		LongTrips                 int     `json:"long_trips"`
		OptimizationOpportunities int     `json:"optimization_opportunities"`
		MostEfficientRoute        string  `json:"most_efficient_route"`
		LeastEfficientRoute       string  `json:"least_efficient_route"`
	}{
		TotalTrips: len(data),
	}

	if len(data) > 0 {
		var totalDistance, totalOptimization float64
		var highSpeedTrips, longTrips, optimizationOpportunities int
		var mostEfficient, leastEfficient string
		var maxEfficiency, minEfficiency float64

		for _, d := range data {
			totalDistance += d.Distance
			totalOptimization += d.OptimizationScore

			if d.SpeedCategory == "High Speed" {
				highSpeedTrips++
			}
			if d.TripCategory == "Long Trip" {
				longTrips++
			}
			if d.OptimizationScore < 80 {
				optimizationOpportunities++
			}

			if d.EfficiencyScore > maxEfficiency {
				maxEfficiency = d.EfficiencyScore
				if d.StartLocation != nil && d.EndLocation != nil {
					mostEfficient = *d.StartLocation + " → " + *d.EndLocation
				}
			}
			if d.EfficiencyScore < minEfficiency || minEfficiency == 0 {
				minEfficiency = d.EfficiencyScore
				if d.StartLocation != nil && d.EndLocation != nil {
					leastEfficient = *d.StartLocation + " → " + *d.EndLocation
				}
			}
		}

		summary.TotalDistance = totalDistance
		summary.AvgOptimizationScore = totalOptimization / float64(len(data))
		summary.HighSpeedTrips = highSpeedTrips
		summary.LongTrips = longTrips
		summary.OptimizationOpportunities = optimizationOpportunities
		summary.MostEfficientRoute = mostEfficient
		summary.LeastEfficientRoute = leastEfficient
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generatePredictiveMaintenanceReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Predictive maintenance recommendations based on usage patterns
	query := config.DB.Table("client_devices cd").
		Select(`
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			cd.status as device_status,
			MAX(g.gps_timestamp) as last_seen,
			SUM(t.distance) as total_mileage,
			COUNT(t.id) as total_trips,
			AVG(t.avg_speed) as avg_speed,
			MAX(t.end_time) as last_trip_date,
			COUNT(CASE WHEN t.avg_speed > 80 THEN 1 END) as high_speed_trips,
			COUNT(CASE WHEN t.duration > 28800 THEN 1 END) as long_trips,
			CASE 
				WHEN SUM(t.distance) >= 15000 THEN 'Critical'
				WHEN SUM(t.distance) >= 10000 THEN 'High'
				WHEN SUM(t.distance) >= 5000 THEN 'Medium'
				ELSE 'Low'
			END as maintenance_priority,
			CASE 
				WHEN SUM(t.distance) >= 15000 THEN DATE_ADD(NOW(), INTERVAL 1 DAY)
				WHEN SUM(t.distance) >= 10000 THEN DATE_ADD(NOW(), INTERVAL 7 DAY)
				WHEN SUM(t.distance) >= 5000 THEN DATE_ADD(NOW(), INTERVAL 14 DAY)
				ELSE DATE_ADD(NOW(), INTERVAL 30 DAY)
			END as recommended_maintenance_date
		`).
		Joins("LEFT JOIN trips t ON cd.id = t.client_device_id").
		Joins("LEFT JOIN (SELECT g1.* FROM gps_data g1 INNER JOIN (SELECT client_device_id, MAX(gps_timestamp) as max_timestamp FROM gps_data GROUP BY client_device_id) g2 ON g1.client_device_id = g2.client_device_id AND g1.gps_timestamp = g2.max_timestamp) g ON cd.id = g.client_device_id").
		Group("cd.id").
		Order("CASE WHEN SUM(t.distance) >= 15000 THEN 4 WHEN SUM(t.distance) >= 10000 THEN 3 WHEN SUM(t.distance) >= 5000 THEN 2 ELSE 1 END DESC, SUM(t.distance) DESC")

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("cd.id IN (?)", filters.ClientDeviceIds)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.ClientDevice{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		DeviceId                   uint       `json:"device_id"`
		DeviceName                 string     `json:"device_name"`
		PlateNumber                string     `json:"plate_number"`
		DeviceStatus               string     `json:"device_status"`
		LastSeen                   *time.Time `json:"last_seen"`
		TotalMileage               float64    `json:"total_mileage"`
		TotalTrips                 int        `json:"total_trips"`
		AvgSpeed                   float64    `json:"avg_speed"`
		LastTripDate               *time.Time `json:"last_trip_date"`
		HighSpeedTrips             int        `json:"high_speed_trips"`
		LongTrips                  int        `json:"long_trips"`
		MaintenancePriority        string     `json:"maintenance_priority"`
		RecommendedMaintenanceDate time.Time  `json:"recommended_maintenance_date"`
		RiskScore                  float64    `json:"risk_score"`
		MaintenanceRecommendations []string   `json:"maintenance_recommendations"`
		EstimatedCost              float64    `json:"estimated_cost"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate risk scores and maintenance recommendations
	for i := range data {
		riskScore := 0.0
		var recommendations []string
		var estimatedCost float64

		// Mileage-based risk
		switch data[i].MaintenancePriority {
		case "Critical":
			riskScore += 40
			recommendations = append(recommendations, "Immediate major service required")
			estimatedCost = 800.0
		case "High":
			riskScore += 25
			recommendations = append(recommendations, "Schedule major service within 7 days")
			estimatedCost = 500.0
		case "Medium":
			riskScore += 15
			recommendations = append(recommendations, "Schedule minor service within 14 days")
			estimatedCost = 200.0
		case "Low":
			riskScore += 5
			recommendations = append(recommendations, "Routine inspection recommended")
			estimatedCost = 100.0
		}

		// Usage pattern risk
		if data[i].HighSpeedTrips > 10 {
			riskScore += 10
			recommendations = append(recommendations, "High-speed driving detected - check brake system")
		}

		if data[i].LongTrips > 5 {
			riskScore += 10
			recommendations = append(recommendations, "Extended trips detected - check engine oil and coolant")
		}

		// Age-based risk
		if data[i].LastSeen != nil {
			daysSinceLastSeen := time.Since(*data[i].LastSeen).Hours() / 24
			if daysSinceLastSeen > 7 {
				riskScore += 15
				recommendations = append(recommendations, "Vehicle inactive - check battery and fuel system")
			}
		}

		if riskScore > 100 {
			riskScore = 100
		}

		data[i].RiskScore = riskScore
		data[i].MaintenanceRecommendations = recommendations
		data[i].EstimatedCost = estimatedCost
	}

	// Summary statistics
	summary := struct {
		TotalVehicles                int     `json:"total_vehicles"`
		CriticalPriority             int     `json:"critical_priority"`
		HighPriority                 int     `json:"high_priority"`
		TotalEstimatedCost           float64 `json:"total_estimated_cost"`
		AvgRiskScore                 float64 `json:"avg_risk_score"`
		NextMaintenanceDate          string  `json:"next_maintenance_date"`
		HighRiskVehicles             int     `json:"high_risk_vehicles"`
		PreventiveMaintenanceSavings float64 `json:"preventive_maintenance_savings"`
	}{
		TotalVehicles: len(data),
	}

	if len(data) > 0 {
		var totalCost, totalRisk float64
		var criticalPriority, highPriority, highRiskVehicles int
		var nextMaintenanceDate time.Time
		var hasNextMaintenance bool

		for _, d := range data {
			totalCost += d.EstimatedCost
			totalRisk += d.RiskScore

			switch d.MaintenancePriority {
			case "Critical":
				criticalPriority++
			case "High":
				highPriority++
			}

			if d.RiskScore > 50 {
				highRiskVehicles++
			}

			if !hasNextMaintenance || d.RecommendedMaintenanceDate.Before(nextMaintenanceDate) {
				nextMaintenanceDate = d.RecommendedMaintenanceDate
				hasNextMaintenance = true
			}
		}

		summary.CriticalPriority = criticalPriority
		summary.HighPriority = highPriority
		summary.TotalEstimatedCost = totalCost
		summary.AvgRiskScore = totalRisk / float64(len(data))
		summary.HighRiskVehicles = highRiskVehicles
		summary.PreventiveMaintenanceSavings = totalCost * 0.3 // 30% savings from preventive maintenance

		if hasNextMaintenance {
			summary.NextMaintenanceDate = nextMaintenanceDate.Format("2006-01-02")
		}
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateFleetOptimizationReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Fleet optimization recommendations with utilization analysis
	query := config.DB.Table("client_devices cd").
		Select(`
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			cd.status as device_status,
			MAX(g.gps_timestamp) as last_seen,
			SUM(t.distance) as total_mileage,
			COUNT(t.id) as total_trips,
			AVG(t.avg_speed) as avg_speed,
			SUM(t.duration) as total_duration,
			COUNT(DISTINCT DATE(t.start_time)) as active_days,
			(SUM(t.duration) / COUNT(DISTINCT DATE(t.start_time))) as avg_daily_usage,
			(COUNT(t.id) / COUNT(DISTINCT DATE(t.start_time))) as avg_daily_trips,
			CASE 
				WHEN (SUM(t.duration) / COUNT(DISTINCT DATE(t.start_time))) > 28800 THEN 'Over-utilized'
				WHEN (SUM(t.duration) / COUNT(DISTINCT DATE(t.start_time))) > 14400 THEN 'Well-utilized'
				WHEN (SUM(t.duration) / COUNT(DISTINCT DATE(t.start_time))) > 7200 THEN 'Under-utilized'
				ELSE 'Poorly-utilized'
			END as utilization_status
		`).
		Joins("LEFT JOIN trips t ON cd.id = t.client_device_id").
		Joins("LEFT JOIN (SELECT g1.* FROM gps_data g1 INNER JOIN (SELECT client_device_id, MAX(gps_timestamp) as max_timestamp FROM gps_data GROUP BY client_device_id) g2 ON g1.client_device_id = g2.client_device_id AND g1.gps_timestamp = g2.max_timestamp) g ON cd.id = g.client_device_id").
		Group("cd.id").
		Order("CASE WHEN (SUM(t.duration) / COUNT(DISTINCT DATE(t.start_time))) > 28800 THEN 4 WHEN (SUM(t.duration) / COUNT(DISTINCT DATE(t.start_time))) > 14400 THEN 3 WHEN (SUM(t.duration) / COUNT(DISTINCT DATE(t.start_time))) > 7200 THEN 2 ELSE 1 END DESC, SUM(t.distance) DESC")

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("cd.id IN (?)", filters.ClientDeviceIds)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.ClientDevice{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		DeviceId          uint       `json:"device_id"`
		DeviceName        string     `json:"device_name"`
		PlateNumber       string     `json:"plate_number"`
		DeviceStatus      string     `json:"device_status"`
		LastSeen          *time.Time `json:"last_seen"`
		TotalMileage      float64    `json:"total_mileage"`
		TotalTrips        int        `json:"total_trips"`
		AvgSpeed          float64    `json:"avg_speed"`
		TotalDuration     int        `json:"total_duration"`
		ActiveDays        int        `json:"active_days"`
		AvgDailyUsage     float64    `json:"avg_daily_usage"`
		AvgDailyTrips     float64    `json:"avg_daily_trips"`
		UtilizationStatus string     `json:"utilization_status"`
		OptimizationScore float64    `json:"optimization_score"`
		Recommendations   []string   `json:"recommendations"`
		PotentialSavings  float64    `json:"potential_savings"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate optimization scores and recommendations
	for i := range data {
		optimizationScore := 100.0
		var recommendations []string
		var potentialSavings float64

		// Utilization-based optimization
		switch data[i].UtilizationStatus {
		case "Over-utilized":
			optimizationScore -= 30
			recommendations = append(recommendations, "Consider adding another vehicle to reduce workload")
			potentialSavings = 5000.0 // Annual savings from reduced wear and tear
		case "Well-utilized":
			optimizationScore += 0
			recommendations = append(recommendations, "Current utilization is optimal")
		case "Under-utilized":
			optimizationScore -= 20
			recommendations = append(recommendations, "Consider consolidating routes or reducing fleet size")
			potentialSavings = 3000.0 // Annual savings from reduced operational costs
		case "Poorly-utilized":
			optimizationScore -= 40
			recommendations = append(recommendations, "Strongly consider fleet reduction or route optimization")
			potentialSavings = 8000.0 // Annual savings from fleet reduction
		}

		// Efficiency-based optimization
		if data[i].AvgSpeed > 80 {
			optimizationScore -= 10
			recommendations = append(recommendations, "Implement speed monitoring to reduce fuel costs")
		}

		if data[i].AvgDailyTrips < 2 {
			optimizationScore -= 15
			recommendations = append(recommendations, "Optimize route planning to increase daily trips")
		}

		if optimizationScore < 0 {
			optimizationScore = 0
		}

		data[i].OptimizationScore = optimizationScore
		data[i].Recommendations = recommendations
		data[i].PotentialSavings = potentialSavings
	}

	// Summary statistics
	summary := struct {
		TotalVehicles           int      `json:"total_vehicles"`
		OverUtilized            int      `json:"over_utilized"`
		WellUtilized            int      `json:"well_utilized"`
		UnderUtilized           int      `json:"under_utilized"`
		PoorlyUtilized          int      `json:"poorly_utilized"`
		AvgOptimizationScore    float64  `json:"avg_optimization_score"`
		TotalPotentialSavings   float64  `json:"total_potential_savings"`
		FleetOptimizationRating string   `json:"fleet_optimization_rating"`
		RecommendedActions      []string `json:"recommended_actions"`
	}{
		TotalVehicles: len(data),
	}

	if len(data) > 0 {
		var totalOptimization, totalSavings float64
		var overUtilized, wellUtilized, underUtilized, poorlyUtilized int

		for _, d := range data {
			totalOptimization += d.OptimizationScore
			totalSavings += d.PotentialSavings

			switch d.UtilizationStatus {
			case "Over-utilized":
				overUtilized++
			case "Well-utilized":
				wellUtilized++
			case "Under-utilized":
				underUtilized++
			case "Poorly-utilized":
				poorlyUtilized++
			}
		}

		summary.OverUtilized = overUtilized
		summary.WellUtilized = wellUtilized
		summary.UnderUtilized = underUtilized
		summary.PoorlyUtilized = poorlyUtilized
		summary.AvgOptimizationScore = totalOptimization / float64(len(data))
		summary.TotalPotentialSavings = totalSavings

		// Fleet optimization rating
		var rating string
		switch {
		case summary.AvgOptimizationScore >= 80:
			rating = "Excellent"
		case summary.AvgOptimizationScore >= 60:
			rating = "Good"
		case summary.AvgOptimizationScore >= 40:
			rating = "Fair"
		default:
			rating = "Poor"
		}
		summary.FleetOptimizationRating = rating

		// Recommended actions
		if overUtilized > 0 {
			summary.RecommendedActions = append(summary.RecommendedActions, "Consider fleet expansion for over-utilized vehicles")
		}
		if underUtilized > 0 || poorlyUtilized > 0 {
			summary.RecommendedActions = append(summary.RecommendedActions, "Optimize routes and consider fleet reduction")
		}
		if summary.TotalPotentialSavings > 10000 {
			summary.RecommendedActions = append(summary.RecommendedActions, "Implement optimization recommendations for significant cost savings")
		}
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateDriverTrainingNeedsReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Driver training recommendations based on performance metrics
	query := config.DB.Table("drivers d").
		Select(`
			d.id as driver_id,
			d.name as driver_name,
			d.driver_license_no as license_number,
			d.phone_number as phone,
			COUNT(DISTINCT t.id) as total_trips,
			SUM(t.distance) as total_distance,
			AVG(t.avg_speed) as avg_speed,
			COUNT(CASE WHEN t.avg_speed > 80 THEN 1 END) as speeding_incidents,
			COUNT(CASE WHEN t.duration > 28800 THEN 1 END) as overtime_violations,
			COUNT(DISTINCT dbe.id) as total_violations,
			SUM(CASE WHEN dbe.event_type = 'overspeed' THEN 1 ELSE 0 END) as speeding_violations,
			SUM(CASE WHEN dbe.event_type IN ('harsh_acceleration', 'harsh_braking', 'harsh_cornering') THEN 1 ELSE 0 END) as harsh_driving_violations,
			(COUNT(CASE WHEN t.avg_speed > 80 THEN 1 END) / COUNT(t.id) * 100) as speeding_rate,
			(COUNT(DISTINCT dbe.id) / COUNT(t.id) * 100) as violation_rate
		`).
		Joins("LEFT JOIN trips t ON d.id = t.driver_id").
		Joins("LEFT JOIN driving_behavior_events dbe ON t.id = dbe.trip_id").
		Group("d.id").
		Order("(COUNT(DISTINCT dbe.id) / COUNT(t.id) * 100) DESC, (COUNT(CASE WHEN t.avg_speed > 80 THEN 1 END) / COUNT(t.id) * 100) DESC")

	// Apply filters
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN (?)", filters.DriverIds)
	}
	if filters.StartDate != nil {
		query = query.Where("t.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("t.start_time <= ?", *filters.EndDate)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Driver{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		DriverId                 uint     `json:"driver_id"`
		DriverName               string   `json:"driver_name"`
		LicenseNumber            string   `json:"license_number"`
		Phone                    string   `json:"phone"`
		TotalTrips               int      `json:"total_trips"`
		TotalDistance            float64  `json:"total_distance"`
		AvgSpeed                 float64  `json:"avg_speed"`
		SpeedingIncidents        int      `json:"speeding_incidents"`
		OvertimeViolations       int      `json:"overtime_violations"`
		TotalViolations          int      `json:"total_violations"`
		SpeedingViolations       int      `json:"speeding_violations"`
		HarshDrivingViolations   int      `json:"harsh_driving_violations"`
		SpeedingRate             float64  `json:"speeding_rate"`
		ViolationRate            float64  `json:"violation_rate"`
		TrainingPriority         string   `json:"training_priority"`
		TrainingNeeds            []string `json:"training_needs"`
		RiskLevel                string   `json:"risk_level"`
		RecommendedTrainingHours int      `json:"recommended_training_hours"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate training priorities and needs
	for i := range data {
		var trainingPriority string
		var trainingNeeds []string
		var riskLevel string
		var recommendedHours int

		// Determine training priority based on violation rates
		if data[i].ViolationRate > 20 {
			trainingPriority = "Critical"
			riskLevel = "High"
			recommendedHours = 16
		} else if data[i].ViolationRate > 10 {
			trainingPriority = "High"
			riskLevel = "Medium"
			recommendedHours = 12
		} else if data[i].ViolationRate > 5 {
			trainingPriority = "Medium"
			riskLevel = "Low"
			recommendedHours = 8
		} else {
			trainingPriority = "Low"
			riskLevel = "Minimal"
			recommendedHours = 4
		}

		// Identify specific training needs
		if data[i].SpeedingRate > 15 {
			trainingNeeds = append(trainingNeeds, "Speed management and safety")
		}
		if data[i].HarshDrivingViolations > 5 {
			trainingNeeds = append(trainingNeeds, "Defensive driving techniques")
		}
		if data[i].OvertimeViolations > 3 {
			trainingNeeds = append(trainingNeeds, "Hours of service compliance")
		}
		if data[i].TotalViolations > 10 {
			trainingNeeds = append(trainingNeeds, "General safety awareness")
		}

		if len(trainingNeeds) == 0 {
			trainingNeeds = append(trainingNeeds, "Refresher training")
		}

		data[i].TrainingPriority = trainingPriority
		data[i].TrainingNeeds = trainingNeeds
		data[i].RiskLevel = riskLevel
		data[i].RecommendedTrainingHours = recommendedHours
	}

	// Summary statistics
	summary := struct {
		TotalDrivers               int     `json:"total_drivers"`
		CriticalPriority           int     `json:"critical_priority"`
		HighPriority               int     `json:"high_priority"`
		MediumPriority             int     `json:"medium_priority"`
		LowPriority                int     `json:"low_priority"`
		AvgViolationRate           float64 `json:"avg_violation_rate"`
		TotalTrainingHours         int     `json:"total_training_hours"`
		HighRiskDrivers            int     `json:"high_risk_drivers"`
		MostCommonTrainingNeed     string  `json:"most_common_training_need"`
		TrainingEffectivenessScore float64 `json:"training_effectiveness_score"`
	}{
		TotalDrivers: len(data),
	}

	if len(data) > 0 {
		var totalViolationRate, totalTrainingHours float64
		var criticalPriority, highPriority, mediumPriority, lowPriority, highRiskDrivers int
		trainingNeedCounts := make(map[string]int)

		for _, d := range data {
			totalViolationRate += d.ViolationRate
			totalTrainingHours += float64(d.RecommendedTrainingHours)

			switch d.TrainingPriority {
			case "Critical":
				criticalPriority++
			case "High":
				highPriority++
			case "Medium":
				mediumPriority++
			case "Low":
				lowPriority++
			}

			if d.RiskLevel == "High" {
				highRiskDrivers++
			}

			// Count training needs
			for _, need := range d.TrainingNeeds {
				trainingNeedCounts[need]++
			}
		}

		summary.CriticalPriority = criticalPriority
		summary.HighPriority = highPriority
		summary.MediumPriority = mediumPriority
		summary.LowPriority = lowPriority
		summary.AvgViolationRate = totalViolationRate / float64(len(data))
		summary.TotalTrainingHours = int(totalTrainingHours)
		summary.HighRiskDrivers = highRiskDrivers

		// Find most common training need
		var mostCommonNeed string
		var maxCount int
		for need, count := range trainingNeedCounts {
			if count > maxCount {
				maxCount = count
				mostCommonNeed = need
			}
		}
		summary.MostCommonTrainingNeed = mostCommonNeed

		// Calculate training effectiveness score
		if summary.AvgViolationRate > 0 {
			summary.TrainingEffectivenessScore = 100 - (summary.AvgViolationRate * 2)
			if summary.TrainingEffectivenessScore < 0 {
				summary.TrainingEffectivenessScore = 0
			}
		}
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateEnvironmentalDashboardReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Environmental impact analysis with carbon footprint and sustainability metrics
	query := config.DB.Table("trips t").
		Select(`
			DATE_FORMAT(t.start_time, '%Y-%m') as month,
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			SUM(t.distance) as total_distance,
			COUNT(t.id) as total_trips,
			AVG(t.avg_speed) as avg_speed,
			SUM(t.duration) as total_duration,
			(SUM(t.distance) * 0.2) as estimated_fuel_consumption,
			(SUM(t.distance) * 0.2 * 2.31) as estimated_co2_emissions,
			(SUM(t.distance) / SUM(t.duration) * 3600) as efficiency_score,
			CASE 
				WHEN AVG(t.avg_speed) > 80 THEN 'High Impact'
				WHEN AVG(t.avg_speed) > 60 THEN 'Medium Impact'
				ELSE 'Low Impact'
			END as environmental_impact
		`).
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m'), cd.id").
		Order("DATE_FORMAT(t.start_time, '%Y-%m') DESC, (SUM(t.distance) * 0.2 * 2.31) DESC")

	// Apply filters
	query = s.applyTripFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Month                    string   `json:"month"`
		DeviceId                 uint     `json:"device_id"`
		DeviceName               string   `json:"device_name"`
		PlateNumber              string   `json:"plate_number"`
		TotalDistance            float64  `json:"total_distance"`
		TotalTrips               int      `json:"total_trips"`
		AvgSpeed                 float64  `json:"avg_speed"`
		TotalDuration            int      `json:"total_duration"`
		EstimatedFuelConsumption float64  `json:"estimated_fuel_consumption"`
		EstimatedCO2Emissions    float64  `json:"estimated_co2_emissions"`
		EfficiencyScore          float64  `json:"efficiency_score"`
		EnvironmentalImpact      string   `json:"environmental_impact"`
		SustainabilityScore      float64  `json:"sustainability_score"`
		CarbonFootprintRating    string   `json:"carbon_footprint_rating"`
		EcoRecommendations       []string `json:"eco_recommendations"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate sustainability scores and recommendations
	for i := range data {
		sustainabilityScore := 100.0
		var ecoRecommendations []string

		// Speed impact on sustainability
		if data[i].AvgSpeed > 80 {
			sustainabilityScore -= 25
			ecoRecommendations = append(ecoRecommendations, "Reduce speed to improve fuel efficiency")
		} else if data[i].AvgSpeed > 60 {
			sustainabilityScore -= 10
			ecoRecommendations = append(ecoRecommendations, "Optimize speed for better fuel economy")
		}

		// Distance efficiency impact
		if data[i].EfficiencyScore < 50 {
			sustainabilityScore -= 20
			ecoRecommendations = append(ecoRecommendations, "Optimize routes to reduce unnecessary travel")
		}

		// CO2 emissions impact
		if data[i].EstimatedCO2Emissions > 1000 {
			sustainabilityScore -= 15
			ecoRecommendations = append(ecoRecommendations, "Consider alternative fuel vehicles")
		}

		if sustainabilityScore < 0 {
			sustainabilityScore = 0
		}

		// Carbon footprint rating
		var carbonRating string
		switch {
		case sustainabilityScore >= 80:
			carbonRating = "Excellent"
		case sustainabilityScore >= 60:
			carbonRating = "Good"
		case sustainabilityScore >= 40:
			carbonRating = "Fair"
		default:
			carbonRating = "Poor"
		}

		if len(ecoRecommendations) == 0 {
			ecoRecommendations = append(ecoRecommendations, "Maintain current eco-friendly practices")
		}

		data[i].SustainabilityScore = sustainabilityScore
		data[i].CarbonFootprintRating = carbonRating
		data[i].EcoRecommendations = ecoRecommendations
	}

	// Summary statistics
	summary := struct {
		TotalRecords           int      `json:"total_records"`
		TotalCO2Emissions      float64  `json:"total_co2_emissions"`
		TotalFuelConsumption   float64  `json:"total_fuel_consumption"`
		AvgSustainabilityScore float64  `json:"avg_sustainability_score"`
		HighImpactVehicles     int      `json:"high_impact_vehicles"`
		LowImpactVehicles      int      `json:"low_impact_vehicles"`
		CarbonNeutralOffset    float64  `json:"carbon_neutral_offset"`
		EnvironmentalRating    string   `json:"environmental_rating"`
		EcoFriendlyInitiatives []string `json:"eco_friendly_initiatives"`
	}{
		TotalRecords: len(data),
	}

	if len(data) > 0 {
		var totalCO2, totalFuel, totalSustainability float64
		var highImpact, lowImpact int

		for _, d := range data {
			totalCO2 += d.EstimatedCO2Emissions
			totalFuel += d.EstimatedFuelConsumption
			totalSustainability += d.SustainabilityScore

			switch d.EnvironmentalImpact {
			case "High Impact":
				highImpact++
			case "Low Impact":
				lowImpact++
			}
		}

		summary.TotalCO2Emissions = totalCO2
		summary.TotalFuelConsumption = totalFuel
		summary.AvgSustainabilityScore = totalSustainability / float64(len(data))
		summary.HighImpactVehicles = highImpact
		summary.LowImpactVehicles = lowImpact
		summary.CarbonNeutralOffset = totalCO2 * 0.1 // 10% offset potential

		// Environmental rating
		var rating string
		switch {
		case summary.AvgSustainabilityScore >= 80:
			rating = "Excellent"
		case summary.AvgSustainabilityScore >= 60:
			rating = "Good"
		case summary.AvgSustainabilityScore >= 40:
			rating = "Fair"
		default:
			rating = "Poor"
		}
		summary.EnvironmentalRating = rating

		// Eco-friendly initiatives
		if summary.AvgSustainabilityScore < 60 {
			summary.EcoFriendlyInitiatives = append(summary.EcoFriendlyInitiatives, "Implement driver training for eco-driving")
		}
		if summary.HighImpactVehicles > summary.LowImpactVehicles {
			summary.EcoFriendlyInitiatives = append(summary.EcoFriendlyInitiatives, "Consider hybrid or electric vehicles")
		}
		if summary.TotalCO2Emissions > 5000 {
			summary.EcoFriendlyInitiatives = append(summary.EcoFriendlyInitiatives, "Develop carbon offset program")
		}
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// === NEW ADDITIONAL REPORTS ===

func (s *ReportService) generateFuelEstimationReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Fuel consumption estimation based on running hours and device data
	query := config.DB.Table("trips t").
		Select(`
			DATE_FORMAT(t.start_time, '%Y-%m') as month,
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			SUM(t.distance) as total_distance,
			SUM(t.duration) as total_running_hours,
			AVG(t.avg_speed) as avg_speed,
			COUNT(t.id) as total_trips,
			(SUM(t.duration) / 3600.0) as running_hours,
			(SUM(t.distance) * 0.12) as estimated_fuel_consumption,
			(SUM(t.distance) * 0.12 * 1.5) as estimated_fuel_cost,
			(SUM(t.distance) / (SUM(t.duration) / 3600.0)) as efficiency_km_per_hour,
			(SUM(t.distance) * 0.12 / (SUM(t.duration) / 3600.0)) as fuel_efficiency_lph
		`).
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m'), cd.id").
		Order("month DESC, estimated_fuel_consumption DESC")

	// Apply filters
	query = s.applyTripFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)

	// Create a separate count query without ORDER BY to avoid column reference issues
	countQuery := config.DB.Table("trips t").
		Select("COUNT(*)").
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m'), cd.id")
	countQuery = s.applyTripFilters(countQuery, filters)
	countQuery.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Month                    string  `json:"month"`
		DeviceId                 uint    `json:"device_id"`
		DeviceName               string  `json:"device_name"`
		PlateNumber              string  `json:"plate_number"`
		TotalDistance            float64 `json:"total_distance"`
		TotalRunningHours        int     `json:"total_running_hours"`
		AvgSpeed                 float64 `json:"avg_speed"`
		TotalTrips               int     `json:"total_trips"`
		RunningHours             float64 `json:"running_hours"`
		EstimatedFuelConsumption float64 `json:"estimated_fuel_consumption"`
		EstimatedFuelCost        float64 `json:"estimated_fuel_cost"`
		EfficiencyKmPerHour      float64 `json:"efficiency_km_per_hour"`
		FuelEfficiencyLph        float64 `json:"fuel_efficiency_lph"`
		FuelEfficiencyRating     string  `json:"fuel_efficiency_rating"`
		CostPerKm                float64 `json:"cost_per_km"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate efficiency ratings and cost per km
	for i := range data {
		// Calculate cost per km
		if data[i].TotalDistance > 0 {
			data[i].CostPerKm = data[i].EstimatedFuelCost / data[i].TotalDistance
		}

		// Fuel efficiency rating
		var rating string
		switch {
		case data[i].FuelEfficiencyLph <= 8:
			rating = "Excellent"
		case data[i].FuelEfficiencyLph <= 12:
			rating = "Good"
		case data[i].FuelEfficiencyLph <= 16:
			rating = "Average"
		case data[i].FuelEfficiencyLph <= 20:
			rating = "Poor"
		default:
			rating = "Very Poor"
		}
		data[i].FuelEfficiencyRating = rating
	}

	// Summary statistics
	summary := struct {
		TotalRecords           int     `json:"total_records"`
		TotalFuelConsumption   float64 `json:"total_fuel_consumption"`
		TotalFuelCost          float64 `json:"total_fuel_cost"`
		AvgFuelEfficiency      float64 `json:"avg_fuel_efficiency"`
		MostEfficientVehicle   string  `json:"most_efficient_vehicle"`
		LeastEfficientVehicle  string  `json:"least_efficient_vehicle"`
		TotalRunningHours      float64 `json:"total_running_hours"`
		AvgCostPerKm           float64 `json:"avg_cost_per_km"`
		FuelSavingsOpportunity float64 `json:"fuel_savings_opportunity"`
	}{
		TotalRecords: len(data),
	}

	if len(data) > 0 {
		var totalFuel, totalCost, totalEfficiency, totalHours, totalCostPerKm float64
		var mostEfficient, leastEfficient string
		var minEfficiency, maxEfficiency float64

		for _, d := range data {
			totalFuel += d.EstimatedFuelConsumption
			totalCost += d.EstimatedFuelCost
			totalEfficiency += d.FuelEfficiencyLph
			totalHours += d.RunningHours
			totalCostPerKm += d.CostPerKm

			if d.FuelEfficiencyLph < minEfficiency || minEfficiency == 0 {
				minEfficiency = d.FuelEfficiencyLph
				mostEfficient = d.DeviceName
			}
			if d.FuelEfficiencyLph > maxEfficiency {
				maxEfficiency = d.FuelEfficiencyLph
				leastEfficient = d.DeviceName
			}
		}

		summary.TotalFuelConsumption = totalFuel
		summary.TotalFuelCost = totalCost
		summary.AvgFuelEfficiency = totalEfficiency / float64(len(data))
		summary.MostEfficientVehicle = mostEfficient
		summary.LeastEfficientVehicle = leastEfficient
		summary.TotalRunningHours = totalHours
		summary.AvgCostPerKm = totalCostPerKm / float64(len(data))
		summary.FuelSavingsOpportunity = totalFuel * 0.15 // 15% potential savings
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateDriverBehaviorAnalysisReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Comprehensive driver behavior analysis with patterns and trends
	query := config.DB.Table("drivers d").
		Select(`
			DATE_FORMAT(t.start_time, '%Y-%m') as month,
			d.id as driver_id,
			d.name as driver_name,
			d.driver_license_no,
			d.phone_number,
			COUNT(t.id) as total_trips,
			SUM(t.distance) as total_distance,
			AVG(t.avg_speed) as avg_speed,
			COUNT(CASE WHEN t.avg_speed > 80 THEN 1 END) as speeding_incidents,
			COUNT(CASE WHEN t.duration > 28800 THEN 1 END) as overtime_violations,
			COUNT(DISTINCT dbe.id) as total_violations,
			SUM(CASE WHEN dbe.event_type = 'overspeed' THEN 1 ELSE 0 END) as speeding_violations,
			SUM(CASE WHEN dbe.event_type IN ('harsh_acceleration', 'harsh_braking', 'harsh_cornering') THEN 1 ELSE 0 END) as harsh_driving_violations,
			SUM(CASE WHEN dbe.event_type = 'geofence_violation' THEN 1 ELSE 0 END) as geofence_violations,
			(COUNT(CASE WHEN t.avg_speed > 80 THEN 1 END) / COUNT(t.id) * 100) as speeding_rate,
			(COUNT(DISTINCT dbe.id) / COUNT(t.id) * 100) as violation_rate
		`).
		Joins("LEFT JOIN trips t ON d.id = t.driver_id").
		Joins("LEFT JOIN driving_behavior_events dbe ON t.id = dbe.trip_id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m'), d.id").
		Order("DATE_FORMAT(t.start_time, '%Y-%m') DESC, (COUNT(DISTINCT dbe.id) / COUNT(t.id) * 100) DESC")

	// Apply filters
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN (?)", filters.DriverIds)
	}
	if filters.StartDate != nil {
		query = query.Where("t.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("t.start_time <= ?", *filters.EndDate)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Driver{}).Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Month                  string   `json:"month"`
		DriverId               uint     `json:"driver_id"`
		DriverName             string   `json:"driver_name"`
		LicenseNumber          string   `json:"license_number"`
		Phone                  string   `json:"phone"`
		TotalTrips             int      `json:"total_trips"`
		TotalDistance          float64  `json:"total_distance"`
		AvgSpeed               float64  `json:"avg_speed"`
		SpeedingIncidents      int      `json:"speeding_incidents"`
		OvertimeViolations     int      `json:"overtime_violations"`
		TotalViolations        int      `json:"total_violations"`
		SpeedingViolations     int      `json:"speeding_violations"`
		HarshDrivingViolations int      `json:"harsh_driving_violations"`
		GeofenceViolations     int      `json:"geofence_violations"`
		SpeedingRate           float64  `json:"speeding_rate"`
		ViolationRate          float64  `json:"violation_rate"`
		BehaviorScore          float64  `json:"behavior_score"`
		RiskLevel              string   `json:"risk_level"`
		TrendDirection         string   `json:"trend_direction"`
		ImprovementAreas       []string `json:"improvement_areas"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate behavior scores and risk levels
	for i := range data {
		behaviorScore := 100.0
		var improvementAreas []string

		// Deduct points for violations
		if data[i].SpeedingRate > 10 {
			behaviorScore -= 20
			improvementAreas = append(improvementAreas, "Speed management")
		}
		if data[i].HarshDrivingViolations > 5 {
			behaviorScore -= 25
			improvementAreas = append(improvementAreas, "Smooth driving")
		}
		if data[i].OvertimeViolations > 3 {
			behaviorScore -= 15
			improvementAreas = append(improvementAreas, "Hours compliance")
		}
		if data[i].GeofenceViolations > 2 {
			behaviorScore -= 10
			improvementAreas = append(improvementAreas, "Route compliance")
		}

		if behaviorScore < 0 {
			behaviorScore = 0
		}

		// Risk level
		var riskLevel string
		switch {
		case behaviorScore >= 80:
			riskLevel = "Low"
		case behaviorScore >= 60:
			riskLevel = "Medium"
		case behaviorScore >= 40:
			riskLevel = "High"
		default:
			riskLevel = "Critical"
		}

		// Trend direction (simplified - would need historical data for real trends)
		var trendDirection string
		if data[i].ViolationRate < 5 {
			trendDirection = "Improving"
		} else if data[i].ViolationRate < 15 {
			trendDirection = "Stable"
		} else {
			trendDirection = "Declining"
		}

		if len(improvementAreas) == 0 {
			improvementAreas = append(improvementAreas, "Maintain current good practices")
		}

		data[i].BehaviorScore = behaviorScore
		data[i].RiskLevel = riskLevel
		data[i].TrendDirection = trendDirection
		data[i].ImprovementAreas = improvementAreas
	}

	// Summary statistics
	summary := struct {
		TotalRecords        int     `json:"total_records"`
		TotalDrivers        int     `json:"total_drivers"`
		AvgBehaviorScore    float64 `json:"avg_behavior_score"`
		HighRiskDrivers     int     `json:"high_risk_drivers"`
		CriticalRiskDrivers int     `json:"critical_risk_drivers"`
		TotalViolations     int     `json:"total_violations"`
		MostCommonViolation string  `json:"most_common_violation"`
		ImprovingDrivers    int     `json:"improving_drivers"`
		DecliningDrivers    int     `json:"declining_drivers"`
		BehaviorTrendScore  float64 `json:"behavior_trend_score"`
	}{
		TotalRecords: len(data),
	}

	if len(data) > 0 {
		var totalScore, totalViolations float64
		var highRisk, criticalRisk, improving, declining int
		violationCounts := make(map[string]int)

		for _, d := range data {
			totalScore += d.BehaviorScore
			totalViolations += float64(d.TotalViolations)

			if d.RiskLevel == "High" {
				highRisk++
			}
			if d.RiskLevel == "Critical" {
				criticalRisk++
			}
			if d.TrendDirection == "Improving" {
				improving++
			}
			if d.TrendDirection == "Declining" {
				declining++
			}

			// Count violation types
			if d.SpeedingViolations > 0 {
				violationCounts["Speeding"] += d.SpeedingViolations
			}
			if d.HarshDrivingViolations > 0 {
				violationCounts["Harsh Driving"] += d.HarshDrivingViolations
			}
			if d.OvertimeViolations > 0 {
				violationCounts["Overtime"] += d.OvertimeViolations
			}
			if d.GeofenceViolations > 0 {
				violationCounts["Geofence"] += d.GeofenceViolations
			}
		}

		summary.TotalDrivers = len(data)
		summary.AvgBehaviorScore = totalScore / float64(len(data))
		summary.HighRiskDrivers = highRisk
		summary.CriticalRiskDrivers = criticalRisk
		summary.TotalViolations = int(totalViolations)
		summary.ImprovingDrivers = improving
		summary.DecliningDrivers = declining

		// Find most common violation
		var mostCommon string
		var maxCount int
		for violation, count := range violationCounts {
			if count > maxCount {
				maxCount = count
				mostCommon = violation
			}
		}
		summary.MostCommonViolation = mostCommon

		// Behavior trend score
		if summary.TotalDrivers > 0 {
			summary.BehaviorTrendScore = float64(improving) / float64(summary.TotalDrivers) * 100
		}
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateFuelEfficiencyTrendsReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Long-term fuel efficiency trends analysis
	query := config.DB.Table("trips").
		Select(`
			DATE_FORMAT(trips.start_time, '%Y-%m') as month,
			SUM(trips.distance) as total_distance,
			SUM(trips.duration) as total_duration,
			AVG(trips.avg_speed) as avg_speed,
			COUNT(trips.id) as total_trips,
			COUNT(DISTINCT trips.client_device_id) as active_vehicles,
			(SUM(trips.distance) * 0.12) as estimated_fuel_consumption,
			(SUM(trips.distance) * 0.12 * 1.5) as estimated_fuel_cost,
			(SUM(trips.distance) / (SUM(trips.duration) / 3600.0)) as efficiency_km_per_hour,
			(SUM(trips.distance) * 0.12 / (SUM(trips.duration) / 3600.0)) as fuel_efficiency_lph,
			(SUM(trips.distance) / COUNT(trips.id)) as avg_trip_distance,
			(SUM(trips.duration) / COUNT(trips.id)) as avg_trip_duration
		`).
		Joins("LEFT JOIN client_devices cd ON trips.client_device_id = cd.id").
		Group("DATE_FORMAT(trips.start_time, '%Y-%m')").
		Order("DATE_FORMAT(trips.start_time, '%Y-%m') DESC")

	// Apply filters
	query = s.applyTripFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	// Create a separate count query with proper JOIN
	countQuery := config.DB.Table("trips").
		Joins("LEFT JOIN client_devices cd ON trips.client_device_id = cd.id").
		Group("DATE_FORMAT(trips.start_time, '%Y-%m')")
	countQuery = s.applyTripFilters(countQuery, filters)
	countQuery.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Month                    string  `json:"month"`
		TotalDistance            float64 `json:"total_distance"`
		TotalDuration            int     `json:"total_duration"`
		AvgSpeed                 float64 `json:"avg_speed"`
		TotalTrips               int     `json:"total_trips"`
		ActiveVehicles           int     `json:"active_vehicles"`
		EstimatedFuelConsumption float64 `json:"estimated_fuel_consumption"`
		EstimatedFuelCost        float64 `json:"estimated_fuel_cost"`
		EfficiencyKmPerHour      float64 `json:"efficiency_km_per_hour"`
		FuelEfficiencyLph        float64 `json:"fuel_efficiency_lph"`
		AvgTripDistance          float64 `json:"avg_trip_distance"`
		AvgTripDuration          float64 `json:"avg_trip_duration"`
		EfficiencyTrend          string  `json:"efficiency_trend"`
		CostPerKm                float64 `json:"cost_per_km"`
		SavingsVsPrevious        float64 `json:"savings_vs_previous"`
		EfficiencyRating         string  `json:"efficiency_rating"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate trends and efficiency metrics
	for i := range data {
		// Calculate cost per km
		if data[i].TotalDistance > 0 {
			data[i].CostPerKm = data[i].EstimatedFuelCost / data[i].TotalDistance
		}

		// Calculate savings vs previous month
		if i < len(data)-1 {
			previousCost := data[i+1].EstimatedFuelCost
			if previousCost > 0 {
				data[i].SavingsVsPrevious = previousCost - data[i].EstimatedFuelCost
			}
		}

		// Efficiency trend
		var trend string
		if i < len(data)-1 {
			previousEfficiency := data[i+1].FuelEfficiencyLph
			if data[i].FuelEfficiencyLph < previousEfficiency {
				trend = "Improving"
			} else if data[i].FuelEfficiencyLph > previousEfficiency {
				trend = "Declining"
			} else {
				trend = "Stable"
			}
		} else {
			trend = "New"
		}

		// Efficiency rating
		var rating string
		switch {
		case data[i].FuelEfficiencyLph <= 8:
			rating = "Excellent"
		case data[i].FuelEfficiencyLph <= 12:
			rating = "Good"
		case data[i].FuelEfficiencyLph <= 16:
			rating = "Average"
		case data[i].FuelEfficiencyLph <= 20:
			rating = "Poor"
		default:
			rating = "Very Poor"
		}

		data[i].EfficiencyTrend = trend
		data[i].EfficiencyRating = rating
	}

	// Summary statistics
	summary := struct {
		TotalMonths            int     `json:"total_months"`
		TotalFuelConsumption   float64 `json:"total_fuel_consumption"`
		TotalFuelCost          float64 `json:"total_fuel_cost"`
		AvgFuelEfficiency      float64 `json:"avg_fuel_efficiency"`
		TotalSavings           float64 `json:"total_savings"`
		ImprovingMonths        int     `json:"improving_months"`
		DecliningMonths        int     `json:"declining_months"`
		BestEfficiencyMonth    string  `json:"best_efficiency_month"`
		WorstEfficiencyMonth   string  `json:"worst_efficiency_month"`
		EfficiencyTrendScore   float64 `json:"efficiency_trend_score"`
		ProjectedAnnualSavings float64 `json:"projected_annual_savings"`
	}{
		TotalMonths: len(data),
	}

	if len(data) > 0 {
		var totalFuel, totalCost, totalEfficiency, totalSavings float64
		var improvingMonths, decliningMonths int
		var bestMonth, worstMonth string
		var bestEfficiency, worstEfficiency float64

		for _, d := range data {
			totalFuel += d.EstimatedFuelConsumption
			totalCost += d.EstimatedFuelCost
			totalEfficiency += d.FuelEfficiencyLph
			totalSavings += d.SavingsVsPrevious

			if d.EfficiencyTrend == "Improving" {
				improvingMonths++
			}
			if d.EfficiencyTrend == "Declining" {
				decliningMonths++
			}

			if d.FuelEfficiencyLph < bestEfficiency || bestEfficiency == 0 {
				bestEfficiency = d.FuelEfficiencyLph
				bestMonth = d.Month
			}
			if d.FuelEfficiencyLph > worstEfficiency {
				worstEfficiency = d.FuelEfficiencyLph
				worstMonth = d.Month
			}
		}

		summary.TotalFuelConsumption = totalFuel
		summary.TotalFuelCost = totalCost
		summary.AvgFuelEfficiency = totalEfficiency / float64(len(data))
		summary.TotalSavings = totalSavings
		summary.ImprovingMonths = improvingMonths
		summary.DecliningMonths = decliningMonths
		summary.BestEfficiencyMonth = bestMonth
		summary.WorstEfficiencyMonth = worstMonth
		summary.EfficiencyTrendScore = float64(improvingMonths) / float64(len(data)) * 100
		summary.ProjectedAnnualSavings = totalSavings * 12 // Project annual savings
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateFleetProductivityReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Overall fleet productivity metrics with utilization and efficiency insights
	query := config.DB.Table("trips t").
		Select(`
			DATE_FORMAT(t.start_time, '%Y-%m') as month,
			COUNT(t.id) as total_trips,
			SUM(t.distance) as total_distance,
			SUM(t.duration) as total_duration,
			COUNT(DISTINCT t.client_device_id) as active_vehicles,
			COUNT(DISTINCT t.driver_id) as active_drivers,
			AVG(t.avg_speed) as avg_speed,
			(SUM(t.distance) / COUNT(DISTINCT t.client_device_id)) as avg_distance_per_vehicle,
			(SUM(t.duration) / COUNT(t.id)) as avg_trip_duration,
			(SUM(t.distance) / (SUM(t.duration) / 3600.0)) as efficiency_km_per_hour,
			(COUNT(t.id) / COUNT(DISTINCT t.client_device_id)) as trips_per_vehicle,
			(SUM(t.duration) / COUNT(DISTINCT t.client_device_id) / 3600.0) as hours_per_vehicle
		`).
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m')").
		Order("DATE_FORMAT(t.start_time, '%Y-%m') DESC")

	// Apply filters
	query = s.applyTripFilters(query, filters)

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	// Create a separate count query with proper JOIN
	countQuery := config.DB.Table("trips t").
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Group("DATE_FORMAT(t.start_time, '%Y-%m')")
	countQuery = s.applyTripFilters(countQuery, filters)
	countQuery.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Month                 string  `json:"month"`
		TotalTrips            int     `json:"total_trips"`
		TotalDistance         float64 `json:"total_distance"`
		TotalDuration         int     `json:"total_duration"`
		ActiveVehicles        int     `json:"active_vehicles"`
		ActiveDrivers         int     `json:"active_drivers"`
		AvgSpeed              float64 `json:"avg_speed"`
		AvgDistancePerVehicle float64 `json:"avg_distance_per_vehicle"`
		AvgTripDuration       float64 `json:"avg_trip_duration"`
		EfficiencyKmPerHour   float64 `json:"efficiency_km_per_hour"`
		TripsPerVehicle       float64 `json:"trips_per_vehicle"`
		HoursPerVehicle       float64 `json:"hours_per_vehicle"`
		ProductivityScore     float64 `json:"productivity_score"`
		UtilizationRate       float64 `json:"utilization_rate"`
		ProductivityTrend     string  `json:"productivity_trend"`
		PerformanceRating     string  `json:"performance_rating"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate productivity metrics
	for i := range data {
		// Productivity score (0-100)
		productivityScore := 0.0

		// Distance efficiency (30% weight)
		if data[i].AvgDistancePerVehicle > 1000 {
			productivityScore += 30
		} else if data[i].AvgDistancePerVehicle > 500 {
			productivityScore += 20
		} else if data[i].AvgDistancePerVehicle > 200 {
			productivityScore += 10
		}

		// Trip efficiency (25% weight)
		if data[i].TripsPerVehicle > 20 {
			productivityScore += 25
		} else if data[i].TripsPerVehicle > 10 {
			productivityScore += 15
		} else if data[i].TripsPerVehicle > 5 {
			productivityScore += 10
		}

		// Speed efficiency (20% weight)
		if data[i].AvgSpeed > 50 && data[i].AvgSpeed < 80 {
			productivityScore += 20
		} else if data[i].AvgSpeed > 40 && data[i].AvgSpeed < 90 {
			productivityScore += 15
		} else if data[i].AvgSpeed > 30 {
			productivityScore += 10
		}

		// Time utilization (25% weight)
		utilizationRate := (data[i].HoursPerVehicle / 160.0) * 100 // Assuming 160 hours per month
		if utilizationRate > 80 {
			productivityScore += 25
		} else if utilizationRate > 60 {
			productivityScore += 20
		} else if utilizationRate > 40 {
			productivityScore += 15
		}

		// Productivity trend
		var trend string
		if i < len(data)-1 {
			previousScore := data[i+1].ProductivityScore
			if productivityScore > previousScore {
				trend = "Improving"
			} else if productivityScore < previousScore {
				trend = "Declining"
			} else {
				trend = "Stable"
			}
		} else {
			trend = "New"
		}

		// Performance rating
		var rating string
		switch {
		case productivityScore >= 80:
			rating = "Excellent"
		case productivityScore >= 60:
			rating = "Good"
		case productivityScore >= 40:
			rating = "Average"
		case productivityScore >= 20:
			rating = "Poor"
		default:
			rating = "Very Poor"
		}

		data[i].ProductivityScore = productivityScore
		data[i].UtilizationRate = utilizationRate
		data[i].ProductivityTrend = trend
		data[i].PerformanceRating = rating
	}

	// Summary statistics
	summary := struct {
		TotalMonths               int      `json:"total_months"`
		TotalTrips                int      `json:"total_trips"`
		TotalDistance             float64  `json:"total_distance"`
		AvgProductivityScore      float64  `json:"avg_productivity_score"`
		AvgUtilizationRate        float64  `json:"avg_utilization_rate"`
		ImprovingMonths           int      `json:"improving_months"`
		DecliningMonths           int      `json:"declining_months"`
		BestProductivityMonth     string   `json:"best_productivity_month"`
		WorstProductivityMonth    string   `json:"worst_productivity_month"`
		ProductivityTrendScore    float64  `json:"productivity_trend_score"`
		FleetEfficiencyRating     string   `json:"fleet_efficiency_rating"`
		OptimizationOpportunities []string `json:"optimization_opportunities"`
	}{
		TotalMonths: len(data),
	}

	if len(data) > 0 {
		var totalTrips, totalDistance, totalScore, totalUtilization float64
		var improvingMonths, decliningMonths int
		var bestMonth, worstMonth string
		var bestScore, worstScore float64

		for _, d := range data {
			totalTrips += float64(d.TotalTrips)
			totalDistance += d.TotalDistance
			totalScore += d.ProductivityScore
			totalUtilization += d.UtilizationRate

			if d.ProductivityTrend == "Improving" {
				improvingMonths++
			}
			if d.ProductivityTrend == "Declining" {
				decliningMonths++
			}

			if d.ProductivityScore > bestScore {
				bestScore = d.ProductivityScore
				bestMonth = d.Month
			}
			if d.ProductivityScore < worstScore || worstScore == 0 {
				worstScore = d.ProductivityScore
				worstMonth = d.Month
			}
		}

		summary.TotalTrips = int(totalTrips)
		summary.TotalDistance = totalDistance
		summary.AvgProductivityScore = totalScore / float64(len(data))
		summary.AvgUtilizationRate = totalUtilization / float64(len(data))
		summary.ImprovingMonths = improvingMonths
		summary.DecliningMonths = decliningMonths
		summary.BestProductivityMonth = bestMonth
		summary.WorstProductivityMonth = worstMonth
		summary.ProductivityTrendScore = float64(improvingMonths) / float64(len(data)) * 100

		// Fleet efficiency rating
		var rating string
		switch {
		case summary.AvgProductivityScore >= 80:
			rating = "Excellent"
		case summary.AvgProductivityScore >= 60:
			rating = "Good"
		case summary.AvgProductivityScore >= 40:
			rating = "Average"
		case summary.AvgProductivityScore >= 20:
			rating = "Poor"
		default:
			rating = "Very Poor"
		}
		summary.FleetEfficiencyRating = rating

		// Optimization opportunities
		if summary.AvgUtilizationRate < 60 {
			summary.OptimizationOpportunities = append(summary.OptimizationOpportunities, "Increase vehicle utilization")
		}
		if summary.AvgProductivityScore < 60 {
			summary.OptimizationOpportunities = append(summary.OptimizationOpportunities, "Optimize route planning")
		}
		if improvingMonths < decliningMonths {
			summary.OptimizationOpportunities = append(summary.OptimizationOpportunities, "Implement productivity improvement programs")
		}
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// generateRouteDeviationReport generates route deviation analysis
func (s *ReportService) generateRouteDeviationReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// For route deviation analysis, we need to:
	// 1. Analyze trips and identify common routes
	// 2. Calculate expected vs actual paths
	// 3. Identify deviations and inefficiencies

	// Get trips with GPS data for route analysis
	var trips []struct {
		ID               uint       `json:"id"`
		DeviceName       string     `json:"device_name"`
		PlateNumber      string     `json:"plate_number"`
		DriverName       *string    `json:"driver_name"`
		StartTime        *time.Time `json:"start_time"`
		EndTime          *time.Time `json:"end_time"`
		StartLatitude    float64    `json:"start_latitude"`
		StartLongitude   float64    `json:"start_longitude"`
		EndLatitude      float64    `json:"end_latitude"`
		EndLongitude     float64    `json:"end_longitude"`
		ActualDistance   float64    `json:"actual_distance"`
		ExpectedDistance float64    `json:"expected_distance"`
		Deviation        float64    `json:"deviation"`
		CommonRoute      string     `json:"common_route"`
	}

	query := config.DB.Table("trips").
		Select(`
			trips.id,
			cd.name as device_name,
			cd.plate_number,
			d.name as driver_name,
			trips.start_time,
			trips.end_time,
			trips.start_latitude as start_latitude,
			trips.start_longitude as start_longitude,
			trips.end_latitude as end_latitude,
			trips.end_longitude as end_longitude,
			trips.distance as actual_distance,
			ROUND(
				SQRT(
					POW((trips.end_latitude - trips.start_latitude) * 111.32, 2) +
					POW((trips.end_longitude - trips.start_longitude) * 111.32 * COS(RADIANS(trips.start_latitude)), 2)
				), 2
			) as expected_distance,
			ROUND(trips.distance - 
				SQRT(
					POW((trips.end_latitude - trips.start_latitude) * 111.32, 2) +
					POW((trips.end_longitude - trips.start_longitude) * 111.32 * COS(RADIANS(trips.start_latitude)), 2)
				), 2
			) as deviation,
			CASE 
				WHEN trips.distance > 50 THEN 'Long Distance'
				WHEN trips.distance > 20 THEN 'Medium Distance'
				ELSE 'Short Distance'
			END as common_route
		`).
		Joins("LEFT JOIN client_devices cd ON trips.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON trips.driver_id = d.id").
		Where("cd.client_id = ?", filters.ClientId).
		Where("trips.distance >= ?", 0.1) // Reduced minimum distance for testing

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("trips.start_time >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("trips.end_time <= ?", filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("trips.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("trips.driver_id IN ?", filters.DriverIds)
	}

	// Get total count
	var totalRecords int64
	config.DB.Table("trips").
		Joins("LEFT JOIN client_devices cd ON trips.client_device_id = cd.id").
		Where("cd.client_id = ?", filters.ClientId).
		Where("trips.distance >= ?", 0.1).
		Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	// Execute query
	err := query.Order("trips.start_time DESC").Find(&trips).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch route deviation data: %v", err)
	}

	// Convert to DTO
	var dtoData []models.RouteDeviationRow
	for _, trip := range trips {
		driverName := ""
		if trip.DriverName != nil {
			driverName = *trip.DriverName
		}

		startTime := ""
		if trip.StartTime != nil {
			startTime = trip.StartTime.Format("2006-01-02 15:04:05")
		}

		endTime := ""
		if trip.EndTime != nil {
			endTime = trip.EndTime.Format("2006-01-02 15:04:05")
		}

		// Calculate additional fields in Go
		deviationPercent := 0.0
		routeEfficiency := 0.0
		optimizationScore := 0.0
		recommendations := "Route is efficient"

		if trip.ActualDistance > 0 {
			deviationPercent = (trip.Deviation / trip.ActualDistance) * 100
			routeEfficiency = (trip.ExpectedDistance / trip.ActualDistance) * 100
			optimizationScore = (trip.ExpectedDistance / trip.ActualDistance) * 100

			if routeEfficiency < 70 {
				recommendations = "Consider route optimization"
			} else if routeEfficiency < 85 {
				recommendations = "Minor route improvements possible"
			}
		}

		dtoRow := models.RouteDeviationRow{
			TripID:            trip.ID,
			DeviceName:        trip.DeviceName,
			PlateNumber:       trip.PlateNumber,
			DriverName:        driverName,
			StartTime:         startTime,
			EndTime:           endTime,
			StartLocation:     fmt.Sprintf("%.6f, %.6f", trip.StartLatitude, trip.StartLongitude),
			EndLocation:       fmt.Sprintf("%.6f, %.6f", trip.EndLatitude, trip.EndLongitude),
			ActualDistance:    trip.ActualDistance,
			ExpectedDistance:  trip.ExpectedDistance,
			Deviation:         trip.Deviation,
			DeviationPercent:  deviationPercent,
			RouteEfficiency:   routeEfficiency,
			CommonRoute:       trip.CommonRoute,
			OptimizationScore: optimizationScore,
			Recommendations:   recommendations,
		}
		dtoData = append(dtoData, dtoRow)
	}

	// Calculate summary statistics
	var totalTrips int
	var totalActualDistance, totalExpectedDistance, totalDeviation float64
	var avgOptimizationScore float64

	for _, trip := range trips {
		totalTrips++
		totalActualDistance += trip.ActualDistance
		totalExpectedDistance += trip.ExpectedDistance
		totalDeviation += trip.Deviation

		// Calculate optimization score for this trip
		if trip.ActualDistance > 0 {
			optimizationScore := (trip.ExpectedDistance / trip.ActualDistance) * 100
			avgOptimizationScore += optimizationScore
		}
	}

	if totalTrips > 0 {
		avgOptimizationScore = avgOptimizationScore / float64(totalTrips)
	}

	// Calculate efficiency safely to avoid division by zero
	overallEfficiency := 0.0
	if totalActualDistance > 0 {
		overallEfficiency = (totalExpectedDistance / totalActualDistance) * 100
	}

	summary := models.RouteDeviationSummary{
		TotalTrips:            totalTrips,
		TotalActualDistance:   totalActualDistance,
		TotalExpectedDistance: totalExpectedDistance,
		TotalDeviation:        totalDeviation,
		AvgOptimizationScore:  avgOptimizationScore,
		OverallEfficiency:     overallEfficiency,
		TotalFuelSavings:      totalDeviation * 0.15, // Estimate fuel savings per km
	}

	// Create DTO
	dto := &models.RouteDeviationDTO{
		Headers: []string{
			"Trip ID", "Device Name", "Plate Number", "Driver Name", "Start Time", "End Time",
			"Start Location", "End Location", "Actual Distance (km)", "Expected Distance (km)",
			"Deviation (km)", "Deviation (%)", "Route Efficiency (%)", "Common Route",
			"Optimization Score", "Recommendations",
		},
		Data:    dtoData,
		Summary: summary,
	}

	return dto, summary, int(totalRecords), len(dtoData), nil
}

// generateLastLocationReport generates asset tracking with last known positions
func (s *ReportService) generateLastLocationReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Get last known location for all devices
	var devices []struct {
		DeviceName     string     `json:"device_name"`
		PlateNumber    string     `json:"plate_number"`
		DriverName     *string    `json:"driver_name"`
		LastLocation   string     `json:"last_location"`
		Latitude       float64    `json:"latitude"`
		Longitude      float64    `json:"longitude"`
		LastSeen       *time.Time `json:"last_seen"`
		Status         string     `json:"status"`
		CurrentTrip    *uint      `json:"current_trip"`
		Speed          *float64   `json:"speed"`
		Direction      *string    `json:"direction"`
		IgnitionStatus *bool      `json:"ignition_status"`
		BatteryLevel   string     `json:"battery_level"`
		SignalStrength string     `json:"signal_strength"`
	}

	query := config.DB.Table("client_devices cd").
		Select(`
			cd.name as device_name,
			cd.plate_number,
			d.name as driver_name,
			COALESCE(last_gps.location_name, 'Unknown Location') as last_location,
			last_gps.latitude,
			last_gps.longitude,
			last_gps.gps_timestamp as last_seen,
			CASE 
				WHEN last_gps.gps_timestamp > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 'Online'
				WHEN last_gps.gps_timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'Recent'
				ELSE 'Offline'
			END as status,
			last_gps.trip_id as current_trip,
			last_gps.speed,
			last_gps.direction,
			last_gps.ignition_status,
			'85%' as battery_level,
			'Strong' as signal_strength
		`).
		Joins("LEFT JOIN drivers d ON cd.driver_id = d.id").
		Joins(`
			LEFT JOIN (
				SELECT g1.* 
				FROM gps_data g1
				INNER JOIN (
					SELECT client_device_id, MAX(gps_timestamp) as max_timestamp
					FROM gps_data
					GROUP BY client_device_id
				) g2 ON g1.client_device_id = g2.client_device_id AND g1.gps_timestamp = g2.max_timestamp
			) last_gps ON cd.id = last_gps.client_device_id
		`).
		Where("cd.client_id = ?", filters.ClientId)

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("cd.id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("cd.driver_id IN ?", filters.DriverIds)
	}

	// Get total count
	var totalRecords int64
	config.DB.Table("client_devices cd").
		Where("cd.client_id = ?", filters.ClientId).
		Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	// Execute query
	err := query.Order("cd.name").Find(&devices).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch last location data: %v", err)
	}

	// Convert to DTO
	var dtoData []models.LastLocationRow
	var totalSpeed float64
	var onlineDevices, offlineDevices, onTripDevices, idleDevices, activeDrivers int

	for _, device := range devices {
		driverName := ""
		if device.DriverName != nil {
			driverName = *device.DriverName
			activeDrivers++
		}

		lastSeen := "Never"
		if device.LastSeen != nil {
			lastSeen = device.LastSeen.Format("2006-01-02 15:04:05")
		}

		currentTrip := "No Trip"
		if device.CurrentTrip != nil {
			currentTrip = fmt.Sprintf("Trip %d", *device.CurrentTrip)
			onTripDevices++
		}

		speed := 0.0
		if device.Speed != nil {
			speed = *device.Speed
			totalSpeed += speed
		}

		direction := "Unknown"
		if device.Direction != nil {
			direction = *device.Direction
		}

		engineStatus := "Off"
		if device.IgnitionStatus != nil && *device.IgnitionStatus {
			engineStatus = "On"
			if device.CurrentTrip == nil {
				idleDevices++
			}
		}

		// Count status
		if device.Status == "Online" {
			onlineDevices++
		} else {
			offlineDevices++
		}

		dtoRow := models.LastLocationRow{
			DeviceName:     device.DeviceName,
			PlateNumber:    device.PlateNumber,
			DriverName:     driverName,
			LastLocation:   device.LastLocation,
			Latitude:       device.Latitude,
			Longitude:      device.Longitude,
			LastSeen:       lastSeen,
			Status:         device.Status,
			CurrentTrip:    currentTrip,
			Speed:          speed,
			Direction:      direction,
			EngineStatus:   engineStatus,
			BatteryLevel:   device.BatteryLevel,
			SignalStrength: device.SignalStrength,
		}
		dtoData = append(dtoData, dtoRow)
	}

	// Calculate summary statistics
	avgSpeed := 0.0
	if len(dtoData) > 0 {
		avgSpeed = totalSpeed / float64(len(dtoData))
	}

	summary := models.LastLocationStats{
		TotalDevices:   len(dtoData),
		OnlineDevices:  onlineDevices,
		OfflineDevices: offlineDevices,
		OnTripDevices:  onTripDevices,
		IdleDevices:    idleDevices,
		AvgSpeed:       avgSpeed,
		ActiveDrivers:  activeDrivers,
	}

	// Create DTO
	dto := &models.LastLocationDTO{
		Headers: []string{
			"Device Name", "Plate Number", "Driver Name", "Last Location", "Latitude", "Longitude",
			"Last Seen", "Status", "Current Trip", "Speed (km/h)", "Direction", "Engine Status",
			"Battery Level", "Signal Strength",
		},
		Data:    dtoData,
		Summary: summary,
	}

	return dto, summary, int(totalRecords), len(dtoData), nil
}

// generateLastLocationHistoryReport generates location history for devices over time
func (s *ReportService) generateLastLocationHistoryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Get location history data for devices
	var historyData []struct {
		DeviceName     string     `json:"device_name"`
		PlateNumber    string     `json:"plate_number"`
		DriverName     *string    `json:"driver_name"`
		LocationName   string     `json:"location_name"`
		Latitude       float64    `json:"latitude"`
		Longitude      float64    `json:"longitude"`
		GPSTimestamp   *time.Time `json:"gps_timestamp"`
		Speed          *float64   `json:"speed"`
		Direction      *string    `json:"direction"`
		IgnitionStatus *bool      `json:"ignition_status"`
		TripId         *uint      `json:"trip_id"`
		Status         string     `json:"status"`
	}

	// Build query for location history
	query := config.DB.Table("gps_data g").
		Select(`
			cd.name as device_name,
			cd.plate_number,
			d.name as driver_name,
			COALESCE(g.location_name, 'Unknown Location') as location_name,
			g.latitude,
			g.longitude,
			g.gps_timestamp,
			g.speed,
			g.direction,
			g.ignition_status,
			g.trip_id,
			CASE 
				WHEN g.gps_timestamp > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 'Online'
				WHEN g.gps_timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'Recent'
				ELSE 'Offline'
			END as status
		`).
		Joins("LEFT JOIN client_devices cd ON g.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON cd.driver_id = d.id").
		Where("cd.client_id = ?", filters.ClientId)

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("cd.id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("cd.driver_id IN ?", filters.DriverIds)
	}

	// Apply date filters
	if filters.StartDate != nil {
		query = query.Where("g.gps_timestamp >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("g.gps_timestamp <= ?", filters.EndDate)
	}

	// Get total count
	var totalRecords int64
	countQuery := config.DB.Table("gps_data g").
		Joins("LEFT JOIN client_devices cd ON g.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON cd.driver_id = d.id").
		Where("cd.client_id = ?", filters.ClientId)

	// Apply same filters to count query
	if len(filters.ClientDeviceIds) > 0 {
		countQuery = countQuery.Where("cd.id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		countQuery = countQuery.Where("cd.driver_id IN ?", filters.DriverIds)
	}
	if filters.StartDate != nil {
		countQuery = countQuery.Where("g.gps_timestamp >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		countQuery = countQuery.Where("g.gps_timestamp <= ?", filters.EndDate)
	}

	countQuery.Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	// Execute query - order by device name and timestamp
	err := query.Order("cd.name, g.gps_timestamp DESC").Find(&historyData).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch location history data: %v", err)
	}

	// Convert to DTO
	var dtoData []models.LocationHistoryRow
	var totalSpeed float64
	var onlineDevices, offlineDevices, onTripDevices, idleDevices, activeDrivers int
	deviceStatus := make(map[string]string) // Track latest status per device

	for _, record := range historyData {
		driverName := ""
		if record.DriverName != nil {
			driverName = *record.DriverName
			if _, exists := deviceStatus[record.DeviceName]; !exists {
				activeDrivers++
			}
		}

		timestamp := "Unknown"
		if record.GPSTimestamp != nil {
			timestamp = record.GPSTimestamp.Format("2006-01-02 15:04:05")
		}

		tripInfo := "No Trip"
		if record.TripId != nil {
			tripInfo = fmt.Sprintf("Trip %d", *record.TripId)
			if _, exists := deviceStatus[record.DeviceName]; !exists {
				onTripDevices++
			}
		}

		speed := 0.0
		if record.Speed != nil {
			speed = *record.Speed
			totalSpeed += speed
		}

		direction := "Unknown"
		if record.Direction != nil {
			direction = *record.Direction
		}

		engineStatus := "Off"
		if record.IgnitionStatus != nil && *record.IgnitionStatus {
			engineStatus = "On"
			if record.TripId == nil {
				if _, exists := deviceStatus[record.DeviceName]; !exists {
					idleDevices++
				}
			}
		}

		// Track device status (only count once per device)
		if _, exists := deviceStatus[record.DeviceName]; !exists {
			deviceStatus[record.DeviceName] = record.Status
			if record.Status == "Online" {
				onlineDevices++
			} else {
				offlineDevices++
			}
		}

		dtoRow := models.LocationHistoryRow{
			DeviceName:   record.DeviceName,
			PlateNumber:  record.PlateNumber,
			DriverName:   driverName,
			LocationName: record.LocationName,
			Latitude:     record.Latitude,
			Longitude:    record.Longitude,
			GPSTimestamp: timestamp,
			Speed:        speed,
			Direction:    direction,
			EngineStatus: engineStatus,
			TripInfo:     tripInfo,
			Status:       record.Status,
		}
		dtoData = append(dtoData, dtoRow)
	}

	// Calculate summary statistics
	avgSpeed := 0.0
	if len(dtoData) > 0 {
		avgSpeed = totalSpeed / float64(len(dtoData))
	}

	summary := models.LocationHistoryStats{
		TotalRecords:   len(dtoData),
		OnlineDevices:  onlineDevices,
		OfflineDevices: offlineDevices,
		OnTripDevices:  onTripDevices,
		IdleDevices:    idleDevices,
		AvgSpeed:       avgSpeed,
		ActiveDrivers:  activeDrivers,
	}

	// Create DTO
	dto := &models.LocationHistoryDTO{
		Headers: []string{
			"Device Name", "Plate Number", "Driver Name", "Location", "Latitude", "Longitude",
			"GPS Timestamp", "Speed (km/h)", "Direction", "Engine Status", "Trip Info", "Status",
		},
		Data:    dtoData,
		Summary: summary,
	}

	return dto, summary, int(totalRecords), len(dtoData), nil
}

// generateDestinationZoneInReport generates destination zone entry analysis
func (s *ReportService) generateDestinationZoneInReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Get destination zone entry data
	var zoneData []struct {
		DeviceName   string     `json:"device_name"`
		PlateNumber  string     `json:"plate_number"`
		DriverName   *string    `json:"driver_name"`
		ZoneName     string     `json:"zone_name"`
		EntryTime    *time.Time `json:"entry_time"`
		ExitTime     *time.Time `json:"exit_time"`
		Duration     *float64   `json:"duration"`
		LocationName string     `json:"location_name"`
		Latitude     float64    `json:"latitude"`
		Longitude    float64    `json:"longitude"`
		TripId       *uint      `json:"trip_id"`
		Status       string     `json:"status"`
	}

	// Build query for destination zone entries
	query := config.DB.Table("geofence_events ge").
		Select(`
			cd.name as device_name,
			cd.plate_number,
			d.name as driver_name,
			g.name as zone_name,
			ge.event_timestamp as entry_time,
			NULL as exit_time,
			0 as duration,
			'Unknown Location' as location_name,
			ge.latitude,
			ge.longitude,
			NULL as trip_id,
			CASE 
				WHEN ge.event_type = 'entry' THEN 'Inside Zone'
				ELSE 'Exited Zone'
			END as status
		`).
		Joins("LEFT JOIN client_devices cd ON ge.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON cd.driver_id = d.id").
		Joins("LEFT JOIN geofences g ON ge.geofence_id = g.id").
		Where("cd.client_id = ? AND ge.event_type = 'entry'", filters.ClientId)

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("cd.id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("cd.driver_id IN ?", filters.DriverIds)
	}

	// Apply date filters
	if filters.StartDate != nil {
		query = query.Where("ge.event_timestamp >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("ge.event_timestamp <= ?", filters.EndDate)
	}

	// Get total count
	var totalRecords int64
	countQuery := config.DB.Table("geofence_events ge").
		Joins("LEFT JOIN client_devices cd ON ge.client_device_id = cd.id").
		Where("cd.client_id = ? AND ge.event_type = 'entry'", filters.ClientId)

	// Apply same filters to count query
	if len(filters.ClientDeviceIds) > 0 {
		countQuery = countQuery.Where("cd.id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		countQuery = countQuery.Where("cd.driver_id IN ?", filters.DriverIds)
	}
	if filters.StartDate != nil {
		countQuery = countQuery.Where("ge.event_timestamp >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		countQuery = countQuery.Where("ge.event_timestamp <= ?", filters.EndDate)
	}

	countQuery.Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	// Execute query - order by event timestamp
	err := query.Order("ge.event_timestamp DESC").Find(&zoneData).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch destination zone data: %v", err)
	}

	// Convert to DTO
	var dtoData []models.DestinationZoneRow
	var totalDuration float64
	var activeZones, completedZones, activeDrivers int
	deviceZones := make(map[string]bool) // Track active zones per device

	for _, record := range zoneData {
		driverName := ""
		if record.DriverName != nil {
			driverName = *record.DriverName
			if _, exists := deviceZones[record.DeviceName]; !exists {
				activeDrivers++
			}
		}

		entryTime := "Unknown"
		if record.EntryTime != nil {
			entryTime = record.EntryTime.Format("2006-01-02 15:04:05")
		}

		exitTime := "Still Inside"
		if record.ExitTime != nil {
			exitTime = record.ExitTime.Format("2006-01-02 15:04:05")
		}

		duration := 0.0
		if record.Duration != nil {
			duration = *record.Duration
			totalDuration += duration
		}

		tripInfo := "No Trip"
		if record.TripId != nil {
			tripInfo = fmt.Sprintf("Trip %d", *record.TripId)
		}

		// Track zone status
		if _, exists := deviceZones[record.DeviceName]; !exists {
			deviceZones[record.DeviceName] = true
			if record.ExitTime == nil {
				activeZones++
			} else {
				completedZones++
			}
		}

		dtoRow := models.DestinationZoneRow{
			DeviceName:   record.DeviceName,
			PlateNumber:  record.PlateNumber,
			DriverName:   driverName,
			ZoneName:     record.ZoneName,
			EntryTime:    entryTime,
			ExitTime:     exitTime,
			Duration:     duration,
			LocationName: record.LocationName,
			Latitude:     record.Latitude,
			Longitude:    record.Longitude,
			TripInfo:     tripInfo,
			Status:       record.Status,
		}
		dtoData = append(dtoData, dtoRow)
	}

	// Calculate summary statistics
	avgDuration := 0.0
	if len(dtoData) > 0 {
		avgDuration = totalDuration / float64(len(dtoData))
	}

	summary := models.DestinationZoneStats{
		TotalEntries:   len(dtoData),
		ActiveZones:    activeZones,
		CompletedZones: completedZones,
		AvgDuration:    avgDuration,
		ActiveDrivers:  activeDrivers,
	}

	// Create DTO
	dto := &models.DestinationZoneDTO{
		Headers: []string{
			"Device Name", "Plate Number", "Driver Name", "Zone Name", "Entry Time", "Exit Time",
			"Duration (min)", "Location", "Latitude", "Longitude", "Trip Info", "Status",
		},
		Data:    dtoData,
		Summary: summary,
	}

	return dto, summary, int(totalRecords), len(dtoData), nil
}

// generateVehicleUsageSummaryReport generates vehicle usage summary
func (s *ReportService) generateVehicleUsageSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Simple implementation - return basic vehicle usage data
	var vehicles []struct {
		DeviceName    string  `json:"device_name"`
		PlateNumber   string  `json:"plate_number"`
		TotalTrips    int     `json:"total_trips"`
		TotalDistance float64 `json:"total_distance"`
		TotalHours    float64 `json:"total_hours"`
		AvgSpeed      float64 `json:"avg_speed"`
		Utilization   float64 `json:"utilization"`
	}

	query := config.DB.Table("client_devices cd").
		Select(`
			cd.name as device_name,
			cd.plate_number,
			COUNT(t.id) as total_trips,
			COALESCE(SUM(t.distance), 0) as total_distance,
			COALESCE(SUM(t.duration), 0) / 3600 as total_hours,
			COALESCE(AVG(t.avg_speed), 0) as avg_speed,
			COALESCE(SUM(t.duration) / 86400 * 100, 0) as utilization
		`).
		Joins("LEFT JOIN trips t ON cd.id = t.client_device_id").
		Where("cd.client_id = ?", filters.ClientId).
		Group("cd.id, cd.name, cd.plate_number")

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("cd.id IN ?", filters.ClientDeviceIds)
	}

	// Apply date filters
	if filters.StartDate != nil {
		query = query.Where("t.start_time >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("t.start_time <= ?", filters.EndDate)
	}

	// Get total count
	var totalRecords int64
	config.DB.Table("client_devices").Where("client_id = ?", filters.ClientId).Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	err := query.Find(&vehicles).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch vehicle usage data: %v", err)
	}

	// Convert to simple DTO
	var dtoData []map[string]interface{}
	var totalTrips, totalDistance, totalHours int
	for _, v := range vehicles {
		dtoData = append(dtoData, map[string]interface{}{
			"device_name":    v.DeviceName,
			"plate_number":   v.PlateNumber,
			"total_trips":    v.TotalTrips,
			"total_distance": v.TotalDistance,
			"total_hours":    v.TotalHours,
			"avg_speed":      v.AvgSpeed,
			"utilization":    v.Utilization,
		})
		totalTrips += v.TotalTrips
		totalDistance += int(v.TotalDistance)
		totalHours += int(v.TotalHours)
	}

	summary := map[string]interface{}{
		"total_vehicles": len(dtoData),
		"total_trips":    totalTrips,
		"total_distance": totalDistance,
		"total_hours":    totalHours,
	}

	return dtoData, summary, int(totalRecords), len(dtoData), nil
}

// generateExceptionSummaryReport generates exception summary
func (s *ReportService) generateExceptionSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Simple implementation - return basic exception data
	var exceptions []struct {
		ExceptionType string `json:"exception_type"`
		Count         int    `json:"count"`
		Severity      string `json:"severity"`
	}

	query := config.DB.Table("alerts").
		Select(`
			alert_type as exception_type,
			COUNT(*) as count,
			alert_level as severity
		`).
		Joins("LEFT JOIN client_devices cd ON alerts.client_device_id = cd.id").
		Where("cd.client_id = ?", filters.ClientId).
		Group("alert_type, alert_level")

	// Apply date filters
	if filters.StartDate != nil {
		query = query.Where("alerts.created_at >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("alerts.created_at <= ?", filters.EndDate)
	}

	// Get total count
	var totalRecords int64
	countQuery := config.DB.Table("alerts").
		Joins("LEFT JOIN client_devices cd ON alerts.client_device_id = cd.id").
		Where("cd.client_id = ?", filters.ClientId)
	if filters.StartDate != nil {
		countQuery = countQuery.Where("alerts.created_at >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		countQuery = countQuery.Where("alerts.created_at <= ?", filters.EndDate)
	}
	countQuery.Count(&totalRecords)

	err := query.Find(&exceptions).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch exception data: %v", err)
	}

	// Convert to simple DTO
	var dtoData []map[string]interface{}
	var totalExceptions int
	for _, e := range exceptions {
		dtoData = append(dtoData, map[string]interface{}{
			"exception_type": e.ExceptionType,
			"count":          e.Count,
			"severity":       e.Severity,
		})
		totalExceptions += e.Count
	}

	summary := map[string]interface{}{
		"total_exceptions": totalExceptions,
		"exception_types":  len(dtoData),
	}

	return dtoData, summary, int(totalRecords), len(dtoData), nil
}

// generateDriverExceptionSummaryReport generates driver exception summary
func (s *ReportService) generateDriverExceptionSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Simple implementation - return basic driver exception data
	var driverExceptions []struct {
		DriverName    string `json:"driver_name"`
		ExceptionType string `json:"exception_type"`
		Count         int    `json:"count"`
		Severity      string `json:"severity"`
	}

	query := config.DB.Table("alerts").
		Select(`
			d.name as driver_name,
			alerts.alert_type as exception_type,
			COUNT(*) as count,
			alerts.alert_level as severity
		`).
		Joins("LEFT JOIN client_devices cd ON alerts.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON cd.driver_id = d.id").
		Where("cd.client_id = ?", filters.ClientId).
		Group("d.name, alerts.alert_type, alerts.alert_level")

	// Apply date filters
	if filters.StartDate != nil {
		query = query.Where("alerts.created_at >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("alerts.created_at <= ?", filters.EndDate)
	}

	// Get total count
	var totalRecords int64
	countQuery := config.DB.Table("alerts").
		Joins("LEFT JOIN client_devices cd ON alerts.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON cd.driver_id = d.id").
		Where("cd.client_id = ?", filters.ClientId)
	if filters.StartDate != nil {
		countQuery = countQuery.Where("alerts.created_at >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		countQuery = countQuery.Where("alerts.created_at <= ?", filters.EndDate)
	}
	countQuery.Count(&totalRecords)

	err := query.Find(&driverExceptions).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch driver exception data: %v", err)
	}

	// Convert to simple DTO
	var dtoData []map[string]interface{}
	var totalExceptions int
	for _, de := range driverExceptions {
		dtoData = append(dtoData, map[string]interface{}{
			"driver_name":    de.DriverName,
			"exception_type": de.ExceptionType,
			"count":          de.Count,
			"severity":       de.Severity,
		})
		totalExceptions += de.Count
	}

	summary := map[string]interface{}{
		"total_exceptions":        totalExceptions,
		"drivers_with_exceptions": len(dtoData),
	}

	return dtoData, summary, int(totalRecords), len(dtoData), nil
}

// generateSpeedingExceptionSummaryReport generates speeding exception summary
func (s *ReportService) generateSpeedingExceptionSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Simple implementation - return basic speeding exception data
	var speedingExceptions []struct {
		DeviceName string  `json:"device_name"`
		DriverName string  `json:"driver_name"`
		Speed      float64 `json:"speed"`
		SpeedLimit float64 `json:"speed_limit"`
		Location   string  `json:"location"`
		Timestamp  string  `json:"timestamp"`
	}

	query := config.DB.Table("alerts").
		Select(`
			cd.name as device_name,
			d.name as driver_name,
			alerts.speed,
			80.0 as speed_limit,
			'Unknown Location' as location,
			DATE_FORMAT(alerts.created_at, '%Y-%m-%d %H:%i:%s') as timestamp
		`).
		Joins("LEFT JOIN client_devices cd ON alerts.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON cd.driver_id = d.id").
		Where("cd.client_id = ? AND alerts.alert_type = 'speeding'", filters.ClientId)

	// Apply date filters
	if filters.StartDate != nil {
		query = query.Where("alerts.created_at >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("alerts.created_at <= ?", filters.EndDate)
	}

	// Get total count
	var totalRecords int64
	countQuery := config.DB.Table("alerts").
		Joins("LEFT JOIN client_devices cd ON alerts.client_device_id = cd.id").
		Where("cd.client_id = ? AND alerts.alert_type = 'speeding'", filters.ClientId)
	if filters.StartDate != nil {
		countQuery = countQuery.Where("alerts.created_at >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		countQuery = countQuery.Where("alerts.created_at <= ?", filters.EndDate)
	}
	countQuery.Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	err := query.Find(&speedingExceptions).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch speeding exception data: %v", err)
	}

	// Convert to simple DTO
	var dtoData []map[string]interface{}
	var totalSpeedViolations int
	for _, se := range speedingExceptions {
		dtoData = append(dtoData, map[string]interface{}{
			"device_name": se.DeviceName,
			"driver_name": se.DriverName,
			"speed":       se.Speed,
			"speed_limit": se.SpeedLimit,
			"location":    se.Location,
			"timestamp":   se.Timestamp,
		})
		totalSpeedViolations++
	}

	summary := map[string]interface{}{
		"total_speeding_violations": totalSpeedViolations,
		"devices_with_violations":   len(dtoData),
	}

	return dtoData, summary, int(totalRecords), len(dtoData), nil
}

// generateVehicleOnlineSummaryReport generates vehicle online summary
func (s *ReportService) generateVehicleOnlineSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Simple implementation - return basic vehicle online status data
	var vehicles []struct {
		DeviceName   string `json:"device_name"`
		PlateNumber  string `json:"plate_number"`
		Status       string `json:"status"`
		LastSeen     string `json:"last_seen"`
		OnlineHours  int    `json:"online_hours"`
		OfflineHours int    `json:"offline_hours"`
	}

	query := config.DB.Table("client_devices cd").
		Select(`
			cd.name as device_name,
			cd.plate_number,
			CASE 
				WHEN last_gps.gps_timestamp > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 'Online'
				WHEN last_gps.gps_timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'Recent'
				ELSE 'Offline'
			END as status,
			COALESCE(DATE_FORMAT(last_gps.gps_timestamp, '%Y-%m-%d %H:%i:%s'), 'Never') as last_seen,
			0 as online_hours,
			0 as offline_hours
		`).
		Joins(`
			LEFT JOIN (
				SELECT g1.* 
				FROM gps_data g1
				INNER JOIN (
					SELECT client_device_id, MAX(gps_timestamp) as max_timestamp
					FROM gps_data
					GROUP BY client_device_id
				) g2 ON g1.client_device_id = g2.client_device_id AND g1.gps_timestamp = g2.max_timestamp
			) last_gps ON cd.id = last_gps.client_device_id
		`).
		Where("cd.client_id = ?", filters.ClientId)

	// Get total count
	var totalRecords int64
	config.DB.Table("client_devices").Where("client_id = ?", filters.ClientId).Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	err := query.Find(&vehicles).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch vehicle online data: %v", err)
	}

	// Convert to simple DTO
	var dtoData []map[string]interface{}
	var onlineVehicles, offlineVehicles int
	for _, v := range vehicles {
		dtoData = append(dtoData, map[string]interface{}{
			"device_name":   v.DeviceName,
			"plate_number":  v.PlateNumber,
			"status":        v.Status,
			"last_seen":     v.LastSeen,
			"online_hours":  v.OnlineHours,
			"offline_hours": v.OfflineHours,
		})
		if v.Status == "Online" {
			onlineVehicles++
		} else {
			offlineVehicles++
		}
	}

	summary := map[string]interface{}{
		"total_vehicles":   len(dtoData),
		"online_vehicles":  onlineVehicles,
		"offline_vehicles": offlineVehicles,
	}

	return dtoData, summary, int(totalRecords), len(dtoData), nil
}

// generateCargoDaySummaryFuelReport generates cargo day summary fuel report
func (s *ReportService) generateCargoDaySummaryFuelReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Simple implementation - return basic cargo fuel data
	var cargoFuel []struct {
		Date         string  `json:"date"`
		DeviceName   string  `json:"device_name"`
		PlateNumber  string  `json:"plate_number"`
		FuelConsumed float64 `json:"fuel_consumed"`
		Distance     float64 `json:"distance"`
		Efficiency   float64 `json:"efficiency"`
	}

	query := config.DB.Table("trips t").
		Select(`
			DATE(t.start_time) as date,
			cd.name as device_name,
			cd.plate_number,
			0.0 as fuel_consumed,
			COALESCE(t.distance, 0) as distance,
			0.0 as efficiency
		`).
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Where("cd.client_id = ?", filters.ClientId)

	// Apply date filters
	if filters.StartDate != nil {
		query = query.Where("t.start_time >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("t.start_time <= ?", filters.EndDate)
	}

	// Get total count
	var totalRecords int64
	countQuery := config.DB.Table("trips t").
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Where("cd.client_id = ?", filters.ClientId)
	if filters.StartDate != nil {
		countQuery = countQuery.Where("t.start_time >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		countQuery = countQuery.Where("t.start_time <= ?", filters.EndDate)
	}
	countQuery.Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	err := query.Find(&cargoFuel).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch cargo fuel data: %v", err)
	}

	// Convert to simple DTO
	var dtoData []map[string]interface{}
	var totalFuel, totalDistance float64
	for _, cf := range cargoFuel {
		dtoData = append(dtoData, map[string]interface{}{
			"date":          cf.Date,
			"device_name":   cf.DeviceName,
			"plate_number":  cf.PlateNumber,
			"fuel_consumed": cf.FuelConsumed,
			"distance":      cf.Distance,
			"efficiency":    cf.Efficiency,
		})
		totalFuel += cf.FuelConsumed
		totalDistance += cf.Distance
	}

	summary := map[string]interface{}{
		"total_records":  len(dtoData),
		"total_fuel":     totalFuel,
		"total_distance": totalDistance,
		"avg_efficiency": totalDistance / totalFuel,
	}

	return dtoData, summary, int(totalRecords), len(dtoData), nil
}

// generateTripDetailDeltaReport generates trip detail with delta analysis
func (s *ReportService) generateTripDetailDeltaReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Get trip data with delta analysis
	var trips []struct {
		DeviceName      string     `json:"device_name"`
		PlateNumber     string     `json:"plate_number"`
		DriverName      *string    `json:"driver_name"`
		TripID          uint       `json:"trip_id"`
		StartTime       *time.Time `json:"start_time"`
		EndTime         *time.Time `json:"end_time"`
		Duration        *float64   `json:"duration"`
		Distance        *float64   `json:"distance"`
		AvgSpeed        *float64   `json:"avg_speed"`
		MaxSpeed        *float64   `json:"max_speed"`
		StartLocation   string     `json:"start_location"`
		EndLocation     string     `json:"end_location"`
		DeltaDistance   float64    `json:"delta_distance"`
		DeltaDuration   float64    `json:"delta_duration"`
		DeltaEfficiency float64    `json:"delta_efficiency"`
		Improvement     string     `json:"improvement"`
		Trend           string     `json:"trend"`
	}

	query := `
		SELECT
			cd.name as device_name,
			cd.plate_number,
			d.name as driver_name,
			t.id as trip_id,
			t.start_time,
			t.end_time,
			t.duration,
			t.distance,
			t.avg_speed,
			t.max_speed,
			'Unknown' as start_location,
			'Unknown' as end_location,
			COALESCE(t.distance - LAG(t.distance) OVER (PARTITION BY t.client_device_id ORDER BY t.start_time), 0) as delta_distance,
			COALESCE(t.duration - LAG(t.duration) OVER (PARTITION BY t.client_device_id ORDER BY t.start_time), 0) as delta_duration,
			COALESCE((t.distance / NULLIF(t.duration, 0)) - LAG(t.distance / NULLIF(t.duration, 0)) OVER (PARTITION BY t.client_device_id ORDER BY t.start_time), 0) as delta_efficiency
		FROM trips t
		LEFT JOIN client_devices cd ON t.client_device_id = cd.id
		LEFT JOIN drivers d ON cd.driver_id = d.id
		WHERE cd.client_id = ?
	`

	args := []interface{}{filters.ClientId}

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query += " AND t.client_device_id IN (?)"
		args = append(args, filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query += " AND cd.driver_id IN (?)"
		args = append(args, filters.DriverIds)
	}
	if filters.StartDate != nil {
		query += " AND t.start_time >= ?"
		args = append(args, *filters.StartDate)
	}
	if filters.EndDate != nil {
		query += " AND t.end_time <= ?"
		args = append(args, *filters.EndDate)
	}

	// Get total count
	var totalRecords int64
	countQuery := `
		SELECT COUNT(*) FROM trips t
		LEFT JOIN client_devices cd ON t.client_device_id = cd.id
		WHERE cd.client_id = ?
	`
	config.DB.Raw(countQuery, filters.ClientId).Scan(&totalRecords)

	query += " ORDER BY t.start_time DESC"

	// Apply pagination
	if filters.PerPage > 0 {
		query += " LIMIT ?"
		args = append(args, filters.PerPage)
		if filters.Page > 0 {
			query += " OFFSET ?"
			args = append(args, (filters.Page-1)*filters.PerPage)
		}
	}

	// Execute query
	err := config.DB.Raw(query, args...).Scan(&trips).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch trip detail delta data: %v", err)
	}

	// Convert to DTO
	var dtoData []models.TripDetailDeltaRow
	var totalDeltaDistance, totalDeltaDuration, totalDeltaEfficiency float64
	var improvedTrips, declinedTrips int

	for _, trip := range trips {
		driverName := ""
		if trip.DriverName != nil {
			driverName = *trip.DriverName
		}

		startTime := "Unknown"
		if trip.StartTime != nil {
			startTime = trip.StartTime.Format("2006-01-02 15:04:05")
		}

		endTime := "Unknown"
		if trip.EndTime != nil {
			endTime = trip.EndTime.Format("2006-01-02 15:04:05")
		}

		duration := 0.0
		if trip.Duration != nil {
			duration = *trip.Duration
		}

		distance := 0.0
		if trip.Distance != nil {
			distance = *trip.Distance
		}

		avgSpeed := 0.0
		if trip.AvgSpeed != nil {
			avgSpeed = *trip.AvgSpeed
		}

		maxSpeed := 0.0
		if trip.MaxSpeed != nil {
			maxSpeed = *trip.MaxSpeed
		}

		// Determine improvement and trend
		improvement := "No Change"
		trend := "Stable"

		if trip.DeltaEfficiency > 0.1 {
			improvement = "Improved"
			trend = "Upward"
			improvedTrips++
		} else if trip.DeltaEfficiency < -0.1 {
			improvement = "Declined"
			trend = "Downward"
			declinedTrips++
		}

		totalDeltaDistance += trip.DeltaDistance
		totalDeltaDuration += trip.DeltaDuration
		totalDeltaEfficiency += trip.DeltaEfficiency

		dtoRow := models.TripDetailDeltaRow{
			DeviceName:      trip.DeviceName,
			PlateNumber:     trip.PlateNumber,
			DriverName:      driverName,
			TripID:          trip.TripID,
			StartTime:       startTime,
			EndTime:         endTime,
			Duration:        fmt.Sprintf("%.2f", duration),
			Distance:        distance,
			AvgSpeed:        avgSpeed,
			MaxSpeed:        maxSpeed,
			StartLocation:   trip.StartLocation,
			EndLocation:     trip.EndLocation,
			DistanceDelta:   trip.DeltaDistance,
			DeltaPercentage: trip.DeltaEfficiency * 100,
			Improvement:     improvement,
			Trend:           trend,
		}
		dtoData = append(dtoData, dtoRow)
	}

	// Calculate summary statistics
	avgDeltaEfficiency := 0.0
	if len(dtoData) > 0 {
		avgDeltaEfficiency = totalDeltaEfficiency / float64(len(dtoData))
	}

	summary := models.TripDetailDeltaStats{
		TotalTrips:     len(dtoData),
		ImprovingTrips: improvedTrips,
		DecliningTrips: declinedTrips,
		AvgDelta:       avgDeltaEfficiency,
		OverallTrend:   "Stable",
	}

	// Create DTO
	dto := &models.TripDetailDeltaDTO{
		Headers: []string{
			"Device Name", "Plate Number", "Driver Name", "Trip ID", "Start Time", "End Time",
			"Duration (hrs)", "Distance (km)", "Avg Speed (km/h)", "Max Speed (km/h)",
			"Start Location", "End Location", "Distance Delta (km)", "Delta (%)", "Improvement", "Trend",
		},
		Data:    dtoData,
		Summary: summary,
	}

	return dto, summary, int(totalRecords), len(dtoData), nil
}

// generatePositionLogDriverReport generates driver-specific GPS position data
func (s *ReportService) generatePositionLogDriverReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Get GPS data with enhanced driver information
	var gpsData []struct {
		Timestamp      *time.Time `json:"timestamp"`
		DeviceName     string     `json:"device_name"`
		PlateNumber    string     `json:"plate_number"`
		DriverName     *string    `json:"driver_name"`
		Latitude       float64    `json:"latitude"`
		Longitude      float64    `json:"longitude"`
		LocationName   string     `json:"location_name"`
		Speed          *float64   `json:"speed"`
		Direction      *string    `json:"direction"`
		IgnitionStatus *bool      `json:"ignition_status"`
		TripID         *uint      `json:"trip_id"`
		DriverStatus   string     `json:"driver_status"`
	}

	query := `
		SELECT
			g.gps_timestamp as timestamp,
			cd.name as device_name,
			cd.plate_number,
			d.name as driver_name,
			g.latitude,
			g.longitude,
			COALESCE(g.location_name, 'Unknown Location') as location_name,
			g.speed,
			g.direction,
			g.ignition_status,
			g.trip_id,
			CASE 
				WHEN g.trip_id IS NOT NULL THEN 'On Trip'
				WHEN g.ignition_status = 1 THEN 'Idle'
				ELSE 'Offline'
			END as driver_status
		FROM gps_data g
		LEFT JOIN client_devices cd ON g.client_device_id = cd.id
		LEFT JOIN drivers d ON cd.driver_id = d.id
		WHERE cd.client_id = ?
	`

	args := []interface{}{filters.ClientId}

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query += " AND g.client_device_id IN (?)"
		args = append(args, filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query += " AND cd.driver_id IN (?)"
		args = append(args, filters.DriverIds)
	}
	if filters.StartDate != nil {
		query += " AND g.gps_timestamp >= ?"
		args = append(args, *filters.StartDate)
	}
	if filters.EndDate != nil {
		query += " AND g.gps_timestamp <= ?"
		args = append(args, *filters.EndDate)
	}

	// Get total count
	var totalRecords int64
	countQuery := `
		SELECT COUNT(*) FROM gps_data g
		LEFT JOIN client_devices cd ON g.client_device_id = cd.id
		WHERE cd.client_id = ?
	`
	config.DB.Raw(countQuery, filters.ClientId).Scan(&totalRecords)

	query += " ORDER BY g.gps_timestamp DESC"

	// Apply pagination
	if filters.PerPage > 0 {
		query += " LIMIT ?"
		args = append(args, filters.PerPage)
		if filters.Page > 0 {
			query += " OFFSET ?"
			args = append(args, (filters.Page-1)*filters.PerPage)
		}
	}

	// Execute query
	err := config.DB.Raw(query, args...).Scan(&gpsData).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch position log driver data: %v", err)
	}

	// Convert to DTO
	var dtoData []models.PositionLogDriverRow
	var totalSpeed float64
	var onTripCount, idleCount, offlineCount int

	for _, item := range gpsData {
		timestamp := "Unknown"
		if item.Timestamp != nil {
			timestamp = item.Timestamp.Format("2006-01-02 15:04:05")
		}

		driverName := ""
		if item.DriverName != nil {
			driverName = *item.DriverName
		}

		speed := 0.0
		if item.Speed != nil {
			speed = *item.Speed
			totalSpeed += speed
		}

		direction := "Unknown"
		if item.Direction != nil {
			direction = *item.Direction
		}

		engineStatus := "Off"
		if item.IgnitionStatus != nil && *item.IgnitionStatus {
			engineStatus = "On"
		}

		currentTrip := "No Trip"
		if item.TripID != nil {
			currentTrip = fmt.Sprintf("Trip %d", *item.TripID)
			onTripCount++
		} else if item.IgnitionStatus != nil && *item.IgnitionStatus {
			idleCount++
		} else {
			offlineCount++
		}

		dtoRow := models.PositionLogDriverRow{
			Timestamp:    timestamp,
			DriverName:   driverName,
			DeviceName:   item.DeviceName,
			PlateNumber:  item.PlateNumber,
			Latitude:     item.Latitude,
			Longitude:    item.Longitude,
			LocationName: item.LocationName,
			Speed:        speed,
			Direction:    direction,
			EngineStatus: engineStatus,
			CurrentTrip:  currentTrip,
			DriverStatus: item.DriverStatus,
		}
		dtoData = append(dtoData, dtoRow)
	}

	// Calculate summary statistics
	avgSpeed := 0.0
	if len(dtoData) > 0 {
		avgSpeed = totalSpeed / float64(len(dtoData))
	}

	summary := models.PositionLogDriverStats{
		TotalRecords:  len(dtoData),
		OnTripDrivers: onTripCount,
		IdleDrivers:   idleCount,
		AvgSpeed:      avgSpeed,
	}

	// Create DTO
	dto := &models.PositionLogDriverDTO{
		Headers: []string{
			"Timestamp", "Driver Name", "Device Name", "Plate Number", "Latitude", "Longitude",
			"Location Name", "Speed (km/h)", "Direction", "Engine Status", "Current Trip", "Driver Status",
		},
		Data:    dtoData,
		Summary: summary,
	}

	return dto, summary, int(totalRecords), len(dtoData), nil
}

// generateDoorDetailReport generates door open/close events with security analysis
func (s *ReportService) generateDoorDetailReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Get door events from driving behavior events
	var doorEvents []struct {
		DeviceName    string    `json:"device_name"`
		PlateNumber   string    `json:"plate_number"`
		DriverName    *string   `json:"driver_name"`
		EventType     string    `json:"event_type"`
		Timestamp     time.Time `json:"timestamp"`
		Latitude      float64   `json:"latitude"`
		Longitude     float64   `json:"longitude"`
		LocationName  *string   `json:"location_name"`
		Duration      *int      `json:"duration"`
		Severity      *float64  `json:"severity"`
		SecurityLevel string    `json:"security_level"`
		AccessType    string    `json:"access_type"`
		Notes         string    `json:"notes"`
	}

	query := config.DB.Table("driving_behavior_events dbe").
		Select(`
			cd.name as device_name,
			cd.plate_number,
			d.name as driver_name,
			dbe.event_type,
			dbe.timestamp,
			dbe.latitude,
			dbe.longitude,
			dbe.location_name,
			dbe.duration,
			dbe.severity,
			CASE 
				WHEN dbe.event_type = 'door_open' AND dbe.severity > 7 THEN 'High Security Risk'
				WHEN dbe.event_type = 'door_open' AND dbe.severity > 4 THEN 'Medium Security Risk'
				WHEN dbe.event_type = 'door_open' THEN 'Low Security Risk'
				ELSE 'Normal Operation'
			END as security_level,
			CASE 
				WHEN dbe.event_type = 'door_open' THEN 'Unauthorized Access'
				WHEN dbe.event_type = 'door_close' THEN 'Secure Closure'
				ELSE 'Unknown'
			END as access_type,
			CASE 
				WHEN dbe.event_type = 'door_open' AND dbe.severity > 7 THEN 'Critical security breach detected'
				WHEN dbe.event_type = 'door_open' AND dbe.severity > 4 THEN 'Suspicious door activity'
				WHEN dbe.event_type = 'door_open' THEN 'Normal door operation'
				ELSE 'Door secured'
			END as notes
		`).
		Joins("LEFT JOIN client_devices cd ON dbe.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON dbe.driver_id = d.id").
		Where("cd.client_id = ?", filters.ClientId).
		Where("dbe.event_type IN (?)", []string{"door_open", "door_close"})

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("dbe.timestamp >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("dbe.timestamp <= ?", filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("dbe.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("dbe.driver_id IN ?", filters.DriverIds)
	}
	if len(filters.EventTypes) > 0 {
		query = query.Where("dbe.event_type IN ?", filters.EventTypes)
	}

	// Get total count
	var totalRecords int64
	config.DB.Table("driving_behavior_events dbe").
		Joins("LEFT JOIN client_devices cd ON dbe.client_device_id = cd.id").
		Where("cd.client_id = ?", filters.ClientId).
		Where("dbe.event_type IN (?)", []string{"door_open", "door_close"}).
		Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	// Execute query
	err := query.Order("dbe.timestamp DESC").Find(&doorEvents).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch door detail data: %v", err)
	}

	// Convert to DTO
	var dtoData []models.DoorDetailRow
	var totalDoorOpens, totalDoorCloses int
	var totalSecurityIncidents int
	var avgSeverity float64

	for _, event := range doorEvents {
		driverName := ""
		if event.DriverName != nil {
			driverName = *event.DriverName
		}

		locationName := "Unknown Location"
		if event.LocationName != nil {
			locationName = *event.LocationName
		}

		duration := ""
		if event.Duration != nil {
			duration = fmt.Sprintf("%d seconds", *event.Duration)
		}

		severity := 0.0
		if event.Severity != nil {
			severity = *event.Severity
		}

		// Count events
		if event.EventType == "door_open" {
			totalDoorOpens++
			if severity > 4 {
				totalSecurityIncidents++
			}
		} else if event.EventType == "door_close" {
			totalDoorCloses++
		}

		avgSeverity += severity

		dtoRow := models.DoorDetailRow{
			DeviceName:    event.DeviceName,
			PlateNumber:   event.PlateNumber,
			DriverName:    driverName,
			EventType:     event.EventType,
			Timestamp:     event.Timestamp.Format("2006-01-02 15:04:05"),
			Location:      locationName,
			Coordinates:   fmt.Sprintf("%.6f, %.6f", event.Latitude, event.Longitude),
			Duration:      duration,
			Severity:      severity,
			SecurityLevel: event.SecurityLevel,
			AccessType:    event.AccessType,
			Notes:         event.Notes,
		}
		dtoData = append(dtoData, dtoRow)
	}

	// Calculate summary statistics
	if len(dtoData) > 0 {
		avgSeverity = avgSeverity / float64(len(dtoData))
	}

	summary := models.DoorDetailSummary{
		TotalEvents:       len(dtoData),
		TotalDoorOpens:    totalDoorOpens,
		TotalDoorCloses:   totalDoorCloses,
		SecurityIncidents: totalSecurityIncidents,
		AvgSeverity:       avgSeverity,
		SecurityRiskLevel: "Low",
		Recommendations:   []string{},
	}

	// Determine security risk level and recommendations
	if totalSecurityIncidents > 10 {
		summary.SecurityRiskLevel = "Critical"
		summary.Recommendations = append(summary.Recommendations, "Implement immediate security measures")
		summary.Recommendations = append(summary.Recommendations, "Review access protocols")
	} else if totalSecurityIncidents > 5 {
		summary.SecurityRiskLevel = "High"
		summary.Recommendations = append(summary.Recommendations, "Enhance door security monitoring")
		summary.Recommendations = append(summary.Recommendations, "Implement access controls")
	} else if totalSecurityIncidents > 0 {
		summary.SecurityRiskLevel = "Medium"
		summary.Recommendations = append(summary.Recommendations, "Monitor door access patterns")
	} else {
		summary.SecurityRiskLevel = "Low"
		summary.Recommendations = append(summary.Recommendations, "Maintain current security protocols")
	}

	// Create DTO
	dto := &models.DoorDetailDTO{
		Headers: []string{
			"Device Name", "Plate Number", "Driver Name", "Event Type", "Timestamp",
			"Location", "Coordinates", "Duration", "Severity", "Security Level",
			"Access Type", "Notes",
		},
		Data:    dtoData,
		Summary: summary,
	}

	return dto, summary, int(totalRecords), len(dtoData), nil
}

// generateTripMileageReport generates trip mileage analysis
func (s *ReportService) generateTripMileageReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Get trip mileage data
	var trips []struct {
		TripID            uint       `json:"trip_id"`
		DeviceName        string     `json:"device_name"`
		PlateNumber       string     `json:"plate_number"`
		DriverName        *string    `json:"driver_name"`
		StartTime         *time.Time `json:"start_time"`
		EndTime           *time.Time `json:"end_time"`
		Duration          *float64   `json:"duration"`
		Distance          *float64   `json:"distance"`
		AvgSpeed          *float64   `json:"avg_speed"`
		MaxSpeed          *float64   `json:"max_speed"`
		StartLocation     string     `json:"start_location"`
		EndLocation       string     `json:"end_location"`
		MileageEfficiency float64    `json:"mileage_efficiency"`
		FuelConsumed      float64    `json:"fuel_consumed"`
		CostPerKm         float64    `json:"cost_per_km"`
		Efficiency        float64    `json:"efficiency"`
	}

	query := `
		SELECT
			t.id as trip_id,
			cd.name as device_name,
			cd.plate_number,
			d.name as driver_name,
			t.start_time,
			t.end_time,
			t.duration,
			t.distance,
			t.avg_speed,
			t.max_speed,
			'Unknown' as start_location,
			'Unknown' as end_location,
			COALESCE(t.distance / NULLIF(t.duration, 0), 0) as mileage_efficiency,
			COALESCE(t.distance * 0.1, 0) as fuel_consumed,
			COALESCE(t.distance * 0.15, 0) as cost_per_km,
			COALESCE(t.distance / NULLIF(t.duration, 0), 0) as efficiency
		FROM trips t
		LEFT JOIN client_devices cd ON t.client_device_id = cd.id
		LEFT JOIN drivers d ON cd.driver_id = d.id
		WHERE cd.client_id = ?
	`

	args := []interface{}{filters.ClientId}

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query += " AND t.client_device_id IN (?)"
		args = append(args, filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query += " AND cd.driver_id IN (?)"
		args = append(args, filters.DriverIds)
	}
	if filters.StartDate != nil {
		query += " AND t.start_time >= ?"
		args = append(args, *filters.StartDate)
	}
	if filters.EndDate != nil {
		query += " AND t.end_time <= ?"
		args = append(args, *filters.EndDate)
	}
	if filters.MinDistance != nil {
		query += " AND t.distance >= ?"
		args = append(args, *filters.MinDistance)
	}

	// Get total count
	var totalRecords int64
	countQuery := `
		SELECT COUNT(*) FROM trips t
		LEFT JOIN client_devices cd ON t.client_device_id = cd.id
		WHERE cd.client_id = ?
	`
	config.DB.Raw(countQuery, filters.ClientId).Scan(&totalRecords)

	query += " ORDER BY t.start_time DESC"

	// Apply pagination
	if filters.PerPage > 0 {
		query += " LIMIT ?"
		args = append(args, filters.PerPage)
		if filters.Page > 0 {
			query += " OFFSET ?"
			args = append(args, (filters.Page-1)*filters.PerPage)
		}
	}

	// Execute query
	err := config.DB.Raw(query, args...).Scan(&trips).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch trip mileage data: %v", err)
	}

	// Convert to DTO
	var dtoData []models.TripMileageRow
	var totalDistance, totalFuel, totalCost, totalEfficiency float64

	for _, trip := range trips {
		driverName := ""
		if trip.DriverName != nil {
			driverName = *trip.DriverName
		}

		startTime := "Unknown"
		if trip.StartTime != nil {
			startTime = trip.StartTime.Format("2006-01-02 15:04:05")
		}

		endTime := "Unknown"
		if trip.EndTime != nil {
			endTime = trip.EndTime.Format("2006-01-02 15:04:05")
		}

		duration := 0.0
		if trip.Duration != nil {
			duration = *trip.Duration
		}

		distance := 0.0
		if trip.Distance != nil {
			distance = *trip.Distance
			totalDistance += distance
		}

		avgSpeed := 0.0
		if trip.AvgSpeed != nil {
			avgSpeed = *trip.AvgSpeed
		}

		maxSpeed := 0.0
		if trip.MaxSpeed != nil {
			maxSpeed = *trip.MaxSpeed
		}

		totalFuel += trip.FuelConsumed
		totalCost += trip.CostPerKm
		totalEfficiency += trip.Efficiency

		dtoRow := models.TripMileageRow{
			TripID:            trip.TripID,
			DeviceName:        trip.DeviceName,
			PlateNumber:       trip.PlateNumber,
			DriverName:        driverName,
			StartTime:         startTime,
			EndTime:           endTime,
			Duration:          fmt.Sprintf("%.2f", duration),
			Distance:          distance,
			AvgSpeed:          avgSpeed,
			MaxSpeed:          maxSpeed,
			StartLocation:     trip.StartLocation,
			EndLocation:       trip.EndLocation,
			MileageEfficiency: trip.MileageEfficiency,
			FuelConsumed:      trip.FuelConsumed,
			CostPerKm:         trip.CostPerKm,
			Efficiency:        trip.Efficiency,
		}
		dtoData = append(dtoData, dtoRow)
	}

	// Calculate summary statistics
	avgEfficiency := 0.0
	avgCostPerKm := 0.0
	if len(dtoData) > 0 {
		avgEfficiency = totalEfficiency / float64(len(dtoData))
		avgCostPerKm = totalCost / float64(len(dtoData))
	}

	summary := models.TripMileageStats{
		TotalTrips:    len(dtoData),
		TotalDistance: totalDistance,
		TotalFuel:     totalFuel,
		TotalCost:     totalCost,
		AvgEfficiency: avgEfficiency,
		AvgCostPerKm:  avgCostPerKm,
	}

	// Create DTO
	dto := &models.TripMileageDTO{
		Headers: []string{
			"Trip ID", "Device Name", "Plate Number", "Driver Name", "Start Time", "End Time",
			"Duration (hrs)", "Distance (km)", "Avg Speed (km/h)", "Max Speed (km/h)",
			"Start Location", "End Location", "Mileage Efficiency (%)", "Fuel Consumed (L)", "Cost per km", "Efficiency (km/L)",
		},
		Data:    dtoData,
		Summary: summary,
	}

	return dto, summary, int(totalRecords), len(dtoData), nil
}

// generateMaintenanceScheduleReport generates maintenance schedule report
func (s *ReportService) generateMaintenanceScheduleReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Maintenance schedule with upcoming and overdue maintenance
	query := config.DB.Table("client_devices cd").
		Select(`
			cd.id as device_id,
			cd.name as device_name,
			cd.plate_number,
			cd.model,
			cd.make,
			cd.manufacture_year,
			cd.current_mileage,
			cd.service_mileage,
			cd.last_service_date,
			cd.next_service_date,
			cd.service_days,
			CASE 
				WHEN cd.next_service_date IS NOT NULL AND cd.next_service_date <= DATE_ADD(NOW(), INTERVAL 7 DAY) THEN 'Due This Week'
				WHEN cd.next_service_date IS NOT NULL AND cd.next_service_date <= DATE_ADD(NOW(), INTERVAL 30 DAY) THEN 'Due This Month'
				WHEN cd.next_service_date IS NOT NULL AND cd.next_service_date <= DATE_ADD(NOW(), INTERVAL 90 DAY) THEN 'Due Soon'
				WHEN cd.next_service_date IS NOT NULL AND cd.next_service_date > DATE_ADD(NOW(), INTERVAL 90 DAY) THEN 'Scheduled'
				WHEN cd.current_mileage IS NOT NULL AND cd.service_mileage IS NOT NULL AND cd.current_mileage >= cd.service_mileage THEN 'Overdue by Mileage'
				ELSE 'No Schedule'
			END as maintenance_status,
			CASE 
				WHEN cd.next_service_date IS NOT NULL THEN DATEDIFF(cd.next_service_date, NOW())
				ELSE NULL
			END as days_until_service,
			CASE 
				WHEN cd.current_mileage IS NOT NULL AND cd.service_mileage IS NOT NULL THEN cd.service_mileage - cd.current_mileage
				ELSE NULL
			END as mileage_until_service,
			cd.description as vehicle_description
		`).
		Where("cd.status = ?", "active")

	// Apply filters
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("cd.id IN (?)", filters.ClientDeviceIds)
	}

	// Get counts
	var totalCount, filteredCount int64
	config.DB.Model(&models.ClientDevice{}).Where("status = ?", "active").Count(&totalCount)
	query.Count(&filteredCount)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		DeviceId            uint       `json:"device_id"`
		DeviceName          string     `json:"device_name"`
		PlateNumber         string     `json:"plate_number"`
		Model               string     `json:"model"`
		Make                string     `json:"make"`
		ManufactureYear     string     `json:"manufacture_year"`
		CurrentMileage      *float64   `json:"current_mileage"`
		ServiceMileage      *float64   `json:"service_mileage"`
		LastServiceDate     *time.Time `json:"last_service_date"`
		NextServiceDate     *time.Time `json:"next_service_date"`
		ServiceDays         *uint      `json:"service_days"`
		MaintenanceStatus   string     `json:"maintenance_status"`
		DaysUntilService    *int       `json:"days_until_service"`
		MileageUntilService *float64   `json:"mileage_until_service"`
		VehicleDescription  string     `json:"vehicle_description"`
		Priority            string     `json:"priority"`
		EstimatedCost       float64    `json:"estimated_cost"`
		ServiceType         string     `json:"service_type"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate priority, estimated cost, and service type for each vehicle
	for i := range data {
		// Determine priority
		switch data[i].MaintenanceStatus {
		case "Due This Week":
			data[i].Priority = "High"
		case "Due This Month":
			data[i].Priority = "Medium"
		case "Due Soon":
			data[i].Priority = "Low"
		case "Overdue by Mileage":
			data[i].Priority = "Critical"
		default:
			data[i].Priority = "Normal"
		}

		// Estimate service cost based on vehicle type and service type
		baseCost := 500.0 // Base maintenance cost
		if data[i].Model != "" {
			// Adjust cost based on vehicle type
			if strings.Contains(strings.ToLower(data[i].Model), "truck") {
				baseCost = 800.0
			} else if strings.Contains(strings.ToLower(data[i].Model), "bus") {
				baseCost = 1000.0
			} else if strings.Contains(strings.ToLower(data[i].Model), "car") {
				baseCost = 300.0
			}
		}

		// Determine service type
		if data[i].CurrentMileage != nil && data[i].ServiceMileage != nil {
			mileageDiff := *data[i].CurrentMileage - *data[i].ServiceMileage
			if mileageDiff > 10000 {
				data[i].ServiceType = "Major Service"
				baseCost *= 2.5
			} else if mileageDiff > 5000 {
				data[i].ServiceType = "Minor Service"
				baseCost *= 1.5
			} else {
				data[i].ServiceType = "Regular Service"
			}
		} else {
			data[i].ServiceType = "Scheduled Service"
		}

		data[i].EstimatedCost = baseCost
	}

	// Summary statistics
	summary := struct {
		TotalVehicles        int     `json:"total_vehicles"`
		DueThisWeek          int     `json:"due_this_week"`
		DueThisMonth         int     `json:"due_this_month"`
		Overdue              int     `json:"overdue"`
		TotalEstimatedCost   float64 `json:"total_estimated_cost"`
		AvgDaysUntilService  float64 `json:"avg_days_until_service"`
		CriticalVehicles     int     `json:"critical_vehicles"`
		HighPriorityVehicles int     `json:"high_priority_vehicles"`
	}{
		TotalVehicles: len(data),
	}

	if len(data) > 0 {
		var totalCost float64
		var totalDays int
		var dayCount int

		for _, d := range data {
			totalCost += d.EstimatedCost

			switch d.MaintenanceStatus {
			case "Due This Week":
				summary.DueThisWeek++
				summary.HighPriorityVehicles++
			case "Due This Month":
				summary.DueThisMonth++
			case "Overdue by Mileage":
				summary.Overdue++
				summary.CriticalVehicles++
			}

			if d.DaysUntilService != nil {
				totalDays += *d.DaysUntilService
				dayCount++
			}
		}

		summary.TotalEstimatedCost = totalCost
		if dayCount > 0 {
			summary.AvgDaysUntilService = float64(totalDays) / float64(dayCount)
		}
	}

	return data, summary, int(totalCount), len(data), nil
}
