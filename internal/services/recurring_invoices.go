package services

import (
	"bytes"
	"fmt"
	"log"
	"strconv"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/templates"
	"yotracker/internal/utils"

	"github.com/SebastiaanKlippert/go-wkhtmltopdf"
	"gorm.io/gorm"
)

type RecurringInvoiceService struct{}

func NewRecurringInvoiceService() *RecurringInvoiceService {
	return &RecurringInvoiceService{}
}

// ProcessDailyRecurringInvoices processes all recurring invoices that are due for generation
func (r *RecurringInvoiceService) ProcessDailyRecurringInvoices() error {
	// Get the generate_invoice_before_days setting
	generateBeforeDays := models.GetSetting("generate_invoice_before_days")
	if generateBeforeDays == "" {
		generateBeforeDays = "7" // default value
	}

	beforeDaysInt, err := strconv.Atoi(generateBeforeDays)
	if err != nil {
		return fmt.Errorf("invalid generate_invoice_before_days setting: %v", err)
	}

	// Calculate the target date (today + generate_invoice_before_days)
	targetDate := time.Now().AddDate(0, 0, beforeDaysInt)

	log.Printf("Processing recurring invoices due on: %s, based on generate_invoice_before_days setting of %s days", targetDate.Format("2006-01-02"), generateBeforeDays)

	// Find all recurring invoices that need to be processed. We do not generate a new one if invoice balance is greater than 0
	var invoices []models.Invoice
	err = config.DB.Where("recurring = ? AND recur_next_date <= ? AND recur_next_date IS NOT NULL and status = 'paid'", true, targetDate).
		Preload("Client").
		Preload("InvoiceItems").
		Preload("Currency").
		Preload("PaymentType").
		Find(&invoices).Error

	if err != nil {
		return fmt.Errorf("failed to fetch recurring invoices: %v", err)
	}

	log.Printf("Found %d recurring invoices to process", len(invoices))

	for _, invoice := range invoices {
		err := r.processRecurringInvoice(invoice)
		if err != nil {
			log.Printf("Failed to process recurring invoice ID %d: %v", invoice.Id, err)
			continue
		}
		log.Printf("Successfully processed recurring invoice ID %d", invoice.Id)
	}

	return nil
}

// processRecurringInvoice processes a single recurring invoice
func (r *RecurringInvoiceService) processRecurringInvoice(originalInvoice models.Invoice) error {
	// Get invoice_due_after_days setting
	dueDays := models.GetSetting("invoice_due_after_days")
	if dueDays == "" {
		dueDays = "15" // default value
	}

	dueDaysInt, err := strconv.Atoi(dueDays)
	if err != nil {
		return fmt.Errorf("invalid invoice_due_after_days setting: %v", err)
	}

	// Create new invoice based on the original
	dueDate := originalInvoice.RecurNextDate.AddDate(0, 0, dueDaysInt)

	newInvoice := models.Invoice{
		ClientId:              originalInvoice.ClientId,
		CreatedById:           originalInvoice.CreatedById,
		CouponId:              originalInvoice.CouponId,
		TaxRateId:             originalInvoice.TaxRateId,
		PaymentTypeId:         originalInvoice.PaymentTypeId,
		CurrencyId:            originalInvoice.CurrencyId,
		Date:                  originalInvoice.RecurNextDate,
		DueDate:               &dueDate,
		Amount:                originalInvoice.Amount,
		Discount:              originalInvoice.Discount,
		DiscountType:          originalInvoice.DiscountType,
		Status:                "draft",
		Balance:               originalInvoice.Amount,
		Xrate:                 originalInvoice.Xrate,
		DiscountAmount:        originalInvoice.DiscountAmount,
		CouponDiscountAmount:  originalInvoice.CouponDiscountAmount,
		TaxAmount:             originalInvoice.TaxAmount,
		Subtotal:              originalInvoice.Subtotal,
		BaseCurrencyTaxAmount: originalInvoice.BaseCurrencyTaxAmount,
		BaseCurrencyAmount:    originalInvoice.BaseCurrencyAmount,
		BaseCurrencySubtotal:  originalInvoice.BaseCurrencySubtotal,
		BaseCurrencyBalance:   originalInvoice.BaseCurrencyAmount,
		AdminNotes:            originalInvoice.AdminNotes,
		Terms:                 originalInvoice.Terms,
		Description:           originalInvoice.Description,
		IsSubscription:        originalInvoice.IsSubscription,
	}

	// Start transaction
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create the new invoice
	if err := tx.Create(&newInvoice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create new invoice: %v", err)
	}

	// Generate reference for the new invoice
	reference := utils.GenerateReference(strconv.Itoa(int(newInvoice.Id)))
	newInvoice.Reference = &reference
	if err := tx.Save(&newInvoice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update invoice reference: %v", err)
	}

	// copy existing items
	err = r.copyInvoiceItems(tx, newInvoice.Id, originalInvoice.InvoiceItems)

	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create invoice items: %v", err)
	}

	// Calculate next recurring date by adding RecurFrequency days
	if originalInvoice.RecurFrequency == nil {
		tx.Rollback()
		return fmt.Errorf("recurring frequency not set for invoice ID %d", originalInvoice.Id)
	}

	nextRecurDate := originalInvoice.RecurNextDate.AddDate(0, 0, int(*originalInvoice.RecurFrequency))

	// Update original invoice's next recurring date
	originalInvoice.RecurNextDate = &nextRecurDate
	if err = tx.Save(&originalInvoice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update original invoice recurring date: %v", err)
	}

	// Update new invoice status (since it's a new invoice with no payments, it should be 'pending')
	newInvoice.Status = "pending"
	if err = tx.Save(&newInvoice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update new invoice status: %v", err)
	}

	// Commit transaction
	if err = tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}
	// Load the complete invoice for email sending
	var completeInvoice models.Invoice
	err = config.DB.Preload("Client").Preload("Client.Country").Preload("InvoiceItems").
		Preload("Currency").Preload("PaymentType").First(&completeInvoice, newInvoice.Id).Error
	if err != nil {
		return fmt.Errorf("failed to load complete invoice: %v", err)
	}
	// Generate PDF and send email
	return r.generatePDFAndSendEmail(completeInvoice)
}

// copyInvoiceItems copies invoice items from the original invoice
func (r *RecurringInvoiceService) copyInvoiceItems(tx *gorm.DB, newInvoiceId uint, originalItems []models.InvoiceItem) error {
	for _, item := range originalItems {
		newItem := models.InvoiceItem{
			InvoiceId:             newInvoiceId,
			ClientDeviceId:        item.ClientDeviceId,
			TaxRateId:             item.TaxRateId,
			Name:                  item.Name,
			Description:           item.Description,
			Quantity:              item.Quantity,
			ItemPosition:          item.ItemPosition,
			UnitCost:              item.UnitCost,
			BaseCurrencyUnitCost:  item.BaseCurrencyUnitCost,
			Discount:              item.Discount,
			DiscountType:          item.DiscountType,
			DiscountAmount:        item.DiscountAmount,
			TaxAmount:             item.TaxAmount,
			BaseCurrencyTaxAmount: item.BaseCurrencyTaxAmount,
			Total:                 item.Total,
		}

		if err := tx.Create(&newItem).Error; err != nil {
			return fmt.Errorf("failed to copy invoice item: %v", err)
		}
	}

	return nil
}

// generatePDFAndSendEmail generates PDF and sends email using existing functionality
func (r *RecurringInvoiceService) generatePDFAndSendEmail(invoice models.Invoice) error {
	// Generate PDF using the same logic as in frontend controller
	tmpl, err := templates.GetInvoiceTemplate()
	if err != nil {
		return fmt.Errorf("template error: %v", err)
	}

	var htmlBuffer bytes.Buffer
	invoiceView := models.ToInvoiceView(invoice)
	err = tmpl.Execute(&htmlBuffer, invoiceView)
	if err != nil {
		return fmt.Errorf("template render error: %v", err)
	}

	// Generate PDF from HTML using wkhtmltopdf
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return fmt.Errorf("PDF generator error: %v", err)
	}

	page := wkhtmltopdf.NewPageReader(&htmlBuffer)
	pdfg.AddPage(page)

	err = pdfg.Create()
	if err != nil {
		return fmt.Errorf("PDF creation failed: %v", err)
	}

	// Send email with PDF attachment using existing helper
	return SendInvoiceEmail(&invoice, pdfg.Bytes())
}
