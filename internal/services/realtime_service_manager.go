package services

import (
	"log"
	"sync"
)

// RealtimeServiceManager manages the singleton real-time trip detection service
type RealtimeServiceManager struct {
	service *RealtimeTripDetectionService
	mu      sync.RWMutex
	started bool
}

var (
	realtimeManager *RealtimeServiceManager
	managerOnce     sync.Once
)

// GetRealtimeServiceManager returns the singleton service manager
func GetRealtimeServiceManager() *RealtimeServiceManager {
	managerOnce.Do(func() {
		realtimeManager = &RealtimeServiceManager{}
	})
	return realtimeManager
}

// GetService returns the real-time trip detection service
func (m *RealtimeServiceManager) GetService() *RealtimeTripDetectionService {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.service
}

// StartService starts the real-time trip detection service
func (m *RealtimeServiceManager) StartService() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.started {
		return nil // Already started
	}

	m.service = NewRealtimeTripDetectionService()
	if err := m.service.Start(); err != nil {
		return err
	}

	m.started = true
	log.Println("Real-time trip detection service started")
	return nil
}

// StopService stops the real-time trip detection service
func (m *RealtimeServiceManager) StopService() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.started || m.service == nil {
		return nil // Not started
	}

	if err := m.service.Stop(); err != nil {
		return err
	}

	m.started = false
	m.service = nil
	log.Println("Real-time trip detection service stopped")
	return nil
}

// IsStarted returns whether the service is started
func (m *RealtimeServiceManager) IsStarted() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.started
}

// GetRealtimeTripDetectionService returns the real-time trip detection service (for backward compatibility)
func GetRealtimeTripDetectionService() *RealtimeTripDetectionService {
	manager := GetRealtimeServiceManager()
	return manager.GetService()
}
