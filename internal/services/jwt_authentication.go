package services

import (
	"errors"
	"fmt"
	"github.com/dgrijalva/jwt-go"
	"os"
	"time"
	"yotracker/internal/models"
)

type Claims struct {
	UserId   uint   `json:"user_id"`
	Email    string `json:"email"`
	UserType string `json:"user_type"`
	jwt.StandardClaims
}

type DeviceShareClaims struct {
	ClientDeviceId uint   `json:"client_device_id"`
	DeviceId       string `json:"device_id"`
	CreatedById    uint   `json:"created_by_id"`
	jwt.StandardClaims
}

func GenerateToken(user *models.User, tokenType string) (string, error) {
	var expirationTime int64
	var subject string
	if tokenType == "access" {
		expirationTime = time.Now().Add(time.Hour * 7 * 24).Unix()
	}
	if tokenType == "refresh" {
		expirationTime = time.Now().Add(time.Hour * 7 * 24).Unix()
		subject = "refresh"
	}
	if tokenType == "password_reset" {
		expirationTime = time.Now().Add(time.Hour * 24).Unix() // 24 hours for password reset
		subject = "password_reset"
	}
	claims := Claims{
		UserId:   user.Id,
		Email:    user.Email,
		UserType: user.UserType,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expirationTime,
			Issuer:    "yotracker",
			Subject:   subject,
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	accessToken, err := token.SignedString([]byte(os.Getenv("APP_KEY")))
	if err != nil {
		return "", err
	}
	return accessToken, nil
}

func GenerateDeviceShareToken(clientDeviceId uint, deviceId string, createdById uint, expiresAt time.Time) (string, error) {
	claims := DeviceShareClaims{
		ClientDeviceId: clientDeviceId,
		DeviceId:       deviceId,
		CreatedById:    createdById,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: expiresAt.Unix(),
			Issuer:    "yotracker",
			Subject:   "device_share",
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	shareToken, err := token.SignedString([]byte(os.Getenv("APP_KEY")))
	if err != nil {
		return "", err
	}
	return shareToken, nil
}
func VerifyToken(tokenString string, tokenType string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("invalid token signing method")
		}
		return []byte(os.Getenv("APP_KEY")), nil
	})
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, errors.New("invalid token")
	}
	// Validate token type based on subject
	if tokenType == "access" && claims.Subject != "" {
		return nil, errors.New("invalid token")
	}
	if tokenType == "refresh" && claims.Subject != "refresh" {
		return nil, errors.New("invalid token")
	}
	if tokenType == "password_reset" && claims.Subject != "password_reset" {
		return nil, errors.New("invalid token")
	}
	if tokenType == "device_share" && claims.Subject != "device_share" {
		return nil, errors.New("invalid token")
	}
	return claims, nil
}

func VerifyDeviceShareToken(tokenString string) (*DeviceShareClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &DeviceShareClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("invalid token signing method")
		}
		return []byte(os.Getenv("APP_KEY")), nil
	})
	if err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(*DeviceShareClaims)
	if !ok || !token.Valid {
		return nil, errors.New("invalid token")
	}

	// Validate token type based on subject
	if claims.Subject != "device_share" {
		return nil, errors.New("invalid token type")
	}
	return claims, nil
}
