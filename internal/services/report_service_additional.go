package services

import (
	"fmt"
	"strings"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

// ===== ADDITIONAL ACTIVE REPORTS IMPLEMENTATION =====

// generateDailyTripSummaryReport generates daily trip summary statistics
func (s *ReportService) generateDailyTripSummaryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	query := config.DB.Table("trips t").
		Select(`
			DATE(t.start_time) as date,
			COUNT(t.id) as total_trips,
			SUM(t.distance) as total_distance,
			SUM(t.duration) as total_duration,
			COUNT(DISTINCT t.client_device_id) as active_vehicles,
			COUNT(DISTINCT t.driver_id) as active_drivers,
			AVG(t.avg_speed) as avg_speed,
			(SUM(t.distance) / COUNT(t.id)) as avg_trip_distance,
			(SUM(t.duration) / COUNT(t.id)) as avg_trip_duration
		`).
		Group("DATE(t.start_time)").
		Order("DATE(t.start_time) DESC")

	query = s.applyTripFilters(query, filters)

	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Date            string  `json:"date"`
		TotalTrips      int     `json:"total_trips"`
		TotalDistance   float64 `json:"total_distance"`
		TotalDuration   int     `json:"total_duration"`
		ActiveVehicles  int     `json:"active_vehicles"`
		ActiveDrivers   int     `json:"active_drivers"`
		AvgSpeed        float64 `json:"avg_speed"`
		AvgTripDistance float64 `json:"avg_trip_distance"`
		AvgTripDuration float64 `json:"avg_trip_duration"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	summary := map[string]interface{}{
		"total_days":           len(data),
		"total_trips":          0,
		"total_distance":       0.0,
		"avg_trips_per_day":    0.0,
		"avg_distance_per_day": 0.0,
	}

	for _, day := range data {
		summary["total_trips"] = summary["total_trips"].(int) + day.TotalTrips
		summary["total_distance"] = summary["total_distance"].(float64) + day.TotalDistance
	}

	if len(data) > 0 {
		summary["avg_trips_per_day"] = float64(summary["total_trips"].(int)) / float64(len(data))
		summary["avg_distance_per_day"] = summary["total_distance"].(float64) / float64(len(data))
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// generateMonthlyMileageReport generates monthly mileage statistics
func (s *ReportService) generateMonthlyMileageReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	query := config.DB.Table("trips t").
		Select(`
			DATE_FORMAT(t.start_time, '%Y-%m') as month,
			COUNT(t.id) as total_trips,
			SUM(t.distance) as total_distance,
			SUM(t.duration) as total_duration,
			COUNT(DISTINCT t.client_device_id) as active_vehicles,
			AVG(t.avg_speed) as avg_speed,
			(SUM(t.distance) / COUNT(t.id)) as avg_trip_distance,
			(SUM(t.duration) / COUNT(t.id)) as avg_trip_duration,
			(SUM(t.distance) * 0.12) as estimated_fuel_consumption,
			(SUM(t.distance) * 0.12 * 1.5) as estimated_fuel_cost
		`).
		Group("DATE_FORMAT(t.start_time, '%Y-%m')").
		Order("DATE_FORMAT(t.start_time, '%Y-%m') DESC")

	query = s.applyTripFilters(query, filters)

	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		Month                    string  `json:"month"`
		TotalTrips               int     `json:"total_trips"`
		TotalDistance            float64 `json:"total_distance"`
		TotalDuration            int     `json:"total_duration"`
		ActiveVehicles           int     `json:"active_vehicles"`
		AvgSpeed                 float64 `json:"avg_speed"`
		AvgTripDistance          float64 `json:"avg_trip_distance"`
		AvgTripDuration          float64 `json:"avg_trip_duration"`
		EstimatedFuelConsumption float64 `json:"estimated_fuel_consumption"`
		EstimatedFuelCost        float64 `json:"estimated_fuel_cost"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	summary := map[string]interface{}{
		"total_months":           len(data),
		"total_distance":         0.0,
		"total_fuel_consumption": 0.0,
		"total_fuel_cost":        0.0,
		"avg_distance_per_month": 0.0,
	}

	for _, month := range data {
		summary["total_distance"] = summary["total_distance"].(float64) + month.TotalDistance
		summary["total_fuel_consumption"] = summary["total_fuel_consumption"].(float64) + month.EstimatedFuelConsumption
		summary["total_fuel_cost"] = summary["total_fuel_cost"].(float64) + month.EstimatedFuelCost
	}

	if len(data) > 0 {
		summary["avg_distance_per_month"] = summary["total_distance"].(float64) / float64(len(data))
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// generateRouteEfficiencyReport generates route efficiency analysis
func (s *ReportService) generateRouteEfficiencyReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	query := config.DB.Table("trips t").
		Select(`
			t.start_location,
			t.end_location,
			COUNT(t.id) as trip_count,
			AVG(t.distance) as avg_distance,
			AVG(t.duration) as avg_duration,
			AVG(t.avg_speed) as avg_speed,
			MIN(t.distance) as min_distance,
			MAX(t.distance) as max_distance,
			(COUNT(t.id) * AVG(t.distance)) as total_distance,
			(COUNT(t.id) * AVG(t.duration)) as total_duration
		`).
		Group("t.start_location, t.end_location").
		Order("COUNT(t.id) DESC")

	query = s.applyTripFilters(query, filters)

	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		StartLocation string  `json:"start_location"`
		EndLocation   string  `json:"end_location"`
		TripCount     int     `json:"trip_count"`
		AvgDistance   float64 `json:"avg_distance"`
		AvgDuration   float64 `json:"avg_duration"`
		AvgSpeed      float64 `json:"avg_speed"`
		MinDistance   float64 `json:"min_distance"`
		MaxDistance   float64 `json:"max_distance"`
		TotalDistance float64 `json:"total_distance"`
		TotalDuration float64 `json:"total_duration"`
		Efficiency    float64 `json:"efficiency"`
		Optimization  string  `json:"optimization"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate efficiency and optimization recommendations
	for i := range data {
		if data[i].AvgDuration > 0 {
			data[i].Efficiency = data[i].AvgDistance / (data[i].AvgDuration / 3600.0)
		}

		// Optimization recommendations
		if data[i].Efficiency < 30 {
			data[i].Optimization = "High - Consider route optimization"
		} else if data[i].Efficiency < 50 {
			data[i].Optimization = "Medium - Review traffic patterns"
		} else {
			data[i].Optimization = "Low - Route is efficient"
		}
	}

	summary := map[string]interface{}{
		"total_routes":        len(data),
		"avg_efficiency":      0.0,
		"optimization_needed": 0,
		"efficient_routes":    0,
	}

	totalEfficiency := 0.0
	for _, route := range data {
		totalEfficiency += route.Efficiency
		if route.Efficiency < 50 {
			summary["optimization_needed"] = summary["optimization_needed"].(int) + 1
		} else {
			summary["efficient_routes"] = summary["efficient_routes"].(int) + 1
		}
	}

	if len(data) > 0 {
		summary["avg_efficiency"] = totalEfficiency / float64(len(data))
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// generateHarshDrivingEventsReport generates harsh driving events analysis
func (s *ReportService) generateHarshDrivingEventsReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	query := config.DB.Table("driving_behavior_events dbe").
		Select(`
			dbe.id,
			dbe.event_type,
			dbe.timestamp,
			dbe.severity,
			COALESCE(dbe.location_name, 'Unknown') as location,
			dbe.speed,
			dbe.acceleration,
			dbe.latitude,
			dbe.longitude,
			d.name as driver_name,
			cd.name as device_name,
			cd.plate_number
		`).
		Joins("LEFT JOIN trips t ON dbe.trip_id = t.id").
		Joins("LEFT JOIN drivers d ON t.driver_id = d.id").
		Joins("LEFT JOIN client_devices cd ON t.client_device_id = cd.id").
		Where("dbe.event_type IN (?, ?, ?)", "harsh_acceleration", "harsh_braking", "harsh_cornering").
		Order("dbe.timestamp DESC")

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("dbe.timestamp >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("dbe.timestamp <= ?", *filters.EndDate)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN (?)", filters.DriverIds)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("cd.id IN (?)", filters.ClientDeviceIds)
	}

	var totalCount, filteredCount int64
	config.DB.Table("driving_behavior_events").Where("event_type IN (?, ?, ?)", "harsh_acceleration", "harsh_braking", "harsh_cornering").Count(&totalCount)
	query.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		ID           uint      `json:"id"`
		EventType    string    `json:"event_type"`
		Timestamp    time.Time `json:"timestamp"`
		Severity     string    `json:"severity"`
		Location     string    `json:"location"`
		Speed        float64   `json:"speed"`
		Acceleration float64   `json:"acceleration"`
		Latitude     float64   `json:"latitude"`
		Longitude    float64   `json:"longitude"`
		DriverName   string    `json:"driver_name"`
		DeviceName   string    `json:"device_name"`
		PlateNumber  string    `json:"plate_number"`
		RiskLevel    string    `json:"risk_level"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate risk levels
	for i := range data {
		switch data[i].Severity {
		case "Critical":
			data[i].RiskLevel = "High"
		case "High":
			data[i].RiskLevel = "Medium"
		default:
			data[i].RiskLevel = "Low"
		}
	}

	summary := map[string]interface{}{
		"total_events":       len(data),
		"harsh_acceleration": 0,
		"harsh_braking":      0,
		"harsh_cornering":    0,
		"high_risk_events":   0,
		"avg_severity":       "Medium",
	}

	for _, event := range data {
		switch event.EventType {
		case "harsh_acceleration":
			summary["harsh_acceleration"] = summary["harsh_acceleration"].(int) + 1
		case "harsh_braking":
			summary["harsh_braking"] = summary["harsh_braking"].(int) + 1
		case "harsh_cornering":
			summary["harsh_cornering"] = summary["harsh_cornering"].(int) + 1
		}
		if event.RiskLevel == "High" {
			summary["high_risk_events"] = summary["high_risk_events"].(int) + 1
		}
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// generateDriverPerformanceRankingReport generates driver performance ranking
func (s *ReportService) generateDriverPerformanceRankingReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	query := config.DB.Table("drivers d").
		Select(`
			d.id as driver_id,
			d.name as driver_name,
			d.driver_license_no,
			COUNT(t.id) as total_trips,
			SUM(t.distance) as total_distance,
			AVG(t.avg_speed) as avg_speed,
			COUNT(CASE WHEN t.avg_speed > 80 THEN 1 END) as speeding_incidents,
			COUNT(DISTINCT dbe.id) as total_violations,
			(COUNT(t.id) * AVG(t.distance)) as performance_score,
			(COUNT(CASE WHEN t.avg_speed > 80 THEN 1 END) / COUNT(t.id) * 100) as speeding_rate,
			(COUNT(DISTINCT dbe.id) / COUNT(t.id) * 100) as violation_rate
		`).
		Joins("LEFT JOIN trips t ON d.id = t.driver_id").
		Joins("LEFT JOIN driving_behavior_events dbe ON t.id = dbe.trip_id").
		Group("d.id").
		Order("(COUNT(t.id) * AVG(t.distance)) DESC")

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("t.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("t.start_time <= ?", *filters.EndDate)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN (?)", filters.DriverIds)
	}

	var totalCount, filteredCount int64
	config.DB.Model(&models.Driver{}).Count(&totalCount)
	query.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		DriverID          uint    `json:"driver_id"`
		DriverName        string  `json:"driver_name"`
		LicenseNumber     string  `json:"license_number"`
		TotalTrips        int     `json:"total_trips"`
		TotalDistance     float64 `json:"total_distance"`
		AvgSpeed          float64 `json:"avg_speed"`
		SpeedingIncidents int     `json:"speeding_incidents"`
		TotalViolations   int     `json:"total_violations"`
		PerformanceScore  float64 `json:"performance_score"`
		SpeedingRate      float64 `json:"speeding_rate"`
		ViolationRate     float64 `json:"violation_rate"`
		Rank              int     `json:"rank"`
		Rating            string  `json:"rating"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate ranks and ratings
	for i := range data {
		data[i].Rank = i + 1

		// Rating based on performance score and violation rate
		if data[i].PerformanceScore > 10000 && data[i].ViolationRate < 5 {
			data[i].Rating = "Excellent"
		} else if data[i].PerformanceScore > 5000 && data[i].ViolationRate < 10 {
			data[i].Rating = "Good"
		} else if data[i].PerformanceScore > 1000 && data[i].ViolationRate < 20 {
			data[i].Rating = "Average"
		} else {
			data[i].Rating = "Needs Improvement"
		}
	}

	summary := map[string]interface{}{
		"total_drivers":     len(data),
		"excellent_drivers": 0,
		"good_drivers":      0,
		"average_drivers":   0,
		"needs_improvement": 0,
		"avg_performance":   0.0,
	}

	totalPerformance := 0.0
	for _, driver := range data {
		totalPerformance += driver.PerformanceScore
		switch driver.Rating {
		case "Excellent":
			summary["excellent_drivers"] = summary["excellent_drivers"].(int) + 1
		case "Good":
			summary["good_drivers"] = summary["good_drivers"].(int) + 1
		case "Average":
			summary["average_drivers"] = summary["average_drivers"].(int) + 1
		case "Needs Improvement":
			summary["needs_improvement"] = summary["needs_improvement"].(int) + 1
		}
	}

	if len(data) > 0 {
		summary["avg_performance"] = totalPerformance / float64(len(data))
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// generateFatigueOvertimeAlertReport generates fatigue and overtime alerts
func (s *ReportService) generateFatigueOvertimeAlertReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	query := config.DB.Table("trips t").
		Select(`
			d.id as driver_id,
			d.name as driver_name,
			DATE(t.start_time) as date,
			COUNT(t.id) as total_trips,
			SUM(t.duration) as total_duration,
			(SUM(t.duration) / 3600.0) as total_hours,
			COUNT(CASE WHEN t.duration > 28800 THEN 1 END) as overtime_trips,
			COUNT(CASE WHEN t.duration > 43200 THEN 1 END) as fatigue_trips,
			MAX(t.duration) as max_trip_duration
		`).
		Joins("LEFT JOIN drivers d ON t.driver_id = d.id").
		Group("d.id, DATE(t.start_time)").
		Having("(SUM(t.duration) / 3600.0) > 8 OR COUNT(CASE WHEN t.duration > 28800 THEN 1 END) > 0 OR COUNT(CASE WHEN t.duration > 43200 THEN 1 END) > 0").
		Order("(SUM(t.duration) / 3600.0) DESC")

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("t.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("t.start_time <= ?", *filters.EndDate)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN (?)", filters.DriverIds)
	}

	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		DriverID        uint    `json:"driver_id"`
		DriverName      string  `json:"driver_name"`
		Date            string  `json:"date"`
		TotalTrips      int     `json:"total_trips"`
		TotalDuration   int     `json:"total_duration"`
		TotalHours      float64 `json:"total_hours"`
		OvertimeTrips   int     `json:"overtime_trips"`
		FatigueTrips    int     `json:"fatigue_trips"`
		MaxTripDuration int     `json:"max_trip_duration"`
		AlertLevel      string  `json:"alert_level"`
		Recommendation  string  `json:"recommendation"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate alert levels and recommendations
	for i := range data {
		if data[i].TotalHours > 12 || data[i].FatigueTrips > 0 {
			data[i].AlertLevel = "Critical"
			data[i].Recommendation = "Immediate rest required"
		} else if data[i].TotalHours > 10 || data[i].OvertimeTrips > 0 {
			data[i].AlertLevel = "High"
			data[i].Recommendation = "Reduce working hours"
		} else if data[i].TotalHours > 8 {
			data[i].AlertLevel = "Medium"
			data[i].Recommendation = "Monitor closely"
		} else {
			data[i].AlertLevel = "Low"
			data[i].Recommendation = "Within limits"
		}
	}

	summary := map[string]interface{}{
		"total_alerts":    len(data),
		"critical_alerts": 0,
		"high_alerts":     0,
		"medium_alerts":   0,
		"low_alerts":      0,
		"total_overtime":  0,
		"total_fatigue":   0,
	}

	for _, alert := range data {
		switch alert.AlertLevel {
		case "Critical":
			summary["critical_alerts"] = summary["critical_alerts"].(int) + 1
		case "High":
			summary["high_alerts"] = summary["high_alerts"].(int) + 1
		case "Medium":
			summary["medium_alerts"] = summary["medium_alerts"].(int) + 1
		case "Low":
			summary["low_alerts"] = summary["low_alerts"].(int) + 1
		}
		summary["total_overtime"] = summary["total_overtime"].(int) + alert.OvertimeTrips
		summary["total_fatigue"] = summary["total_fatigue"].(int) + alert.FatigueTrips
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// generateHoursOfServiceReport generates hours of service compliance report
func (s *ReportService) generateHoursOfServiceReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	query := config.DB.Table("trips t").
		Select(`
			d.id as driver_id,
			d.name as driver_name,
			DATE(t.start_time) as date,
			COUNT(t.id) as total_trips,
			SUM(t.duration) as total_duration,
			(SUM(t.duration) / 3600.0) as total_hours,
			COUNT(CASE WHEN t.duration > 28800 THEN 1 END) as violations,
			MAX(t.duration) as max_trip_duration,
			(SUM(t.duration) / COUNT(t.id)) as avg_trip_duration
		`).
		Joins("LEFT JOIN drivers d ON t.driver_id = d.id").
		Group("d.id, DATE(t.start_time)").
		Order("(SUM(t.duration) / 3600.0) DESC")

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("t.start_time >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("t.start_time <= ?", *filters.EndDate)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN (?)", filters.DriverIds)
	}

	var totalCount, filteredCount int64
	config.DB.Model(&models.Trip{}).Count(&totalCount)
	query.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		DriverID        uint    `json:"driver_id"`
		DriverName      string  `json:"driver_name"`
		Date            string  `json:"date"`
		TotalTrips      int     `json:"total_trips"`
		TotalDuration   int     `json:"total_duration"`
		TotalHours      float64 `json:"total_hours"`
		Violations      int     `json:"violations"`
		MaxTripDuration int     `json:"max_trip_duration"`
		AvgTripDuration float64 `json:"avg_trip_duration"`
		Compliance      string  `json:"compliance"`
		Status          string  `json:"status"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate compliance status
	for i := range data {
		if data[i].TotalHours <= 8 && data[i].Violations == 0 {
			data[i].Compliance = "Compliant"
			data[i].Status = "Good"
		} else if data[i].TotalHours <= 10 && data[i].Violations <= 1 {
			data[i].Compliance = "Minor Violation"
			data[i].Status = "Warning"
		} else if data[i].TotalHours <= 12 && data[i].Violations <= 2 {
			data[i].Compliance = "Major Violation"
			data[i].Status = "Critical"
		} else {
			data[i].Compliance = "Severe Violation"
			data[i].Status = "Emergency"
		}
	}

	summary := map[string]interface{}{
		"total_records":     len(data),
		"compliant_days":    0,
		"minor_violations":  0,
		"major_violations":  0,
		"severe_violations": 0,
		"avg_hours_per_day": 0.0,
		"total_violations":  0,
	}

	totalHours := 0.0
	for _, record := range data {
		totalHours += record.TotalHours
		summary["total_violations"] = summary["total_violations"].(int) + record.Violations
		switch record.Compliance {
		case "Compliant":
			summary["compliant_days"] = summary["compliant_days"].(int) + 1
		case "Minor Violation":
			summary["minor_violations"] = summary["minor_violations"].(int) + 1
		case "Major Violation":
			summary["major_violations"] = summary["major_violations"].(int) + 1
		case "Severe Violation":
			summary["severe_violations"] = summary["severe_violations"].(int) + 1
		}
	}

	if len(data) > 0 {
		summary["avg_hours_per_day"] = totalHours / float64(len(data))
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

// Comprehensive report implementations with real data

// generateCustomerVisitsReport generates customer visit analysis
func (s *ReportService) generateCustomerVisitsReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	query := config.DB.Table("customer_visits cv").
		Select(`
			cv.id,
			cv.customer_name,
			cv.customer_address,
			cv.visit_date,
			cv.arrival_time,
			cv.departure_time,
			cv.visit_duration,
			cv.visit_type,
			cv.status,
			d.name as driver_name,
			cd.name as device_name,
			cd.plate_number,
			(cv.visit_duration / 3600.0) as visit_hours
		`).
		Joins("LEFT JOIN drivers d ON cv.driver_id = d.id").
		Joins("LEFT JOIN client_devices cd ON cv.client_device_id = cd.id").
		Where("cv.client_id = ?", filters.ClientId).
		Order("cv.visit_date DESC, cv.arrival_time DESC")

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("cv.visit_date >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("cv.visit_date <= ?", *filters.EndDate)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("cv.driver_id IN (?)", filters.DriverIds)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("cv.client_device_id IN (?)", filters.ClientDeviceIds)
	}

	var totalCount, filteredCount int64
	config.DB.Table("customer_visits").Where("client_id = ?", filters.ClientId).Count(&totalCount)
	query.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		ID              uint    `json:"id"`
		CustomerName    string  `json:"customer_name"`
		CustomerAddress string  `json:"customer_address"`
		VisitDate       string  `json:"visit_date"`
		ArrivalTime     string  `json:"arrival_time"`
		DepartureTime   string  `json:"departure_time"`
		VisitDuration   int     `json:"visit_duration"`
		VisitHours      float64 `json:"visit_hours"`
		VisitType       string  `json:"visit_type"`
		Status          string  `json:"status"`
		DriverName      string  `json:"driver_name"`
		DeviceName      string  `json:"device_name"`
		PlateNumber     string  `json:"plate_number"`
		VisitValue      string  `json:"visit_value"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate visit value based on type and duration
	for i := range data {
		switch data[i].VisitType {
		case "delivery":
			data[i].VisitValue = "High Priority"
		case "sales":
			data[i].VisitValue = "Revenue Generating"
		case "service":
			data[i].VisitValue = "Maintenance"
		case "pickup":
			data[i].VisitValue = "Logistics"
		default:
			data[i].VisitValue = "Standard"
		}
	}

	summary := map[string]interface{}{
		"total_visits":       len(data),
		"delivery_visits":    0,
		"sales_visits":       0,
		"service_visits":     0,
		"pickup_visits":      0,
		"completed_visits":   0,
		"total_visit_hours":  0.0,
		"avg_visit_duration": 0.0,
	}

	totalHours := 0.0
	for _, visit := range data {
		totalHours += visit.VisitHours
		switch visit.VisitType {
		case "delivery":
			summary["delivery_visits"] = summary["delivery_visits"].(int) + 1
		case "sales":
			summary["sales_visits"] = summary["sales_visits"].(int) + 1
		case "service":
			summary["service_visits"] = summary["service_visits"].(int) + 1
		case "pickup":
			summary["pickup_visits"] = summary["pickup_visits"].(int) + 1
		}
		if visit.Status == "completed" {
			summary["completed_visits"] = summary["completed_visits"].(int) + 1
		}
	}

	summary["total_visit_hours"] = totalHours
	if len(data) > 0 {
		summary["avg_visit_duration"] = totalHours / float64(len(data))
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateUnauthorizedLocationsReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Query geofence events for unauthorized location detection
	// We'll look for geofence events where the geofence name contains "restricted", "unauthorized", "prohibited", etc.
	query := config.DB.Table("geofence_events ge").
		Select(`
			ge.id,
			ge.event_type,
			ge.event_timestamp,
			ge.latitude,
			ge.longitude,
			g.name as geofence_name,
			g.geofence_type,
			cd.name as device_name,
			cd.plate_number,
			COALESCE(d.name, 'Unknown Driver') as driver_name,
			t.start_time as trip_start_time,
			t.end_time as trip_end_time
		`).
		Joins("LEFT JOIN geofences g ON ge.geofence_id = g.id").
		Joins("LEFT JOIN client_devices cd ON ge.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON cd.driver_id = d.id").
		Joins("LEFT JOIN trips t ON ge.client_device_id = t.client_device_id AND ge.event_timestamp BETWEEN t.start_time AND t.end_time").
		Where("cd.client_id = ?", filters.ClientId).
		Where("g.name LIKE ? OR g.name LIKE ? OR g.name LIKE ? OR g.name LIKE ?",
			"%restricted%", "%unauthorized%", "%prohibited%", "%no access%").
		Where("ge.event_type = ?", "entry").
		Order("ge.event_timestamp DESC")

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("ge.event_timestamp >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("ge.event_timestamp <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("ge.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN ?", filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		query = query.Where("cd.fleet_id IN ?", filters.FleetIds)
	}

	// Get total count
	var totalRecords int64
	query.Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	var data []struct {
		ID             uint       `json:"id"`
		EventType      string     `json:"event_type"`
		EventTimestamp time.Time  `json:"event_timestamp"`
		Latitude       float64    `json:"latitude"`
		Longitude      float64    `json:"longitude"`
		GeofenceName   string     `json:"geofence_name"`
		GeofenceType   string     `json:"geofence_type"`
		DeviceName     string     `json:"device_name"`
		PlateNumber    string     `json:"plate_number"`
		DriverName     string     `json:"driver_name"`
		TripStartTime  *time.Time `json:"trip_start_time"`
		TripEndTime    *time.Time `json:"trip_end_time"`
		ViolationType  string     `json:"violation_type"`
		Severity       string     `json:"severity"`
		Recommendation string     `json:"recommendation"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch unauthorized location events: %v", err)
	}

	// Process the data and add violation details
	var totalViolations int
	var violationsByType map[string]int = make(map[string]int)
	var violationsBySeverity map[string]int = make(map[string]int)

	for i := range data {
		// Determine violation type based on geofence name
		violationType := "Unauthorized Area Entry"
		if strings.Contains(strings.ToLower(data[i].GeofenceName), "restricted") {
			violationType = "Restricted Area Entry"
		} else if strings.Contains(strings.ToLower(data[i].GeofenceName), "prohibited") {
			violationType = "Prohibited Area Entry"
		} else if strings.Contains(strings.ToLower(data[i].GeofenceName), "no access") {
			violationType = "No Access Zone Entry"
		}

		// Determine severity based on geofence type and context
		severity := "Medium"
		if strings.Contains(strings.ToLower(data[i].GeofenceName), "high security") ||
			strings.Contains(strings.ToLower(data[i].GeofenceName), "critical") {
			severity = "High"
		} else if strings.Contains(strings.ToLower(data[i].GeofenceName), "low risk") {
			severity = "Low"
		}

		// Generate recommendation
		recommendation := "Review route planning and driver training"
		switch severity {
		case "High":
			recommendation = "Immediate investigation required - potential security breach"
		case "Medium":
			recommendation = "Driver training needed - review access policies"
		case "Low":
			recommendation = "Monitor for patterns - consider route optimization"
		}

		data[i].ViolationType = violationType
		data[i].Severity = severity
		data[i].Recommendation = recommendation

		// Count violations
		totalViolations++
		violationsByType[violationType]++
		violationsBySeverity[severity]++
	}

	// Create summary
	summary := map[string]interface{}{
		"total_violations":       totalViolations,
		"violations_by_type":     violationsByType,
		"violations_by_severity": violationsBySeverity,
		"high_severity_count":    violationsBySeverity["High"],
		"medium_severity_count":  violationsBySeverity["Medium"],
		"low_severity_count":     violationsBySeverity["Low"],
		"risk_assessment":        assessUnauthorizedLocationRisk(violationsBySeverity),
		"recommendations":        generateUnauthorizedLocationRecommendations(violationsByType, violationsBySeverity),
	}

	return data, summary, int(totalRecords), len(data), nil
}

// Helper function to assess risk level
func assessUnauthorizedLocationRisk(violationsBySeverity map[string]int) string {
	highCount := violationsBySeverity["High"]
	mediumCount := violationsBySeverity["Medium"]

	if highCount > 0 {
		return "Critical - Immediate security review required"
	} else if highCount > 0 || mediumCount > 5 {
		return "High - Enhanced monitoring and training needed"
	} else if mediumCount > 0 || violationsBySeverity["Low"] > 10 {
		return "Medium - Regular monitoring recommended"
	} else {
		return "Low - Normal operations"
	}
}

// Helper function to generate recommendations
func generateUnauthorizedLocationRecommendations(violationsByType map[string]int, violationsBySeverity map[string]int) []string {
	var recommendations []string

	if violationsBySeverity["High"] > 0 {
		recommendations = append(recommendations, "Implement immediate security protocols")
		recommendations = append(recommendations, "Review and update access control policies")
	}

	if violationsBySeverity["Medium"] > 0 {
		recommendations = append(recommendations, "Enhance driver training on restricted areas")
		recommendations = append(recommendations, "Update route planning to avoid restricted zones")
	}

	if len(violationsByType) > 0 {
		recommendations = append(recommendations, "Regular review of geofence configurations")
		recommendations = append(recommendations, "Implement automated alerts for unauthorized entries")
	}

	return recommendations
}

func (s *ReportService) generateJobsiteProductivityReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Query for stop events and job site productivity analysis
	// We'll analyze GPS data to identify stops and their characteristics
	query := config.DB.Table("gps_data g").
		Select(`
			g.id,
			g.gps_timestamp,
			g.latitude,
			g.longitude,
			g.speed,
			g.ignition_status,
			g.vehicle_status,
			cd.name as device_name,
			cd.plate_number,
			COALESCE(d.name, 'Unknown Driver') as driver_name,
			COALESCE(f.name, 'Unknown Fleet') as fleet_name,
			-- Calculate stop duration by finding consecutive points with speed < 5 km/h
			(SELECT COUNT(*) FROM gps_data g2 
			 WHERE g2.client_device_id = g.client_device_id 
			 AND g2.gps_timestamp BETWEEN g.gps_timestamp AND DATE_ADD(g.gps_timestamp, INTERVAL 1 HOUR)
			 AND g2.speed < 5) * 60 as stop_duration_minutes,
			-- Identify if this is likely a job site stop
			CASE 
				WHEN g.speed < 5 AND g.ignition_status = true THEN 'Job Site Stop'
				WHEN g.speed < 5 AND g.ignition_status = false THEN 'Parking Stop'
				ELSE 'In Transit'
			END as stop_type,
			-- Get trip context if available
			t.id as trip_id,
			t.start_time as trip_start_time,
			t.end_time as trip_end_time
		`).
		Joins("LEFT JOIN client_devices cd ON g.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON cd.driver_id = d.id").
		Joins("LEFT JOIN fleets f ON cd.fleet_id = f.id").
		Joins("LEFT JOIN trips t ON g.client_device_id = t.client_device_id AND g.gps_timestamp BETWEEN t.start_time AND t.end_time").
		Where("cd.client_id = ?", filters.ClientId).
		Where("g.speed < 5"). // Focus on stops (speed < 5 km/h)
		Order("g.gps_timestamp DESC")

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("g.gps_timestamp >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("g.gps_timestamp <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("g.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN ?", filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		query = query.Where("cd.fleet_id IN ?", filters.FleetIds)
	}

	// Get total count
	var totalRecords int64
	query.Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	var data []struct {
		ID                  uint       `json:"id"`
		GPSTimestamp        *time.Time `json:"gps_timestamp"`
		Latitude            float64    `json:"latitude"`
		Longitude           float64    `json:"longitude"`
		Speed               *float64   `json:"speed"`
		IgnitionStatus      *bool      `json:"ignition_status"`
		VehicleStatus       *string    `json:"vehicle_status"`
		DeviceName          string     `json:"device_name"`
		PlateNumber         string     `json:"plate_number"`
		DriverName          string     `json:"driver_name"`
		FleetName           string     `json:"fleet_name"`
		StopDurationMinutes int        `json:"stop_duration_minutes"`
		StopType            string     `json:"stop_type"`
		TripID              *uint      `json:"trip_id"`
		TripStartTime       *time.Time `json:"trip_start_time"`
		TripEndTime         *time.Time `json:"trip_end_time"`
		ProductivityScore   string     `json:"productivity_score"`
		EfficiencyRating    string     `json:"efficiency_rating"`
		Recommendation      string     `json:"recommendation"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch stop detail data: %v", err)
	}

	// Process the data and add productivity metrics
	var totalStops int
	var jobSiteStops int
	var parkingStops int
	var totalStopTime int
	var stopsByDuration map[string]int = make(map[string]int)
	var stopsByType map[string]int = make(map[string]int)

	for i := range data {
		// Calculate productivity score based on stop duration and type
		productivityScore := "Good"
		if data[i].StopDurationMinutes > 120 {
			productivityScore = "Poor"
		} else if data[i].StopDurationMinutes > 60 {
			productivityScore = "Fair"
		} else if data[i].StopDurationMinutes < 15 {
			productivityScore = "Excellent"
		}

		// Calculate efficiency rating
		efficiencyRating := "Efficient"
		if data[i].StopType == "Job Site Stop" && data[i].StopDurationMinutes > 90 {
			efficiencyRating = "Needs Improvement"
		} else if data[i].StopType == "Parking Stop" && data[i].StopDurationMinutes > 30 {
			efficiencyRating = "Inefficient"
		}

		// Generate recommendations
		recommendation := "Normal operation"
		switch {
		case data[i].StopType == "Job Site Stop" && data[i].StopDurationMinutes > 120:
			recommendation = "Review job site processes - consider workflow optimization"
		case data[i].StopType == "Parking Stop" && data[i].StopDurationMinutes > 60:
			recommendation = "Optimize parking locations and reduce idle time"
		case data[i].StopType == "Job Site Stop" && data[i].StopDurationMinutes < 15:
			recommendation = "Excellent efficiency - consider as benchmark"
		}

		data[i].ProductivityScore = productivityScore
		data[i].EfficiencyRating = efficiencyRating
		data[i].Recommendation = recommendation

		// Count statistics
		totalStops++
		totalStopTime += data[i].StopDurationMinutes
		stopsByType[data[i].StopType]++

		// Categorize by duration
		if data[i].StopDurationMinutes < 15 {
			stopsByDuration["Short (< 15 min)"]++
		} else if data[i].StopDurationMinutes < 60 {
			stopsByDuration["Medium (15-60 min)"]++
		} else if data[i].StopDurationMinutes < 120 {
			stopsByDuration["Long (60-120 min)"]++
		} else {
			stopsByDuration["Extended (> 120 min)"]++
		}

		// Count by type
		if data[i].StopType == "Job Site Stop" {
			jobSiteStops++
		} else if data[i].StopType == "Parking Stop" {
			parkingStops++
		}
	}

	// Calculate averages
	avgStopDuration := 0.0
	if totalStops > 0 {
		avgStopDuration = float64(totalStopTime) / float64(totalStops)
	}

	// Create summary
	summary := map[string]interface{}{
		"total_stops":           totalStops,
		"job_site_stops":        jobSiteStops,
		"parking_stops":         parkingStops,
		"total_stop_time_hours": float64(totalStopTime) / 60.0,
		"avg_stop_duration_min": avgStopDuration,
		"stops_by_duration":     stopsByDuration,
		"stops_by_type":         stopsByType,
		"productivity_metrics": map[string]interface{}{
			"excellent_stops": 0,
			"good_stops":      0,
			"fair_stops":      0,
			"poor_stops":      0,
		},
		"efficiency_analysis": map[string]interface{}{
			"efficient_stops":   0,
			"needs_improvement": 0,
			"inefficient_stops": 0,
		},
		"recommendations": generateStopDetailRecommendations(stopsByType, stopsByDuration, avgStopDuration),
	}

	// Calculate productivity metrics
	for _, stop := range data {
		switch stop.ProductivityScore {
		case "Excellent":
			summary["productivity_metrics"].(map[string]interface{})["excellent_stops"] = summary["productivity_metrics"].(map[string]interface{})["excellent_stops"].(int) + 1
		case "Good":
			summary["productivity_metrics"].(map[string]interface{})["good_stops"] = summary["productivity_metrics"].(map[string]interface{})["good_stops"].(int) + 1
		case "Fair":
			summary["productivity_metrics"].(map[string]interface{})["fair_stops"] = summary["productivity_metrics"].(map[string]interface{})["fair_stops"].(int) + 1
		case "Poor":
			summary["productivity_metrics"].(map[string]interface{})["poor_stops"] = summary["productivity_metrics"].(map[string]interface{})["poor_stops"].(int) + 1
		}

		switch stop.EfficiencyRating {
		case "Efficient":
			summary["efficiency_analysis"].(map[string]interface{})["efficient_stops"] = summary["efficiency_analysis"].(map[string]interface{})["efficient_stops"].(int) + 1
		case "Needs Improvement":
			summary["efficiency_analysis"].(map[string]interface{})["needs_improvement"] = summary["efficiency_analysis"].(map[string]interface{})["needs_improvement"].(int) + 1
		case "Inefficient":
			summary["efficiency_analysis"].(map[string]interface{})["inefficient_stops"] = summary["efficiency_analysis"].(map[string]interface{})["inefficient_stops"].(int) + 1
		}
	}

	return data, summary, int(totalRecords), len(data), nil
}

// Helper function to generate stop detail recommendations
func generateStopDetailRecommendations(stopsByType map[string]int, stopsByDuration map[string]int, avgStopDuration float64) []string {
	var recommendations []string

	// Analyze job site stops
	if jobSiteStops := stopsByType["Job Site Stop"]; jobSiteStops > 0 {
		if extendedStops := stopsByDuration["Extended (> 120 min)"]; extendedStops > 0 {
			recommendations = append(recommendations, "Review job site processes for extended stops")
			recommendations = append(recommendations, "Consider workflow optimization and resource allocation")
		}
		if avgStopDuration > 90 {
			recommendations = append(recommendations, "Implement job site efficiency training")
		}
	}

	// Analyze parking stops
	if parkingStops := stopsByType["Parking Stop"]; parkingStops > 0 {
		if longStops := stopsByDuration["Long (60-120 min)"]; longStops > 0 {
			recommendations = append(recommendations, "Optimize parking locations to reduce idle time")
		}
		if avgStopDuration > 45 {
			recommendations = append(recommendations, "Review parking policies and driver training")
		}
	}

	// General recommendations
	if avgStopDuration > 60 {
		recommendations = append(recommendations, "Implement stop time monitoring and alerts")
		recommendations = append(recommendations, "Set productivity benchmarks and goals")
	}

	if len(recommendations) == 0 {
		recommendations = append(recommendations, "Current stop patterns are within acceptable ranges")
		recommendations = append(recommendations, "Continue monitoring for optimization opportunities")
	}

	return recommendations
}

func (s *ReportService) generateOperatingCostReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	query := config.DB.Table("operating_costs oc").
		Select(`
			oc.id,
			oc.cost_date,
			oc.cost_type,
			oc.cost_category,
			oc.amount,
			oc.description,
			oc.vendor,
			cd.name as device_name,
			cd.plate_number,
			DATE_FORMAT(oc.cost_date, '%Y-%m') as month,
			(oc.amount * 30) as monthly_estimate
		`).
		Joins("LEFT JOIN client_devices cd ON oc.client_device_id = cd.id").
		Where("oc.client_id = ?", filters.ClientId).
		Order("oc.cost_date DESC")

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("oc.cost_date >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("oc.cost_date <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("oc.client_device_id IN (?)", filters.ClientDeviceIds)
	}

	var totalCount, filteredCount int64
	config.DB.Table("operating_costs").Where("client_id = ?", filters.ClientId).Count(&totalCount)
	query.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		ID              uint    `json:"id"`
		CostDate        string  `json:"cost_date"`
		CostType        string  `json:"cost_type"`
		CostCategory    string  `json:"cost_category"`
		Amount          float64 `json:"amount"`
		Description     string  `json:"description"`
		Vendor          string  `json:"vendor"`
		DeviceName      string  `json:"device_name"`
		PlateNumber     string  `json:"plate_number"`
		Month           string  `json:"month"`
		MonthlyEstimate float64 `json:"monthly_estimate"`
		CostImpact      string  `json:"cost_impact"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate cost impact
	for i := range data {
		if data[i].Amount > 500 {
			data[i].CostImpact = "High"
		} else if data[i].Amount > 200 {
			data[i].CostImpact = "Medium"
		} else {
			data[i].CostImpact = "Low"
		}
	}

	summary := map[string]interface{}{
		"total_costs":        len(data),
		"fuel_costs":         0.0,
		"maintenance_costs":  0.0,
		"labor_costs":        0.0,
		"insurance_costs":    0.0,
		"depreciation_costs": 0.0,
		"total_amount":       0.0,
		"avg_daily_cost":     0.0,
		"cost_trend":         "Stable",
	}

	totalAmount := 0.0
	for _, cost := range data {
		totalAmount += cost.Amount
		switch cost.CostType {
		case "fuel":
			summary["fuel_costs"] = summary["fuel_costs"].(float64) + cost.Amount
		case "maintenance":
			summary["maintenance_costs"] = summary["maintenance_costs"].(float64) + cost.Amount
		case "labor":
			summary["labor_costs"] = summary["labor_costs"].(float64) + cost.Amount
		case "insurance":
			summary["insurance_costs"] = summary["insurance_costs"].(float64) + cost.Amount
		case "depreciation":
			summary["depreciation_costs"] = summary["depreciation_costs"].(float64) + cost.Amount
		}
	}

	summary["total_amount"] = totalAmount
	if len(data) > 0 {
		summary["avg_daily_cost"] = totalAmount / float64(len(data))
	}

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateIdleTimeCostReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":         "Idle time cost report - coming soon",
		"total_idle_cost": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateMaintenanceDueReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	query := config.DB.Table("maintenance_schedules ms").
		Select(`
			ms.id,
			ms.maintenance_type,
			ms.service_type,
			ms.scheduled_date,
			ms.due_date,
			ms.completed_date,
			ms.cost,
			ms.service_provider,
			ms.description,
			ms.status,
			ms.priority,
			cd.name as device_name,
			cd.plate_number,
			DATEDIFF(ms.due_date, CURDATE()) as days_until_due,
			CASE 
				WHEN ms.due_date < CURDATE() THEN 'Overdue'
				WHEN ms.due_date = CURDATE() THEN 'Due Today'
				WHEN DATEDIFF(ms.due_date, CURDATE()) <= 7 THEN 'Due Soon'
				ELSE 'Scheduled'
			END as urgency_level
		`).
		Joins("LEFT JOIN client_devices cd ON ms.client_device_id = cd.id").
		Where("ms.client_id = ?", filters.ClientId).
		Order("ms.due_date ASC")

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("ms.due_date >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("ms.due_date <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("ms.client_device_id IN (?)", filters.ClientDeviceIds)
	}

	var totalCount, filteredCount int64
	config.DB.Table("maintenance_schedules").Where("client_id = ?", filters.ClientId).Count(&totalCount)
	query.Count(&filteredCount)

	if filters.PerPage > 0 {
		offset := 0
		if filters.Page > 0 {
			offset = (filters.Page - 1) * filters.PerPage
		}
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	var data []struct {
		ID              uint    `json:"id"`
		MaintenanceType string  `json:"maintenance_type"`
		ServiceType     string  `json:"service_type"`
		ScheduledDate   string  `json:"scheduled_date"`
		DueDate         string  `json:"due_date"`
		CompletedDate   *string `json:"completed_date"`
		Cost            float64 `json:"cost"`
		ServiceProvider string  `json:"service_provider"`
		Description     string  `json:"description"`
		Status          string  `json:"status"`
		Priority        string  `json:"priority"`
		DeviceName      string  `json:"device_name"`
		PlateNumber     string  `json:"plate_number"`
		DaysUntilDue    int     `json:"days_until_due"`
		UrgencyLevel    string  `json:"urgency_level"`
		ActionRequired  string  `json:"action_required"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, err
	}

	// Calculate action required
	for i := range data {
		switch data[i].UrgencyLevel {
		case "Overdue":
			data[i].ActionRequired = "Immediate Action Required"
		case "Due Today":
			data[i].ActionRequired = "Schedule Today"
		case "Due Soon":
			data[i].ActionRequired = "Schedule This Week"
		default:
			data[i].ActionRequired = "Monitor"
		}
	}

	summary := map[string]interface{}{
		"total_maintenance_items": len(data),
		"overdue_items":           0,
		"due_today":               0,
		"due_soon":                0,
		"scheduled_items":         0,
		"completed_items":         0,
		"total_cost":              0.0,
		"critical_priority":       0,
		"high_priority":           0,
		"medium_priority":         0,
		"low_priority":            0,
	}

	totalCost := 0.0
	for _, item := range data {
		totalCost += item.Cost
		switch item.UrgencyLevel {
		case "Overdue":
			summary["overdue_items"] = summary["overdue_items"].(int) + 1
		case "Due Today":
			summary["due_today"] = summary["due_today"].(int) + 1
		case "Due Soon":
			summary["due_soon"] = summary["due_soon"].(int) + 1
		default:
			summary["scheduled_items"] = summary["scheduled_items"].(int) + 1
		}
		if item.Status == "completed" {
			summary["completed_items"] = summary["completed_items"].(int) + 1
		}
		switch item.Priority {
		case "critical":
			summary["critical_priority"] = summary["critical_priority"].(int) + 1
		case "high":
			summary["high_priority"] = summary["high_priority"].(int) + 1
		case "medium":
			summary["medium_priority"] = summary["medium_priority"].(int) + 1
		case "low":
			summary["low_priority"] = summary["low_priority"].(int) + 1
		}
	}

	summary["total_cost"] = totalCost

	return data, summary, int(totalCount), int(filteredCount), nil
}

func (s *ReportService) generateVehicleHealthDashboardReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":        "Vehicle health dashboard - coming soon",
		"total_vehicles": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateBreakdownRepairHistoryReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Query maintenance schedule for breakdown and repair history
	query := config.DB.Table("maintenance_schedule ms").
		Select(`
			ms.id,
			ms.maintenance_type,
			ms.service_type,
			ms.scheduled_date,
			ms.due_date,
			ms.completed_date,
			ms.cost,
			ms.service_provider,
			ms.description,
			ms.status,
			ms.priority,
			cd.name as device_name,
			cd.plate_number,
			COALESCE(d.name, 'Unknown Driver') as driver_name,
			COALESCE(f.name, 'Unknown Fleet') as fleet_name,
			-- Calculate days overdue
			CASE 
				WHEN ms.status = 'overdue' THEN DATEDIFF(NOW(), ms.due_date)
				ELSE 0
			END as days_overdue,
			-- Calculate completion time
			CASE 
				WHEN ms.completed_date IS NOT NULL THEN DATEDIFF(ms.completed_date, ms.scheduled_date)
				ELSE NULL
			END as completion_days,
			-- Calculate cost impact
			CASE 
				WHEN ms.cost > 1000 THEN 'High'
				WHEN ms.cost > 500 THEN 'Medium'
				ELSE 'Low'
			END as cost_impact,
			-- Determine urgency level
			CASE 
				WHEN ms.status = 'overdue' THEN 'Overdue'
				WHEN ms.due_date = CURDATE() THEN 'Due Today'
				WHEN ms.due_date <= DATE_ADD(CURDATE(), INTERVAL 7 DAY) THEN 'Due Soon'
				ELSE 'Scheduled'
			END as urgency_level
		`).
		Joins("LEFT JOIN client_devices cd ON ms.client_device_id = cd.id").
		Joins("LEFT JOIN drivers d ON cd.driver_id = d.id").
		Joins("LEFT JOIN fleets f ON cd.fleet_id = f.id").
		Where("cd.client_id = ?", filters.ClientId).
		Order("ms.due_date ASC, ms.priority DESC")

	// Apply filters
	if filters.StartDate != nil {
		query = query.Where("ms.scheduled_date >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("ms.scheduled_date <= ?", *filters.EndDate)
	}
	if len(filters.ClientDeviceIds) > 0 {
		query = query.Where("ms.client_device_id IN ?", filters.ClientDeviceIds)
	}
	if len(filters.DriverIds) > 0 {
		query = query.Where("d.id IN ?", filters.DriverIds)
	}
	if len(filters.FleetIds) > 0 {
		query = query.Where("cd.fleet_id IN ?", filters.FleetIds)
	}

	// Get total count
	var totalRecords int64
	query.Count(&totalRecords)

	// Apply pagination
	if filters.PerPage > 0 {
		query = query.Limit(filters.PerPage)
		if filters.Page > 0 {
			query = query.Offset((filters.Page - 1) * filters.PerPage)
		}
	}

	var data []struct {
		ID              uint       `json:"id"`
		MaintenanceType string     `json:"maintenance_type"`
		ServiceType     string     `json:"service_type"`
		ScheduledDate   time.Time  `json:"scheduled_date"`
		DueDate         time.Time  `json:"due_date"`
		CompletedDate   *time.Time `json:"completed_date"`
		Cost            float64    `json:"cost"`
		ServiceProvider string     `json:"service_provider"`
		Description     string     `json:"description"`
		Status          string     `json:"status"`
		Priority        string     `json:"priority"`
		DeviceName      string     `json:"device_name"`
		PlateNumber     string     `json:"plate_number"`
		DriverName      string     `json:"driver_name"`
		FleetName       string     `json:"fleet_name"`
		DaysOverdue     int        `json:"days_overdue"`
		CompletionDays  *int       `json:"completion_days"`
		CostImpact      string     `json:"cost_impact"`
		UrgencyLevel    string     `json:"urgency_level"`
		Recommendation  string     `json:"recommendation"`
	}

	err := query.Scan(&data).Error
	if err != nil {
		return nil, nil, 0, 0, fmt.Errorf("failed to fetch maintenance data: %v", err)
	}

	// Process the data and add recommendations
	var totalMaintenance int
	var totalCost float64
	var completedMaintenance int
	var overdueMaintenance int
	var maintenanceByType map[string]int = make(map[string]int)
	var maintenanceByStatus map[string]int = make(map[string]int)
	var maintenanceByPriority map[string]int = make(map[string]int)

	for i := range data {
		// Generate recommendations based on maintenance data
		recommendation := "Normal maintenance schedule"
		switch {
		case data[i].Status == "overdue" && data[i].DaysOverdue > 30:
			recommendation = "Critical: Immediate attention required - vehicle may be unsafe"
		case data[i].Status == "overdue" && data[i].DaysOverdue > 7:
			recommendation = "High priority: Schedule maintenance immediately"
		case data[i].Status == "overdue":
			recommendation = "Overdue: Reschedule maintenance as soon as possible"
		case data[i].UrgencyLevel == "Due Today":
			recommendation = "Due today: Ensure maintenance is completed"
		case data[i].UrgencyLevel == "Due Soon":
			recommendation = "Due soon: Plan maintenance schedule"
		case data[i].CostImpact == "High" && data[i].Status == "scheduled":
			recommendation = "High cost maintenance: Review budget allocation"
		case data[i].MaintenanceType == "emergency":
			recommendation = "Emergency maintenance: Review preventive measures"
		}

		data[i].Recommendation = recommendation

		// Count statistics
		totalMaintenance++
		totalCost += data[i].Cost
		maintenanceByType[data[i].MaintenanceType]++
		maintenanceByStatus[data[i].Status]++
		maintenanceByPriority[data[i].Priority]++

		if data[i].Status == "completed" {
			completedMaintenance++
		}
		if data[i].Status == "overdue" {
			overdueMaintenance++
		}
	}

	// Calculate completion rate
	completionRate := 0.0
	if totalMaintenance > 0 {
		completionRate = float64(completedMaintenance) / float64(totalMaintenance) * 100
	}

	// Create summary
	summary := map[string]interface{}{
		"total_maintenance":     totalMaintenance,
		"completed_maintenance": completedMaintenance,
		"overdue_maintenance":   overdueMaintenance,
		"total_cost":            totalCost,
		"avg_cost_per_maintenance": func() float64 {
			if totalMaintenance > 0 {
				return totalCost / float64(totalMaintenance)
			}
			return 0.0
		}(),
		"completion_rate":         completionRate,
		"maintenance_by_type":     maintenanceByType,
		"maintenance_by_status":   maintenanceByStatus,
		"maintenance_by_priority": maintenanceByPriority,
		"cost_breakdown": map[string]interface{}{
			"high_cost":   0,
			"medium_cost": 0,
			"low_cost":    0,
		},
		"urgency_breakdown": map[string]interface{}{
			"overdue":   0,
			"due_today": 0,
			"due_soon":  0,
			"scheduled": 0,
		},
		"recommendations": generateMaintenanceRecommendations(maintenanceByType, maintenanceByStatus, overdueMaintenance, completionRate),
	}

	// Calculate cost and urgency breakdowns
	for _, item := range data {
		switch item.CostImpact {
		case "High":
			summary["cost_breakdown"].(map[string]interface{})["high_cost"] = summary["cost_breakdown"].(map[string]interface{})["high_cost"].(int) + 1
		case "Medium":
			summary["cost_breakdown"].(map[string]interface{})["medium_cost"] = summary["cost_breakdown"].(map[string]interface{})["medium_cost"].(int) + 1
		case "Low":
			summary["cost_breakdown"].(map[string]interface{})["low_cost"] = summary["cost_breakdown"].(map[string]interface{})["low_cost"].(int) + 1
		}

		switch item.UrgencyLevel {
		case "Overdue":
			summary["urgency_breakdown"].(map[string]interface{})["overdue"] = summary["urgency_breakdown"].(map[string]interface{})["overdue"].(int) + 1
		case "Due Today":
			summary["urgency_breakdown"].(map[string]interface{})["due_today"] = summary["urgency_breakdown"].(map[string]interface{})["due_today"].(int) + 1
		case "Due Soon":
			summary["urgency_breakdown"].(map[string]interface{})["due_soon"] = summary["urgency_breakdown"].(map[string]interface{})["due_soon"].(int) + 1
		case "Scheduled":
			summary["urgency_breakdown"].(map[string]interface{})["scheduled"] = summary["urgency_breakdown"].(map[string]interface{})["scheduled"].(int) + 1
		}
	}

	return data, summary, int(totalRecords), len(data), nil
}

// Helper function to generate maintenance recommendations
func generateMaintenanceRecommendations(maintenanceByType map[string]int, maintenanceByStatus map[string]int, overdueCount int, completionRate float64) []string {
	var recommendations []string

	// Analyze overdue maintenance
	if overdueCount > 0 {
		recommendations = append(recommendations, "Address overdue maintenance immediately to prevent vehicle downtime")
		if overdueCount > 5 {
			recommendations = append(recommendations, "Implement preventive maintenance scheduling system")
		}
	}

	// Analyze completion rate
	if completionRate < 80 {
		recommendations = append(recommendations, "Improve maintenance completion rate through better scheduling")
		recommendations = append(recommendations, "Consider automated maintenance reminders")
	}

	// Analyze maintenance types
	if emergencyCount := maintenanceByType["emergency"]; emergencyCount > 0 {
		recommendations = append(recommendations, "Reduce emergency maintenance through preventive measures")
	}
	if preventiveCount := maintenanceByType["preventive"]; preventiveCount > 0 {
		recommendations = append(recommendations, "Maintain preventive maintenance schedule for optimal vehicle health")
	}

	// General recommendations
	if len(recommendations) == 0 {
		recommendations = append(recommendations, "Maintenance schedule is well-managed")
		recommendations = append(recommendations, "Continue monitoring for optimization opportunities")
	}

	return recommendations
}

func (s *ReportService) generateVehicleSecurityReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":         "Vehicle security report - coming soon",
		"security_events": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateSpeedLimitComplianceReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":         "Speed limit compliance report - coming soon",
		"compliance_rate": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateFleetPerformanceTrendsReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":       "Fleet performance trends report - coming soon",
		"trend_periods": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateCostCenterAnalysisReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":      "Cost center analysis report - coming soon",
		"cost_centers": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateEnvironmentalImpactReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	// Environmental Impact Report is disabled for Zimbabwe region
	// This report requires emissions data that is not available in the current database schema
	// and is not applicable for the Zimbabwe market context

	summary := map[string]interface{}{
		"message":              "Environmental Impact Report is not available in Zimbabwe region",
		"reason":               "This report requires emissions data that is not applicable for the Zimbabwe market context",
		"status":               "disabled",
		"total_records":        0,
		"total_co2_emissions":  0.0,
		"total_fuel_consumed":  0.0,
		"total_distance":       0.0,
		"avg_carbon_footprint": 0.0,
		"avg_fuel_efficiency":  0.0,
	}

	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateDailyOperationsDashboardReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":       "Daily operations dashboard - coming soon",
		"daily_metrics": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateVehicleAvailabilityReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":            "Vehicle availability report - coming soon",
		"available_vehicles": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateAssetTrackingReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":      "Asset tracking report - coming soon",
		"total_assets": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateDeliveryPerformanceReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":               "Delivery performance report - coming soon",
		"delivery_success_rate": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateServiceTechnicianReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":          "Service technician report - coming soon",
		"technician_count": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateConstructionEquipmentReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":         "Construction equipment report - coming soon",
		"equipment_count": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateSalesTerritoryAnalysisReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":     "Sales territory analysis report - coming soon",
		"territories": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateMaintenanceCostAnalysisReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":                "Maintenance cost analysis report - coming soon",
		"total_maintenance_cost": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateSafetyComplianceReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":         "Safety compliance report - coming soon",
		"compliance_rate": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateAssetPerformanceReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":     "Asset performance report - coming soon",
		"asset_count": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateRouteOptimizationReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":                    "Route optimization report - coming soon",
		"optimization_opportunities": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateDriverTrainingReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":        "Driver training report - coming soon",
		"training_needs": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateVehicleLifecycleReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":          "Vehicle lifecycle report - coming soon",
		"lifecycle_stages": 0,
	}
	return []string{}, summary, 0, 0, nil
}

func (s *ReportService) generateOperationalEfficiencyReport(filters models.ReportFilters) (interface{}, interface{}, int, int, error) {
	summary := map[string]interface{}{
		"message":          "Operational efficiency report - coming soon",
		"efficiency_score": 0,
	}
	return []string{}, summary, 0, 0, nil
}
