package services

import (
	"bytes"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"html/template"
	"strings"
	"time"
	"yotracker/internal/models"

	"github.com/Sebastiaan<PERSON>lippert/go-wkhtmltopdf"
	"github.com/xuri/excelize/v2"
)

type ReportExportService struct{}

func NewReportExportService() *ReportExportService {
	return &ReportExportService{}
}

// ExportReport exports report data in the specified format
func (s *ReportExportService) ExportReport(reportData *models.ReportData, format string) ([]byte, string, error) {
	switch strings.ToLower(format) {
	case "pdf":
		data, err := s.ExportToPDF(reportData)
		return data, "application/pdf", err
	case "excel", "xlsx":
		data, err := s.ExportToExcel(reportData)
		return data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", err
	case "csv":
		data, err := s.ExportToCSV(reportData)
		return data, "text/csv", err
	default:
		return nil, "", fmt.Errorf("unsupported export format: %s", format)
	}
}

// ExportToPDF generates a PDF report
func (s *ReportExportService) ExportToPDF(reportData *models.ReportData) ([]byte, error) {
	// Create HTML template for the report
	htmlTemplate := s.createReportHTMLTemplate()

	tmpl, err := template.New("report").Parse(htmlTemplate)
	if err != nil {
		return nil, fmt.Errorf("template parse error: %v", err)
	}

	var htmlBuffer bytes.Buffer
	err = tmpl.Execute(&htmlBuffer, reportData)
	if err != nil {
		return nil, fmt.Errorf("template execution error: %v", err)
	}

	// Generate PDF from HTML using wkhtmltopdf
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return nil, fmt.Errorf("PDF generator error: %v", err)
	}

	// Configure PDF options
	pdfg.Dpi.Set(300)
	pdfg.Orientation.Set(wkhtmltopdf.OrientationLandscape)
	pdfg.Grayscale.Set(false)
	pdfg.PageSize.Set(wkhtmltopdf.PageSizeA4)

	page := wkhtmltopdf.NewPageReader(&htmlBuffer)
	pdfg.AddPage(page)

	err = pdfg.Create()
	if err != nil {
		return nil, fmt.Errorf("PDF creation failed: %v", err)
	}

	return pdfg.Bytes(), nil
}

// ExportToExcel generates an Excel report
func (s *ReportExportService) ExportToExcel(reportData *models.ReportData) ([]byte, error) {
	f := excelize.NewFile()
	defer f.Close()

	// Create main sheet
	sheetName := "Report Data"
	f.SetSheetName("Sheet1", sheetName)

	// Add report header
	f.SetCellValue(sheetName, "A1", reportData.ReportInfo.Name)
	f.SetCellValue(sheetName, "A2", reportData.ReportInfo.Description)
	f.SetCellValue(sheetName, "A3", fmt.Sprintf("Generated: %s", reportData.Metadata.GeneratedAt.Format("2006-01-02 15:04:05")))
	f.SetCellValue(sheetName, "A4", fmt.Sprintf("Total Records: %d", reportData.Metadata.TotalRecords))
	f.SetCellValue(sheetName, "A5", fmt.Sprintf("Filtered Records: %d", reportData.Metadata.FilteredRecords))

	// Style the header
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true, Size: 14},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"#E6F3FF"}, Pattern: 1},
	})
	f.SetCellStyle(sheetName, "A1", "A5", headerStyle)

	// Add data starting from row 7
	startRow := 7
	err := s.addDataToExcel(f, sheetName, reportData.Data, startRow)
	if err != nil {
		return nil, fmt.Errorf("failed to add data to Excel: %v", err)
	}

	// Add summary sheet if available
	if reportData.Summary != nil {
		s.addSummarySheet(f, reportData.Summary)
	}

	// Save to buffer
	buf, err := f.WriteToBuffer()
	if err != nil {
		return nil, fmt.Errorf("failed to write Excel file: %v", err)
	}

	return buf.Bytes(), nil
}

// ExportToCSV generates a CSV report
func (s *ReportExportService) ExportToCSV(reportData *models.ReportData) ([]byte, error) {
	var buf bytes.Buffer
	writer := csv.NewWriter(&buf)

	// Write report metadata header
	metadataRows := [][]string{
		{"Report", reportData.ReportInfo.Name},
		{"Description", reportData.ReportInfo.Description},
		{"Total Records", fmt.Sprintf("%d", reportData.Metadata.TotalRecords)},
		{"Filtered Records", fmt.Sprintf("%d", reportData.Metadata.FilteredRecords)},
		{"Generated At", reportData.Metadata.GeneratedAt.Format("2006-01-02 15:04:05")},
		{}, // Empty row for spacing
	}

	for _, row := range metadataRows {
		if err := writer.Write(row); err != nil {
			return nil, fmt.Errorf("failed to write CSV metadata: %v", err)
		}
	}

	// Write data
	err := s.addDataToCSV(writer, reportData.Data)
	if err != nil {
		return nil, fmt.Errorf("failed to write CSV data: %v", err)
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return nil, fmt.Errorf("CSV writer error: %v", err)
	}

	return buf.Bytes(), nil
}

// addDataToExcel adds report data to Excel sheet
func (s *ReportExportService) addDataToExcel(f *excelize.File, sheetName string, data interface{}, startRow int) error {
	// Convert data to slice of maps for easier processing
	records, headers, err := s.convertDataToRecords(data)
	if err != nil {
		return err
	}

	if len(records) == 0 {
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", startRow), "No data available")
		return nil
	}

	// Write headers
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"#D9E1F2"}, Pattern: 1},
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
		},
	})

	for i, header := range headers {
		cell := fmt.Sprintf("%s%d", s.getExcelColumn(i), startRow)
		f.SetCellValue(sheetName, cell, header)
		f.SetCellStyle(sheetName, cell, cell, headerStyle)
	}

	// Write data rows
	for i, record := range records {
		row := startRow + 1 + i
		for j, header := range headers {
			cell := fmt.Sprintf("%s%d", s.getExcelColumn(j), row)
			value := record[header]
			f.SetCellValue(sheetName, cell, value)
		}
	}

	// Auto-fit columns
	for i := range headers {
		col := s.getExcelColumn(i)
		f.SetColWidth(sheetName, col, col, 15)
	}

	return nil
}

// addDataToCSV adds report data to CSV writer
func (s *ReportExportService) addDataToCSV(writer *csv.Writer, data interface{}) error {
	records, headers, err := s.convertDataToRecords(data)
	if err != nil {
		return err
	}

	if len(records) == 0 {
		writer.Write([]string{"No data available"})
		return nil
	}

	// Write headers
	writer.Write(headers)

	// Write data rows
	for _, record := range records {
		row := make([]string, len(headers))
		for i, header := range headers {
			if value, exists := record[header]; exists {
				row[i] = fmt.Sprintf("%v", value)
			}
		}
		writer.Write(row)
	}

	return nil
}

// convertDataToRecords converts various data types to slice of maps
func (s *ReportExportService) convertDataToRecords(data interface{}) ([]map[string]interface{}, []string, error) {
	if data == nil {
		return nil, nil, nil
	}

	// Handle different data types
	switch v := data.(type) {
	case []interface{}:
		if len(v) == 0 {
			return nil, nil, nil
		}

		// Convert to JSON and back to get consistent map structure
		jsonData, err := json.Marshal(v)
		if err != nil {
			return nil, nil, err
		}

		var records []map[string]interface{}
		err = json.Unmarshal(jsonData, &records)
		if err != nil {
			return nil, nil, err
		}

		// Extract headers from first record
		if len(records) > 0 {
			headers := make([]string, 0, len(records[0]))
			for key := range records[0] {
				headers = append(headers, key)
			}
			return records, headers, nil
		}

	case []models.Trip:
		return s.convertTripsToRecords(v)

	case []models.DrivingBehaviorEvent:
		return s.convertBehaviorEventsToRecords(v)

	case []models.GPSData:
		return s.convertGPSDataToRecords(v)

	case *models.PositionLogDTO:
		return s.convertPositionLogDTOToRecords(v)

	case *models.TripDetailDTO:
		return s.convertTripDetailDTOToRecords(v)

	case *models.DriverSafetyScorecardDTO:
		return s.convertDriverSafetyScorecardDTOToRecords(v)
	case *models.FuelEstimationDTO:
		return s.convertFuelEstimationDTOToRecords(v)
	case *models.DriverBehaviorAnalysisDTO:
		return s.convertDriverBehaviorAnalysisDTOToRecords(v)
	case *models.FuelEfficiencyTrendsDTO:
		return s.convertFuelEfficiencyTrendsDTOToRecords(v)
	case *models.FleetProductivityDTO:
		return s.convertFleetProductivityDTOToRecords(v)
	case *models.RealTimeDashboardDTO:
		return s.convertRealTimeDashboardDTOToRecords(v)
	case *models.DriverScorecardDTO:
		return s.convertDriverScorecardDTOToRecords(v)
	case *models.FleetHealthMonitorDTO:
		return s.convertFleetHealthMonitorDTOToRecords(v)
	case *models.CostAnalysisDashboardDTO:
		return s.convertCostAnalysisDashboardDTOToRecords(v)
	case *models.ComplianceDashboardDTO:
		return s.convertComplianceDashboardDTOToRecords(v)
	case *models.RouteAnalysisDTO:
		return s.convertRouteAnalysisDTOToRecords(v)
	case *models.PredictiveMaintenanceDTO:
		return s.convertPredictiveMaintenanceDTOToRecords(v)
	case *models.FleetOptimizationDTO:
		return s.convertFleetOptimizationDTOToRecords(v)
	case *models.DriverTrainingNeedsDTO:
		return s.convertDriverTrainingNeedsDTOToRecords(v)
	case *models.EnvironmentalDashboardDTO:
		return s.convertEnvironmentalDashboardDTOToRecords(v)
	case *models.RouteDeviationDTO:
		return s.convertRouteDeviationDTOToRecords(v)
	case *models.MileageMonthSummaryDTO:
		return s.convertMileageMonthSummaryDTOToRecords(v)
	case *models.VehicleUtilizationDTO:
		return s.convertVehicleUtilizationDTOToRecords(v)
	case *models.TripDetailDeltaDTO:
		return s.convertTripDetailDeltaDTOToRecords(v)
	case *models.PositionLogDriverDTO:
		return s.convertPositionLogDriverDTOToRecords(v)
	case *models.TripMileageDTO:
		return s.convertTripMileageDTOToRecords(v)
	case *models.LastLocationDTO:
		return s.convertLastLocationDTOToRecords(v)
	case *models.DoorDetailDTO:
		return s.convertDoorDetailDTOToRecords(v)

	default:
		// Try to handle DTOs that might be passed as interface{}
		if dto, ok := data.(*models.PositionLogDTO); ok {
			return s.convertPositionLogDTOToRecords(dto)
		}
		if dto, ok := data.(*models.TripDetailDTO); ok {
			return s.convertTripDetailDTOToRecords(dto)
		}
		if dto, ok := data.(*models.DriverSafetyScorecardDTO); ok {
			return s.convertDriverSafetyScorecardDTOToRecords(dto)
		}
		if dto, ok := data.(*models.FuelEstimationDTO); ok {
			return s.convertFuelEstimationDTOToRecords(dto)
		}
		if dto, ok := data.(*models.DriverBehaviorAnalysisDTO); ok {
			return s.convertDriverBehaviorAnalysisDTOToRecords(dto)
		}
		if dto, ok := data.(*models.FuelEfficiencyTrendsDTO); ok {
			return s.convertFuelEfficiencyTrendsDTOToRecords(dto)
		}
		if dto, ok := data.(*models.FleetProductivityDTO); ok {
			return s.convertFleetProductivityDTOToRecords(dto)
		}
		if dto, ok := data.(*models.RealTimeDashboardDTO); ok {
			return s.convertRealTimeDashboardDTOToRecords(dto)
		}
		if dto, ok := data.(*models.DriverScorecardDTO); ok {
			return s.convertDriverScorecardDTOToRecords(dto)
		}
		if dto, ok := data.(*models.FleetHealthMonitorDTO); ok {
			return s.convertFleetHealthMonitorDTOToRecords(dto)
		}
		if dto, ok := data.(*models.CostAnalysisDashboardDTO); ok {
			return s.convertCostAnalysisDashboardDTOToRecords(dto)
		}
		if dto, ok := data.(*models.ComplianceDashboardDTO); ok {
			return s.convertComplianceDashboardDTOToRecords(dto)
		}
		if dto, ok := data.(*models.RouteAnalysisDTO); ok {
			return s.convertRouteAnalysisDTOToRecords(dto)
		}
		if dto, ok := data.(*models.PredictiveMaintenanceDTO); ok {
			return s.convertPredictiveMaintenanceDTOToRecords(dto)
		}
		if dto, ok := data.(*models.FleetOptimizationDTO); ok {
			return s.convertFleetOptimizationDTOToRecords(dto)
		}
		if dto, ok := data.(*models.DriverTrainingNeedsDTO); ok {
			return s.convertDriverTrainingNeedsDTOToRecords(dto)
		}
		if dto, ok := data.(*models.EnvironmentalDashboardDTO); ok {
			return s.convertEnvironmentalDashboardDTOToRecords(dto)
		}
		if dto, ok := data.(*models.RouteDeviationDTO); ok {
			return s.convertRouteDeviationDTOToRecords(dto)
		}
		if dto, ok := data.(*models.MileageMonthSummaryDTO); ok {
			return s.convertMileageMonthSummaryDTOToRecords(dto)
		}
		if dto, ok := data.(*models.VehicleUtilizationDTO); ok {
			return s.convertVehicleUtilizationDTOToRecords(dto)
		}
		// For any other type, try to convert via JSON marshaling
		jsonData, err := json.Marshal(data)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to marshal data: %v", err)
		}

		var records []map[string]interface{}
		err = json.Unmarshal(jsonData, &records)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to unmarshal data: %v", err)
		}

		if len(records) == 0 {
			return nil, nil, nil
		}

		// Extract headers from first record
		headers := make([]string, 0, len(records[0]))
		for key := range records[0] {
			headers = append(headers, key)
		}
		return records, headers, nil
	}

	return nil, nil, fmt.Errorf("unsupported data type: %T", data)
}

// Helper functions for specific model conversions
func (s *ReportExportService) convertTripsToRecords(trips []models.Trip) ([]map[string]interface{}, []string, error) {
	headers := []string{"ID", "Client Device ID", "Driver ID", "Start Time", "End Time", "Distance", "Duration", "Avg Speed", "Max Speed", "Idle Time"}
	records := make([]map[string]interface{}, len(trips))

	for i, trip := range trips {
		records[i] = map[string]interface{}{
			"ID":               trip.Id,
			"Client Device ID": trip.ClientDeviceId,
			"Driver ID":        trip.DriverId,
			"Start Time":       trip.StartTime.Format("2006-01-02 15:04:05"),
			"End Time":         s.formatTimePtr(trip.EndTime),
			"Distance":         fmt.Sprintf("%.2f km", trip.Distance),
			"Duration":         s.formatDurationPtr(trip.Duration),
			"Avg Speed":        s.formatFloatPtr(trip.AvgSpeed, "%.1f km/h"),
			"Max Speed":        s.formatFloatPtr(trip.MaxSpeed, "%.1f km/h"),
			"Idle Time":        s.formatDurationPtr(trip.IdleTime),
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertBehaviorEventsToRecords(events []models.DrivingBehaviorEvent) ([]map[string]interface{}, []string, error) {
	headers := []string{"ID", "Client Device ID", "Driver ID", "Event Type", "Timestamp", "Latitude", "Longitude", "Speed", "Severity"}
	records := make([]map[string]interface{}, len(events))

	for i, event := range events {
		records[i] = map[string]interface{}{
			"ID":               event.Id,
			"Client Device ID": event.ClientDeviceId,
			"Driver ID":        event.DriverId,
			"Event Type":       event.EventType,
			"Timestamp":        event.Timestamp.Format("2006-01-02 15:04:05"),
			"Latitude":         fmt.Sprintf("%.6f", event.Latitude),
			"Longitude":        fmt.Sprintf("%.6f", event.Longitude),
			"Speed":            s.formatFloatPtr(event.Speed, "%.1f km/h"),
			"Severity":         s.formatFloatPtr(event.Severity, "%.1f"),
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertGPSDataToRecords(gpsData []models.GPSData) ([]map[string]interface{}, []string, error) {
	headers := []string{"ID", "Client Device ID", "Timestamp", "Latitude", "Longitude", "Speed", "Location"}
	records := make([]map[string]interface{}, len(gpsData))

	for i, gps := range gpsData {
		records[i] = map[string]interface{}{
			"ID":               gps.Id,
			"Client Device ID": s.formatUintPtr(gps.ClientDeviceId),
			"Timestamp":        s.formatTimePtr(gps.GPSTimestamp),
			"Latitude":         fmt.Sprintf("%.6f", gps.Latitude),
			"Longitude":        fmt.Sprintf("%.6f", gps.Longitude),
			"Speed":            s.formatFloatPtr(gps.Speed, "%.1f km/h"),
			"Location":         s.formatStringPtr(gps.LocationName),
		}
	}

	return records, headers, nil
}

// DTO conversion functions
func (s *ReportExportService) convertPositionLogDTOToRecords(dto *models.PositionLogDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, dto.Headers, nil
	}

	records := make([]map[string]interface{}, len(dto.Data))
	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Timestamp":     row.Timestamp,
			"Name":          row.Name,
			"Driver Name":   row.DriverName,
			"Latitude":      fmt.Sprintf("%.6f", row.Latitude),
			"Longitude":     fmt.Sprintf("%.6f", row.Longitude),
			"Location Name": row.LocationName,
			"Speed (km/h)":  fmt.Sprintf("%.1f", row.Speed),
			"Direction":     row.Direction,
			"Engine Status": row.EngineStatus,
		}
	}

	return records, dto.Headers, nil
}

func (s *ReportExportService) convertTripDetailDTOToRecords(dto *models.TripDetailDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, dto.Headers, nil
	}

	records := make([]map[string]interface{}, len(dto.Data))
	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Trip ID":           row.TripID,
			"Device Name":       row.DeviceName,
			"Plate Number":      row.PlateNumber,
			"Driver Name":       row.DriverName,
			"Start Time":        row.StartTime,
			"End Time":          row.EndTime,
			"Duration":          row.Duration,
			"Distance (km)":     fmt.Sprintf("%.2f", row.Distance),
			"Avg Speed (km/h)":  fmt.Sprintf("%.1f", row.AvgSpeed),
			"Max Speed (km/h)":  fmt.Sprintf("%.1f", row.MaxSpeed),
			"Start Location":    row.StartLocation,
			"End Location":      row.EndLocation,
			"Fuel Consumed (L)": fmt.Sprintf("%.2f", row.FuelConsumed),
			"Fuel Cost":         fmt.Sprintf("%.2f", row.FuelCost),
			"Status":            row.Status,
		}
	}

	return records, dto.Headers, nil
}

func (s *ReportExportService) convertDriverSafetyScorecardDTOToRecords(dto *models.DriverSafetyScorecardDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, dto.Headers, nil
	}

	records := make([]map[string]interface{}, len(dto.Data))
	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Driver Name":         row.DriverName,
			"Device Name":         row.DeviceName,
			"Plate Number":        row.PlateNumber,
			"Safety Score":        fmt.Sprintf("%.1f", row.SafetyScore),
			"Speeding Events":     row.SpeedingEvents,
			"Harsh Braking":       row.HarshBraking,
			"Harsh Acceleration":  row.HarshAcceleration,
			"Harsh Cornering":     row.HarshCornering,
			"Idle Time":           row.IdleTime,
			"Total Trips":         row.TotalTrips,
			"Total Distance (km)": fmt.Sprintf("%.2f", row.TotalDistance),
		}
	}

	return records, dto.Headers, nil
}

// Helper functions
func (s *ReportExportService) getExcelColumn(index int) string {
	column := ""
	for index >= 0 {
		column = string(rune('A'+index%26)) + column
		index = index/26 - 1
	}
	return column
}

func (s *ReportExportService) formatTimePtr(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02 15:04:05")
}

func (s *ReportExportService) formatDurationPtr(d *int) string {
	if d == nil {
		return ""
	}
	duration := time.Duration(*d) * time.Second
	return duration.String()
}

func (s *ReportExportService) formatFloatPtr(f *float64, format string) string {
	if f == nil {
		return ""
	}
	return fmt.Sprintf(format, *f)
}

func (s *ReportExportService) formatUintPtr(u *uint) string {
	if u == nil {
		return ""
	}
	return fmt.Sprintf("%d", *u)
}

func (s *ReportExportService) formatStringPtr(str *string) string {
	if str == nil {
		return ""
	}
	return *str
}

func (s *ReportExportService) addSummarySheet(f *excelize.File, summary interface{}) {
	sheetName := "Summary"
	f.NewSheet(sheetName)

	// Convert summary to key-value pairs
	summaryMap, ok := summary.(map[string]interface{})
	if !ok {
		return
	}

	row := 1
	for key, value := range summaryMap {
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), key)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), value)
		row++
	}
}

// createReportHTMLTemplate creates an HTML template for PDF generation
func (s *ReportExportService) createReportHTMLTemplate() string {
	return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{.ReportInfo.Name}}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            color: #333;
            font-size: 12px;
        }
        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-bottom: 2px solid #007bff;
            margin-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            color: #007bff;
            font-size: 24px;
        }
        .header p {
            margin: 5px 0;
            color: #666;
        }
        .metadata {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .metadata table {
            width: 100%;
            border-collapse: collapse;
        }
        .metadata td {
            padding: 5px 10px;
            border: none;
        }
        .metadata .label {
            font-weight: bold;
            width: 150px;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .data-table th,
        .data-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .summary {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .summary h3 {
            margin-top: 0;
            color: #155724;
        }
        .no-data {
            text-align: center;
            color: #6c757d;
            font-style: italic;
            padding: 40px;
        }
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            color: #6c757d;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{.ReportInfo.Name}}</h1>
        <p>{{.ReportInfo.Description}}</p>
    </div>

    <div class="metadata">
        <table>
            <tr>
                <td class="label">Generated:</td>
                <td>{{.Metadata.GeneratedAt.Format "2006-01-02 15:04:05"}}</td>
                <td class="label">Total Records:</td>
                <td>{{.Metadata.TotalRecords}}</td>
            </tr>
            <tr>
                <td class="label">Filtered Records:</td>
                <td>{{.Metadata.FilteredRecords}}</td>
                <td class="label">Execution Time:</td>
                <td>{{.Metadata.ExecutionTime}}</td>
            </tr>
            <tr>
                <td class="label">Format:</td>
                <td>{{.Metadata.Format}}</td>
                <td class="label">Category:</td>
                <td>{{.ReportInfo.Category}}</td>
            </tr>
        </table>
    </div>

    {{if .Summary}}
    <div class="summary">
        <h3>Summary</h3>
        {{range $key, $value := .Summary}}
        <p><strong>{{$key}}:</strong> {{$value}}</p>
        {{end}}
    </div>
    {{end}}

    <div class="data-section">
        <h3>Report Data</h3>
        {{if .Data}}
            {{$dataType := printf "%T" .Data}}
            {{if eq $dataType "*models.PositionLogDTO"}}
                <!-- DTO format with headers and data -->
                <table class="data-table">
                    <thead>
                        <tr>
                            {{range .Data.Headers}}
                            <th>{{.}}</th>
                            {{end}}
                        </tr>
                    </thead>
                    <tbody>
                        {{range .Data.Data}}
                        <tr>
                            <td>{{.Timestamp}}</td>
                            <td>{{.Name}}</td>
                            <td>{{.DriverName}}</td>
                            <td>{{.Latitude}}</td>
                            <td>{{.Longitude}}</td>
                            <td>{{.LocationName}}</td>
                            <td>{{.Speed}}</td>
                            <td>{{.Direction}}</td>
                            <td>{{.EngineStatus}}</td>
                        </tr>
                        {{end}}
                    </tbody>
                </table>
            {{else}}
                <!-- Legacy format or simple data -->
                <p>Data records: {{.Metadata.FilteredRecords}}</p>
                <p>Data type: {{$dataType}}</p>
                <p>Data available: {{if .Data}}Yes{{else}}No{{end}}</p>
            {{end}}
        {{else}}
            <div class="no-data">No data available for the selected criteria</div>
        {{end}}
    </div>

    <div class="footer">
        <p>Generated by YoTracker Fleet Management System | {{.Metadata.GeneratedAt.Format "2006-01-02 15:04:05"}}</p>
    </div>
</body>
</html>`
}

// ============================================================================
// DTO CONVERSION FUNCTIONS
// ============================================================================

func (s *ReportExportService) convertFuelEstimationDTOToRecords(dto *models.FuelEstimationDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Month":                 row.Month,
			"Device Name":           row.DeviceName,
			"Plate Number":          row.PlateNumber,
			"Total Distance (km)":   fmt.Sprintf("%.2f", row.TotalDistance),
			"Running Hours":         fmt.Sprintf("%.2f", row.RunningHours),
			"Avg Speed (km/h)":      fmt.Sprintf("%.2f", row.AvgSpeed),
			"Total Trips":           row.TotalTrips,
			"Estimated Fuel (L)":    fmt.Sprintf("%.2f", row.EstimatedFuelConsumption),
			"Estimated Cost":        fmt.Sprintf("%.2f", row.EstimatedFuelCost),
			"Efficiency (km/h)":     fmt.Sprintf("%.2f", row.EfficiencyKmPerHour),
			"Fuel Efficiency (L/h)": fmt.Sprintf("%.2f", row.FuelEfficiencyLph),
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertDriverBehaviorAnalysisDTOToRecords(dto *models.DriverBehaviorAnalysisDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Month":                  row.Month,
			"Driver Name":            row.DriverName,
			"Device Name":            row.DeviceName,
			"Plate Number":           row.PlateNumber,
			"Behavior Score":         fmt.Sprintf("%.2f", row.BehaviorScore),
			"Speeding Events":        row.SpeedingEvents,
			"Harsh Braking":          row.HarshBrakingEvents,
			"Harsh Acceleration":     row.HarshAccelEvents,
			"Harsh Cornering":        row.HarshCorneringEvents,
			"Idle Time (%)":          fmt.Sprintf("%.2f", row.IdleTimePercentage),
			"Fuel Efficiency (km/L)": fmt.Sprintf("%.2f", row.FuelEfficiency),
			"Safety Violations":      row.SafetyViolations,
			"Risk Level":             row.RiskLevel,
			"Improvement Areas":      row.ImprovementAreas,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertFuelEfficiencyTrendsDTOToRecords(dto *models.FuelEfficiencyTrendsDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Month":               row.Month,
			"Device Name":         row.DeviceName,
			"Plate Number":        row.PlateNumber,
			"Driver Name":         row.DriverName,
			"Total Distance (km)": fmt.Sprintf("%.2f", row.TotalDistance),
			"Fuel Consumed (L)":   fmt.Sprintf("%.2f", row.FuelConsumed),
			"Efficiency (km/L)":   fmt.Sprintf("%.2f", row.EfficiencyKmPerL),
			"Efficiency (L/h)":    fmt.Sprintf("%.2f", row.EfficiencyLph),
			"Avg Speed (km/h)":    fmt.Sprintf("%.2f", row.AvgSpeed),
			"Idle Time":           row.IdleTime,
			"Cost per km":         fmt.Sprintf("%.2f", row.CostPerKm),
			"Trend":               row.Trend,
			"Improvement (%)":     fmt.Sprintf("%.2f", row.Improvement),
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertFleetProductivityDTOToRecords(dto *models.FleetProductivityDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Month":                  row.Month,
			"Total Vehicles":         row.TotalVehicles,
			"Active Vehicles":        row.ActiveVehicles,
			"Total Trips":            row.TotalTrips,
			"Total Distance (km)":    fmt.Sprintf("%.2f", row.TotalDistance),
			"Total Hours":            row.TotalHours,
			"Productive Hours":       row.ProductiveHours,
			"Productivity Rate (%)":  fmt.Sprintf("%.2f", row.ProductivityRate),
			"Avg Utilization (%)":    fmt.Sprintf("%.2f", row.AvgUtilization),
			"Fuel Efficiency (km/L)": fmt.Sprintf("%.2f", row.FuelEfficiency),
			"Cost per km":            fmt.Sprintf("%.2f", row.CostPerKm),
			"Productivity Score":     fmt.Sprintf("%.2f", row.ProductivityScore),
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertRealTimeDashboardDTOToRecords(dto *models.RealTimeDashboardDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Device Name":    row.DeviceName,
			"Plate Number":   row.PlateNumber,
			"Driver Name":    row.DriverName,
			"Current Status": row.CurrentStatus,
			"Location":       row.Location,
			"Speed (km/h)":   fmt.Sprintf("%.2f", row.Speed),
			"Engine Status":  row.EngineStatus,
			"Last Update":    row.LastUpdate,
			"Current Trip":   row.CurrentTrip,
			"Fuel Level (%)": fmt.Sprintf("%.2f", row.FuelLevel),
			"Alerts":         row.Alerts,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertDriverScorecardDTOToRecords(dto *models.DriverScorecardDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Driver Name":            row.DriverName,
			"Device Name":            row.DeviceName,
			"Plate Number":           row.PlateNumber,
			"Overall Score":          fmt.Sprintf("%.2f", row.OverallScore),
			"Safety Score":           fmt.Sprintf("%.2f", row.SafetyScore),
			"Efficiency Score":       fmt.Sprintf("%.2f", row.EfficiencyScore),
			"Compliance Score":       fmt.Sprintf("%.2f", row.ComplianceScore),
			"Total Trips":            row.TotalTrips,
			"Total Distance (km)":    fmt.Sprintf("%.2f", row.TotalDistance),
			"Avg Speed (km/h)":       fmt.Sprintf("%.2f", row.AvgSpeed),
			"Fuel Efficiency (km/L)": fmt.Sprintf("%.2f", row.FuelEfficiency),
			"Violations":             row.Violations,
			"Risk Level":             row.RiskLevel,
			"Ranking":                row.Ranking,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertFleetHealthMonitorDTOToRecords(dto *models.FleetHealthMonitorDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Device Name":          row.DeviceName,
			"Plate Number":         row.PlateNumber,
			"Health Score":         fmt.Sprintf("%.2f", row.HealthScore),
			"Status":               row.Status,
			"Last Maintenance":     row.LastMaintenance,
			"Next Maintenance":     row.NextMaintenance,
			"Current Mileage (km)": fmt.Sprintf("%.2f", row.CurrentMileage),
			"Engine Hours":         fmt.Sprintf("%.2f", row.EngineHours),
			"Battery Status":       row.BatteryStatus,
			"Fault Codes":          row.FaultCodes,
			"Health Level":         row.HealthLevel,
			"Recommendations":      row.Recommendations,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertCostAnalysisDashboardDTOToRecords(dto *models.CostAnalysisDashboardDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Device Name":             row.DeviceName,
			"Plate Number":            row.PlateNumber,
			"Total Cost":              fmt.Sprintf("%.2f", row.TotalCost),
			"Fuel Cost":               fmt.Sprintf("%.2f", row.FuelCost),
			"Maintenance Cost":        fmt.Sprintf("%.2f", row.MaintenanceCost),
			"Insurance Cost":          fmt.Sprintf("%.2f", row.InsuranceCost),
			"Depreciation Cost":       fmt.Sprintf("%.2f", row.DepreciationCost),
			"Total Distance (km)":     fmt.Sprintf("%.2f", row.TotalDistance),
			"Cost per km":             fmt.Sprintf("%.2f", row.CostPerKm),
			"Fuel Cost per km":        fmt.Sprintf("%.2f", row.FuelCostPerKm),
			"Maintenance Cost per km": fmt.Sprintf("%.2f", row.MaintenanceCostPerKm),
			"ROI (%)":                 fmt.Sprintf("%.2f", row.ROI),
			"Cost Efficiency":         row.CostEfficiency,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertComplianceDashboardDTOToRecords(dto *models.ComplianceDashboardDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Driver Name":          row.DriverName,
			"Device Name":          row.DeviceName,
			"Plate Number":         row.PlateNumber,
			"Compliance Score":     fmt.Sprintf("%.2f", row.ComplianceScore),
			"Hours of Service":     row.HoursOfService,
			"Rest Compliance":      row.RestCompliance,
			"Speed Compliance":     row.SpeedCompliance,
			"Safety Violations":    row.SafetyViolations,
			"License Status":       row.LicenseStatus,
			"Certification Status": row.CertificationStatus,
			"Last Audit":           row.LastAudit,
			"Risk Level":           row.RiskLevel,
			"Compliance Status":    row.ComplianceStatus,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertRouteAnalysisDTOToRecords(dto *models.RouteAnalysisDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Route Name":             row.RouteName,
			"Device Name":            row.DeviceName,
			"Plate Number":           row.PlateNumber,
			"Driver Name":            row.DriverName,
			"Total Trips":            row.TotalTrips,
			"Avg Distance (km)":      fmt.Sprintf("%.2f", row.AvgDistance),
			"Avg Duration":           row.AvgDuration,
			"Avg Speed (km/h)":       fmt.Sprintf("%.2f", row.AvgSpeed),
			"Fuel Efficiency (km/L)": fmt.Sprintf("%.2f", row.FuelEfficiency),
			"Traffic Delays":         row.TrafficDelays,
			"Route Efficiency (%)":   fmt.Sprintf("%.2f", row.RouteEfficiency),
			"Optimization Score":     fmt.Sprintf("%.2f", row.OptimizationScore),
			"Recommendations":        row.Recommendations,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertPredictiveMaintenanceDTOToRecords(dto *models.PredictiveMaintenanceDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Device Name":        row.DeviceName,
			"Plate Number":       row.PlateNumber,
			"Component":          row.Component,
			"Current Health (%)": fmt.Sprintf("%.2f", row.CurrentHealth),
			"Predicted Failure":  row.PredictedFailure,
			"Risk Level":         row.RiskLevel,
			"Last Inspection":    row.LastInspection,
			"Next Inspection":    row.NextInspection,
			"Maintenance Cost":   fmt.Sprintf("%.2f", row.MaintenanceCost),
			"Downtime Risk":      row.DowntimeRisk,
			"Recommendations":    row.Recommendations,
			"Priority":           row.Priority,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertFleetOptimizationDTOToRecords(dto *models.FleetOptimizationDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Device Name":          row.DeviceName,
			"Plate Number":         row.PlateNumber,
			"Utilization Rate (%)": fmt.Sprintf("%.2f", row.UtilizationRate),
			"Efficiency Score":     fmt.Sprintf("%.2f", row.EfficiencyScore),
			"Cost per km":          fmt.Sprintf("%.2f", row.CostPerKm),
			"ROI (%)":              fmt.Sprintf("%.2f", row.ROI),
			"Optimization Score":   fmt.Sprintf("%.2f", row.OptimizationScore),
			"Recommendations":      row.Recommendations,
			"Right Sizing":         row.RightSizing,
			"Replacement Value":    fmt.Sprintf("%.2f", row.ReplacementValue),
			"Savings Potential":    fmt.Sprintf("%.2f", row.SavingsPotential),
			"Status":               row.Status,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertDriverTrainingNeedsDTOToRecords(dto *models.DriverTrainingNeedsDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Driver Name":          row.DriverName,
			"Device Name":          row.DeviceName,
			"Plate Number":         row.PlateNumber,
			"Safety Score":         fmt.Sprintf("%.2f", row.SafetyScore),
			"Violations":           row.Violations,
			"Training Priority":    row.TrainingPriority,
			"Skill Gaps":           row.SkillGaps,
			"Recommended Training": row.RecommendedTraining,
			"Last Training":        row.LastTraining,
			"Training Due":         row.TrainingDue,
			"Risk Level":           row.RiskLevel,
			"Improvement Areas":    row.ImprovementAreas,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertEnvironmentalDashboardDTOToRecords(dto *models.EnvironmentalDashboardDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Month":                    row.Month,
			"Device Name":              row.DeviceName,
			"Plate Number":             row.PlateNumber,
			"Fuel Consumed (L)":        fmt.Sprintf("%.2f", row.FuelConsumed),
			"CO2 Emissions (kg)":       fmt.Sprintf("%.2f", row.CO2Emissions),
			"Carbon Footprint (kg/km)": fmt.Sprintf("%.2f", row.CarbonFootprint),
			"Fuel Efficiency (km/L)":   fmt.Sprintf("%.2f", row.FuelEfficiency),
			"Idle Time":                row.IdleTime,
			"Idle Emissions (kg)":      fmt.Sprintf("%.2f", row.IdleEmissions),
			"Total Distance (km)":      fmt.Sprintf("%.2f", row.TotalDistance),
			"Environmental Score":      fmt.Sprintf("%.2f", row.EnvironmentalScore),
			"Sustainability Level":     row.SustainabilityLevel,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertDoorDetailDTOToRecords(dto *models.DoorDetailDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Device Name":    row.DeviceName,
			"Plate Number":   row.PlateNumber,
			"Driver Name":    row.DriverName,
			"Event Type":     row.EventType,
			"Timestamp":      row.Timestamp,
			"Location":       row.Location,
			"Coordinates":    row.Coordinates,
			"Duration":       row.Duration,
			"Severity":       fmt.Sprintf("%.2f", row.Severity),
			"Security Level": row.SecurityLevel,
			"Access Type":    row.AccessType,
			"Notes":          row.Notes,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertRouteDeviationDTOToRecords(dto *models.RouteDeviationDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Trip ID":                row.TripID,
			"Device Name":            row.DeviceName,
			"Plate Number":           row.PlateNumber,
			"Driver Name":            row.DriverName,
			"Start Time":             row.StartTime,
			"End Time":               row.EndTime,
			"Start Location":         row.StartLocation,
			"End Location":           row.EndLocation,
			"Actual Distance (km)":   fmt.Sprintf("%.2f", row.ActualDistance),
			"Expected Distance (km)": fmt.Sprintf("%.2f", row.ExpectedDistance),
			"Deviation (km)":         fmt.Sprintf("%.2f", row.Deviation),
			"Deviation (%)":          fmt.Sprintf("%.2f", row.DeviationPercent),
			"Route Efficiency (%)":   fmt.Sprintf("%.2f", row.RouteEfficiency),
			"Common Route":           row.CommonRoute,
			"Optimization Score":     fmt.Sprintf("%.2f", row.OptimizationScore),
			"Recommendations":        row.Recommendations,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertMileageMonthSummaryDTOToRecords(dto *models.MileageMonthSummaryDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Month":                     row.Month,
			"Device Name":               row.DeviceName,
			"Plate Number":              row.PlateNumber,
			"Driver Name":               row.DriverName,
			"Total Mileage (km)":        fmt.Sprintf("%.2f", row.TotalMileage),
			"Total Trips":               row.TotalTrips,
			"Avg Mileage per Trip (km)": fmt.Sprintf("%.2f", row.AvgMileagePerTrip),
			"Total Fuel Cost":           fmt.Sprintf("%.2f", row.TotalFuelCost),
			"Cost per km":               fmt.Sprintf("%.2f", row.CostPerKm),
			"Maintenance Due":           row.MaintenanceDue,
			"Efficiency (km/L)":         fmt.Sprintf("%.2f", row.Efficiency),
			"Recommendations":           row.Recommendations,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertVehicleUtilizationDTOToRecords(dto *models.VehicleUtilizationDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Device Name":          row.DeviceName,
			"Plate Number":         row.PlateNumber,
			"Driver Name":          row.DriverName,
			"Total Hours":          row.TotalHours,
			"Active Hours":         row.ActiveHours,
			"Idle Hours":           row.IdleHours,
			"Utilization Rate (%)": fmt.Sprintf("%.2f", row.UtilizationRate),
			"Total Trips":          row.TotalTrips,
			"Total Distance (km)":  fmt.Sprintf("%.2f", row.TotalDistance),
			"Avg Speed (km/h)":     fmt.Sprintf("%.2f", row.AvgSpeed),
			"Efficiency (km/L)":    fmt.Sprintf("%.2f", row.Efficiency),
			"Status":               row.Status,
			"Idle Time (%)":        fmt.Sprintf("%.2f", row.IdleTimePercent),
			"Productivity Score":   fmt.Sprintf("%.2f", row.ProductivityScore),
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertTripDetailDeltaDTOToRecords(dto *models.TripDetailDeltaDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Trip ID":                row.TripID,
			"Device Name":            row.DeviceName,
			"Plate Number":           row.PlateNumber,
			"Driver Name":            row.DriverName,
			"Start Time":             row.StartTime,
			"End Time":               row.EndTime,
			"Duration":               row.Duration,
			"Distance (km)":          fmt.Sprintf("%.2f", row.Distance),
			"Avg Speed (km/h)":       fmt.Sprintf("%.2f", row.AvgSpeed),
			"Max Speed (km/h)":       fmt.Sprintf("%.2f", row.MaxSpeed),
			"Start Location":         row.StartLocation,
			"End Location":           row.EndLocation,
			"Previous Distance (km)": fmt.Sprintf("%.2f", row.PreviousDistance),
			"Distance Delta (km)":    fmt.Sprintf("%.2f", row.DistanceDelta),
			"Delta (%)":              fmt.Sprintf("%.2f", row.DeltaPercentage),
			"Improvement":            row.Improvement,
			"Trend":                  row.Trend,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertPositionLogDriverDTOToRecords(dto *models.PositionLogDriverDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Timestamp":     row.Timestamp,
			"Driver Name":   row.DriverName,
			"Device Name":   row.DeviceName,
			"Plate Number":  row.PlateNumber,
			"Latitude":      fmt.Sprintf("%.6f", row.Latitude),
			"Longitude":     fmt.Sprintf("%.6f", row.Longitude),
			"Location Name": row.LocationName,
			"Speed (km/h)":  fmt.Sprintf("%.2f", row.Speed),
			"Direction":     row.Direction,
			"Engine Status": row.EngineStatus,
			"Current Trip":  row.CurrentTrip,
			"Driver Status": row.DriverStatus,
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertTripMileageDTOToRecords(dto *models.TripMileageDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Trip ID":                row.TripID,
			"Device Name":            row.DeviceName,
			"Plate Number":           row.PlateNumber,
			"Driver Name":            row.DriverName,
			"Start Time":             row.StartTime,
			"End Time":               row.EndTime,
			"Duration":               row.Duration,
			"Distance (km)":          fmt.Sprintf("%.2f", row.Distance),
			"Avg Speed (km/h)":       fmt.Sprintf("%.2f", row.AvgSpeed),
			"Max Speed (km/h)":       fmt.Sprintf("%.2f", row.MaxSpeed),
			"Start Location":         row.StartLocation,
			"End Location":           row.EndLocation,
			"Mileage Efficiency (%)": fmt.Sprintf("%.2f", row.MileageEfficiency),
			"Fuel Consumed (L)":      fmt.Sprintf("%.2f", row.FuelConsumed),
			"Cost per km":            fmt.Sprintf("%.2f", row.CostPerKm),
			"Efficiency (km/L)":      fmt.Sprintf("%.2f", row.Efficiency),
		}
	}

	return records, headers, nil
}

func (s *ReportExportService) convertLastLocationDTOToRecords(dto *models.LastLocationDTO) ([]map[string]interface{}, []string, error) {
	if dto == nil || len(dto.Data) == 0 {
		return nil, nil, nil
	}

	headers := dto.Headers
	records := make([]map[string]interface{}, len(dto.Data))

	for i, row := range dto.Data {
		records[i] = map[string]interface{}{
			"Device Name":     row.DeviceName,
			"Plate Number":    row.PlateNumber,
			"Driver Name":     row.DriverName,
			"Last Location":   row.LastLocation,
			"Latitude":        fmt.Sprintf("%.6f", row.Latitude),
			"Longitude":       fmt.Sprintf("%.6f", row.Longitude),
			"Last Seen":       row.LastSeen,
			"Status":          row.Status,
			"Current Trip":    row.CurrentTrip,
			"Speed (km/h)":    fmt.Sprintf("%.2f", row.Speed),
			"Direction":       row.Direction,
			"Engine Status":   row.EngineStatus,
			"Battery Level":   row.BatteryLevel,
			"Signal Strength": row.SignalStrength,
		}
	}

	return records, headers, nil
}
