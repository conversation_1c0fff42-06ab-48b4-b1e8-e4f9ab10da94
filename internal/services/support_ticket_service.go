package services

import (
	"errors"
	"fmt"
	"log"
	"os"
	"strings"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

type SupportTicketService struct{}

func NewSupportTicketService() *SupportTicketService {
	return &SupportTicketService{}
}

// GetSupportTickets returns all support tickets with filtering and pagination
func (s *SupportTicketService) GetSupportTickets(filters models.SupportTicketFilters) ([]models.SupportTicket, int64, error) {
	var tickets []models.SupportTicket
	var total int64

	query := config.DB.Preload("Client").Preload("CreatedBy").Preload("AssignedTo").Preload("ClientDevice")

	// Apply filters
	if filters.ClientId > 0 {
		query = query.Where("client_id = ?", filters.ClientId)
	}
	if filters.Department != "" {
		query = query.Where("department = ?", filters.Department)
	}
	if filters.Priority != "" {
		query = query.Where("priority = ?", filters.Priority)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.AssignedToId != nil {
		query = query.Where("assigned_to_id = ?", *filters.AssignedToId)
	}
	if filters.ClientDeviceId != nil {
		query = query.Where("client_device_id = ?", *filters.ClientDeviceId)
	}
	if filters.CreatedById != nil {
		query = query.Where("created_by_id = ?", *filters.CreatedById)
	}

	// Get total count
	query.Model(&models.SupportTicket{}).Count(&total)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := (filters.Page - 1) * filters.PerPage
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	// Order by priority and creation date
	query = query.Order("CASE priority WHEN 'urgent' THEN 1 WHEN 'high' THEN 2 WHEN 'medium' THEN 3 WHEN 'low' THEN 4 END, created_at DESC")

	err := query.Find(&tickets).Error
	return tickets, total, err
}

// GetSupportTicket returns a specific support ticket with replies
func (s *SupportTicketService) GetSupportTicket(id uint, clientId uint) (*models.SupportTicket, error) {
	var ticket models.SupportTicket
	err := config.DB.Preload("Client").Preload("CreatedBy").Preload("AssignedTo").Preload("ClientDevice").
		Where("id = ? AND client_id = ?", id, clientId).First(&ticket).Error
	if err != nil {
		return nil, err
	}
	return &ticket, nil
}

// GetSupportTicketReplies returns all replies for a ticket (filters out internal replies for clients)
func (s *SupportTicketService) GetSupportTicketReplies(ticketId uint, clientId uint) ([]models.SupportTicketReply, error) {
	var replies []models.SupportTicketReply

	// First verify the ticket belongs to the client
	var ticket models.SupportTicket
	if err := config.DB.Where("id = ? AND client_id = ?", ticketId, clientId).First(&ticket).Error; err != nil {
		return nil, errors.New("ticket not found")
	}

	// Filter out internal replies for client access
	err := config.DB.Preload("CreatedBy").
		Where("ticket_id = ? AND is_internal = ?", ticketId, false).
		Order("created_at ASC").
		Find(&replies).Error

	return replies, err
}

// GetSupportTicketRepliesAdmin returns all replies for a ticket including internal ones (admin access)
func (s *SupportTicketService) GetSupportTicketRepliesAdmin(ticketId uint) ([]models.SupportTicketReply, error) {
	var replies []models.SupportTicketReply

	// Admin can access any ticket - no client restriction
	err := config.DB.Preload("CreatedBy").
		Where("ticket_id = ?", ticketId).
		Order("created_at ASC").
		Find(&replies).Error

	return replies, err
}

// CreateSupportTicket creates a new support ticket
func (s *SupportTicketService) CreateSupportTicket(ticket models.SupportTicket) (*models.SupportTicket, error) {
	// Set default values
	if ticket.Priority == "" {
		ticket.Priority = "medium"
	}
	if ticket.Status == "" {
		ticket.Status = "open"
	}

	err := config.DB.Create(&ticket).Error
	if err != nil {
		return nil, err
	}

	// Load the created ticket with relations
	var createdTicket models.SupportTicket
	err = config.DB.Preload("Client").Preload("CreatedBy").Preload("AssignedTo").Preload("ClientDevice").
		First(&createdTicket, ticket.Id).Error
	if err != nil {
		return nil, err
	}

	// Send notifications asynchronously
	go s.sendTicketCreatedNotifications(&createdTicket)

	return &createdTicket, nil
}

// UpdateSupportTicket updates an existing support ticket
func (s *SupportTicketService) UpdateSupportTicket(ticket models.SupportTicket) (*models.SupportTicket, error) {
	// Check if ticket exists and belongs to client
	existingTicket, err := s.GetSupportTicket(ticket.Id, ticket.ClientId)
	if err != nil {
		return nil, errors.New("ticket not found")
	}

	// Track status changes for notifications
	oldStatus := existingTicket.Status
	oldAssignedToId := existingTicket.AssignedToId

	// Update fields
	if ticket.AssignedToId != nil {
		existingTicket.AssignedToId = ticket.AssignedToId
	}
	if ticket.Priority != "" {
		existingTicket.Priority = ticket.Priority
	}
	if ticket.Status != "" {
		existingTicket.Status = ticket.Status
		// Set resolved/closed timestamps
		if ticket.Status == "resolved" && existingTicket.ResolvedAt == nil {
			now := time.Now()
			existingTicket.ResolvedAt = &now
		}
		if ticket.Status == "closed" && existingTicket.ClosedAt == nil {
			now := time.Now()
			existingTicket.ClosedAt = &now
		}
	}
	if ticket.Subject != "" {
		existingTicket.Subject = ticket.Subject
	}
	if ticket.Description != "" {
		existingTicket.Description = ticket.Description
	}
	if ticket.Resolution != nil {
		existingTicket.Resolution = ticket.Resolution
	}

	err = config.DB.Save(existingTicket).Error
	if err != nil {
		return nil, err
	}

	// Load updated ticket with relations
	var updatedTicket models.SupportTicket
	err = config.DB.Preload("Client").Preload("CreatedBy").Preload("AssignedTo").Preload("ClientDevice").
		First(&updatedTicket, ticket.Id).Error
	if err != nil {
		return nil, err
	}

	// Send notifications for status/assignment changes
	if oldStatus != updatedTicket.Status {
		go s.sendTicketStatusChangedNotifications(&updatedTicket, oldStatus)
	}
	if oldAssignedToId != updatedTicket.AssignedToId && updatedTicket.AssignedToId != nil {
		go s.sendTicketAssignedNotifications(&updatedTicket)
	}

	return &updatedTicket, nil
}

// ChangeSupportTicketStatus changes the status of a support ticket
func (s *SupportTicketService) ChangeSupportTicketStatus(ticketId uint, clientId uint, newStatus string) (*models.SupportTicket, error) {
	// Check if ticket exists and belongs to client
	existingTicket, err := s.GetSupportTicket(ticketId, clientId)
	if err != nil {
		return nil, errors.New("ticket not found")
	}

	// Track status change for notifications
	oldStatus := existingTicket.Status

	// Update status
	existingTicket.Status = newStatus

	// Set resolved/closed timestamps
	if newStatus == "resolved" && existingTicket.ResolvedAt == nil {
		now := time.Now()
		existingTicket.ResolvedAt = &now
	}
	if newStatus == "closed" && existingTicket.ClosedAt == nil {
		now := time.Now()
		existingTicket.ClosedAt = &now
	}

	err = config.DB.Save(existingTicket).Error
	if err != nil {
		return nil, err
	}

	// Load updated ticket with relations
	var updatedTicket models.SupportTicket
	err = config.DB.Preload("Client").Preload("CreatedBy").Preload("AssignedTo").Preload("ClientDevice").
		First(&updatedTicket, ticketId).Error
	if err != nil {
		return nil, err
	}

	// Send notifications for status changes
	if oldStatus != updatedTicket.Status {
		go s.sendTicketStatusChangedNotifications(&updatedTicket, oldStatus)
	}

	return &updatedTicket, nil
}

// AssignSupportTicket assigns a support ticket to a user
func (s *SupportTicketService) AssignSupportTicket(ticketId uint, assignedToId uint) (*models.SupportTicket, error) {
	// Check if ticket exists (admin can assign any ticket)
	var existingTicket models.SupportTicket
	err := config.DB.Where("id = ?", ticketId).First(&existingTicket).Error
	if err != nil {
		return nil, errors.New("ticket not found")
	}

	// Check if assigned user exists and is a staff member (not a client)
	var assignedUser models.User
	err = config.DB.Where("id = ? AND client_id IS NULL", assignedToId).First(&assignedUser).Error
	if err != nil {
		return nil, errors.New("assigned user not found or not a staff member")
	}

	// Track assignment change for notifications
	oldAssignedToId := existingTicket.AssignedToId

	// Update assignment using direct field updates to avoid GORM issues
	updates := map[string]interface{}{
		"assigned_to_id": assignedToId,
	}

	// Update status to "assigned" if it's currently "open"
	if existingTicket.Status == "open" {
		updates["status"] = "assigned"
	}

	err = config.DB.Model(&existingTicket).Updates(updates).Error
	if err != nil {
		return nil, err
	}

	// Load updated ticket with relations
	var updatedTicket models.SupportTicket
	err = config.DB.Preload("Client").Preload("CreatedBy").Preload("AssignedTo").Preload("ClientDevice").
		First(&updatedTicket, ticketId).Error
	if err != nil {
		return nil, err
	}

	// Send notifications for assignment changes
	if oldAssignedToId != updatedTicket.AssignedToId && updatedTicket.AssignedToId != nil {
		go s.sendTicketAssignedNotifications(&updatedTicket)
	}

	return &updatedTicket, nil
}

// CreateSupportTicketReply creates a new reply to a support ticket
func (s *SupportTicketService) CreateSupportTicketReply(reply models.SupportTicketReply) (*models.SupportTicketReply, error) {
	// Verify the ticket exists and belongs to the client
	var ticket models.SupportTicket
	err := config.DB.Where("id = ?", reply.TicketId).First(&ticket).Error
	if err != nil {
		return nil, errors.New("ticket not found")
	}

	err = config.DB.Create(&reply).Error
	if err != nil {
		return nil, err
	}

	// Load the created reply with relations
	var createdReply models.SupportTicketReply
	err = config.DB.Preload("CreatedBy").Preload("Ticket").Preload("Ticket.Client").
		First(&createdReply, reply.Id).Error
	if err != nil {
		return nil, err
	}

	// Send reply notifications asynchronously
	go s.sendTicketReplyNotifications(&createdReply)

	return &createdReply, nil
}

// GetSupportTicketStats returns ticket statistics
func (s *SupportTicketService) GetSupportTicketStats(clientId uint) (*models.SupportTicketStats, error) {
	var stats models.SupportTicketStats

	query := config.DB.Model(&models.SupportTicket{}).Where("client_id = ?", clientId)

	// Get total tickets
	query.Count(&stats.TotalTickets)

	// Get tickets by status
	config.DB.Model(&models.SupportTicket{}).Where("client_id = ? AND status = ?", clientId, "open").Count(&stats.OpenTickets)
	config.DB.Model(&models.SupportTicket{}).Where("client_id = ? AND status = ?", clientId, "assigned").Count(&stats.AssignedTickets)
	config.DB.Model(&models.SupportTicket{}).Where("client_id = ? AND status = ?", clientId, "in_progress").Count(&stats.InProgressTickets)
	config.DB.Model(&models.SupportTicket{}).Where("client_id = ? AND status = ?", clientId, "resolved").Count(&stats.ResolvedTickets)
	config.DB.Model(&models.SupportTicket{}).Where("client_id = ? AND status = ?", clientId, "closed").Count(&stats.ClosedTickets)

	// Get tickets by priority
	config.DB.Model(&models.SupportTicket{}).Where("client_id = ? AND priority = ?", clientId, "urgent").Count(&stats.UrgentTickets)
	config.DB.Model(&models.SupportTicket{}).Where("client_id = ? AND priority IN (?)", clientId, []string{"urgent", "high"}).Count(&stats.HighPriorityTickets)

	return &stats, nil
}

// GetSupportTicketStatsWithFilters returns ticket statistics with optional filters
func (s *SupportTicketService) GetSupportTicketStatsWithFilters(clientId uint, assignedToId uint) (*models.SupportTicketStats, error) {
	var stats models.SupportTicketStats

	// Build base query with optional filters
	baseQuery := config.DB.Model(&models.SupportTicket{})

	if clientId > 0 {
		baseQuery = baseQuery.Where("client_id = ?", clientId)
	}

	if assignedToId > 0 {
		baseQuery = baseQuery.Where("assigned_to_id = ?", assignedToId)
	}

	// Get total tickets
	baseQuery.Count(&stats.TotalTickets)

	// Get tickets by status
	statusQuery := config.DB.Model(&models.SupportTicket{})
	if clientId > 0 {
		statusQuery = statusQuery.Where("client_id = ?", clientId)
	}
	if assignedToId > 0 {
		statusQuery = statusQuery.Where("assigned_to_id = ?", assignedToId)
	}

	statusQuery.Where("status = ?", "open").Count(&stats.OpenTickets)
	statusQuery.Where("status = ?", "assigned").Count(&stats.AssignedTickets)
	statusQuery.Where("status = ?", "in_progress").Count(&stats.InProgressTickets)
	statusQuery.Where("status = ?", "resolved").Count(&stats.ResolvedTickets)
	statusQuery.Where("status = ?", "closed").Count(&stats.ClosedTickets)

	// Get tickets by priority
	priorityQuery := config.DB.Model(&models.SupportTicket{})
	if clientId > 0 {
		priorityQuery = priorityQuery.Where("client_id = ?", clientId)
	}
	if assignedToId > 0 {
		priorityQuery = priorityQuery.Where("assigned_to_id = ?", assignedToId)
	}

	priorityQuery.Where("priority = ?", "urgent").Count(&stats.UrgentTickets)
	priorityQuery.Where("priority IN (?)", []string{"urgent", "high"}).Count(&stats.HighPriorityTickets)

	return &stats, nil
}

// DeleteSupportTicket deletes a support ticket (soft delete or hard delete based on requirements)
func (s *SupportTicketService) DeleteSupportTicket(id uint, clientId uint) error {
	result := config.DB.Where("id = ? AND client_id = ?", id, clientId).Delete(&models.SupportTicket{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("ticket not found")
	}
	return nil
}

// sendTicketCreatedNotifications sends notifications when a ticket is created
func (s *SupportTicketService) sendTicketCreatedNotifications(ticket *models.SupportTicket) {
	// Send email to admins
	s.sendAdminTicketNotification(ticket, "New Support Ticket Created", fmt.Sprintf("A new %s priority ticket has been created by %s", ticket.Priority, ticket.CreatedBy.Name))

	// Send email to client
	s.sendClientTicketCreatedNotification(ticket)

	// Send Slack notification
	s.sendSlackTicketNotification(ticket, "New Support Ticket Created", "good")
}

// sendTicketStatusChangedNotifications sends notifications when ticket status changes
func (s *SupportTicketService) sendTicketStatusChangedNotifications(ticket *models.SupportTicket, oldStatus string) {
	// Send email to admins
	s.sendAdminTicketNotification(ticket, "Support Ticket Status Updated", fmt.Sprintf("Ticket status changed from %s to %s", oldStatus, ticket.Status))

	// Send Slack notification
	s.sendSlackTicketNotification(ticket, "Support Ticket Status Updated", "warning")
}

// sendTicketAssignedNotifications sends notifications when ticket is assigned
func (s *SupportTicketService) sendTicketAssignedNotifications(ticket *models.SupportTicket) {
	if ticket.AssignedTo != nil {
		// Send email to assigned staff member
		s.sendStaffTicketNotification(ticket, "Support Ticket Assigned", fmt.Sprintf("You have been assigned a %s priority ticket", ticket.Priority))
	}
}

// sendTicketReplyNotifications sends notifications when a reply is added
func (s *SupportTicketService) sendTicketReplyNotifications(reply *models.SupportTicketReply) {
	// Send email to client if reply is not internal AND from staff (not from client)
	if !reply.IsInternal && reply.CreatedBy.ClientId == nil {
		s.sendClientReplyNotification(reply)
	}

	// Send email to admins
	s.sendAdminTicketNotification(&reply.Ticket, "Support Ticket Reply Added", fmt.Sprintf("A new reply has been added to the ticket by %s", reply.CreatedBy.Name))
}

// sendAdminTicketNotification sends email notification to admins
func (s *SupportTicketService) sendAdminTicketNotification(ticket *models.SupportTicket, subject, message string) {
	// Get admin emails from settings or use default
	adminEmails := []string{"<EMAIL>"} // This should be configurable

	// Send email using mail service
	mailService, err := NewMailService()
	if err != nil {
		log.Printf("Failed to initialize mail service: %v", err)
		return
	}

	// For admin notifications, use backend URL
	backendURL := os.Getenv("BACKEND_URL")
	if backendURL == "" {
		backendURL = "https://admin.yourdomain.com"
	}
	ticketURL := fmt.Sprintf("%s/support_tickets/%d", backendURL, ticket.Id)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting("Hello Admin Team,").
		AddContent(
			message,
			"",
			"Ticket Details:",
			fmt.Sprintf("• Ticket ID: #%d", ticket.Id),
			fmt.Sprintf("• Subject: %s", ticket.Subject),
			fmt.Sprintf("• Department: %s", ticket.Department),
			fmt.Sprintf("• Priority: %s", ticket.Priority),
			fmt.Sprintf("• Status: %s", ticket.Status),
			fmt.Sprintf("• Client: %s", ticket.Client.Name),
			fmt.Sprintf("• Created By: %s", ticket.CreatedBy.Name),
		).
		SetAction("View Ticket", ticketURL)

	// Send to all admin emails
	for _, email := range adminEmails {
		err = mailService.SendTemplatedEmail(email, subject, func(t *EmailTemplate) *EmailTemplate {
			return template
		})
		if err != nil {
			log.Printf("Failed to send admin ticket notification to %s: %v", email, err)
		}
	}
}

// sendStaffTicketNotification sends email notification to assigned staff member
func (s *SupportTicketService) sendStaffTicketNotification(ticket *models.SupportTicket, subject, message string) {
	if ticket.AssignedTo == nil {
		return
	}

	// Send email using mail service
	mailService, err := NewMailService()
	if err != nil {
		log.Printf("Failed to initialize mail service: %v", err)
		return
	}

	// For staff notifications, use backend URL
	backendURL := os.Getenv("BACKEND_URL")
	if backendURL == "" {
		backendURL = "https://admin.yourdomain.com"
	}
	ticketURL := fmt.Sprintf("%s/support_tickets/%d", backendURL, ticket.Id)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Hello %s,", ticket.AssignedTo.Name)).
		AddContent(
			message,
			"",
			"Ticket Details:",
			fmt.Sprintf("• Ticket ID: #%d", ticket.Id),
			fmt.Sprintf("• Subject: %s", ticket.Subject),
			fmt.Sprintf("• Department: %s", ticket.Department),
			fmt.Sprintf("• Priority: %s", ticket.Priority),
			fmt.Sprintf("• Status: %s", ticket.Status),
			fmt.Sprintf("• Client: %s", ticket.Client.Name),
		).
		SetAction("View Ticket", ticketURL)

	err = mailService.SendTemplatedEmail(ticket.AssignedTo.Email, subject, func(t *EmailTemplate) *EmailTemplate {
		return template
	})
	if err != nil {
		log.Printf("Failed to send staff ticket notification: %v", err)
	}
}

// sendClientReplyNotification sends email notification to client when staff replies
func (s *SupportTicketService) sendClientReplyNotification(reply *models.SupportTicketReply) {
	// Send email using mail service
	mailService, err := NewMailService()
	if err != nil {
		log.Printf("Failed to initialize mail service: %v", err)
		return
	}

	// Determine the correct URL based on who created the ticket
	ticketURL := s.getTicketURL(&reply.Ticket)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Hello %s,", reply.Ticket.CreatedBy.Name)).
		AddContent(
			"A new reply has been added to your support ticket.",
			"",
			"Ticket Details:",
			fmt.Sprintf("• Ticket ID: #%d", reply.Ticket.Id),
			fmt.Sprintf("• Subject: %s", reply.Ticket.Subject),
			"",
			"Reply:",
			reply.Message,
			"",
			"Please log into your YoTracker dashboard to view the full conversation.",
		).
		SetAction("View Ticket", ticketURL)

	err = mailService.SendTemplatedEmail(reply.Ticket.CreatedBy.Email, "Support Ticket Reply", func(t *EmailTemplate) *EmailTemplate {
		return template
	})
	if err != nil {
		log.Printf("Failed to send client reply notification: %v", err)
	}
}

// sendClientTicketCreatedNotification sends email notification to client when a ticket is created
func (s *SupportTicketService) sendClientTicketCreatedNotification(ticket *models.SupportTicket) {
	// Send email using mail service
	mailService, err := NewMailService()
	if err != nil {
		log.Printf("Failed to initialize mail service: %v", err)
		return
	}

	// Determine the correct URL - always use frontend URL for client notifications
	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "https://yourdomain.com"
	}
	ticketURL := fmt.Sprintf("%s/support_tickets/%d", appURL, ticket.Id)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Hello %s,", ticket.CreatedBy.Name)).
		AddContent(
			"Your support ticket has been successfully created and received by our support team.",
			"",
			"Ticket Details:",
			fmt.Sprintf("• Ticket ID: #%d", ticket.Id),
			fmt.Sprintf("• Subject: %s", ticket.Subject),
			fmt.Sprintf("• Department: %s", ticket.Department),
			fmt.Sprintf("• Priority: %s", ticket.Priority),
			fmt.Sprintf("• Status: %s", ticket.Status),
			"",
			"Our support team will review your ticket and respond as soon as possible.",
			"You can track the progress and add additional information through your dashboard.",
		).
		SetAction("View Ticket", ticketURL)

	err = mailService.SendTemplatedEmail(ticket.CreatedBy.Email, "Support Ticket Created", func(t *EmailTemplate) *EmailTemplate {
		return template
	})
	if err != nil {
		log.Printf("Failed to send client ticket created notification: %v", err)
	}
}

// sendSlackTicketNotification sends Slack notification with rich formatting
func (s *SupportTicketService) sendSlackTicketNotification(ticket *models.SupportTicket, title, color string) {
	slackService, err := NewSlackService()
	if err != nil {
		log.Printf("Failed to initialize Slack service: %v", err)
		return
	}

	// Determine emoji based on title/type
	var emoji string
	switch {
	case strings.Contains(strings.ToLower(title), "created"):
		emoji = "📝" // New ticket
	case strings.Contains(strings.ToLower(title), "reply"):
		emoji = "💬" // Reply
	case strings.Contains(strings.ToLower(title), "status"):
		emoji = "🔄" // Status change
	case strings.Contains(strings.ToLower(title), "assigned"):
		emoji = "👤" // Assignment
	default:
		emoji = "🎫" // Default ticket
	}

	// Build rich Slack blocks
	blocks := []SlackBlock{
		{
			Type: "header",
			Text: &SlackText{
				Type: "plain_text",
				Text: fmt.Sprintf("%s %s", emoji, title),
			},
		},
		{
			Type: "section",
			Fields: []SlackText{
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Ticket ID:*\n#%d", ticket.Id),
				},
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Department:*\n%s", ticket.Department),
				},
			},
		},
		{
			Type: "section",
			Fields: []SlackText{
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Priority:*\n%s", ticket.Priority),
				},
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Status:*\n%s", ticket.Status),
				},
			},
		},
		{
			Type: "section",
			Fields: []SlackText{
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Client:*\n%s", ticket.Client.Name),
				},
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Created By:*\n%s", ticket.CreatedBy.Name),
				},
			},
		},
		{
			Type: "section",
			Text: &SlackText{
				Type: "mrkdwn",
				Text: fmt.Sprintf("*Subject:*\n%s", ticket.Subject),
			},
		},
	}

	// Add description if available
	if ticket.Description != "" {
		// Truncate description if too long
		description := ticket.Description
		if len(description) > 200 {
			description = description[:200] + "..."
		}
		blocks = append(blocks, SlackBlock{
			Type: "section",
			Text: &SlackText{
				Type: "mrkdwn",
				Text: fmt.Sprintf("*Description:*\n%s", description),
			},
		})
	}

	// Add timestamp
	blocks = append(blocks, SlackBlock{
		Type: "section",
		Text: &SlackText{
			Type: "mrkdwn",
			Text: fmt.Sprintf("⏰ %s", ticket.UpdatedAt.Format("2006-01-02 15:04:05 MST")),
		},
	})

	// Add action button
	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "https://yourdomain.com"
	}

	blocks = append(blocks, SlackBlock{
		Type: "actions",
		Elements: []SlackElement{
			{
				Type: "button",
				Text: &SlackText{
					Type: "plain_text",
					Text: "View Ticket",
				},
				URL:      fmt.Sprintf("%s/support-tickets/%d", appURL, ticket.Id),
				ActionID: "view_ticket",
				Style:    "primary",
			},
		},
	})

	// Send to #yotracker-support channel
	err = slackService.SendCustomMessage("#yotracker-support", fmt.Sprintf("%s %s", emoji, title), blocks)
	if err != nil {
		log.Printf("Failed to send Slack ticket notification: %v", err)
	}
}

// sendSlackReplyNotification sends Slack notification for ticket replies with rich formatting
func (s *SupportTicketService) sendSlackReplyNotification(reply *models.SupportTicketReply) {
	slackService, err := NewSlackService()
	if err != nil {
		log.Printf("Failed to initialize Slack service: %v", err)
		return
	}

	// Determine if reply is from staff or client
	isStaffReply := reply.CreatedBy.ClientId == nil
	var emoji, title string

	if isStaffReply {
		emoji = "💬"
		title = "Staff Reply Added"
	} else {
		emoji = "👤"
		title = "Client Reply Added"
	}

	// Build rich Slack blocks
	blocks := []SlackBlock{
		{
			Type: "header",
			Text: &SlackText{
				Type: "plain_text",
				Text: fmt.Sprintf("%s %s", emoji, title),
			},
		},
		{
			Type: "section",
			Fields: []SlackText{
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Ticket ID:*\n#%d", reply.Ticket.Id),
				},
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Department:*\n%s", reply.Ticket.Department),
				},
			},
		},
		{
			Type: "section",
			Fields: []SlackText{
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Priority:*\n%s", reply.Ticket.Priority),
				},
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Status:*\n%s", reply.Ticket.Status),
				},
			},
		},
		{
			Type: "section",
			Fields: []SlackText{
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Client:*\n%s", reply.Ticket.Client.Name),
				},
				{
					Type: "mrkdwn",
					Text: fmt.Sprintf("*Replied By:*\n%s", reply.CreatedBy.Name),
				},
			},
		},
		{
			Type: "section",
			Text: &SlackText{
				Type: "mrkdwn",
				Text: fmt.Sprintf("*Subject:*\n%s", reply.Ticket.Subject),
			},
		},
		{
			Type: "section",
			Text: &SlackText{
				Type: "mrkdwn",
				Text: fmt.Sprintf("*Reply:*\n%s", reply.Message),
			},
		},
	}

	// Add internal note indicator if applicable
	if reply.IsInternal {
		blocks = append(blocks, SlackBlock{
			Type: "section",
			Text: &SlackText{
				Type: "mrkdwn",
				Text: "🔒 *Internal Note* (not visible to client)",
			},
		})
	}

	// Add timestamp
	blocks = append(blocks, SlackBlock{
		Type: "section",
		Text: &SlackText{
			Type: "mrkdwn",
			Text: fmt.Sprintf("⏰ %s", reply.CreatedAt.Format("2006-01-02 15:04:05 MST")),
		},
	})

	// Add action button
	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "https://yourdomain.com"
	}

	blocks = append(blocks, SlackBlock{
		Type: "actions",
		Elements: []SlackElement{
			{
				Type: "button",
				Text: &SlackText{
					Type: "plain_text",
					Text: "View Ticket",
				},
				URL:      fmt.Sprintf("%s/support-tickets/%d", appURL, reply.Ticket.Id),
				ActionID: "view_ticket",
				Style:    "primary",
			},
		},
	})

	// Send to #yotracker-support channel
	err = slackService.SendCustomMessage("#yotracker-support", fmt.Sprintf("%s %s", emoji, title), blocks)
	if err != nil {
		log.Printf("Failed to send Slack reply notification: %v", err)
	}
}

// SearchSupportTicketsWithFilters searches support tickets with filters and search query
func (s *SupportTicketService) SearchSupportTicketsWithFilters(query string, filters models.SupportTicketFilters) ([]models.SupportTicket, int64, error) {
	var tickets []models.SupportTicket
	var total int64

	// Build the base query with search
	baseQuery := config.DB.Preload("Client").Preload("CreatedBy").Preload("AssignedTo").Preload("ClientDevice").
		Where("client_id = ? AND (subject LIKE ? OR description LIKE ?)",
			filters.ClientId, "%"+query+"%", "%"+query+"%")

	// Apply additional filters
	if filters.Department != "" {
		baseQuery = baseQuery.Where("department = ?", filters.Department)
	}
	if filters.Priority != "" {
		baseQuery = baseQuery.Where("priority = ?", filters.Priority)
	}
	if filters.Status != "" {
		baseQuery = baseQuery.Where("status = ?", filters.Status)
	}
	if filters.AssignedToId != nil {
		baseQuery = baseQuery.Where("assigned_to_id = ?", *filters.AssignedToId)
	}
	if filters.ClientDeviceId != nil {
		baseQuery = baseQuery.Where("client_device_id = ?", *filters.ClientDeviceId)
	}
	if filters.CreatedById != nil {
		baseQuery = baseQuery.Where("created_by_id = ?", *filters.CreatedById)
	}

	// Get total count
	baseQuery.Model(&models.SupportTicket{}).Count(&total)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := (filters.Page - 1) * filters.PerPage
		baseQuery = baseQuery.Offset(offset).Limit(filters.PerPage)
	}

	// Order by priority and creation date
	baseQuery = baseQuery.Order("CASE priority WHEN 'urgent' THEN 1 WHEN 'high' THEN 2 WHEN 'medium' THEN 3 WHEN 'low' THEN 4 END, created_at DESC")

	err := baseQuery.Find(&tickets).Error
	return tickets, total, err
}

// getTicketURL returns the appropriate URL based on who created the ticket
func (s *SupportTicketService) getTicketURL(ticket *models.SupportTicket) string {
	appURL := os.Getenv("APP_URL")
	backendURL := os.Getenv("BACKEND_URL")

	if appURL == "" {
		appURL = "https://yourdomain.com"
	}
	if backendURL == "" {
		backendURL = "https://admin.yourdomain.com"
	}

	// Since CreatedBy is now always the client, we need to determine the URL differently
	// For now, let's use frontend URL for all client notifications and backend URL for admin notifications
	// This can be adjusted based on your specific requirements

	// For client notifications, always use frontend URL
	return fmt.Sprintf("%s/support_tickets/%d", appURL, ticket.Id)
}
