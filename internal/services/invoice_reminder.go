package services

import (
	"fmt"
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

// InvoiceReminderService handles sending overdue invoice reminders
type InvoiceReminderService struct{}

// NewInvoiceReminderService creates a new invoice reminder service
func NewInvoiceReminderService() *InvoiceReminderService {
	return &InvoiceReminderService{}
}

// ProcessFirstOverdueReminders processes and sends first overdue reminders
func (s *InvoiceReminderService) ProcessFirstOverdueReminders() error {
	return s.processOverdueReminders("first_invoice_overdue_reminder_days", "first")
}

// ProcessSecondOverdueReminders processes and sends second overdue reminders
func (s *InvoiceReminderService) ProcessSecondOverdueReminders() error {
	return s.processOverdueReminders("second_invoice_overdue_reminder_days", "second")
}

// ProcessThirdOverdueReminders processes and sends third overdue reminders
func (s *InvoiceReminderService) ProcessThirdOverdueReminders() error {
	return s.processOverdueReminders("third_invoice_overdue_reminder_days", "third")
}

// processOverdueReminders is the core function that processes overdue reminders
func (s *InvoiceReminderService) processOverdueReminders(settingKey, reminderType string) error {
	// Get the reminder days setting
	var setting models.Setting
	err := config.DB.Where("setting_key = ?", settingKey).First(&setting).Error
	if err != nil {
		return fmt.Errorf("failed to fetch %s setting: %v", settingKey, err)
	}

	// Parse the days value
	var reminderDays int
	_, err = fmt.Sscanf(setting.SettingValue, "%d", &reminderDays)
	if err != nil {
		return fmt.Errorf("invalid %s setting value: %v", settingKey, err)
	}

	// Calculate cutoff date (today - reminder_days)
	cutoffDate := time.Now().AddDate(0, 0, -reminderDays)
	log.Printf("Processing %s overdue reminders for invoices overdue since: %s",
		reminderType, cutoffDate.Format("2006-01-02"))

	// Find invoices that are overdue by exactly the reminder days with balance > 0
	// We use DATE() function to match exact day to avoid sending multiple reminders
	var overdueInvoices []models.Invoice
	err = config.DB.Where("DATE(due_date) = DATE(?) AND balance > 0 AND status != ?",
		cutoffDate, "paid").
		Preload("Client").
		Preload("Currency").
		Find(&overdueInvoices).Error
	if err != nil {
		return fmt.Errorf("failed to fetch overdue invoices: %v", err)
	}

	log.Printf("Found %d invoices for %s overdue reminder", len(overdueInvoices), reminderType)

	// Send reminders for each invoice
	for _, invoice := range overdueInvoices {
		err := s.sendOverdueReminder(invoice, reminderType)
		if err != nil {
			log.Printf("Failed to send %s overdue reminder for invoice ID %d: %v",
				reminderType, invoice.Id, err)
			continue
		}
		log.Printf("Successfully sent %s overdue reminder for invoice %s to %s",
			reminderType, *invoice.Reference, invoice.Client.Email)
	}

	return nil
}

// sendOverdueReminder sends an overdue reminder email for a specific invoice
func (s *InvoiceReminderService) sendOverdueReminder(invoice models.Invoice, reminderType string) error {
	switch reminderType {
	case "first":
		return SendFirstOverdueReminderEmail(invoice)
	case "second":
		return SendSecondOverdueReminderEmail(invoice)
	case "third":
		return SendThirdOverdueReminderEmail(invoice)
	default:
		return fmt.Errorf("unknown reminder type: %s", reminderType)
	}
}
