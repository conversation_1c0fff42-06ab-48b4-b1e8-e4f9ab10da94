package services

import (
	"context"
	"testing"
	"time"

	"firebase.google.com/go/v4/messaging"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockFCMClient is a mock implementation of the FCM client
type MockFCMClient struct {
	mock.Mock
}

func (m *MockFCMClient) Send(ctx context.Context, msgs ...*messaging.Message) (*messaging.BatchResponse, error) {
	args := m.Called(ctx, msgs)
	return args.Get(0).(*messaging.BatchResponse), args.Error(1)
}

func TestNewPushNotificationService(t *testing.T) {
	t.Run("Service creation", func(t *testing.T) {
		service := NewPushNotificationService()

		assert.NotNil(t, service)
		// Note: Client might be nil if FCM client creation fails due to missing credentials
		// This is expected in test environments without proper Firebase setup
	})
}

func TestSendPushNotification(t *testing.T) {
	// Create mock FCM client
	mockClient := new(MockFCMClient)

	// Create service with mock client
	service := &PushNotificationService{
		Client: mockClient,
	}

	// Test request
	req := PushNotificationRequest{
		To:             "test-fcm-token",
		Title:          "Test Title",
		Body:           "Test Body",
		Type:           "ignition_alert",
		ClientDeviceID: uintPtr(123),
		Data: map[string]interface{}{
			"custom_field": "custom_value",
		},
	}

	// Mock successful FCM response
	mockResponse := &messaging.BatchResponse{
		SuccessCount: 1,
		FailureCount: 0,
		Responses: []*messaging.SendResponse{
			{MessageID: "test-message-id-123"},
		},
	}

	// Set up mock expectations
	mockClient.On("Send", mock.Anything, mock.MatchedBy(func(msgs []*messaging.Message) bool {
		if len(msgs) != 1 {
			return false
		}
		msg := msgs[0]
		return msg.Token == "test-fcm-token" &&
			msg.Notification.Title == "Test Title" &&
			msg.Notification.Body == "Test Body" &&
			msg.Data["type"] == "ignition_alert" &&
			msg.Data["client_device_id"] == "123" &&
			msg.Data["custom_field"] == "custom_value"
	})).Return(mockResponse, nil)

	// Send notification
	response, err := service.SendPushNotification(req)

	// Verify response
	assert.NoError(t, err)
	assert.Equal(t, 1, response.Success)
	assert.Equal(t, 0, response.Failure)
	assert.Len(t, response.Results, 1)
	assert.Equal(t, "test-message-id-123", response.Results[0].MessageID)

	// Verify mock was called
	mockClient.AssertExpectations(t)
}

func TestSendPushNotificationWithTimestamp(t *testing.T) {
	// Create mock FCM client
	mockClient := new(MockFCMClient)

	// Create service with mock client
	service := &PushNotificationService{
		Client: mockClient,
	}

	req := PushNotificationRequest{
		To:    "test-fcm-token",
		Title: "Test Title",
		Body:  "Test Body",
		Type:  "ignition_alert",
	}

	// Mock successful FCM response
	mockResponse := &messaging.BatchResponse{
		SuccessCount: 1,
		FailureCount: 0,
		Responses: []*messaging.SendResponse{
			{MessageID: "test-message-id"},
		},
	}

	// Set up mock expectations
	mockClient.On("Send", mock.Anything, mock.MatchedBy(func(msgs []*messaging.Message) bool {
		if len(msgs) != 1 {
			return false
		}
		msg := msgs[0]
		// Verify timestamp is set
		if msg.Data["timestamp"] == "" {
			return false
		}

		// Parse timestamp to verify it's valid RFC3339
		_, err := time.Parse(time.RFC3339, msg.Data["timestamp"])
		return err == nil
	})).Return(mockResponse, nil)

	_, err := service.SendPushNotification(req)
	assert.NoError(t, err)
	mockClient.AssertExpectations(t)
}

func TestSendPushNotificationWithCustomTimestamp(t *testing.T) {
	// Create mock FCM client
	mockClient := new(MockFCMClient)

	// Create service with mock client
	service := &PushNotificationService{
		Client: mockClient,
	}

	customTimestamp := "2023-12-01T10:00:00Z"
	req := PushNotificationRequest{
		To:        "test-fcm-token",
		Title:     "Test Title",
		Body:      "Test Body",
		Type:      "ignition_alert",
		Timestamp: &customTimestamp,
	}

	// Mock successful FCM response
	mockResponse := &messaging.BatchResponse{
		SuccessCount: 1,
		FailureCount: 0,
		Responses: []*messaging.SendResponse{
			{MessageID: "test-message-id"},
		},
	}

	// Set up mock expectations
	mockClient.On("Send", mock.Anything, mock.MatchedBy(func(msgs []*messaging.Message) bool {
		if len(msgs) != 1 {
			return false
		}
		msg := msgs[0]
		// Verify custom timestamp is used
		expectedTimestamp := "2023-12-01T10:00:00Z"
		return msg.Data["timestamp"] == expectedTimestamp
	})).Return(mockResponse, nil)

	_, err := service.SendPushNotification(req)
	assert.NoError(t, err)
	mockClient.AssertExpectations(t)
}

func TestSendBulkPushNotification(t *testing.T) {
	// Create mock FCM client
	mockClient := new(MockFCMClient)

	// Create service with mock client
	service := &PushNotificationService{
		Client: mockClient,
	}

	requests := []PushNotificationRequest{
		{
			To:    "token-1",
			Title: "Test Title 1",
			Body:  "Test Body 1",
			Type:  "ignition_alert",
		},
		{
			To:    "token-2",
			Title: "Test Title 2",
			Body:  "Test Body 2",
			Type:  "geofence_alert",
		},
	}

	// Mock successful FCM responses for each request
	mockResponse1 := &messaging.BatchResponse{
		SuccessCount: 1,
		FailureCount: 0,
		Responses: []*messaging.SendResponse{
			{MessageID: "test-message-id-1"},
		},
	}

	mockResponse2 := &messaging.BatchResponse{
		SuccessCount: 1,
		FailureCount: 0,
		Responses: []*messaging.SendResponse{
			{MessageID: "test-message-id-2"},
		},
	}

	// Set up mock expectations for both calls
	mockClient.On("Send", mock.Anything, mock.MatchedBy(func(msgs []*messaging.Message) bool {
		if len(msgs) != 1 {
			return false
		}
		msg := msgs[0]
		return msg.Token == "token-1" && msg.Notification.Title == "Test Title 1"
	})).Return(mockResponse1, nil)

	mockClient.On("Send", mock.Anything, mock.MatchedBy(func(msgs []*messaging.Message) bool {
		if len(msgs) != 1 {
			return false
		}
		msg := msgs[0]
		return msg.Token == "token-2" && msg.Notification.Title == "Test Title 2"
	})).Return(mockResponse2, nil)

	response, err := service.SendBulkPushNotification(requests)

	// Verify response
	assert.NoError(t, err)
	assert.Equal(t, 2, response.Success)
	assert.Equal(t, 0, response.Failure)
	assert.Len(t, response.Results, 2)

	// Verify mock was called twice
	mockClient.AssertExpectations(t)
}

func TestSendBulkPushNotificationEmpty(t *testing.T) {
	// Create mock FCM client
	mockClient := new(MockFCMClient)

	// Create service with mock client
	service := &PushNotificationService{
		Client: mockClient,
	}

	_, err := service.SendBulkPushNotification([]PushNotificationRequest{})
	assert.Error(t, err)
	assert.Equal(t, "no notifications to send", err.Error())
}

func TestSendPushNotificationFirebaseError(t *testing.T) {
	// Create mock FCM client
	mockClient := new(MockFCMClient)

	// Create service with mock client
	service := &PushNotificationService{
		Client: mockClient,
	}

	req := PushNotificationRequest{
		To:    "invalid-token",
		Title: "Test Title",
		Body:  "Test Body",
		Type:  "ignition_alert",
	}

	// Mock FCM error response
	mockResponse := &messaging.BatchResponse{
		SuccessCount: 0,
		FailureCount: 1,
		Responses: []*messaging.SendResponse{
			{Error: assert.AnError},
		},
	}

	// Set up mock expectations
	mockClient.On("Send", mock.Anything, mock.Anything).Return(mockResponse, nil)

	response, err := service.SendPushNotification(req)

	// Verify error response
	assert.NoError(t, err)
	assert.Equal(t, 0, response.Success)
	assert.Equal(t, 1, response.Failure)
	assert.Contains(t, response.Results[0].Error, "assert.AnError general error for testing")

	mockClient.AssertExpectations(t)
}

func TestSendPushNotificationHTTPError(t *testing.T) {
	// Create mock FCM client
	mockClient := new(MockFCMClient)

	// Create service with mock client
	service := &PushNotificationService{
		Client: mockClient,
	}

	req := PushNotificationRequest{
		To:    "test-token",
		Title: "Test Title",
		Body:  "Test Body",
		Type:  "ignition_alert",
	}

	// Mock FCM client error
	mockClient.On("Send", mock.Anything, mock.Anything).Return((*messaging.BatchResponse)(nil), assert.AnError)

	_, err := service.SendPushNotification(req)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "assert.AnError general error for testing")
}

func TestValidateNotificationType(t *testing.T) {
	tests := []struct {
		notificationType string
		expected         bool
	}{
		{"ignition_alert", true},
		{"admin_message", true},
		{"geofence_alert", true},
		{"trip_summary", true},
		{"system_notice", true},
		{"new_chat_message", true},
		{"maintenance_alert", true},
		{"speed_alert", true},
		{"fuel_alert", true},
		{"battery_alert", true},
		{"panic_alert", true},
		{"driver_alert", true},
		{"device_offline", true},
		{"device_online", true},
		{"invalid_type", false},
		{"", false},
		{"unknown_alert", false},
	}

	for _, test := range tests {
		result := ValidateNotificationType(test.notificationType)
		assert.Equal(t, test.expected, result, "ValidateNotificationType(%s) should return %v", test.notificationType, test.expected)
	}
}
