package services

import (
	"errors"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

type SafetyComplianceService struct{}

func NewSafetyComplianceService() *SafetyComplianceService {
	return &SafetyComplianceService{}
}

// GetSafetyCompliances returns all safety compliance records with filtering and pagination
func (s *SafetyComplianceService) GetSafetyCompliances(filters models.SafetyComplianceFilters) ([]models.SafetyCompliance, int64, error) {
	var compliances []models.SafetyCompliance
	var total int64

	query := config.DB.Where("client_id = ?", filters.ClientId)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.ComplianceType != "" {
		query = query.Where("compliance_type = ?", filters.ComplianceType)
	}
	if filters.DriverId != "" {
		query = query.Where("driver_id = ?", filters.DriverId)
	}
	if filters.StartDate != nil {
		query = query.Where("compliance_date >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("compliance_date <= ?", filters.EndDate)
	}

	// Get total count
	query.Model(&models.SafetyCompliance{}).Count(&total)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := (filters.Page - 1) * filters.PerPage
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	// Order by compliance date
	query = query.Order("compliance_date DESC")

	err := query.Find(&compliances).Error
	return compliances, total, err
}

// GetSafetyCompliance returns a specific safety compliance record
func (s *SafetyComplianceService) GetSafetyCompliance(id uint, clientId uint) (*models.SafetyCompliance, error) {
	var compliance models.SafetyCompliance
	err := config.DB.Where("id = ? AND client_id = ?", id, clientId).First(&compliance).Error
	if err != nil {
		return nil, err
	}
	return &compliance, nil
}

// CreateSafetyCompliance creates a new safety compliance record
func (s *SafetyComplianceService) CreateSafetyCompliance(compliance models.SafetyCompliance) (*models.SafetyCompliance, error) {
	// Set default status if not provided
	if compliance.Status == "" {
		compliance.Status = "pending"
	}

	err := config.DB.Create(&compliance).Error
	if err != nil {
		return nil, err
	}

	return &compliance, nil
}

// UpdateSafetyCompliance updates an existing safety compliance record
func (s *SafetyComplianceService) UpdateSafetyCompliance(compliance models.SafetyCompliance) (*models.SafetyCompliance, error) {
	// Check if compliance exists and belongs to client
	existingCompliance, err := s.GetSafetyCompliance(compliance.Id, compliance.ClientId)
	if err != nil {
		return nil, errors.New("safety compliance record not found")
	}

	// Update fields
	existingCompliance.ComplianceType = compliance.ComplianceType
	existingCompliance.ComplianceDate = compliance.ComplianceDate
	existingCompliance.ExpiryDate = compliance.ExpiryDate
	existingCompliance.Status = compliance.Status
	existingCompliance.LastCheckDate = compliance.LastCheckDate
	existingCompliance.NextCheckDate = compliance.NextCheckDate
	existingCompliance.Notes = compliance.Notes
	existingCompliance.DriverId = compliance.DriverId

	err = config.DB.Save(existingCompliance).Error
	if err != nil {
		return nil, err
	}

	return existingCompliance, nil
}

// DeleteSafetyCompliance deletes a safety compliance record
func (s *SafetyComplianceService) DeleteSafetyCompliance(id uint, clientId uint) error {
	result := config.DB.Where("id = ? AND client_id = ?", id, clientId).Delete(&models.SafetyCompliance{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("safety compliance record not found")
	}
	return nil
}

// GetExpiringCompliances returns compliance records that are expiring within specified days
func (s *SafetyComplianceService) GetExpiringCompliances(clientId uint, days int) ([]models.SafetyCompliance, error) {
	var compliances []models.SafetyCompliance
	expiryDate := time.Now().AddDate(0, 0, days)

	err := config.DB.Where("client_id = ? AND expiry_date <= ? AND status = ?",
		clientId, expiryDate, "compliant").
		Order("expiry_date ASC").
		Find(&compliances).Error

	return compliances, err
}

// GetSafetyComplianceStats returns safety compliance statistics
func (s *SafetyComplianceService) GetSafetyComplianceStats(clientId uint) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total compliance records
	var total int64
	config.DB.Model(&models.SafetyCompliance{}).Where("client_id = ?", clientId).Count(&total)
	stats["total"] = total

	// Compliant records
	var compliant int64
	config.DB.Model(&models.SafetyCompliance{}).Where("client_id = ? AND status = ?",
		clientId, "compliant").Count(&compliant)
	stats["compliant"] = compliant

	// Non-compliant records
	var nonCompliant int64
	config.DB.Model(&models.SafetyCompliance{}).Where("client_id = ? AND status = ?",
		clientId, "non_compliant").Count(&nonCompliant)
	stats["non_compliant"] = nonCompliant

	// Pending records
	var pending int64
	config.DB.Model(&models.SafetyCompliance{}).Where("client_id = ? AND status = ?",
		clientId, "pending").Count(&pending)
	stats["pending"] = pending

	// Expired records
	var expired int64
	config.DB.Model(&models.SafetyCompliance{}).Where("client_id = ? AND expiry_date < ? AND status = ?",
		clientId, time.Now(), "compliant").Count(&expired)
	stats["expired"] = expired

	// Expiring soon (within 30 days)
	var expiringSoon int64
	expiryDate := time.Now().AddDate(0, 0, 30)
	config.DB.Model(&models.SafetyCompliance{}).Where("client_id = ? AND expiry_date <= ? AND status = ?",
		clientId, expiryDate, "compliant").Count(&expiringSoon)
	stats["expiring_soon"] = expiringSoon

	// Compliance rate percentage
	var complianceRate float64
	if total > 0 {
		complianceRate = float64(compliant) / float64(total) * 100
	}
	stats["compliance_rate"] = complianceRate

	// Status breakdown
	var statusBreakdown []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	config.DB.Model(&models.SafetyCompliance{}).
		Select("status, COUNT(*) as count").
		Where("client_id = ?", clientId).
		Group("status").
		Scan(&statusBreakdown)
	stats["status_breakdown"] = statusBreakdown

	// Compliance type breakdown
	var complianceTypeBreakdown []struct {
		ComplianceType string `json:"compliance_type"`
		Count          int64  `json:"count"`
	}
	config.DB.Model(&models.SafetyCompliance{}).
		Select("compliance_type, COUNT(*) as count").
		Where("client_id = ?", clientId).
		Group("compliance_type").
		Scan(&complianceTypeBreakdown)
	stats["compliance_type_breakdown"] = complianceTypeBreakdown

	// Monthly compliance trend (last 12 months)
	var monthlyTrend []struct {
		Month     string `json:"month"`
		Count     int64  `json:"count"`
		Compliant int64  `json:"compliant"`
	}
	config.DB.Model(&models.SafetyCompliance{}).
		Select("DATE_FORMAT(compliance_date, '%Y-%m') as month, COUNT(*) as count, SUM(CASE WHEN status = 'compliant' THEN 1 ELSE 0 END) as compliant").
		Where("client_id = ? AND compliance_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)", clientId).
		Group("month").
		Order("month DESC").
		Scan(&monthlyTrend)
	stats["monthly_trend"] = monthlyTrend

	return stats, nil
}
