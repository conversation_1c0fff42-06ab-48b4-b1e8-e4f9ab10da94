package services

import (
	"fmt"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

// IgnitionStatusService provides methods to work with ignition status and timing
type IgnitionStatusService struct{}

// NewIgnitionStatusService creates a new ignition status service
func NewIgnitionStatusService() *IgnitionStatusService {
	return &IgnitionStatusService{}
}

// GetIgnitionStatusInfo returns comprehensive ignition status information for a device
func (s *IgnitionStatusService) GetIgnitionStatusInfo(deviceId uint) (*IgnitionStatusInfo, error) {
	var device models.ClientDevice
	err := config.DB.First(&device, deviceId).Error
	if err != nil {
		return nil, fmt.Errorf("failed to find device %d: %v", deviceId, err)
	}

	info := &IgnitionStatusInfo{
		DeviceId:       deviceId,
		IgnitionStatus: device.IgnitionStatus,
		StatusTime:     device.IgnitionStatusTime,
		LastUpdate:     device.LastStatusUpdate,
	}

	// Calculate duration since last ignition status change
	if device.IgnitionStatusTime != nil {
		duration := time.Since(*device.IgnitionStatusTime)
		info.Duration = &duration
		info.DurationMinutes = duration.Minutes()
		info.DurationHours = duration.Hours()
		info.DurationFormatted = s.formatDuration(duration)
	}

	// Determine status description
	if device.IgnitionStatus != nil {
		if *device.IgnitionStatus {
			info.StatusDescription = "Ignition ON"
		} else {
			info.StatusDescription = "Ignition OFF"
		}
	} else {
		info.StatusDescription = "Unknown"
	}

	return info, nil
}

// GetDevicesByIgnitionStatus returns devices filtered by ignition status
func (s *IgnitionStatusService) GetDevicesByIgnitionStatus(ignitionOn bool) ([]*IgnitionStatusInfo, error) {
	var devices []models.ClientDevice
	query := config.DB.Where("ignition_status = ?", ignitionOn)

	if ignitionOn {
		// For devices with ignition ON, also check that they have a recent status update
		query = query.Where("last_status_update > ?", time.Now().Add(-24*time.Hour))
	}

	err := query.Find(&devices).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query devices: %v", err)
	}

	var results []*IgnitionStatusInfo
	for _, device := range devices {
		info, err := s.GetIgnitionStatusInfo(device.Id)
		if err != nil {
			continue // Skip devices with errors
		}
		results = append(results, info)
	}

	return results, nil
}

// GetDevicesWithIgnitionOnForDuration returns devices that have had ignition ON for a specific duration
func (s *IgnitionStatusService) GetDevicesWithIgnitionOnForDuration(minDuration time.Duration) ([]*IgnitionStatusInfo, error) {
	var devices []models.ClientDevice
	cutoffTime := time.Now().Add(-minDuration)

	err := config.DB.Where("ignition_status = ? AND ignition_status_time <= ?", true, cutoffTime).Find(&devices).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query devices: %v", err)
	}

	var results []*IgnitionStatusInfo
	for _, device := range devices {
		info, err := s.GetIgnitionStatusInfo(device.Id)
		if err != nil {
			continue
		}
		results = append(results, info)
	}

	return results, nil
}

// GetDevicesWithIgnitionOffForDuration returns devices that have had ignition OFF for a specific duration
func (s *IgnitionStatusService) GetDevicesWithIgnitionOffForDuration(minDuration time.Duration) ([]*IgnitionStatusInfo, error) {
	var devices []models.ClientDevice
	cutoffTime := time.Now().Add(-minDuration)

	err := config.DB.Where("ignition_status = ? AND ignition_status_time <= ?", false, cutoffTime).Find(&devices).Error
	if err != nil {
		return nil, fmt.Errorf("failed to query devices: %v", err)
	}

	var results []*IgnitionStatusInfo
	for _, device := range devices {
		info, err := s.GetIgnitionStatusInfo(device.Id)
		if err != nil {
			continue
		}
		results = append(results, info)
	}

	return results, nil
}

// UpdateIgnitionStatus manually updates ignition status for a device
func (s *IgnitionStatusService) UpdateIgnitionStatus(deviceId uint, ignitionStatus bool, timestamp *time.Time) error {
	if timestamp == nil {
		now := time.Now()
		timestamp = &now
	}

	err := config.DB.Model(&models.ClientDevice{}).
		Where("id = ?", deviceId).
		Updates(map[string]interface{}{
			"ignition_status":      ignitionStatus,
			"ignition_status_time": timestamp,
			"last_status_update":   timestamp,
		}).Error

	if err != nil {
		return fmt.Errorf("failed to update ignition status for device %d: %v", deviceId, err)
	}

	return nil
}

// formatDuration formats a duration into a human-readable string
func (s *IgnitionStatusService) formatDuration(duration time.Duration) string {
	hours := int(duration.Hours())
	minutes := int(duration.Minutes()) % 60
	seconds := int(duration.Seconds()) % 60

	if hours > 0 {
		return fmt.Sprintf("%dh %dm %ds", hours, minutes, seconds)
	} else if minutes > 0 {
		return fmt.Sprintf("%dm %ds", minutes, seconds)
	} else {
		return fmt.Sprintf("%ds", seconds)
	}
}

// IgnitionStatusInfo contains comprehensive ignition status information
type IgnitionStatusInfo struct {
	DeviceId          uint           `json:"device_id"`
	IgnitionStatus    *bool          `json:"ignition_status"`
	StatusTime        *time.Time     `json:"status_time"`
	LastUpdate        *time.Time     `json:"last_update"`
	Duration          *time.Duration `json:"duration,omitempty"`
	DurationMinutes   float64        `json:"duration_minutes"`
	DurationHours     float64        `json:"duration_hours"`
	DurationFormatted string         `json:"duration_formatted"`
	StatusDescription string         `json:"status_description"`
}
