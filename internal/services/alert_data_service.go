package services

import (
	"encoding/json"
	"fmt"
	"log"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

type Alert struct {
	DeviceId       string
	AlertType      string
	AlertName      string
	AlertLevel     string
	Message        string
	RawData        string
	AdditionalData json.RawMessage
	Speed          float64
	Direction      string
	Timestamp      time.Time
}

func (a *Alert) SaveAlert() bool {
	//check for client device id
	clientDevice := models.ClientDevice{DeviceId: a.DeviceId}
	config.DB.First(&clientDevice)
	if clientDevice.Id != 0 {
		// Use alert name as fallback if no message is provided
		message := a.Message
		if message == "" {
			message = a.AlertName
		}

		// Auto-categorize alert level if not provided
		alertLevel := a.AlertLevel
		if alertLevel == "" {
			categorizationService := NewAlertCategorizationService()
			alertLevel = string(categorizationService.CategorizeAlert(a.AlertType))
		}

		alert := models.Alert{
			ClientDeviceId: clientDevice.Id,
			DeviceId:       &a.<PERSON>ce<PERSON>d,
			AlertType:      a.AlertType,
			AlertName:      &a.Alert<PERSON>ame,
			AlertLevel:     &alertLevel,
			Message:        &message,
			RawData:        &a.RawData,
			AdditionalData: a.AdditionalData,
			Speed:          &a.Speed,
			Direction:      &a.Direction,
			AlertTimestamp: a.Timestamp,
		}
		result := config.DB.Create(&alert)
		if result.Error != nil {
			return false
		}
		fmt.Println("Successfully saved")

		// Send multi-channel alerts asynchronously
		go func() {
			// Load the client with alert preferences
			var client models.Client
			if err := config.DB.First(&client, clientDevice.ClientId).Error; err != nil {
				log.Printf("Failed to load client for alerts: %v", err)
				return
			}

			// Send alerts through all enabled channels
			if err := SendMultiChannelAlert(&client, &alert, &clientDevice); err != nil {
				log.Printf("Failed to send multi-channel alerts: %v", err)
			}

			// Also send Slack notification (legacy support)
			slackService, err := NewSlackService()
			if err != nil {
				log.Printf("Failed to initialize Slack service: %v", err)
				return
			}

			if err := slackService.SendAlert(&alert, &clientDevice); err != nil {
				log.Printf("Failed to send Slack alert: %v", err)
			}
		}()

		return true
	} else {
		fmt.Println("Could not find client")
	}
	return false
}
