package services

import (
	"yotracker/internal/models"
)

// GeneratePositionLogDTOExample demonstrates how to use the new DTO structure
func (s *ReportService) GeneratePositionLogDTOExample(filters models.ReportFilters) (*models.PositionLogDTO, int, int, error) {
	// Example data - in real implementation, this would come from database queries
	dtoData := []models.PositionLogRow{
		{
			Timestamp:    "2024-01-15 10:30:00",
			Name:         "Truck-001 (ABC123)",
			DriverName:   "John Doe",
			Latitude:     -17.8252,
			Longitude:    31.0335,
			LocationName: "Harare CBD",
			Speed:        65.5,
			Direction:    "South",
			EngineStatus: "On",
		},
		{
			Timestamp:    "2024-01-15 10:31:00",
			Name:         "Truck-001 (ABC123)",
			DriverName:   "<PERSON>",
			Latitude:     -17.8255,
			Longitude:    31.0340,
			LocationName: "Harare CBD",
			Speed:        68.2,
			Direction:    "South",
			EngineStatus: "On",
		},
	}

	// Create DTO with headers
	dto := &models.PositionLogDTO{
		Headers: GetHeadersFromStruct([]models.PositionLogRow{}),
		Data:    dtoData,
	}

	return dto, len(dtoData), len(dtoData), nil
}

// GenerateTripDetailDTOExample demonstrates how to use the TripDetailDTO
func (s *ReportService) GenerateTripDetailDTOExample(filters models.ReportFilters) (*models.TripDetailDTO, int, int, error) {
	// Example data - in real implementation, this would come from database queries
	dtoData := []models.TripDetailRow{
		{
			TripID:        1,
			DeviceName:    "Truck-001",
			PlateNumber:   "ABC123",
			DriverName:    "John Doe",
			StartTime:     "2024-01-15 08:00:00",
			EndTime:       "2024-01-15 12:00:00",
			Duration:      "4h 0m",
			Distance:      250.5,
			AvgSpeed:      62.6,
			MaxSpeed:      85.0,
			StartLocation: "Harare CBD",
			EndLocation:   "Bulawayo",
			FuelConsumed:  25.5,
			FuelCost:      38.25,
			Status:        "Completed",
		},
		{
			TripID:        2,
			DeviceName:    "Truck-002",
			PlateNumber:   "XYZ789",
			DriverName:    "Jane Smith",
			StartTime:     "2024-01-15 09:00:00",
			EndTime:       "2024-01-15 14:00:00",
			Duration:      "5h 0m",
			Distance:      300.0,
			AvgSpeed:      60.0,
			MaxSpeed:      80.0,
			StartLocation: "Harare",
			EndLocation:   "Mutare",
			FuelConsumed:  30.0,
			FuelCost:      45.0,
			Status:        "Completed",
		},
	}

	// Create DTO with headers
	dto := &models.TripDetailDTO{
		Headers: GetHeadersFromStruct([]models.TripDetailRow{}),
		Data:    dtoData,
	}

	return dto, len(dtoData), len(dtoData), nil
}

// GenerateDriverSafetyScorecardDTOExample demonstrates how to use the DriverSafetyScorecardDTO
func (s *ReportService) GenerateDriverSafetyScorecardDTOExample(filters models.ReportFilters) (*models.DriverSafetyScorecardDTO, int, int, error) {
	// Example data - in real implementation, this would come from database queries
	dtoData := []models.DriverSafetyScoreRow{
		{
			DriverName:        "John Doe",
			DeviceName:        "Truck-001",
			PlateNumber:       "ABC123",
			SafetyScore:       85.5,
			SpeedingEvents:    3,
			HarshBraking:      2,
			HarshAcceleration: 1,
			HarshCornering:    0,
			IdleTime:          "2h 30m",
			TotalTrips:        25,
			TotalDistance:     1250.5,
			AvgSpeed:          65.2,
			RiskLevel:         CalculateRiskLevel(85.5),
		},
		{
			DriverName:        "Jane Smith",
			DeviceName:        "Truck-002",
			PlateNumber:       "XYZ789",
			SafetyScore:       92.3,
			SpeedingEvents:    1,
			HarshBraking:      0,
			HarshAcceleration: 0,
			HarshCornering:    0,
			IdleTime:          "1h 45m",
			TotalTrips:        30,
			TotalDistance:     1800.0,
			AvgSpeed:          62.8,
			RiskLevel:         CalculateRiskLevel(92.3),
		},
	}

	// Calculate summary
	summary := models.DriverSafetySummary{
		TotalDrivers:     len(dtoData),
		AvgSafetyScore:   88.9,
		HighRiskDrivers:  0,
		TotalViolations:  7,
		TotalSpeeding:    4,
		TotalHarshEvents: 3,
	}

	// Create DTO with headers
	dto := &models.DriverSafetyScorecardDTO{
		Headers: GetHeadersFromStruct([]models.DriverSafetyScoreRow{}),
		Data:    dtoData,
		Summary: summary,
	}

	return dto, len(dtoData), len(dtoData), nil
}

// GenerateFuelConsumptionDTOExample demonstrates how to use the FuelConsumptionDTO
func (s *ReportService) GenerateFuelConsumptionDTOExample(filters models.ReportFilters) (*models.FuelConsumptionDTO, int, int, error) {
	// Example data - in real implementation, this would come from database queries
	dtoData := []models.FuelConsumptionRow{
		{
			Date:          "2024-01-15",
			DeviceName:    "Truck-001",
			PlateNumber:   "ABC123",
			DriverName:    "John Doe",
			FuelConsumed:  25.5,
			FuelCost:      38.25,
			Distance:      250.5,
			Efficiency:    9.8,
			AvgSpeed:      62.6,
			IdleTime:      "30m",
			IdleFuelWaste: 2.5,
			CostPerKm:     0.15,
		},
		{
			Date:          "2024-01-15",
			DeviceName:    "Truck-002",
			PlateNumber:   "XYZ789",
			DriverName:    "Jane Smith",
			FuelConsumed:  30.0,
			FuelCost:      45.0,
			Distance:      300.0,
			Efficiency:    10.0,
			AvgSpeed:      60.0,
			IdleTime:      "45m",
			IdleFuelWaste: 3.0,
			CostPerKm:     0.15,
		},
	}

	// Calculate summary
	summary := models.FuelConsumptionSummary{
		TotalFuelConsumed: 55.5,
		TotalFuelCost:     83.25,
		TotalDistance:     550.5,
		AvgEfficiency:     9.9,
		TotalIdleWaste:    5.5,
		AvgCostPerKm:      0.15,
	}

	// Create DTO with headers
	dto := &models.FuelConsumptionDTO{
		Headers: GetHeadersFromStruct([]models.FuelConsumptionRow{}),
		Data:    dtoData,
		Summary: summary,
	}

	return dto, len(dtoData), len(dtoData), nil
}

// Example of how to convert DTO to CSV format
func (s *ReportService) convertDTOToCSV(dto interface{}) [][]string {
	return ConvertStructToCSV(dto)
}

// Example of how to get headers from DTO
func (s *ReportService) getDTOHeaders(dto interface{}) []string {
	return GetHeadersFromStruct(dto)
}
