package services

import (
	"strings"
)

// AlertLevel represents the severity level of an alert
type AlertLevel string

const (
	AlertLevelCritical AlertLevel = "critical"
	AlertLevelWarning  AlertLevel = "warning"
	AlertLevelInfo     AlertLevel = "info"
)

// AlertCategorizationService handles categorizing alerts by their severity level
type AlertCategorizationService struct{}

// NewAlertCategorizationService creates a new instance of the alert categorization service
func NewAlertCategorizationService() *AlertCategorizationService {
	return &AlertCategorizationService{}
}

// CategorizeAlert determines the appropriate alert level based on the alert type
func (s *AlertCategorizationService) CategorizeAlert(alertType string) AlertLevel {
	alertType = strings.ToLower(strings.TrimSpace(alertType))

	// Critical alerts - immediate attention required
	if s.isCriticalAlert(alertType) {
		return AlertLevelCritical
	}

	// Warning alerts - attention needed but not immediately critical
	if s.isWarningAlert(alertType) {
		return AlertLevelWarning
	}

	// Default to info level
	return AlertLevelInfo
}

// isCriticalAlert checks if an alert type is critical
func (s *AlertCategorizationService) isCriticalAlert(alertType string) bool {
	criticalTypes := []string{
		"sos",               // SOS emergency
		"demolition",        // Device tampering
		"collision",         // Vehicle collision
		"crash",             // Crash detection
		"power cut",         // Power loss
		"power off",         // Device shutdown
		"off line",          // Device offline
		"battery remove",    // Battery removal
		"oil cut",           // Fuel/oil cut
		"illegal ignition",  // Unauthorized ignition
		"illegal door open", // Unauthorized access
		"shock",             // Impact/shock detection
		"tilt",              // Vehicle tilt/flip
		"flip",              // Vehicle rollover
		"tow",               // Vehicle towing
		"theft",             // Theft detection
		"emergency",         // General emergency
		"panic",             // Panic button
		"medical",           // Medical emergency
		"fire",              // Fire detection
		"break-in",          // Break-in detection
		"intrusion",         // Intrusion detection
	}

	for _, criticalType := range criticalTypes {
		if strings.Contains(alertType, criticalType) {
			return true
		}
	}

	return false
}

// isWarningAlert checks if an alert type is a warning
func (s *AlertCategorizationService) isWarningAlert(alertType string) bool {
	warningTypes := []string{
		"speeding",           // Speed violations
		"over speed",         // Excessive speed
		"rapid acceleration", // Harsh acceleration
		"harsh breaking",     // Harsh braking
		"sharp turn",         // Sharp turning
		"geofence",           // Geofence violations
		"fence in",           // Entering restricted area
		"fence out",          // Leaving restricted area
		"movement",           // Unauthorized movement
		"low battery",        // Low battery warning
		"external power low", // External power issues
		"gps blind",          // GPS signal issues
		"gps antenna",        // GPS antenna problems
		"door",               // Door status changes
		"light",              // Light status
		"maintenance",        // Maintenance required
		"fuel",               // Fuel level issues
		"temperature",        // Temperature warnings
		"pressure",           // Pressure warnings
		"vibration",          // Excessive vibration
		"engine",             // Engine issues
		"transmission",       // Transmission problems
		"brake",              // Brake system issues
		"tire",               // Tire pressure/monitoring
		"oil",                // Oil level/pressure
		"coolant",            // Coolant level/temperature
		"exhaust",            // Exhaust system
		"emission",           // Emission warnings
		"security",           // Security alerts
		"unauthorized",       // Unauthorized access attempts
	}

	for _, warningType := range warningTypes {
		if strings.Contains(alertType, warningType) {
			return true
		}
	}

	return false
}

// GetAlertLevelDescription returns a human-readable description of the alert level
func (s *AlertCategorizationService) GetAlertLevelDescription(level AlertLevel) string {
	switch level {
	case AlertLevelCritical:
		return "Critical - Immediate attention required"
	case AlertLevelWarning:
		return "Warning - Attention needed"
	case AlertLevelInfo:
		return "Information - For monitoring purposes"
	default:
		return "Unknown level"
	}
}

// GetAlertLevelPriority returns a numeric priority for sorting (higher = more important)
func (s *AlertCategorizationService) GetAlertLevelPriority(level AlertLevel) int {
	switch level {
	case AlertLevelCritical:
		return 3
	case AlertLevelWarning:
		return 2
	case AlertLevelInfo:
		return 1
	default:
		return 0
	}
}

// IsUrgent returns true if the alert level requires immediate attention
func (s *AlertCategorizationService) IsUrgent(level AlertLevel) bool {
	return level == AlertLevelCritical
}

// IsImportant returns true if the alert level is important (critical or warning)
func (s *AlertCategorizationService) IsImportant(level AlertLevel) bool {
	return level == AlertLevelCritical || level == AlertLevelWarning
}
