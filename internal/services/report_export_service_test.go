package services

import (
	"os"
	"strings"
	"testing"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
	"yotracker/migrations"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

type ReportExportServiceTestSuite struct {
	suite.Suite
	exportService  *ReportExportService
	testReportData *models.ReportData
}

func (suite *ReportExportServiceTestSuite) SetupSuite() {
	// Setup test environment using the same pattern as other tests
	utils.ForceProjectRoot()

	// Set up test environment variables using the same pattern
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}
	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}

	// Initialize test database
	config.InitTestDB()
	migrations.Migrate()

	suite.exportService = NewReportExportService()
	suite.createTestReportData()
}

func (suite *ReportExportServiceTestSuite) createTestReportData() {
	now := time.Now()

	// Create test trips data
	testTrips := []models.Trip{
		{
			Id:             1,
			ClientDeviceId: 1,
			DriverId:       uintPtr(1),
			StartTime:      now.Add(-2 * time.Hour),
			EndTime:        timePtr(now.Add(-1 * time.Hour)),
			Distance:       25.5,
			Duration:       intPtr(3600), // 1 hour
			AvgSpeed:       floatPtr(25.5),
			MaxSpeed:       floatPtr(45.0),
			IdleTime:       intPtr(300), // 5 minutes
		},
		{
			Id:             2,
			ClientDeviceId: 2,
			DriverId:       uintPtr(2),
			StartTime:      now.Add(-4 * time.Hour),
			EndTime:        timePtr(now.Add(-3 * time.Hour)),
			Distance:       15.2,
			Duration:       intPtr(2400), // 40 minutes
			AvgSpeed:       floatPtr(22.8),
			MaxSpeed:       floatPtr(60.0),
			IdleTime:       intPtr(180), // 3 minutes
		},
	}

	// Note: testBehaviorEvents and testGPSData are created inline in specific tests

	suite.testReportData = &models.ReportData{
		ReportInfo: models.Report{
			Id:          1,
			Name:        "Test Trip Report",
			Description: "Test report for export functionality",
			Category:    "Detail",
			ReportType:  "trip_detail",
			Status:      "active",
		},
		Filters: models.ReportFilters{
			StartDate: timePtr(now.Add(-24 * time.Hour)),
			EndDate:   timePtr(now),
			PerPage:   10,
		},
		Data: testTrips,
		Summary: map[string]interface{}{
			"total_trips":    2,
			"total_distance": 40.7,
			"avg_speed":      24.15,
			"total_duration": 6000,
		},
		Metadata: models.ReportMetadata{
			GeneratedAt:     now,
			TotalRecords:    2,
			FilteredRecords: 2,
			ExecutionTime:   "45ms",
			Format:          "json",
		},
	}
}

// Test PDF Export
func (suite *ReportExportServiceTestSuite) TestExportToPDF() {
	pdfData, err := suite.exportService.ExportToPDF(suite.testReportData)

	if err != nil && strings.Contains(err.Error(), "wkhtmltopdf not found") {
		suite.T().Skip("Skipping PDF test - wkhtmltopdf not available in CI environment")
		return
	}

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), pdfData)
	assert.Greater(suite.T(), len(pdfData), 1000, "PDF should have substantial content")

	// Check PDF header
	if len(pdfData) >= 4 {
		assert.True(suite.T(), strings.HasPrefix(string(pdfData[:4]), "%PDF"), "Should start with PDF header")
	}
}

// Test Excel Export
func (suite *ReportExportServiceTestSuite) TestExportToExcel() {
	excelData, err := suite.exportService.ExportToExcel(suite.testReportData)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), excelData)
	assert.Greater(suite.T(), len(excelData), 1000, "Excel file should have substantial content")

	// Check Excel file signature (ZIP format)
	assert.Equal(suite.T(), "PK", string(excelData[:2]), "Excel file should start with ZIP signature")
}

// Test CSV Export
func (suite *ReportExportServiceTestSuite) TestExportToCSV() {
	csvData, err := suite.exportService.ExportToCSV(suite.testReportData)

	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), csvData)
	assert.Greater(suite.T(), len(csvData), 100, "CSV should have content")

	csvString := string(csvData)

	// Check CSV structure
	assert.Contains(suite.T(), csvString, "Report,Test Trip Report")
	assert.Contains(suite.T(), csvString, "Description,Test report for export functionality")
	assert.Contains(suite.T(), csvString, "Total Records,2")
	assert.Contains(suite.T(), csvString, "Filtered Records,2")

	// Check data headers
	assert.Contains(suite.T(), csvString, "ID,Client Device ID,Driver ID")
	assert.Contains(suite.T(), csvString, "Start Time,End Time,Distance")
}

// Test Export with Different Data Types
func (suite *ReportExportServiceTestSuite) TestExportWithBehaviorEvents() {
	// Create report data with behavior events
	behaviorEvents := []models.DrivingBehaviorEvent{
		{
			Id:             1,
			ClientDeviceId: 1,
			EventType:      "overspeed",
			Timestamp:      time.Now(),
			Latitude:       -17.8250,
			Longitude:      31.0335,
			Speed:          floatPtr(65.0),
			Severity:       floatPtr(7.5),
		},
	}

	reportData := &models.ReportData{
		ReportInfo: models.Report{
			Name:        "Behavior Events Report",
			Description: "Test behavior events export",
			Category:    "Behavior",
			Status:      "active",
		},
		Data: behaviorEvents,
		Metadata: models.ReportMetadata{
			GeneratedAt:     time.Now(),
			TotalRecords:    1,
			FilteredRecords: 1,
		},
	}

	// Test CSV export with behavior events
	csvData, err := suite.exportService.ExportToCSV(reportData)
	assert.NoError(suite.T(), err)

	csvString := string(csvData)
	assert.Contains(suite.T(), csvString, "Event Type")
	assert.Contains(suite.T(), csvString, "overspeed")
	assert.Contains(suite.T(), csvString, "Severity")
}

// Test Export with GPS Data
func (suite *ReportExportServiceTestSuite) TestExportWithGPSData() {
	gpsData := []models.GPSData{
		{
			Id:             1,
			ClientDeviceId: uintPtr(1),
			GPSTimestamp:   timePtr(time.Now()),
			Latitude:       -17.8250,
			Longitude:      31.0335,
			Speed:          floatPtr(45.0),
			LocationName:   stringPtrExport("Test Location"),
		},
	}

	reportData := &models.ReportData{
		ReportInfo: models.Report{
			Name:        "GPS Data Report",
			Description: "Test GPS data export",
			Category:    "Detail",
			Status:      "active",
		},
		Data: gpsData,
		Metadata: models.ReportMetadata{
			GeneratedAt:     time.Now(),
			TotalRecords:    1,
			FilteredRecords: 1,
		},
	}

	// Test Excel export with GPS data
	excelData, err := suite.exportService.ExportToExcel(reportData)
	assert.NoError(suite.T(), err)
	assert.Greater(suite.T(), len(excelData), 1000)
}

// Test Export Service Main Function
func (suite *ReportExportServiceTestSuite) TestExportReport() {
	// Test PDF export
	pdfData, contentType, err := suite.exportService.ExportReport(suite.testReportData, "pdf")
	if err != nil && strings.Contains(err.Error(), "wkhtmltopdf not found") {
		suite.T().Log("Skipping PDF test - wkhtmltopdf not available in CI environment")
	} else {
		assert.NoError(suite.T(), err)
		assert.Equal(suite.T(), "application/pdf", contentType)
		assert.Greater(suite.T(), len(pdfData), 1000)
	}

	// Test Excel export
	excelData, contentType, err := suite.exportService.ExportReport(suite.testReportData, "excel")
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", contentType)
	assert.Greater(suite.T(), len(excelData), 1000)

	// Test CSV export
	csvData, contentType, err := suite.exportService.ExportReport(suite.testReportData, "csv")
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "text/csv", contentType)
	assert.Greater(suite.T(), len(csvData), 100)

	// Test invalid format
	_, _, err = suite.exportService.ExportReport(suite.testReportData, "invalid")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "unsupported export format")
}

// Test Empty Data Export
func (suite *ReportExportServiceTestSuite) TestExportEmptyData() {
	emptyReportData := &models.ReportData{
		ReportInfo: models.Report{
			Name:        "Empty Report",
			Description: "Report with no data",
			Category:    "Detail",
			Status:      "active",
		},
		Data: []interface{}{},
		Metadata: models.ReportMetadata{
			GeneratedAt:     time.Now(),
			TotalRecords:    0,
			FilteredRecords: 0,
		},
	}

	// Test CSV export with empty data
	csvData, err := suite.exportService.ExportToCSV(emptyReportData)
	assert.NoError(suite.T(), err)

	csvString := string(csvData)
	assert.Contains(suite.T(), csvString, "No data available")
}

// Test Performance
func (suite *ReportExportServiceTestSuite) TestExportPerformance() {
	start := time.Now()

	// Test PDF generation performance
	_, err := suite.exportService.ExportToPDF(suite.testReportData)
	pdfDuration := time.Since(start)

	if err != nil && strings.Contains(err.Error(), "wkhtmltopdf not found") {
		suite.T().Log("Skipping PDF performance test - wkhtmltopdf not available in CI environment")
	} else {
		assert.NoError(suite.T(), err)
		assert.Less(suite.T(), pdfDuration, 10*time.Second, "PDF generation should complete within 10 seconds")
	}

	start = time.Now()

	// Test Excel generation performance
	_, err = suite.exportService.ExportToExcel(suite.testReportData)
	excelDuration := time.Since(start)

	assert.NoError(suite.T(), err)
	assert.Less(suite.T(), excelDuration, 5*time.Second, "Excel generation should complete within 5 seconds")

	start = time.Now()

	// Test CSV generation performance
	_, err = suite.exportService.ExportToCSV(suite.testReportData)
	csvDuration := time.Since(start)

	assert.NoError(suite.T(), err)
	assert.Less(suite.T(), csvDuration, 1*time.Second, "CSV generation should complete within 1 second")
}

// Helper functions
func uintPtr(u uint) *uint {
	return &u
}

func intPtr(i int) *int {
	return &i
}

func floatPtr(f float64) *float64 {
	return &f
}

func stringPtrExport(s string) *string {
	return &s
}

func timePtr(t time.Time) *time.Time {
	return &t
}

// Run the test suite
func TestReportExportServiceTestSuite(t *testing.T) {
	suite.Run(t, new(ReportExportServiceTestSuite))
}
