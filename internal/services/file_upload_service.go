package services

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

type FileUploadService struct {
	MediaDir string
}

func NewFileUploadService() *FileUploadService {
	return &FileUploadService{
		MediaDir: "/var/www/yotrackermedia",
	}
}

// UploadFile handles file upload, storage, and database record creation
func (s *FileUploadService) UploadFile(file *multipart.FileHeader, clientId *uint, createdById uint, description *string) (*models.FileUpload, error) {
	// Validate file
	if err := s.validateFile(file); err != nil {
		return nil, err
	}

	// Generate unique filename
	fileName := s.generateUniqueFileName(file.Filename)

	// Determine file type
	fileType := s.determineFileType(file.Header.Get("Content-Type"))

	// Create file path
	filePath := filepath.Join(s.MediaDir, fileName)

	// Create directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(filePath), 0755); err != nil {
		return nil, fmt.Errorf("failed to create directory: %v", err)
	}

	// Open source file
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open uploaded file: %v", err)
	}
	defer src.Close()

	// Create destination file
	dst, err := os.Create(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to create destination file: %v", err)
	}
	defer dst.Close()

	// Copy file content
	if _, err = io.Copy(dst, src); err != nil {
		return nil, fmt.Errorf("failed to copy file: %v", err)
	}

	// Generate file URL (assuming files are served from /media/ path)
	fileUrl := fmt.Sprintf("/media/%s", fileName)

	// Create database record
	fileUpload := &models.FileUpload{
		ClientId:     clientId,
		CreatedById:  createdById,
		FileName:     fileName,
		OriginalName: file.Filename,
		FileSize:     file.Size,
		MimeType:     file.Header.Get("Content-Type"),
		FileUrl:      fileUrl,
		FilePath:     filePath,
		FileType:     fileType,
		Description:  description,
	}

	if err := config.DB.Create(fileUpload).Error; err != nil {
		// Clean up file if database insert fails
		os.Remove(filePath)
		return nil, fmt.Errorf("failed to save file record: %v", err)
	}

	return fileUpload, nil
}

// GetFileUploads retrieves file uploads with filtering and pagination
func (s *FileUploadService) GetFileUploads(filters models.FileUploadFilters) ([]models.FileUpload, int64, error) {
	var files []models.FileUpload
	var total int64

	query := config.DB.Model(&models.FileUpload{}).Preload("CreatedBy")

	// Apply filters
	if filters.ClientId > 0 {
		query = query.Where("client_id = ?", filters.ClientId)
	}
	if filters.FileType != "" {
		query = query.Where("file_type = ?", filters.FileType)
	}
	if filters.CreatedById != nil {
		query = query.Where("created_by_id = ?", *filters.CreatedById)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.PerPage == 0 {
		filters.PerPage = 20
	}

	// Apply pagination and get results
	offset := (filters.Page - 1) * filters.PerPage
	if err := query.Offset(offset).Limit(filters.PerPage).Order("created_at DESC").Find(&files).Error; err != nil {
		return nil, 0, err
	}

	return files, total, nil
}

// GetFileUploadById retrieves a specific file upload
func (s *FileUploadService) GetFileUploadById(id uint, clientId *uint) (*models.FileUpload, error) {
	var file models.FileUpload

	query := config.DB.Preload("CreatedBy").Where("id = ?", id)

	// If clientId is provided, filter by client
	if clientId != nil {
		query = query.Where("client_id = ?", *clientId)
	}

	if err := query.First(&file).Error; err != nil {
		return nil, err
	}

	return &file, nil
}

// DeleteFileUpload deletes a file upload and removes the physical file
func (s *FileUploadService) DeleteFileUpload(id uint, clientId *uint) error {
	// Get file record first
	file, err := s.GetFileUploadById(id, clientId)
	if err != nil {
		return err
	}

	// Delete physical file
	if err := os.Remove(file.FilePath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete physical file: %v", err)
	}

	// Delete database record
	if err := config.DB.Delete(file).Error; err != nil {
		return fmt.Errorf("failed to delete file record: %v", err)
	}

	return nil
}

// validateFile validates the uploaded file
func (s *FileUploadService) validateFile(file *multipart.FileHeader) error {
	// Check file size (50MB limit)
	const maxFileSize = 50 * 1024 * 1024 // 50MB
	if file.Size > maxFileSize {
		return fmt.Errorf("file size exceeds maximum limit of 50MB")
	}

	// Check file type
	allowedMimeTypes := map[string]bool{
		"image/jpeg":         true,
		"image/png":          true,
		"image/gif":          true,
		"image/webp":         true,
		"application/pdf":    true,
		"application/msword": true,
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document": true,
		"application/vnd.ms-excel": true,
		"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": true,
		"text/plain": true,
		"text/csv":   true,
		"video/mp4":  true,
		"video/avi":  true,
		"video/mov":  true,
		"audio/mpeg": true,
		"audio/wav":  true,
		"audio/mp3":  true,
	}

	if !allowedMimeTypes[file.Header.Get("Content-Type")] {
		return fmt.Errorf("file type not allowed")
	}

	return nil
}

// generateUniqueFileName generates a unique filename
func (s *FileUploadService) generateUniqueFileName(originalName string) string {
	// Generate random bytes
	bytes := make([]byte, 16)
	rand.Read(bytes)

	// Create hex string
	randomHex := hex.EncodeToString(bytes)

	// Get file extension
	ext := filepath.Ext(originalName)

	// Create timestamp
	timestamp := time.Now().Format("20060102_150405")

	// Combine parts
	return fmt.Sprintf("%s_%s%s", timestamp, randomHex, ext)
}

// determineFileType determines the file type based on MIME type
func (s *FileUploadService) determineFileType(mimeType string) string {
	switch {
	case strings.HasPrefix(mimeType, "image/"):
		return "image"
	case strings.HasPrefix(mimeType, "video/"):
		return "video"
	case strings.HasPrefix(mimeType, "audio/"):
		return "audio"
	case strings.HasPrefix(mimeType, "application/pdf"),
		strings.HasPrefix(mimeType, "application/msword"),
		strings.HasPrefix(mimeType, "application/vnd.openxmlformats-officedocument"),
		strings.HasPrefix(mimeType, "text/"):
		return "document"
	default:
		return "other"
	}
}
