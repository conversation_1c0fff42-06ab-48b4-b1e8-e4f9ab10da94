package services

import (
	"log"
	"yotracker/config"
)

// StartupService handles application startup initialization
type StartupService struct{}

// NewStartupService creates a new startup service
func NewStartupService() *StartupService {
	return &StartupService{}
}

// InitializeServices initializes all required services on startup
func (s *StartupService) InitializeServices() error {
	log.Println("Initializing application services...")

	// Initialize database
	config.InitDB()

	// Start real-time trip detection service
	manager := GetRealtimeServiceManager()
	if err := manager.StartService(); err != nil {
		log.Printf("Failed to start real-time trip detection service: %v", err)
		// Don't fail startup if real-time service fails - fallback to cron
	}

	// Initialize other services as needed
	// - Alert detection service
	// - Geofence service
	// - Notification services
	// etc.

	log.Println("Application services initialized successfully")
	return nil
}

// ShutdownServices gracefully shuts down all services
func (s *StartupService) ShutdownServices() error {
	log.Println("Shutting down application services...")

	// Stop real-time trip detection service
	manager := GetRealtimeServiceManager()
	if err := manager.StopService(); err != nil {
		log.Printf("Failed to stop real-time trip detection service: %v", err)
	}

	log.Println("Application services shut down successfully")
	return nil
}
