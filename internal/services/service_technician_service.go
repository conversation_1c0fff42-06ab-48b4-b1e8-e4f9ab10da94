package services

import (
	"errors"
	"yotracker/config"
	"yotracker/internal/models"
)

type ServiceTechnicianService struct{}

func NewServiceTechnicianService() *ServiceTechnicianService {
	return &ServiceTechnicianService{}
}

// GetServiceTechnicians returns all service technicians with filtering and pagination
func (s *ServiceTechnicianService) GetServiceTechnicians(filters models.ServiceTechnicianFilters) ([]models.ServiceTechnician, int64, error) {
	var technicians []models.ServiceTechnician
	var total int64

	query := config.DB.Where("client_id = ?", filters.ClientId)

	// Apply filters
	if filters.AvailabilityStatus != "" {
		query = query.Where("availability_status = ?", filters.AvailabilityStatus)
	}
	if filters.Specialization != "" {
		query = query.Where("specialization = ?", filters.Specialization)
	}

	// Get total count
	query.Model(&models.ServiceTechnician{}).Count(&total)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := (filters.Page - 1) * filters.PerPage
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	// Order by name
	query = query.Order("name ASC")

	err := query.Find(&technicians).Error
	return technicians, total, err
}

// GetServiceTechnician returns a specific service technician
func (s *ServiceTechnicianService) GetServiceTechnician(id uint, clientId uint) (*models.ServiceTechnician, error) {
	var technician models.ServiceTechnician
	err := config.DB.Where("id = ? AND client_id = ?", id, clientId).First(&technician).Error
	if err != nil {
		return nil, err
	}
	return &technician, nil
}

// CreateServiceTechnician creates a new service technician
func (s *ServiceTechnicianService) CreateServiceTechnician(technician models.ServiceTechnician) (*models.ServiceTechnician, error) {
	// Set default availability status if not provided
	if technician.AvailabilityStatus == "" {
		technician.AvailabilityStatus = "available"
	}

	err := config.DB.Create(&technician).Error
	if err != nil {
		return nil, err
	}

	return &technician, nil
}

// UpdateServiceTechnician updates an existing service technician
func (s *ServiceTechnicianService) UpdateServiceTechnician(technician models.ServiceTechnician) (*models.ServiceTechnician, error) {
	// Check if technician exists and belongs to client
	existingTechnician, err := s.GetServiceTechnician(technician.Id, technician.ClientId)
	if err != nil {
		return nil, errors.New("service technician not found")
	}

	// Update fields
	existingTechnician.TechnicianName = technician.TechnicianName
	existingTechnician.Email = technician.Email
	existingTechnician.ContactNumber = technician.ContactNumber
	existingTechnician.Specialization = technician.Specialization
	existingTechnician.AvailabilityStatus = technician.AvailabilityStatus
	existingTechnician.CurrentLocation = technician.CurrentLocation
	existingTechnician.Rating = technician.Rating
	existingTechnician.TotalAssignments = technician.TotalAssignments
	existingTechnician.SuccessfulRepairs = technician.SuccessfulRepairs

	err = config.DB.Save(existingTechnician).Error
	if err != nil {
		return nil, err
	}

	return existingTechnician, nil
}

// DeleteServiceTechnician deletes a service technician
func (s *ServiceTechnicianService) DeleteServiceTechnician(id uint, clientId uint) error {
	result := config.DB.Where("id = ? AND client_id = ?", id, clientId).Delete(&models.ServiceTechnician{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("service technician not found")
	}
	return nil
}

// GetAvailableTechnicians returns available service technicians
func (s *ServiceTechnicianService) GetAvailableTechnicians(clientId uint) ([]models.ServiceTechnician, error) {
	var technicians []models.ServiceTechnician

	err := config.DB.Where("client_id = ? AND availability_status = ?",
		clientId, "available").
		Order("name ASC").
		Find(&technicians).Error

	return technicians, err
}

// GetServiceTechnicianStats returns service technician statistics
func (s *ServiceTechnicianService) GetServiceTechnicianStats(clientId uint) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total technicians
	var total int64
	config.DB.Model(&models.ServiceTechnician{}).Where("client_id = ?", clientId).Count(&total)
	stats["total"] = total

	// Available technicians
	var available int64
	config.DB.Model(&models.ServiceTechnician{}).Where("client_id = ? AND availability_status = ?",
		clientId, "available").Count(&available)
	stats["available"] = available

	// Busy technicians
	var busy int64
	config.DB.Model(&models.ServiceTechnician{}).Where("client_id = ? AND availability_status = ?",
		clientId, "busy").Count(&busy)
	stats["busy"] = busy

	// Off duty technicians
	var offDuty int64
	config.DB.Model(&models.ServiceTechnician{}).Where("client_id = ? AND availability_status = ?",
		clientId, "off_duty").Count(&offDuty)
	stats["off_duty"] = offDuty

	// Average rating
	var avgRating float64
	config.DB.Model(&models.ServiceTechnician{}).
		Where("client_id = ? AND rating > 0", clientId).
		Select("AVG(rating)").Scan(&avgRating)
	stats["avg_rating"] = avgRating

	// Average successful repairs
	var avgSuccessfulRepairs float64
	config.DB.Model(&models.ServiceTechnician{}).
		Where("client_id = ? AND successful_repairs > 0", clientId).
		Select("AVG(successful_repairs)").Scan(&avgSuccessfulRepairs)
	stats["avg_successful_repairs"] = avgSuccessfulRepairs

	// Specialization breakdown
	var specializationBreakdown []struct {
		Specialization string `json:"specialization"`
		Count          int64  `json:"count"`
	}
	config.DB.Model(&models.ServiceTechnician{}).
		Select("specialization, COUNT(*) as count").
		Where("client_id = ?", clientId).
		Group("specialization").
		Scan(&specializationBreakdown)
	stats["specialization_breakdown"] = specializationBreakdown

	// Availability status breakdown
	var availabilityBreakdown []struct {
		AvailabilityStatus string `json:"availability_status"`
		Count              int64  `json:"count"`
	}
	config.DB.Model(&models.ServiceTechnician{}).
		Select("availability_status, COUNT(*) as count").
		Where("client_id = ?", clientId).
		Group("availability_status").
		Scan(&availabilityBreakdown)
	stats["availability_breakdown"] = availabilityBreakdown

	// Top technicians by rating
	var topTechnicians []struct {
		TechnicianName    string  `json:"technician_name"`
		Rating            float64 `json:"rating"`
		Specialization    string  `json:"specialization"`
		SuccessfulRepairs int     `json:"successful_repairs"`
	}
	config.DB.Model(&models.ServiceTechnician{}).
		Select("technician_name, rating, specialization, successful_repairs").
		Where("client_id = ?", clientId).
		Order("rating DESC").
		Limit(10).
		Scan(&topTechnicians)
	stats["top_technicians"] = topTechnicians

	// Total assignments
	var totalAssignments int64
	config.DB.Model(&models.ServiceTechnician{}).
		Where("client_id = ?", clientId).
		Select("COALESCE(SUM(total_assignments), 0)").Scan(&totalAssignments)
	stats["total_assignments"] = totalAssignments

	return stats, nil
}
