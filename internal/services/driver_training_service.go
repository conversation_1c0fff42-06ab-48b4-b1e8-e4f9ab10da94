package services

import (
	"errors"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

type DriverTrainingService struct{}

func NewDriverTrainingService() *DriverTrainingService {
	return &DriverTrainingService{}
}

// GetDriverTrainings returns all driver training records with filtering and pagination
func (s *DriverTrainingService) GetDriverTrainings(filters models.DriverTrainingFilters) ([]models.DriverTraining, int64, error) {
	var trainings []models.DriverTraining
	var total int64

	query := config.DB.Where("client_id = ?", filters.ClientId)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.DriverId != "" {
		query = query.Where("driver_id = ?", filters.DriverId)
	}
	if filters.TrainingType != "" {
		query = query.Where("training_type = ?", filters.TrainingType)
	}
	if filters.StartDate != nil {
		query = query.Where("training_date >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("training_date <= ?", filters.EndDate)
	}

	// Get total count
	query.Model(&models.DriverTraining{}).Count(&total)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := (filters.Page - 1) * filters.PerPage
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	// Order by training date
	query = query.Order("training_date DESC")

	err := query.Find(&trainings).Error
	return trainings, total, err
}

// GetDriverTraining returns a specific driver training record
func (s *DriverTrainingService) GetDriverTraining(id uint, clientId uint) (*models.DriverTraining, error) {
	var training models.DriverTraining
	err := config.DB.Where("id = ? AND client_id = ?", id, clientId).First(&training).Error
	if err != nil {
		return nil, err
	}
	return &training, nil
}

// CreateDriverTraining creates a new driver training record
func (s *DriverTrainingService) CreateDriverTraining(training models.DriverTraining) (*models.DriverTraining, error) {
	// Set default status if not provided
	if training.Status == "" {
		training.Status = "scheduled"
	}

	err := config.DB.Create(&training).Error
	if err != nil {
		return nil, err
	}

	return &training, nil
}

// UpdateDriverTraining updates an existing driver training record
func (s *DriverTrainingService) UpdateDriverTraining(training models.DriverTraining) (*models.DriverTraining, error) {
	// Check if training exists and belongs to client
	existingTraining, err := s.GetDriverTraining(training.Id, training.ClientId)
	if err != nil {
		return nil, errors.New("driver training record not found")
	}

	// Update fields
	existingTraining.TrainingType = training.TrainingType
	existingTraining.TrainingDate = training.TrainingDate
	existingTraining.CompletionDate = training.CompletionDate
	existingTraining.Score = training.Score
	existingTraining.Status = training.Status
	existingTraining.Trainer = training.Trainer
	existingTraining.TrainingProvider = training.TrainingProvider
	existingTraining.Cost = training.Cost
	existingTraining.Notes = training.Notes
	existingTraining.NextRefreshDate = training.NextRefreshDate

	err = config.DB.Save(existingTraining).Error
	if err != nil {
		return nil, err
	}

	return existingTraining, nil
}

// DeleteDriverTraining deletes a driver training record
func (s *DriverTrainingService) DeleteDriverTraining(id uint, clientId uint) error {
	result := config.DB.Where("id = ? AND client_id = ?", id, clientId).Delete(&models.DriverTraining{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("driver training record not found")
	}
	return nil
}

// GetExpiringTrainings returns training records that are expiring within specified days
func (s *DriverTrainingService) GetExpiringTrainings(clientId uint, days int) ([]models.DriverTraining, error) {
	var trainings []models.DriverTraining
	expiryDate := time.Now().AddDate(0, 0, days)

	err := config.DB.Where("client_id = ? AND next_refresh_date <= ? AND status = ?",
		clientId, expiryDate, "completed").
		Order("next_refresh_date ASC").
		Find(&trainings).Error

	return trainings, err
}

// GetDriverTrainingStats returns driver training statistics
func (s *DriverTrainingService) GetDriverTrainingStats(clientId uint) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total training records
	var total int64
	config.DB.Model(&models.DriverTraining{}).Where("client_id = ?", clientId).Count(&total)
	stats["total"] = total

	// Completed trainings
	var completed int64
	config.DB.Model(&models.DriverTraining{}).Where("client_id = ? AND status = ?",
		clientId, "completed").Count(&completed)
	stats["completed"] = completed

	// Scheduled trainings
	var scheduled int64
	config.DB.Model(&models.DriverTraining{}).Where("client_id = ? AND status = ?",
		clientId, "scheduled").Count(&scheduled)
	stats["scheduled"] = scheduled

	// Failed trainings
	var failed int64
	config.DB.Model(&models.DriverTraining{}).Where("client_id = ? AND status = ?",
		clientId, "failed").Count(&failed)
	stats["failed"] = failed

	// Expired trainings
	var expired int64
	config.DB.Model(&models.DriverTraining{}).Where("client_id = ? AND next_refresh_date < ? AND status = ?",
		clientId, time.Now(), "completed").Count(&expired)
	stats["expired"] = expired

	// Expiring soon (within 30 days)
	var expiringSoon int64
	expiryDate := time.Now().AddDate(0, 0, 30)
	config.DB.Model(&models.DriverTraining{}).Where("client_id = ? AND next_refresh_date <= ? AND status = ?",
		clientId, expiryDate, "completed").Count(&expiringSoon)
	stats["expiring_soon"] = expiringSoon

	// Average score
	var avgScore float64
	config.DB.Model(&models.DriverTraining{}).
		Where("client_id = ? AND score > 0", clientId).
		Select("AVG(score)").Scan(&avgScore)
	stats["avg_score"] = avgScore

	// Total cost
	var totalCost float64
	config.DB.Model(&models.DriverTraining{}).
		Where("client_id = ?", clientId).
		Select("COALESCE(SUM(cost), 0)").Scan(&totalCost)
	stats["total_cost"] = totalCost

	// Training type breakdown
	var trainingTypeBreakdown []struct {
		TrainingType string `json:"training_type"`
		Count        int64  `json:"count"`
	}
	config.DB.Model(&models.DriverTraining{}).
		Select("training_type, COUNT(*) as count").
		Where("client_id = ?", clientId).
		Group("training_type").
		Scan(&trainingTypeBreakdown)
	stats["training_type_breakdown"] = trainingTypeBreakdown

	// Status breakdown
	var statusBreakdown []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	config.DB.Model(&models.DriverTraining{}).
		Select("status, COUNT(*) as count").
		Where("client_id = ?", clientId).
		Group("status").
		Scan(&statusBreakdown)
	stats["status_breakdown"] = statusBreakdown

	// Top training providers
	var topProviders []struct {
		TrainingProvider string `json:"training_provider"`
		Count            int64  `json:"count"`
	}
	config.DB.Model(&models.DriverTraining{}).
		Select("training_provider, COUNT(*) as count").
		Where("client_id = ? AND training_provider != ''", clientId).
		Group("training_provider").
		Order("count DESC").
		Limit(10).
		Scan(&topProviders)
	stats["top_providers"] = topProviders

	return stats, nil
}
