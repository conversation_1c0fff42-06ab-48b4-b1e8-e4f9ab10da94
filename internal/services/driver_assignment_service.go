package services

import (
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

// GetCurrentDriverForDevice returns the current active driver assignment for a device on a specific date
func GetCurrentDriverForDevice(deviceId uint, date time.Time) (*models.Driver, error) {
	var assignment models.DriverDeviceAssignment

	err := config.DB.Where("client_device_id = ? AND assignment_date = ? AND status = 'active'",
		deviceId, date.Format("2006-01-02")).
		Preload("Driver").
		First(&assignment).Error

	if err != nil {
		return nil, err
	}

	return &assignment.Driver, nil
}

// GetCurrentDriverForDeviceToday returns the current active driver assignment for a device today
func GetCurrentDriverForDeviceToday(deviceId uint) (*models.Driver, error) {
	return GetCurrentDriverForDevice(deviceId, time.Now())
}

// GetDriverAssignmentsForDateRange returns all driver assignments for a device within a date range
func GetDriverAssignmentsForDateRange(deviceId uint, startDate, endDate time.Time) ([]models.DriverDeviceAssignment, error) {
	var assignments []models.DriverDeviceAssignment

	err := config.DB.Where("client_device_id = ? AND assignment_date BETWEEN ? AND ?",
		deviceId, startDate.Format("2006-01-02"), endDate.Format("2006-01-02")).
		Preload("Driver").
		Order("assignment_date ASC, start_time ASC").
		Find(&assignments).Error

	return assignments, err
}

// GetDriverAssignmentsForDriver returns all assignments for a specific driver within a date range
func GetDriverAssignmentsForDriver(driverId uint, startDate, endDate time.Time) ([]models.DriverDeviceAssignment, error) {
	var assignments []models.DriverDeviceAssignment

	err := config.DB.Where("driver_id = ? AND assignment_date BETWEEN ? AND ?",
		driverId, startDate.Format("2006-01-02"), endDate.Format("2006-01-02")).
		Preload("ClientDevice").
		Order("assignment_date ASC, start_time ASC").
		Find(&assignments).Error

	return assignments, err
}

// CheckDeviceAvailability checks if a device is available for assignment on a specific date
func CheckDeviceAvailability(deviceId uint, date time.Time) (bool, error) {
	var count int64

	err := config.DB.Model(&models.DriverDeviceAssignment{}).
		Where("client_device_id = ? AND assignment_date = ? AND status = 'active'",
			deviceId, date.Format("2006-01-02")).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count == 0, nil
}

// GetActiveAssignmentsForClient returns all active assignments for a client
func GetActiveAssignmentsForClient(clientId uint) ([]models.DriverDeviceAssignment, error) {
	var assignments []models.DriverDeviceAssignment

	err := config.DB.Where("client_id = ? AND status = 'active'", clientId).
		Preload("Driver").
		Preload("ClientDevice").
		Order("assignment_date DESC").
		Find(&assignments).Error

	return assignments, err
}

// CompleteAssignment marks an assignment as completed
func CompleteAssignment(assignmentId uint) error {
	return config.DB.Model(&models.DriverDeviceAssignment{}).
		Where("id = ?", assignmentId).
		Update("status", "completed").Error
}

// CancelAssignment marks an assignment as cancelled
func CancelAssignment(assignmentId uint) error {
	return config.DB.Model(&models.DriverDeviceAssignment{}).
		Where("id = ?", assignmentId).
		Update("status", "cancelled").Error
}
