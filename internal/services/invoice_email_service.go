package services

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"yotracker/internal/models"
)

// SendFirstOverdueReminderEmail sends a gentle first overdue reminder with invoice attachment
func SendFirstOverdueReminderEmail(invoice models.Invoice) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping first overdue reminder email to %s", invoice.Client.Email)
		return nil
	}

	mailService, err := NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Get invoice details
	invoiceRef := "N/A"
	if invoice.Reference != nil {
		invoiceRef = *invoice.Reference
	}

	dueDate := "N/A"
	if invoice.DueDate != nil {
		dueDate = invoice.DueDate.Format("January 2, 2006")
	}

	balance := 0.0
	if invoice.Balance != nil {
		balance = *invoice.Balance
	}

	currency := "$"
	if invoice.CurrencyId != nil {
		currency = invoice.Currency.Symbol
	}

	invoiceId := strconv.Itoa(int(invoice.Id))
	subject := fmt.Sprintf("Payment Reminder - Invoice %s", invoiceRef)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Dear %s,", invoice.Client.Name)).
		AddContent(
			"We hope this message finds you well.",
			"",
			fmt.Sprintf("This is a friendly reminder that Invoice %s was due on %s.", invoiceRef, dueDate),
			"",
			"Invoice Details:",
			fmt.Sprintf("• Invoice Number: %s", invoiceRef),
			fmt.Sprintf("• Due Date: %s", dueDate),
			fmt.Sprintf("• Outstanding Amount: %s%.2f", currency, balance),
			"",
			"If you have already made this payment, please disregard this reminder.",
			"If you have any questions about this invoice, please don't hesitate to contact us.",
			"",
			"Thank you for your business!",
		).
		SetAction("View Invoice", template.AppURL+"/invoices/"+invoiceId)

	return mailService.SendTemplatedEmail(invoice.Client.Email, subject, func(t *EmailTemplate) *EmailTemplate {
		return template
	})
}

// SendSecondOverdueReminderEmail sends a second overdue reminder
func SendSecondOverdueReminderEmail(invoice models.Invoice) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping second overdue reminder email to %s", invoice.Client.Email)
		return nil
	}

	mailService, err := NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Get invoice details
	invoiceRef := "N/A"
	if invoice.Reference != nil {
		invoiceRef = *invoice.Reference
	}

	dueDate := "N/A"
	if invoice.DueDate != nil {
		dueDate = invoice.DueDate.Format("January 2, 2006")
	}

	balance := 0.0
	if invoice.Balance != nil {
		balance = *invoice.Balance
	}

	currency := "$"
	if invoice.CurrencyId != nil {
		currency = invoice.Currency.Symbol
	}

	invoiceId := strconv.Itoa(int(invoice.Id))
	subject := fmt.Sprintf("Payment Overdue - Invoice %s", invoiceRef)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Dear %s,", invoice.Client.Name)).
		AddContent(
			"We hope this message finds you well.",
			"",
			fmt.Sprintf("This is a reminder that Invoice %s was due on %s and payment is now overdue.", invoiceRef, dueDate),
			"",
			"Invoice Details:",
			fmt.Sprintf("• Invoice Number: %s", invoiceRef),
			fmt.Sprintf("• Due Date: %s", dueDate),
			fmt.Sprintf("• Outstanding Amount: %s%.2f", currency, balance),
			"",
			"To avoid any service interruptions, please make payment as soon as possible.",
			"If you have any questions about this invoice, please don't hesitate to contact us.",
			"",
			"Thank you for your business!",
		).
		SetAction("Make Payment", template.AppURL+"/invoices/"+invoiceId)

	return mailService.SendTemplatedEmail(invoice.Client.Email, subject, func(t *EmailTemplate) *EmailTemplate {
		return template
	})
}

// SendThirdOverdueReminderEmail sends a final overdue reminder
func SendThirdOverdueReminderEmail(invoice models.Invoice) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping third overdue reminder email to %s", invoice.Client.Email)
		return nil
	}

	mailService, err := NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Get invoice details
	invoiceRef := "N/A"
	if invoice.Reference != nil {
		invoiceRef = *invoice.Reference
	}

	dueDate := "N/A"
	if invoice.DueDate != nil {
		dueDate = invoice.DueDate.Format("January 2, 2006")
	}

	balance := 0.0
	if invoice.Balance != nil {
		balance = *invoice.Balance
	}

	currency := "$"
	if invoice.CurrencyId != nil {
		currency = invoice.Currency.Symbol
	}

	invoiceId := strconv.Itoa(int(invoice.Id))
	subject := fmt.Sprintf("Final Notice - Payment Overdue - Invoice %s", invoiceRef)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Dear %s,", invoice.Client.Name)).
		AddContent(
			"This is our final notice regarding your overdue payment.",
			"",
			fmt.Sprintf("Invoice %s was due on %s and payment is now significantly overdue.", invoiceRef, dueDate),
			"",
			"Invoice Details:",
			fmt.Sprintf("• Invoice Number: %s", invoiceRef),
			fmt.Sprintf("• Due Date: %s", dueDate),
			fmt.Sprintf("• Outstanding Amount: %s%.2f", currency, balance),
			"",
			"To avoid service suspension, please make payment immediately.",
			"If you have any questions about this invoice, please contact us immediately.",
			"",
			"Thank you for your business!",
		).
		SetAction("Make Payment", template.AppURL+"/invoices/"+invoiceId)

	return mailService.SendTemplatedEmail(invoice.Client.Email, subject, func(t *EmailTemplate) *EmailTemplate {
		return template
	})
}

// SendInvoiceEmail sends an invoice email with PDF attachment
func SendInvoiceEmail(invoice *models.Invoice, pdfContent []byte) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping invoice email to %s", invoice.Client.Email)
		return nil
	}

	mailService, err := NewMailService()
	if err != nil {
		return fmt.Errorf("failed to initialize mail service: %v", err)
	}

	// Get invoice details
	invoiceRef := "N/A"
	if invoice.Reference != nil {
		invoiceRef = *invoice.Reference
	}

	dueDate := "N/A"
	if invoice.DueDate != nil {
		dueDate = invoice.DueDate.Format("January 2, 2006")
	}

	balance := 0.0
	if invoice.Balance != nil {
		balance = *invoice.Balance
	}

	currency := "$"
	if invoice.CurrencyId != nil {
		currency = invoice.Currency.Symbol
	}

	subject := fmt.Sprintf("Invoice %s - %s", invoiceRef, invoice.Client.Name)

	// Create templated email
	template := mailService.NewEmailTemplate()
	template = template.
		SetGreeting(fmt.Sprintf("Dear %s,", invoice.Client.Name)).
		AddContent(
			"Please find your invoice attached.",
			"",
			"Invoice Details:",
			fmt.Sprintf("• Invoice Number: %s", invoiceRef),
			fmt.Sprintf("• Due Date: %s", dueDate),
			fmt.Sprintf("• Amount: %s%.2f", currency, balance),
			"",
			"Thank you for your business!",
		).
		SetAction("View Invoice", template.AppURL+"/invoices/"+strconv.Itoa(int(invoice.Id)))

	return mailService.SendMailWithAttachment([]string{invoice.Client.Email}, subject, template.GenerateHTML(), true, "invoice.pdf", pdfContent, "application/pdf")
}
