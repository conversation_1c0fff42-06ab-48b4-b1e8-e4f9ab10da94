package services

import (
	"encoding/json"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/migrations"

	"github.com/stretchr/testify/suite"
)

// FlexibleNotificationServiceIntegrationTestSuite tests the notification logic
type FlexibleNotificationServiceIntegrationTestSuite struct {
	suite.Suite
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) SetupSuite() {
	config.InitTestDB()

	// Run migrations to create tables
	migrations.Migrate()
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) SetupTest() {
	// Clean up test data in proper order to avoid foreign key constraint issues
	// Start with child tables and work up to parent tables

	// First, delete from tables that depend on other tables
	config.DB.Exec("DELETE FROM device_alert_preferences")
	config.DB.Exec("DELETE FROM fleet_alert_preferences")
	config.DB.Exec("DELETE FROM alert_preferences")
	config.DB.Exec("DELETE FROM alerts")
	config.DB.Exec("DELETE FROM client_devices")
	config.DB.Exec("DELETE FROM trips")
	config.DB.Exec("DELETE FROM gps_data")
	config.DB.Exec("DELETE FROM trip_replays")
	config.DB.Exec("DELETE FROM invoice_items")
	config.DB.Exec("DELETE FROM invoice_payments")
	config.DB.Exec("DELETE FROM invoices")
	config.DB.Exec("DELETE FROM support_tickets")
	config.DB.Exec("DELETE FROM file_uploads")
	config.DB.Exec("DELETE FROM driver_device_assignments")
	config.DB.Exec("DELETE FROM client_device_daily_stats")

	// Then delete from intermediate tables
	config.DB.Exec("DELETE FROM device_types")
	config.DB.Exec("DELETE FROM protocols")
	config.DB.Exec("DELETE FROM fleets")
	config.DB.Exec("DELETE FROM users WHERE id > 1") // Keep seeded admin user

	// Finally delete from parent tables
	config.DB.Exec("DELETE FROM clients WHERE id > 1") // Keep seeded client
}

// Helper functions for creating test data
func (suite *FlexibleNotificationServiceIntegrationTestSuite) createTestProtocol() models.Protocol {
	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)
	return protocol
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) createTestDeviceType(protocolId uint) models.DeviceType {
	deviceType := models.DeviceType{
		ProtocolId: protocolId,
		Name:       "Test Device Type",
		Active:     true,
	}
	config.DB.Create(&deviceType)
	return deviceType
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) createTestClient(emailEnabled, smsEnabled, whatsappEnabled bool) models.Client {
	// Create email addresses JSON
	emails := []string{"<EMAIL>", "<EMAIL>"}
	emailsJSON, _ := json.Marshal(emails)
	emailsStr := string(emailsJSON)

	// Create phone numbers JSON
	phoneNumbers := []string{"+1234567890", "+0987654321"}
	phoneNumbersJSON, _ := json.Marshal(phoneNumbers)
	phoneNumbersStr := string(phoneNumbersJSON)

	// Create WhatsApp numbers JSON
	whatsappNumbers := []string{"+1234567890"}
	whatsappNumbersJSON, _ := json.Marshal(whatsappNumbers)
	whatsappNumbersStr := string(whatsappNumbersJSON)

	client := models.Client{
		Name:                  "Test Client",
		Status:                "active",
		PhoneNumber:           "1234567890",
		EmailAlertsEnabled:    &emailEnabled,
		SmsAlertsEnabled:      &smsEnabled,
		WhatsappAlertsEnabled: &whatsappEnabled,
		AlertsEmail:           &emailsStr,
		SmsPhoneNumber:        &phoneNumbersStr,
		WhatsappPhoneNumber:   &whatsappNumbersStr,
	}
	config.DB.Create(&client)
	return client
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) createTestFleet(clientId uint) models.Fleet {
	fleet := models.Fleet{
		ClientId: clientId,
		Name:     "Test Fleet",
	}
	config.DB.Create(&fleet)
	return fleet
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) createTestDevice(clientId, deviceTypeId, fleetId uint) models.ClientDevice {
	device := models.ClientDevice{
		DeviceTypeId: deviceTypeId,
		ClientId:     clientId,
		FleetId:      &fleetId,
		DeviceId:     "TEST123",
	}
	config.DB.Create(&device)
	return device
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) createClientAlertPreference(clientId uint, alertType models.AlertType, enabled bool, channels []models.NotificationChannel) models.AlertPreference {
	channelsJSON, _ := json.Marshal(channels)
	pref := models.AlertPreference{
		ClientId:  clientId,
		AlertType: alertType,
		Enabled:   enabled,
		Channels:  channelsJSON,
		Priority:  "normal",
	}
	config.DB.Create(&pref)
	return pref
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) createFleetAlertPreference(fleetId uint, alertType models.AlertType, enabled bool, channels []models.NotificationChannel) models.FleetAlertPreference {
	channelsJSON, _ := json.Marshal(channels)
	pref := models.FleetAlertPreference{
		FleetId:   fleetId,
		AlertType: alertType,
		Enabled:   enabled,
		Channels:  channelsJSON,
		Priority:  "normal",
	}
	config.DB.Create(&pref)
	return pref
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) createDeviceAlertPreference(deviceId uint, alertType models.AlertType, enabled bool, channels []models.NotificationChannel) models.DeviceAlertPreference {
	channelsJSON, _ := json.Marshal(channels)
	pref := models.DeviceAlertPreference{
		ClientDeviceId: deviceId,
		AlertType:      alertType,
		Enabled:        enabled,
		Channels:       channelsJSON,
		Priority:       "normal",
	}
	config.DB.Create(&pref)
	return pref
}

// Test Cases
func (suite *FlexibleNotificationServiceIntegrationTestSuite) TestAlertPreferenceCreation() {
	// Setup
	protocol := suite.createTestProtocol()
	deviceType := suite.createTestDeviceType(protocol.Id)
	client := suite.createTestClient(true, true, true)
	fleet := suite.createTestFleet(client.Id)
	device := suite.createTestDevice(client.Id, deviceType.Id, fleet.Id)

	// Test creating preferences at different levels
	clientPref := suite.createClientAlertPreference(
		client.Id,
		models.AlertTypeSpeed,
		true,
		[]models.NotificationChannel{models.NotificationChannelEmail},
	)

	fleetPref := suite.createFleetAlertPreference(
		fleet.Id,
		models.AlertTypeSpeed,
		true,
		[]models.NotificationChannel{models.NotificationChannelSMS},
	)

	devicePref := suite.createDeviceAlertPreference(
		device.Id,
		models.AlertTypeSpeed,
		true,
		[]models.NotificationChannel{models.NotificationChannelWhatsApp},
	)

	// Assertions
	suite.NotZero(clientPref.Id, "Client preference should be created")
	suite.NotZero(fleetPref.Id, "Fleet preference should be created")
	suite.NotZero(devicePref.Id, "Device preference should be created")

	// Verify channels are stored correctly
	clientChannels := clientPref.GetChannelsAsSlice()
	fleetChannels := fleetPref.GetChannelsAsSlice()
	deviceChannels := devicePref.GetChannelsAsSlice()

	suite.Len(clientChannels, 1, "Client preference should have 1 channel")
	suite.Len(fleetChannels, 1, "Fleet preference should have 1 channel")
	suite.Len(deviceChannels, 1, "Device preference should have 1 channel")

	suite.Contains(clientChannels, models.NotificationChannelEmail, "Client preference should have email channel")
	suite.Contains(fleetChannels, models.NotificationChannelSMS, "Fleet preference should have SMS channel")
	suite.Contains(deviceChannels, models.NotificationChannelWhatsApp, "Device preference should have WhatsApp channel")
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) TestAlertPreferenceHierarchy() {
	// Setup
	protocol := suite.createTestProtocol()
	deviceType := suite.createTestDeviceType(protocol.Id)
	client := suite.createTestClient(false, false, false) // All channels disabled
	fleet := suite.createTestFleet(client.Id)
	device := suite.createTestDevice(client.Id, deviceType.Id, fleet.Id)

	// Create preferences at different levels
	suite.createClientAlertPreference(
		client.Id,
		models.AlertTypeSpeed,
		true,
		[]models.NotificationChannel{models.NotificationChannelEmail},
	)

	suite.createFleetAlertPreference(
		fleet.Id,
		models.AlertTypeSpeed,
		true,
		[]models.NotificationChannel{models.NotificationChannelSMS},
	)

	suite.createDeviceAlertPreference(
		device.Id,
		models.AlertTypeSpeed,
		true,
		[]models.NotificationChannel{models.NotificationChannelWhatsApp},
	)

	// Test that device-level preference overrides others
	var devicePref models.DeviceAlertPreference
	err := config.DB.Where("client_device_id = ? AND alert_type = ? AND enabled = ?",
		device.Id, models.AlertTypeSpeed, true).First(&devicePref).Error

	suite.NoError(err, "Should find device preference")
	suite.Equal(device.Id, devicePref.ClientDeviceId, "Device preference should belong to correct device")

	channels := devicePref.GetChannelsAsSlice()
	suite.Len(channels, 1, "Device preference should have 1 channel")
	suite.Contains(channels, models.NotificationChannelWhatsApp, "Device preference should have WhatsApp channel")
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) TestAlertTypeConstants() {
	// Test that all alert types are properly defined
	suite.Equal("speed_alert", string(models.AlertTypeSpeed))
	suite.Equal("shutdown_time_alert", string(models.AlertTypeShutdown))
	suite.Equal("towing_event", string(models.AlertTypeTowing))
	suite.Equal("impact_detection", string(models.AlertTypeImpact))
	suite.Equal("geofence_event", string(models.AlertTypeGeofence))
	suite.Equal("maintenance_alert", string(models.AlertTypeMaintenance))
	suite.Equal("battery_alert", string(models.AlertTypeBattery))
	suite.Equal("sos_alert", string(models.AlertTypeSOS))
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) TestNotificationChannelConstants() {
	// Test that all notification channels are properly defined
	suite.Equal("email", string(models.NotificationChannelEmail))
	suite.Equal("sms", string(models.NotificationChannelSMS))
	suite.Equal("whatsapp", string(models.NotificationChannelWhatsApp))
	suite.Equal("slack", string(models.NotificationChannelSlack))
}

func (suite *FlexibleNotificationServiceIntegrationTestSuite) TestClientConfiguration() {
	// Setup
	protocol := suite.createTestProtocol()
	deviceType := suite.createTestDeviceType(protocol.Id)
	client := suite.createTestClient(true, true, true) // All channels enabled
	fleet := suite.createTestFleet(client.Id)
	_ = suite.createTestDevice(client.Id, deviceType.Id, fleet.Id) // Device not used in this test

	// Verify client configuration
	suite.True(*client.EmailAlertsEnabled, "Client should have email alerts enabled")
	suite.True(*client.SmsAlertsEnabled, "Client should have SMS alerts enabled")
	suite.True(*client.WhatsappAlertsEnabled, "Client should have WhatsApp alerts enabled")

	// Verify contact information is configured
	suite.NotNil(client.AlertsEmail, "Client should have email addresses configured")
	suite.NotNil(client.SmsPhoneNumber, "Client should have SMS phone numbers configured")
	suite.NotNil(client.WhatsappPhoneNumber, "Client should have WhatsApp phone numbers configured")

	// Parse and verify JSON data
	var emails []string
	var phoneNumbers []string
	var whatsappNumbers []string

	json.Unmarshal([]byte(*client.AlertsEmail), &emails)
	json.Unmarshal([]byte(*client.SmsPhoneNumber), &phoneNumbers)
	json.Unmarshal([]byte(*client.WhatsappPhoneNumber), &whatsappNumbers)

	suite.Len(emails, 2, "Client should have 2 email addresses")
	suite.Len(phoneNumbers, 2, "Client should have 2 phone numbers")
	suite.Len(whatsappNumbers, 1, "Client should have 1 WhatsApp number")
}

func TestFlexibleNotificationServiceIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(FlexibleNotificationServiceIntegrationTestSuite))
}
