package services

import (
	"testing"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/seed"
	"yotracker/migrations"

	"github.com/stretchr/testify/assert"
)

func TestSupportTicketService(t *testing.T) {
	// Initialize test database
	config.InitTestDB()

	// Run migrations and seed data
	migrations.Migrate()
	seed.Seed()

	// Clean up any existing data (in reverse dependency order)
	config.DB.Exec("DELETE FROM support_ticket_replies")
	config.DB.Exec("DELETE FROM support_tickets")
	config.DB.Exec("DELETE FROM geofence_events")
	config.DB.Exec("DELETE FROM client_devices")
	config.DB.Exec("DELETE FROM file_uploads")
	config.DB.Exec("DELETE FROM invoices")
	config.DB.Exec("DELETE FROM users")
	config.DB.Exec("DELETE FROM clients")

	// Create test data
	client := models.Client{
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	user := models.User{
		Name:     "Test User",
		Email:    "<EMAIL>",
		Password: "password123",
		UserType: "backend",
	}
	config.DB.Create(&user)

	adminUser := models.User{
		Name:     "Admin User",
		Email:    "<EMAIL>",
		Password: "password123",
		UserType: "backend",
	}
	config.DB.Create(&adminUser)

	// Create a client user for testing
	clientUser := models.User{
		Name:     "Client User",
		Email:    "<EMAIL>",
		Password: "password123",
		UserType: "frontend",
		ClientId: &client.Id, // This makes it a client user
	}
	config.DB.Create(&clientUser)

	// Get existing device type from seeded data
	var deviceType models.DeviceType
	config.DB.Where("name = ?", "st901").First(&deviceType)

	deviceName := "Test Device"
	device := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "TEST001",
		Name:         &deviceName,
	}
	config.DB.Create(&device)

	service := NewSupportTicketService()

	t.Run("CreateSupportTicket", func(t *testing.T) {
		ticket := models.SupportTicket{
			ClientId:       client.Id,
			CreatedById:    user.Id,
			ClientDeviceId: &device.Id,
			Department:     "technical",
			Priority:       "high",
			Subject:        "Test Ticket",
			Description:    "Test description",
			Status:         "open",
		}

		createdTicket, err := service.CreateSupportTicket(ticket)
		assert.NoError(t, err)
		assert.NotNil(t, createdTicket)
		assert.Equal(t, "Test Ticket", createdTicket.Subject)
		assert.Equal(t, "technical", createdTicket.Department)
		assert.Equal(t, "high", createdTicket.Priority)
		assert.Equal(t, "open", createdTicket.Status)
		assert.Equal(t, client.Id, createdTicket.ClientId)
		assert.Equal(t, user.Id, createdTicket.CreatedById)
	})

	t.Run("GetSupportTickets", func(t *testing.T) {
		// Create additional test tickets
		ticket1 := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "billing",
			Priority:    "medium",
			Subject:     "Billing Issue",
			Description: "Billing problem",
			Status:      "open",
		}
		config.DB.Create(&ticket1)

		ticket2 := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "technical",
			Priority:    "urgent",
			Subject:     "Urgent Issue",
			Description: "Urgent problem",
			Status:      "assigned",
		}
		config.DB.Create(&ticket2)

		// Test getting all tickets for client
		filters := models.SupportTicketFilters{
			ClientId: client.Id,
			Page:     1,
			PerPage:  10,
		}

		tickets, total, err := service.GetSupportTickets(filters)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(3))
		assert.GreaterOrEqual(t, len(tickets), 3)

		// Test filtering by department
		filters.Department = "technical"
		tickets, total, err = service.GetSupportTickets(filters)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(2))

		// Test filtering by priority
		filters.Priority = "urgent"
		tickets, total, err = service.GetSupportTickets(filters)
		assert.NoError(t, err)
		assert.GreaterOrEqual(t, total, int64(1))
	})

	t.Run("GetSupportTicket", func(t *testing.T) {
		// Create a test ticket
		ticket := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "general",
			Priority:    "low",
			Subject:     "Get Test Ticket",
			Description: "Test for getting ticket",
			Status:      "open",
		}
		config.DB.Create(&ticket)

		// Test getting the ticket
		retrievedTicket, err := service.GetSupportTicket(ticket.Id, client.Id)
		assert.NoError(t, err)
		assert.NotNil(t, retrievedTicket)
		assert.Equal(t, "Get Test Ticket", retrievedTicket.Subject)
		assert.Equal(t, client.Id, retrievedTicket.ClientId)

		// Test getting ticket with wrong client ID (should fail)
		_, err = service.GetSupportTicket(ticket.Id, client.Id+1)
		assert.Error(t, err)
	})

	t.Run("UpdateSupportTicket", func(t *testing.T) {
		// Create a test ticket
		ticket := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "general",
			Priority:    "low",
			Subject:     "Update Test Ticket",
			Description: "Test for updating ticket",
			Status:      "open",
		}
		config.DB.Create(&ticket)

		// Update the ticket
		updateData := models.SupportTicket{
			Id:           ticket.Id,
			ClientId:     client.Id,
			Status:       "in_progress",
			Priority:     "high",
			AssignedToId: &adminUser.Id,
		}

		updatedTicket, err := service.UpdateSupportTicket(updateData)
		assert.NoError(t, err)
		assert.NotNil(t, updatedTicket)
		assert.Equal(t, "in_progress", updatedTicket.Status)
		assert.Equal(t, "high", updatedTicket.Priority)
		assert.Equal(t, adminUser.Id, *updatedTicket.AssignedToId)
	})

	t.Run("GetSupportTicketStats", func(t *testing.T) {
		// Create tickets with different statuses
		tickets := []models.SupportTicket{
			{
				ClientId:    client.Id,
				CreatedById: user.Id,
				Department:  "technical",
				Priority:    "urgent",
				Subject:     "Stats Urgent Ticket",
				Description: "Urgent issue for stats",
				Status:      "open",
			},
			{
				ClientId:    client.Id,
				CreatedById: user.Id,
				Department:  "billing",
				Priority:    "high",
				Subject:     "Stats High Priority",
				Description: "High priority issue for stats",
				Status:      "assigned",
			},
			{
				ClientId:    client.Id,
				CreatedById: user.Id,
				Department:  "general",
				Priority:    "medium",
				Subject:     "Stats Resolved Ticket",
				Description: "Resolved issue for stats",
				Status:      "resolved",
			},
		}

		for _, ticket := range tickets {
			config.DB.Create(&ticket)
		}

		stats, err := service.GetSupportTicketStats(client.Id)
		assert.NoError(t, err)
		assert.NotNil(t, stats)
		assert.GreaterOrEqual(t, stats.TotalTickets, int64(6)) // Including previous test tickets
		assert.GreaterOrEqual(t, stats.OpenTickets, int64(1))
		assert.GreaterOrEqual(t, stats.UrgentTickets, int64(1))
	})

	t.Run("DeleteSupportTicket", func(t *testing.T) {
		// Create a test ticket
		ticket := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "general",
			Priority:    "low",
			Subject:     "Delete Test Ticket",
			Description: "Test for deleting ticket",
			Status:      "open",
		}
		config.DB.Create(&ticket)

		// Delete the ticket
		err := service.DeleteSupportTicket(ticket.Id, client.Id)
		assert.NoError(t, err)

		// Verify ticket is deleted
		var deletedTicket models.SupportTicket
		err = config.DB.First(&deletedTicket, ticket.Id).Error
		assert.Error(t, err) // Should not find the ticket
	})

	t.Run("AssignSupportTicket", func(t *testing.T) {
		// Create a test ticket
		ticket := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "technical",
			Priority:    "medium",
			Subject:     "Assignment Test Ticket",
			Description: "Test for assigning ticket",
			Status:      "open",
		}
		config.DB.Create(&ticket)

		// Assign ticket to admin user
		updatedTicket, err := service.AssignSupportTicket(ticket.Id, adminUser.Id)
		assert.NoError(t, err)
		assert.NotNil(t, updatedTicket)
		assert.Equal(t, adminUser.Id, *updatedTicket.AssignedToId)
		assert.Equal(t, "assigned", updatedTicket.Status) // Status should change from "open" to "assigned"

		// Verify assignment in database
		var dbTicket models.SupportTicket
		err = config.DB.Preload("AssignedTo").First(&dbTicket, ticket.Id).Error
		assert.NoError(t, err)
		assert.Equal(t, adminUser.Id, *dbTicket.AssignedToId)
		assert.Equal(t, "assigned", dbTicket.Status)
		assert.NotNil(t, dbTicket.AssignedTo)
		assert.Equal(t, adminUser.Name, dbTicket.AssignedTo.Name)
	})

	t.Run("AssignSupportTicket_InvalidUser", func(t *testing.T) {
		// Create a test ticket
		ticket := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "technical",
			Priority:    "medium",
			Subject:     "Invalid Assignment Test",
			Description: "Test for invalid assignment",
			Status:      "open",
		}
		config.DB.Create(&ticket)

		// Try to assign to non-existent user
		updatedTicket, err := service.AssignSupportTicket(ticket.Id, 99999)
		assert.Error(t, err)
		assert.Nil(t, updatedTicket)
		assert.Contains(t, err.Error(), "assigned user not found")

		// Try to assign to client user (should fail)
		updatedTicket, err = service.AssignSupportTicket(ticket.Id, clientUser.Id)
		assert.Error(t, err)
		assert.Nil(t, updatedTicket)
		assert.Contains(t, err.Error(), "not a staff member")
	})

	t.Run("AssignSupportTicket_NonExistentTicket", func(t *testing.T) {
		// Try to assign non-existent ticket
		updatedTicket, err := service.AssignSupportTicket(99999, adminUser.Id)
		assert.Error(t, err)
		assert.Nil(t, updatedTicket)
		assert.Contains(t, err.Error(), "ticket not found")
	})

	// Clean up
	config.DB.Exec("DELETE FROM support_ticket_replies")
	config.DB.Exec("DELETE FROM support_tickets")
	config.DB.Exec("DELETE FROM client_devices")
	config.DB.Exec("DELETE FROM file_uploads")
	config.DB.Exec("DELETE FROM invoices")
	config.DB.Exec("DELETE FROM users")
	config.DB.Exec("DELETE FROM clients")
}
