package services

import (
	"fmt"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

// AlertWithLocationService provides methods to work with alerts and their GPS data
type AlertWithLocationService struct{}

// NewAlertWithLocationService creates a new alert with location service
func NewAlertWithLocationService() *AlertWithLocationService {
	return &AlertWithLocationService{}
}

// GetAlertsWithLocation retrieves alerts with their associated GPS data
func (s *AlertWithLocationService) GetAlertsWithLocation(clientDeviceId *uint, alertType *string, startDate, endDate *time.Time, limit int) ([]models.Alert, error) {
	var alerts []models.Alert

	query := config.DB.Preload("GPSData").Preload("ClientDevice")

	// Apply filters
	if clientDeviceId != nil {
		query = query.Where("client_device_id = ?", *clientDeviceId)
	}
	if alertType != nil {
		query = query.Where("alert_type = ?", *alertType)
	}
	if startDate != nil {
		query = query.Where("alert_timestamp >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("alert_timestamp <= ?", *endDate)
	}

	// Order by timestamp descending and limit results
	query = query.Order("alert_timestamp DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&alerts).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve alerts with location: %w", err)
	}

	return alerts, nil
}

// GetAlertWithLocation retrieves a specific alert with its GPS data
func (s *AlertWithLocationService) GetAlertWithLocation(alertId uint) (*models.Alert, error) {
	var alert models.Alert

	if err := config.DB.Preload("GPSData").Preload("ClientDevice").First(&alert, alertId).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve alert with location: %w", err)
	}

	return &alert, nil
}

// GetAlertsByLocation retrieves alerts within a specific geographic area
func (s *AlertWithLocationService) GetAlertsByLocation(minLat, maxLat, minLng, maxLng float64, alertType *string, limit int) ([]models.Alert, error) {
	var alerts []models.Alert

	query := config.DB.Preload("GPSData").Preload("ClientDevice").
		Joins("JOIN gps_data ON alerts.gps_data_id = gps_data.id").
		Where("gps_data.latitude >= ? AND gps_data.latitude <= ? AND gps_data.longitude >= ? AND gps_data.longitude <= ?",
			minLat, maxLat, minLng, maxLng)

	// Apply alert type filter if provided
	if alertType != nil {
		query = query.Where("alerts.alert_type = ?", *alertType)
	}

	// Order by timestamp descending and limit results
	query = query.Order("alerts.alert_timestamp DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&alerts).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve alerts by location: %w", err)
	}

	return alerts, nil
}

// GetAlertsByDeviceWithLocation retrieves alerts for a specific device with GPS data
func (s *AlertWithLocationService) GetAlertsByDeviceWithLocation(deviceId string, alertType *string, limit int) ([]models.Alert, error) {
	var alerts []models.Alert

	query := config.DB.Preload("GPSData").Preload("ClientDevice").
		Where("alerts.device_id = ?", deviceId)

	// Apply alert type filter if provided
	if alertType != nil {
		query = query.Where("alerts.alert_type = ?", *alertType)
	}

	// Order by timestamp descending and limit results
	query = query.Order("alerts.alert_timestamp DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&alerts).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve alerts by device with location: %w", err)
	}

	return alerts, nil
}

// GetAlertStatisticsWithLocation provides statistics about alerts with location data
func (s *AlertWithLocationService) GetAlertStatisticsWithLocation(clientDeviceId *uint, startDate, endDate *time.Time) (*AlertLocationStatistics, error) {
	var stats AlertLocationStatistics

	query := config.DB.Model(&models.Alert{})

	// Apply filters
	if clientDeviceId != nil {
		query = query.Where("client_device_id = ?", *clientDeviceId)
	}
	if startDate != nil {
		query = query.Where("alert_timestamp >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("alert_timestamp <= ?", *endDate)
	}

	// Count total alerts
	if err := query.Count(&stats.TotalAlerts).Error; err != nil {
		return nil, fmt.Errorf("failed to count total alerts: %w", err)
	}

	// Count alerts with GPS data
	if err := query.Where("gps_data_id IS NOT NULL").Count(&stats.AlertsWithLocation).Error; err != nil {
		return nil, fmt.Errorf("failed to count alerts with location: %w", err)
	}

	// Count by alert type
	var alertTypeCounts []struct {
		AlertType string `json:"alert_type"`
		Count     int    `json:"count"`
	}
	if err := query.Select("alert_type, COUNT(*) as count").Group("alert_type").Scan(&alertTypeCounts).Error; err != nil {
		return nil, fmt.Errorf("failed to count alerts by type: %w", err)
	}

	stats.AlertsByType = make(map[string]int)
	for _, count := range alertTypeCounts {
		stats.AlertsByType[count.AlertType] = count.Count
	}

	// Calculate percentage with location
	if stats.TotalAlerts > 0 {
		stats.LocationCoveragePercentage = float64(stats.AlertsWithLocation) / float64(stats.TotalAlerts) * 100
	}

	return &stats, nil
}

// AlertLocationStatistics represents statistics about alerts with location data
type AlertLocationStatistics struct {
	TotalAlerts                int64          `json:"total_alerts"`
	AlertsWithLocation         int64          `json:"alerts_with_location"`
	LocationCoveragePercentage float64        `json:"location_coverage_percentage"`
	AlertsByType               map[string]int `json:"alerts_by_type"`
}

// GetAlertsForMapView retrieves alerts optimized for map visualization
func (s *AlertWithLocationService) GetAlertsForMapView(clientDeviceId *uint, alertType *string, startDate, endDate *time.Time) ([]MapAlert, error) {
	var alerts []models.Alert

	query := config.DB.Preload("GPSData").Preload("ClientDevice").
		Where("gps_data_id IS NOT NULL") // Only alerts with GPS data

	// Apply filters
	if clientDeviceId != nil {
		query = query.Where("client_device_id = ?", *clientDeviceId)
	}
	if alertType != nil {
		query = query.Where("alert_type = ?", *alertType)
	}
	if startDate != nil {
		query = query.Where("alert_timestamp >= ?", *startDate)
	}
	if endDate != nil {
		query = query.Where("alert_timestamp <= ?", *endDate)
	}

	// Order by timestamp descending
	query = query.Order("alert_timestamp DESC")

	if err := query.Find(&alerts).Error; err != nil {
		return nil, fmt.Errorf("failed to retrieve alerts for map view: %w", err)
	}

	// Convert to map alerts
	mapAlerts := make([]MapAlert, len(alerts))
	for i, alert := range alerts {
		if alert.GPSData != nil {
			mapAlerts[i] = MapAlert{
				Id:             alert.Id,
				AlertType:      alert.AlertType,
				AlertName:      alert.AlertName,
				AlertLevel:     alert.AlertLevel,
				Message:        alert.Message,
				AlertTimestamp: alert.AlertTimestamp,
				Latitude:       alert.GPSData.Latitude,
				Longitude:      alert.GPSData.Longitude,
				Speed:          alert.GPSData.Speed,
				Direction:      alert.GPSData.Direction,
				LocationName:   alert.GPSData.LocationName,
				DeviceId:       alert.DeviceId,
				ClientDeviceId: alert.ClientDeviceId,
			}
		}
	}

	return mapAlerts, nil
}

// MapAlert represents an alert optimized for map visualization
type MapAlert struct {
	Id             uint      `json:"id"`
	AlertType      string    `json:"alert_type"`
	AlertName      *string   `json:"alert_name"`
	AlertLevel     *string   `json:"alert_level"`
	Message        *string   `json:"message"`
	AlertTimestamp time.Time `json:"alert_timestamp"`
	Latitude       float64   `json:"latitude"`
	Longitude      float64   `json:"longitude"`
	Speed          *float64  `json:"speed"`
	Direction      *string   `json:"direction"`
	LocationName   *string   `json:"location_name"`
	DeviceId       *string   `json:"device_id"`
	ClientDeviceId uint      `json:"client_device_id"`
}
