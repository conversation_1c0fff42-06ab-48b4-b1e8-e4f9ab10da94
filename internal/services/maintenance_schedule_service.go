package services

import (
	"errors"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

type MaintenanceScheduleService struct{}

func NewMaintenanceScheduleService() *MaintenanceScheduleService {
	return &MaintenanceScheduleService{}
}

// GetMaintenanceSchedules returns all maintenance schedules with filtering and pagination
func (s *MaintenanceScheduleService) GetMaintenanceSchedules(filters models.MaintenanceScheduleFilters) ([]models.MaintenanceSchedule, int64, error) {
	var schedules []models.MaintenanceSchedule
	var total int64

	query := config.DB.Where("client_id = ?", filters.ClientId)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.Priority != "" {
		query = query.Where("priority = ?", filters.Priority)
	}
	if filters.MaintenanceType != "" {
		query = query.Where("maintenance_type = ?", filters.MaintenanceType)
	}
	if filters.ClientDeviceId != "" {
		query = query.Where("client_device_id = ?", filters.ClientDeviceId)
	}
	if filters.StartDate != nil {
		query = query.Where("due_date >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("due_date <= ?", filters.EndDate)
	}

	// Get total count
	query.Model(&models.MaintenanceSchedule{}).Count(&total)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := (filters.Page - 1) * filters.PerPage
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	// Order by due date
	query = query.Order("due_date ASC")

	err := query.Find(&schedules).Error
	return schedules, total, err
}

// GetMaintenanceSchedule returns a specific maintenance schedule
func (s *MaintenanceScheduleService) GetMaintenanceSchedule(id uint, clientId uint) (*models.MaintenanceSchedule, error) {
	var schedule models.MaintenanceSchedule
	err := config.DB.Where("id = ? AND client_id = ?", id, clientId).First(&schedule).Error
	if err != nil {
		return nil, err
	}
	return &schedule, nil
}

// CreateMaintenanceSchedule creates a new maintenance schedule
func (s *MaintenanceScheduleService) CreateMaintenanceSchedule(schedule models.MaintenanceSchedule) (*models.MaintenanceSchedule, error) {
	// Set default status if not provided
	if schedule.Status == "" {
		schedule.Status = "scheduled"
	}

	// Set default priority if not provided
	if schedule.Priority == "" {
		schedule.Priority = "medium"
	}

	// Set default maintenance type if not provided
	if schedule.MaintenanceType == "" {
		schedule.MaintenanceType = "scheduled"
	}

	err := config.DB.Create(&schedule).Error
	if err != nil {
		return nil, err
	}

	return &schedule, nil
}

// UpdateMaintenanceSchedule updates an existing maintenance schedule
func (s *MaintenanceScheduleService) UpdateMaintenanceSchedule(schedule models.MaintenanceSchedule) (*models.MaintenanceSchedule, error) {
	// Check if schedule exists and belongs to client
	existingSchedule, err := s.GetMaintenanceSchedule(schedule.Id, schedule.ClientId)
	if err != nil {
		return nil, errors.New("maintenance schedule not found")
	}

	// Update fields
	existingSchedule.ServiceType = schedule.ServiceType
	existingSchedule.ScheduledDate = schedule.ScheduledDate
	existingSchedule.DueDate = schedule.DueDate
	existingSchedule.CompletedDate = schedule.CompletedDate
	existingSchedule.Cost = schedule.Cost
	existingSchedule.ServiceProvider = schedule.ServiceProvider
	existingSchedule.Description = schedule.Description
	existingSchedule.Status = schedule.Status
	existingSchedule.Priority = schedule.Priority
	existingSchedule.MaintenanceType = schedule.MaintenanceType

	err = config.DB.Save(existingSchedule).Error
	if err != nil {
		return nil, err
	}

	return existingSchedule, nil
}

// DeleteMaintenanceSchedule deletes a maintenance schedule
func (s *MaintenanceScheduleService) DeleteMaintenanceSchedule(id uint, clientId uint) error {
	result := config.DB.Where("id = ? AND client_id = ?", id, clientId).Delete(&models.MaintenanceSchedule{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("maintenance schedule not found")
	}
	return nil
}

// GetUpcomingMaintenance returns maintenance schedules due within specified days
func (s *MaintenanceScheduleService) GetUpcomingMaintenance(clientId uint, days int) ([]models.MaintenanceSchedule, error) {
	var schedules []models.MaintenanceSchedule
	dueDate := time.Now().AddDate(0, 0, days)

	err := config.DB.Where("client_id = ? AND due_date <= ? AND status IN (?, ?)",
		clientId, dueDate, "scheduled", "overdue").
		Order("due_date ASC").
		Find(&schedules).Error

	return schedules, err
}

// GetMaintenanceStats returns maintenance statistics
func (s *MaintenanceScheduleService) GetMaintenanceStats(clientId uint) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total maintenance schedules
	var total int64
	config.DB.Model(&models.MaintenanceSchedule{}).Where("client_id = ?", clientId).Count(&total)
	stats["total"] = total

	// Overdue maintenance
	var overdue int64
	config.DB.Model(&models.MaintenanceSchedule{}).Where("client_id = ? AND due_date < ? AND status = ?",
		clientId, time.Now(), "scheduled").Count(&overdue)
	stats["overdue"] = overdue

	// Due today
	var dueToday int64
	today := time.Now().Format("2006-01-02")
	config.DB.Model(&models.MaintenanceSchedule{}).Where("client_id = ? AND DATE(due_date) = ? AND status = ?",
		clientId, today, "scheduled").Count(&dueToday)
	stats["due_today"] = dueToday

	// Due this week
	var dueThisWeek int64
	weekStart := time.Now().Truncate(24*time.Hour).AddDate(0, 0, -int(time.Now().Weekday()))
	weekEnd := weekStart.AddDate(0, 0, 7)
	config.DB.Model(&models.MaintenanceSchedule{}).Where("client_id = ? AND due_date BETWEEN ? AND ? AND status = ?",
		clientId, weekStart, weekEnd, "scheduled").Count(&dueThisWeek)
	stats["due_this_week"] = dueThisWeek

	// Completed this month
	var completedThisMonth int64
	monthStart := time.Now().Truncate(24*time.Hour).AddDate(0, 0, -time.Now().Day()+1)
	config.DB.Model(&models.MaintenanceSchedule{}).Where("client_id = ? AND completed_date >= ? AND status = ?",
		clientId, monthStart, "completed").Count(&completedThisMonth)
	stats["completed_this_month"] = completedThisMonth

	// Total cost this month
	var totalCost float64
	config.DB.Model(&models.MaintenanceSchedule{}).Where("client_id = ? AND completed_date >= ? AND status = ?",
		clientId, monthStart, "completed").Select("COALESCE(SUM(cost), 0)").Scan(&totalCost)
	stats["total_cost_this_month"] = totalCost

	// Status breakdown
	var statusBreakdown []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	config.DB.Model(&models.MaintenanceSchedule{}).
		Select("status, COUNT(*) as count").
		Where("client_id = ?", clientId).
		Group("status").
		Scan(&statusBreakdown)
	stats["status_breakdown"] = statusBreakdown

	// Priority breakdown
	var priorityBreakdown []struct {
		Priority string `json:"priority"`
		Count    int64  `json:"count"`
	}
	config.DB.Model(&models.MaintenanceSchedule{}).
		Select("priority, COUNT(*) as count").
		Where("client_id = ?", clientId).
		Group("priority").
		Scan(&priorityBreakdown)
	stats["priority_breakdown"] = priorityBreakdown

	return stats, nil
}
