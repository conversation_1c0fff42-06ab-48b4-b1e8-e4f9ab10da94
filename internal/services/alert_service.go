package services

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"
	"yotracker/internal/models"
)

// AlertService defines the interface for sending alerts
type AlertService interface {
	SendAlert(client *models.Client, alert *models.Alert, clientDevice *models.ClientDevice) error
	IsEnabled() bool
}

// AlertMessage represents a generic alert message
type AlertMessage struct {
	To      string
	Subject string
	Body    string
	Type    string // "sms", "whatsapp", "email"
}

// TwilioSMSService implements SMS alerts using Twilio
type TwilioSMSService struct {
	accountSid string
	authToken  string
	fromNumber string
	httpClient *http.Client
}

// NewTwilioSMSService creates a new Twilio SMS service instance
func NewTwilioSMSService() (*TwilioSMSService, error) {
	accountSid := os.Getenv("TWILIO_ACCOUNT_SID")
	authToken := os.Getenv("TWILIO_AUTH_TOKEN")
	fromNumber := os.Getenv("TWILIO_FROM_NUMBER")

	if accountSid == "" || authToken == "" || fromNumber == "" {
		return nil, errors.New("TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, and TWILIO_FROM_NUMBER environment variables are required")
	}

	return &TwilioSMSService{
		accountSid: accountSid,
		authToken:  authToken,
		fromNumber: fromNumber,
		httpClient: &http.Client{Timeout: 30 * time.Second},
	}, nil
}

// IsEnabled checks if SMS alerts are enabled globally
func (t *TwilioSMSService) IsEnabled() bool {
	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		return false
	}

	enabled := models.GetSetting("sms_alerts_enabled")
	return enabled == "true" || enabled == "1"
}

// SendAlert sends an SMS alert via Twilio
func (t *TwilioSMSService) SendAlert(client *models.Client, alert *models.Alert, clientDevice *models.ClientDevice) error {
	if !t.IsEnabled() {
		return nil // Silently skip if disabled
	}

	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		smsNumbers := client.GetSmsPhoneNumbers()
		if len(smsNumbers) > 0 {
			log.Printf("TEST MODE: Skipping SMS alert to %s", smsNumbers[0])
		} else {
			log.Printf("TEST MODE: Skipping SMS alert (no numbers configured)")
		}
		return nil
	}

	// Check if client has SMS alerts enabled
	if client.SmsAlertsEnabled == nil || !*client.SmsAlertsEnabled {
		return nil
	}

	// Check if client has SMS phone numbers
	smsNumbers := client.GetSmsPhoneNumbers()
	if len(smsNumbers) == 0 {
		return fmt.Errorf("client %s has SMS alerts enabled but no SMS phone numbers configured", client.Name)
	}

	// Build SMS message
	message := t.buildSMSMessage(alert, clientDevice)

	// Initialize communication campaign log service
	logService := NewCommunicationCampaignLogService()

	// Send via Twilio API to all SMS numbers
	var lastError error
	for _, phoneNumber := range smsNumbers {
		metadata := map[string]interface{}{
			"alert_id":         alert.Id,
			"alert_type":       alert.AlertType,
			"device_id":        alert.DeviceId,
			"client_device_id": alert.ClientDeviceId,
		}

		if err := t.sendViaTwilio(phoneNumber, message); err != nil {
			lastError = err
			// Log failed SMS
			logService.LogSMS(&client.Id, phoneNumber, message, false, err.Error(), metadata)
		} else {
			// Log successful SMS
			logService.LogSMS(&client.Id, phoneNumber, message, true, "", metadata)
		}
	}

	return lastError
}

// buildSMSMessage builds the SMS message content
func (t *TwilioSMSService) buildSMSMessage(alert *models.Alert, clientDevice *models.ClientDevice) string {
	var message strings.Builder

	// Add device info
	if clientDevice != nil && clientDevice.Name != nil {
		message.WriteString(fmt.Sprintf("Device: %s\n", *clientDevice.Name))
	}
	if alert.DeviceId != nil {
		message.WriteString(fmt.Sprintf("ID: %s\n", *alert.DeviceId))
	}

	// Add alert info
	message.WriteString(fmt.Sprintf("Alert: %s\n", alert.AlertType))
	if alert.AlertName != nil && *alert.AlertName != "" {
		message.WriteString(fmt.Sprintf("Type: %s\n", *alert.AlertName))
	}

	// Add message if available
	if alert.Message != nil && *alert.Message != "" {
		message.WriteString(fmt.Sprintf("Message: %s\n", *alert.Message))
	}

	// Add timestamp
	message.WriteString(fmt.Sprintf("Time: %s", alert.AlertTimestamp.Format("2006-01-02 15:04:05")))

	return message.String()
}

// formatPhoneNumber formats phone number for SMS API (same logic as WhatsApp)
func (t *TwilioSMSService) formatPhoneNumber(phone string) string {
	// Remove any non-digit characters except +
	formatted := ""
	for _, char := range phone {
		if char >= '0' && char <= '9' || char == '+' {
			formatted += string(char)
		}
	}

	// If no country code, handle Zimbabwe format
	if !strings.HasPrefix(formatted, "+") {
		// Check if it's a Zimbabwe number (10 digits starting with 0)
		if len(formatted) == 10 && strings.HasPrefix(formatted, "0") {
			// Remove the first 0 and add Zimbabwe country code
			formatted = "+263" + formatted[1:]
		} else {
			// Use default country code for other formats
			defaultCountryCode := models.GetSetting("whatsapp_default_country_code")
			if defaultCountryCode == "" {
				defaultCountryCode = "263" // Default to Zimbabwe
			}
			formatted = "+" + defaultCountryCode + formatted
		}
	}

	return formatted
}

// sendViaTwilio sends SMS via Twilio API
func (t *TwilioSMSService) sendViaTwilio(to, message string) error {
	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping SMS to %s", to)
		return nil
	}

	// Format phone number for Twilio
	formattedPhone := t.formatPhoneNumber(to)

	// This is a placeholder implementation
	// In a real implementation, you would use the Twilio Go SDK
	// For now, we'll just log the message
	log.Printf("SMS Alert via Twilio - To: %s (formatted: %s), Message: %s", to, formattedPhone, message)
	return nil
}

// WhatsAppService implements WhatsApp alerts using WhatsApp Business API
type WhatsAppService struct {
	AccessToken   string
	PhoneNumberID string
	BaseURL       string
	HTTPClient    *http.Client
}

// WhatsAppMessage represents a WhatsApp message
type WhatsAppMessage struct {
	MessagingProduct string              `json:"messaging_product"`
	RecipientType    string              `json:"recipient_type"`
	To               string              `json:"to"`
	Type             string              `json:"type"`
	Text             *TextContent        `json:"text,omitempty"`
	Interactive      *InteractiveContent `json:"interactive,omitempty"`
	Template         *TemplateContent    `json:"template,omitempty"`
}

// TemplateContent represents template message content
type TemplateContent struct {
	Name       string              `json:"name"`
	Language   *Language           `json:"language"`
	Components []TemplateComponent `json:"components,omitempty"`
}

// Language represents template language
type Language struct {
	Code string `json:"code"`
}

// TemplateComponent represents template component
type TemplateComponent struct {
	Type       string              `json:"type"`
	SubType    string              `json:"sub_type,omitempty"`
	Index      string              `json:"index,omitempty"`
	Parameters []TemplateParameter `json:"parameters,omitempty"`
}

// TemplateParameter represents template parameter
type TemplateParameter struct {
	Type string `json:"type"`
	Text string `json:"text,omitempty"`
}

// TextContent represents text message content
type TextContent struct {
	Body string `json:"body"`
}

// InteractiveContent represents interactive message content
type InteractiveContent struct {
	Type   string  `json:"type"`
	Header *Header `json:"header,omitempty"`
	Body   *Body   `json:"body"`
	Footer *Footer `json:"footer,omitempty"`
	Action *Action `json:"action"`
}

// Header represents interactive message header
type Header struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

// Body represents interactive message body
type Body struct {
	Text string `json:"text"`
}

// Footer represents interactive message footer
type Footer struct {
	Text string `json:"text"`
}

// Action represents interactive message action (buttons, etc.)
type Action struct {
	Buttons []Button `json:"buttons"`
}

// Button represents an interactive button
type Button struct {
	Type  string `json:"type"`
	Reply Reply  `json:"reply"`
}

// Reply represents button reply data
type Reply struct {
	ID    string `json:"id"`
	Title string `json:"title"`
}

// WhatsAppResponse represents the API response
type WhatsAppResponse struct {
	MessagingProduct string `json:"messaging_product"`
	Contacts         []struct {
		Input string `json:"input"`
		WaID  string `json:"wa_id"`
	} `json:"contacts"`
	Messages []struct {
		ID string `json:"id"`
	} `json:"messages"`
}

// NewWhatsAppService creates a new WhatsApp service instance
func NewWhatsAppService() (*WhatsAppService, error) {
	accessToken := models.GetSetting("whatsapp_access_token")
	if accessToken == "" {
		return nil, fmt.Errorf("whatsapp_access_token setting is required")
	}

	phoneNumberID := models.GetSetting("whatsapp_phone_number_id")
	if phoneNumberID == "" {
		return nil, fmt.Errorf("whatsapp_phone_number_id setting is required")
	}

	baseURL := models.GetSetting("whatsapp_base_url")
	if baseURL == "" {
		baseURL = "https://graph.facebook.com/v18.0"
	}

	return &WhatsAppService{
		AccessToken:   accessToken,
		PhoneNumberID: phoneNumberID,
		BaseURL:       baseURL,
		HTTPClient:    &http.Client{Timeout: 30 * time.Second},
	}, nil
}

// IsEnabled checks if WhatsApp alerts are enabled globally
func (w *WhatsAppService) IsEnabled() bool {
	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		return false
	}

	enabled := models.GetSetting("whatsapp_alerts_enabled")
	return enabled == "true" || enabled == "1"
}

// SendAlert sends a WhatsApp alert
func (w *WhatsAppService) SendAlert(client *models.Client, alert *models.Alert, clientDevice *models.ClientDevice) error {
	fmt.Println("Sending WhatsApp alert")
	if !w.IsEnabled() {
		return nil
	}

	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		whatsappNumbers := client.GetWhatsappPhoneNumbers()
		if len(whatsappNumbers) > 0 {
			log.Printf("TEST MODE: Skipping WhatsApp alert to %s", whatsappNumbers[0])
		} else {
			log.Printf("TEST MODE: Skipping WhatsApp alert (no numbers configured)")
		}
		return nil
	}

	// Check if client has WhatsApp alerts enabled
	if client.WhatsappAlertsEnabled == nil || !*client.WhatsappAlertsEnabled {
		fmt.Println("WhatsApp alerts are not enabled for the client")
		return nil
	}

	// Check if client has WhatsApp phone numbers
	whatsappNumbers := client.GetWhatsappPhoneNumbers()
	fmt.Printf("Retrieved WhatsApp numbers from client: %v\n", whatsappNumbers)
	fmt.Printf("Client WhatsApp phone number field: %v\n", client.WhatsappPhoneNumber)
	if len(whatsappNumbers) == 0 {
		fmt.Println("Client has no WhatsApp numbers")
		return fmt.Errorf("client %s has WhatsApp alerts enabled but no WhatsApp phone numbers configured", client.Name)
	}
	fmt.Printf("Client has %d WhatsApp numbers configured\n", len(whatsappNumbers))
	// Get device name with plate number if available
	deviceName := "Unknown Device"
	if clientDevice != nil && clientDevice.Name != nil {
		deviceName = *clientDevice.Name
		if clientDevice.PlateNumber != nil && *clientDevice.PlateNumber != "" {
			deviceName = fmt.Sprintf("%s (%s)", *clientDevice.Name, *clientDevice.PlateNumber)
		}
	}

	// Get device ID for button
	deviceId := "0"
	if clientDevice != nil {
		deviceId = fmt.Sprintf("%d", clientDevice.Id)
	}

	// Get alert message
	alertMessage := "Device alert triggered"
	if alert.Message != nil && *alert.Message != "" {
		alertMessage = *alert.Message
	}

	// Initialize communication campaign log service
	logService := NewCommunicationCampaignLogService()

	// Send template messages to all WhatsApp numbers
	var lastError error
	for _, phoneNumber := range whatsappNumbers {
		// Validate phone number
		if phoneNumber == "" {
			fmt.Printf("Skipping empty phone number\n")
			continue
		}

		formattedPhone := w.formatPhoneNumber(phoneNumber)
		if formattedPhone == "" {
			fmt.Printf("Skipping invalid phone number: %s\n", phoneNumber)
			continue
		}

		fmt.Printf("Sending WhatsApp template message to: %s (formatted: %s)\n", phoneNumber, formattedPhone)

		// Prepare metadata for logging
		metadata := map[string]interface{}{
			"alert_id":         alert.Id,
			"alert_type":       alert.AlertType,
			"device_id":        alert.DeviceId,
			"client_device_id": alert.ClientDeviceId,
			"device_name":      deviceName,
			"formatted_phone":  formattedPhone,
		}

		// Use single template for all alert types
		err := w.SendDeviceAlertTemplate(phoneNumber, deviceName, alert.AlertType, alertMessage, deviceId)

		if err != nil {
			fmt.Printf("Error sending to %s: %v\n", phoneNumber, err)
			lastError = err
			// Log failed WhatsApp message
			logService.LogWhatsApp(&client.Id, phoneNumber, alertMessage, false, err.Error(), metadata)
		} else {
			fmt.Printf("Successfully sent WhatsApp template message to %s\n", phoneNumber)
			// Log successful WhatsApp message
			logService.LogWhatsApp(&client.Id, phoneNumber, alertMessage, true, "", metadata)
		}
	}

	return lastError
}

// buildWhatsAppMessage builds the appropriate WhatsApp message based on alert type
func (w *WhatsAppService) buildWhatsAppMessage(alert *models.Alert, clientDevice *models.ClientDevice) WhatsAppMessage {
	deviceName := "Unknown Device"
	if clientDevice != nil && clientDevice.Name != nil {
		deviceName = *clientDevice.Name
		if clientDevice.PlateNumber != nil && *clientDevice.PlateNumber != "" {
			deviceName = fmt.Sprintf("%s (%s)", *clientDevice.Name, *clientDevice.PlateNumber)
		}
	}

	// Build message based on alert type
	switch alert.AlertType {
	case "geofence":
		return w.buildGeofenceMessage("", deviceName, alert)
	case "speed":
		return w.buildSpeedMessage("", deviceName, alert)
	case "maintenance":
		return w.buildMaintenanceMessage("", deviceName, alert)
	default:
		return w.buildGenericMessage("", deviceName, alert)
	}
}

// buildGeofenceMessage builds a geofence alert message
func (w *WhatsAppService) buildGeofenceMessage(to, deviceName string, alert *models.Alert) WhatsAppMessage {
	bodyText := fmt.Sprintf("Device %s has triggered a geofence alert", deviceName)
	if alert.Message != nil {
		bodyText = *alert.Message
	}

	buttons := []Button{
		{
			Type: "reply",
			Reply: Reply{
				ID:    "view_location",
				Title: "View Location",
			},
		},
	}

	interactive := &InteractiveContent{
		Type: "button",
		Header: &Header{
			Type: "text",
			Text: "📍 Geofence Alert",
		},
		Body: &Body{
			Text: bodyText,
		},
		Footer: &Footer{
			Text: "Click to view device location on map",
		},
		Action: &Action{
			Buttons: buttons,
		},
	}

	return WhatsAppMessage{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               to,
		Type:             "interactive",
		Interactive:      interactive,
	}
}

// buildSpeedMessage builds a speed alert message
func (w *WhatsAppService) buildSpeedMessage(to, deviceName string, alert *models.Alert) WhatsAppMessage {
	bodyText := fmt.Sprintf("Device %s has triggered a speed alert", deviceName)
	if alert.Message != nil {
		bodyText = *alert.Message
	}

	buttons := []Button{
		{
			Type: "reply",
			Reply: Reply{
				ID:    "view_speed_details",
				Title: "View Details",
			},
		},
	}

	interactive := &InteractiveContent{
		Type: "button",
		Header: &Header{
			Type: "text",
			Text: "⚡ Speed Alert",
		},
		Body: &Body{
			Text: bodyText,
		},
		Footer: &Footer{
			Text: "Click to view speed details and location",
		},
		Action: &Action{
			Buttons: buttons,
		},
	}

	return WhatsAppMessage{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               to,
		Type:             "interactive",
		Interactive:      interactive,
	}
}

// buildMaintenanceMessage builds a maintenance alert message
func (w *WhatsAppService) buildMaintenanceMessage(to, deviceName string, alert *models.Alert) WhatsAppMessage {
	bodyText := fmt.Sprintf("Device %s requires maintenance", deviceName)
	if alert.Message != nil {
		bodyText = *alert.Message
	}

	buttons := []Button{
		{
			Type: "reply",
			Reply: Reply{
				ID:    "schedule_maintenance",
				Title: "Schedule Maintenance",
			},
		},
	}

	interactive := &InteractiveContent{
		Type: "button",
		Header: &Header{
			Type: "text",
			Text: "🔧 Maintenance Alert",
		},
		Body: &Body{
			Text: bodyText,
		},
		Footer: &Footer{
			Text: "Click to schedule maintenance",
		},
		Action: &Action{
			Buttons: buttons,
		},
	}

	return WhatsAppMessage{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               to,
		Type:             "interactive",
		Interactive:      interactive,
	}
}

// buildGenericMessage builds a generic alert message
func (w *WhatsAppService) buildGenericMessage(to, deviceName string, alert *models.Alert) WhatsAppMessage {
	bodyText := fmt.Sprintf("Device %s has triggered an alert", deviceName)
	if alert.Message != nil {
		bodyText = *alert.Message
	}

	buttons := []Button{
		{
			Type: "reply",
			Reply: Reply{
				ID:    "view_device",
				Title: "View Device",
			},
		},
	}

	interactive := &InteractiveContent{
		Type: "button",
		Header: &Header{
			Type: "text",
			Text: "🚨 Device Alert",
		},
		Body: &Body{
			Text: bodyText,
		},
		Footer: &Footer{
			Text: "Click to view device details",
		},
		Action: &Action{
			Buttons: buttons,
		},
	}

	return WhatsAppMessage{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               to,
		Type:             "interactive",
		Interactive:      interactive,
	}
}

// sendMessage sends a message to the WhatsApp API
func (w *WhatsAppService) sendMessage(to string, message WhatsAppMessage) error {
	// Format phone number
	formattedPhone := w.formatPhoneNumber(to)

	// Set the To field in the message
	message.To = formattedPhone

	// Validate that we have a proper phone number
	if message.To == "" {
		return fmt.Errorf("cannot send WhatsApp message: phone number is empty after formatting (original: %s)", to)
	}

	// Skip sending in test environment
	if os.Getenv("TEST_ENV") == "true" {
		log.Printf("[WHATSAPP TEST] Would send message to %s (formatted: %s): %+v", to, formattedPhone, message)
		return nil
	}

	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	fmt.Printf("Sending WhatsApp message JSON: %s\n", string(jsonData))

	url := fmt.Sprintf("%s/%s/messages", w.BaseURL, w.PhoneNumberID)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+w.AccessToken)

	resp, err := w.HTTPClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		var errorResponse map[string]interface{}
		if err := json.NewDecoder(resp.Body).Decode(&errorResponse); err != nil {
			return fmt.Errorf("failed to decode error response: %w", err)
		}
		return fmt.Errorf("WhatsApp API error: %d - %v", resp.StatusCode, errorResponse)
	}

	// Parse response
	var response WhatsAppResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return fmt.Errorf("failed to decode response: %w", err)
	}

	return nil
}

// formatPhoneNumber formats phone number for WhatsApp API
func (w *WhatsAppService) formatPhoneNumber(phone string) string {
	fmt.Printf("Formatting phone number: %s\n", phone)

	// Remove any non-digit characters except +
	formatted := ""
	for _, char := range phone {
		if char >= '0' && char <= '9' || char == '+' {
			formatted += string(char)
		}
	}

	fmt.Printf("After cleaning: %s\n", formatted)

	// If no country code, handle Zimbabwe format
	if !strings.HasPrefix(formatted, "+") {
		// Check if it's a Zimbabwe number (10 digits starting with 0)
		if len(formatted) == 10 && strings.HasPrefix(formatted, "0") {
			// Remove the first 0 and add Zimbabwe country code
			formatted = "+263" + formatted[1:]
			fmt.Printf("Zimbabwe number detected, formatted to: %s\n", formatted)
		} else {
			// Use default country code for other formats
			defaultCountryCode := models.GetSetting("whatsapp_default_country_code")
			fmt.Printf("Retrieved default country code from settings: '%s'\n", defaultCountryCode)
			if defaultCountryCode == "" {
				defaultCountryCode = "263" // Default to Zimbabwe
				fmt.Printf("Using hardcoded default country code: %s\n", defaultCountryCode)
			}
			formatted = "+" + defaultCountryCode + formatted
			fmt.Printf("Using default country code %s, formatted to: %s\n", defaultCountryCode, formatted)
		}
	} else {
		fmt.Printf("Already has country code: %s\n", formatted)
	}

	fmt.Printf("Final formatted number: %s\n", formatted)
	return formatted
}

// ValidatePhoneNumber validates if a phone number is in correct format
func (w *WhatsAppService) ValidatePhoneNumber(phone string) bool {
	formatted := w.formatPhoneNumber(phone)
	// Basic validation - should start with + and have at least 10 digits
	if !strings.HasPrefix(formatted, "+") {
		return false
	}

	digitCount := 0
	for _, char := range formatted {
		if char >= '0' && char <= '9' {
			digitCount++
		}
	}

	return digitCount >= 10
}

// SendTextMessage sends a simple text message
func (w *WhatsAppService) SendTextMessage(to, message string) error {
	// Format phone number
	formattedPhone := w.formatPhoneNumber(to)

	whatsappMessage := WhatsAppMessage{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               formattedPhone,
		Type:             "text",
		Text: &TextContent{
			Body: message,
		},
	}

	return w.sendMessage(formattedPhone, whatsappMessage)
}

// SendInteractiveMessage sends an interactive message with buttons
func (w *WhatsAppService) SendInteractiveMessage(to, headerText, bodyText, footerText string, buttons []Button) error {
	// Format phone number
	formattedPhone := w.formatPhoneNumber(to)

	// Build interactive content
	interactive := &InteractiveContent{
		Type: "button",
		Body: &Body{
			Text: bodyText,
		},
		Action: &Action{
			Buttons: buttons,
		},
	}

	// Add header if provided
	if headerText != "" {
		interactive.Header = &Header{
			Type: "text",
			Text: headerText,
		}
	}

	// Add footer if provided
	if footerText != "" {
		interactive.Footer = &Footer{
			Text: footerText,
		}
	}

	whatsappMessage := WhatsAppMessage{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               formattedPhone,
		Type:             "interactive",
		Interactive:      interactive,
	}

	return w.sendMessage(formattedPhone, whatsappMessage)
}

// FormatPhoneNumber formats phone number for WhatsApp API (exported version)
func (w *WhatsAppService) FormatPhoneNumber(phone string) string {
	return w.formatPhoneNumber(phone)
}

// SendTemplateMessage sends a template message
func (w *WhatsAppService) SendTemplateMessage(to, templateName string, parameters []string) error {
	// Format phone number
	formattedPhone := w.formatPhoneNumber(to)

	// Get template language from settings
	language := models.GetSetting("whatsapp_template_language")
	if language == "" {
		language = "en" // Default to English
	}

	// Convert parameters to template parameters
	var bodyParams []TemplateParameter
	var buttonParams []TemplateParameter

	// For your template structure:
	// {{1}} - Alert title (body)
	// {{2}} - Alert message (body)
	// {{1}} - URL parameter (button) - if template has button

	// Body parameters (always send these)
	if len(parameters) >= 2 {
		bodyParams = []TemplateParameter{
			{Type: "text", Text: parameters[0]}, // {{1}} - Alert title
			{Type: "text", Text: parameters[1]}, // {{2}} - Alert message
		}
	}

	// Button parameter (if we have a third parameter)
	if len(parameters) >= 3 {
		buttonParams = []TemplateParameter{
			{Type: "text", Text: parameters[2]}, // {{1}} - URL parameter
		}
	}

	// Create components
	var components []TemplateComponent

	// Add body component
	if len(bodyParams) > 0 {
		components = append(components, TemplateComponent{
			Type:       "body",
			Parameters: bodyParams,
		})
	}

	// Add button component if we have button parameters
	if len(buttonParams) > 0 {
		components = append(components, TemplateComponent{
			Type:       "button",
			SubType:    "url",
			Index:      "0", // First button (index 0)
			Parameters: buttonParams,
		})
	}

	// Create template message
	whatsappMessage := WhatsAppMessage{
		MessagingProduct: "whatsapp",
		RecipientType:    "individual",
		To:               formattedPhone,
		Type:             "template",
		Template: &TemplateContent{
			Name: templateName,
			Language: &Language{
				Code: language,
			},
			Components: components,
		},
	}

	err := w.sendMessage(formattedPhone, whatsappMessage)
	if err != nil {
		// If template doesn't exist, try sending a simple text message as fallback
		if strings.Contains(err.Error(), "Template name does not exist") {
			fmt.Printf("DEBUG: Template not found, sending fallback text message\n")
			return w.SendTextMessage(formattedPhone, fmt.Sprintf("🚨 %s\n\n%s", parameters[0], parameters[1]))
		}
	}
	return err
}

// SendDeviceAlertTemplate sends a device alert using template
func (w *WhatsAppService) SendDeviceAlertTemplate(to, deviceName, alertType, alertMessage, deviceId string) error {
	// Get template name from settings
	templateName := models.GetSetting("whatsapp_device_alert_template")
	if templateName == "" {
		templateName = "device_alert" // Default fallback
	}

	// Build alert title with emoji
	alertTitle := fmt.Sprintf("%s Alert", strings.Title(alertType))

	// For your template structure:
	// {{1}} - Alert title (body)
	// {{2}} - Alert message (body)
	// {{1}} - URL parameter (button)
	parameters := []string{
		alertTitle,     // {{1}} - Alert title (🚨 Speed Alert, 🚨 Geofence Alert, etc.)
		alertMessage,   // {{2}} - Alert message
		"/" + deviceId, // {{1}} - URL parameter for button (with forward slash)
	}

	return w.SendTemplateMessage(to, templateName, parameters)
}

// EmailAlertService implements email alerts using the existing mail service
type EmailAlertService struct {
	mailService *MailService
}

// NewEmailAlertService creates a new email alert service instance
func NewEmailAlertService() (*EmailAlertService, error) {
	mailService, err := NewMailService()
	if err != nil {
		return nil, fmt.Errorf("failed to initialize mail service: %w", err)
	}

	return &EmailAlertService{
		mailService: mailService,
	}, nil
}

// IsEnabled checks if email alerts are enabled globally
func (e *EmailAlertService) IsEnabled() bool {
	// Skip in test environment
	if os.Getenv("GO_ENV") == "test" {
		return false
	}

	enabled := models.GetSetting("email_alerts_enabled")
	return enabled == "true" || enabled == "1"
}

// SendAlert sends an email alert
func (e *EmailAlertService) SendAlert(client *models.Client, alert *models.Alert, clientDevice *models.ClientDevice) error {
	if !e.IsEnabled() {
		return nil
	}

	// Skip in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		alertEmails := client.GetAlertEmails()
		if len(alertEmails) > 0 {
			log.Printf("TEST MODE: Skipping email alert to %s", alertEmails[0])
		} else {
			log.Printf("TEST MODE: Skipping email alert (no emails configured)")
		}
		return nil
	}

	// Check if client has email alerts enabled
	if client.EmailAlertsEnabled == nil || !*client.EmailAlertsEnabled {
		return nil
	}

	// Get alert email addresses
	alertEmails := client.GetAlertEmails()
	if len(alertEmails) == 0 {
		// Fallback to main email if no alert emails configured
		alertEmails = []string{client.Email}
	}

	// Build email subject and content
	subject := fmt.Sprintf("Device Alert: %s", alert.AlertType)
	if alert.AlertName != nil && *alert.AlertName != "" {
		subject = fmt.Sprintf("Device Alert: %s", *alert.AlertName)
	}

	// Get device display name with plate number if available
	deviceDisplayName := "Unknown Device"
	if clientDevice != nil && clientDevice.Name != nil {
		deviceDisplayName = *clientDevice.Name
		if clientDevice.PlateNumber != nil && *clientDevice.PlateNumber != "" {
			deviceDisplayName = fmt.Sprintf("%s (%s)", *clientDevice.Name, *clientDevice.PlateNumber)
		}
	}

	// Get device URL
	deviceURL := ""
	if clientDevice != nil {
		appURL := os.Getenv("APP_URL")
		if appURL == "" {
			appURL = "https://app.yotracker.co.zw" // Default fallback
		}
		deviceURL = fmt.Sprintf("%s/devices/%d", appURL, clientDevice.Id)
	}

	// Initialize communication campaign log service
	logService := NewCommunicationCampaignLogService()

	// Send templated email to all alert email addresses
	var lastError error
	for _, emailAddress := range alertEmails {
		// Build the email content for logging
		var emailContent strings.Builder
		emailContent.WriteString(fmt.Sprintf("Hello %s,\n\n", client.Name))
		emailContent.WriteString("A device alert has been triggered:\n")
		emailContent.WriteString(fmt.Sprintf("Device: %s\n", deviceDisplayName))
		emailContent.WriteString(fmt.Sprintf("Alert Type: %s\n", alert.AlertType))

		// Add alert message if available
		if alert.Message != nil && *alert.Message != "" {
			emailContent.WriteString(fmt.Sprintf("Alert Message: %s\n", *alert.Message))
		}

		emailContent.WriteString(fmt.Sprintf("\nView Device: %s", deviceURL))

		// Prepare metadata for logging
		metadata := map[string]interface{}{
			"alert_id":         alert.Id,
			"alert_type":       alert.AlertType,
			"device_id":        alert.DeviceId,
			"client_device_id": alert.ClientDeviceId,
			"device_name":      deviceDisplayName,
			"subject":          subject,
			"device_url":       deviceURL,
		}

		if err := e.mailService.SendTemplatedEmail(emailAddress, subject, func(template *EmailTemplate) *EmailTemplate {
			template = template.
				SetGreeting(fmt.Sprintf("Hello %s,", client.Name)).
				AddContent(
					fmt.Sprintf("A device alert has been triggered:"),
					fmt.Sprintf("Device: %s", deviceDisplayName),
					fmt.Sprintf("Alert Type: %s", alert.AlertType),
				)

			// Add alert message if available (add it right after the basic info)
			if alert.Message != nil && *alert.Message != "" {
				template.AddContent(fmt.Sprintf("Alert Message: %s", *alert.Message))
			}

			return template.SetAction("View Device", deviceURL)
		}); err != nil {
			lastError = err
			// Log failed email with full content
			logService.LogEmail(&client.Id, emailAddress, emailContent.String(), false, err.Error(), metadata)
		} else {
			// Log successful email with full content
			logService.LogEmail(&client.Id, emailAddress, emailContent.String(), true, "", metadata)
		}
	}

	return lastError
}

// AlertManager manages multiple alert services
type AlertManager struct {
	smsService      *TwilioSMSService
	whatsappService *WhatsAppService
	emailService    *EmailAlertService
}

// NewAlertManager creates a new alert manager with all services
func NewAlertManager() (*AlertManager, error) {
	smsService, err := NewTwilioSMSService()
	if err != nil {
		log.Printf("Warning: Failed to initialize SMS service: %v", err)
		smsService = nil
	}

	whatsappService, err := NewWhatsAppService()
	if err != nil {
		log.Printf("Warning: Failed to initialize WhatsApp service: %v", err)
		whatsappService = nil
	}

	emailService, err := NewEmailAlertService()
	if err != nil {
		log.Printf("Warning: Failed to initialize email service: %v", err)
		emailService = nil
	}

	return &AlertManager{
		smsService:      smsService,
		whatsappService: whatsappService,
		emailService:    emailService,
	}, nil
}

// SendAlert sends alerts through all enabled services for a client
func (am *AlertManager) SendAlert(client *models.Client, alert *models.Alert, clientDevice *models.ClientDevice) error {
	var errs []string

	// Send SMS alert
	if am.smsService != nil {
		if err := am.smsService.SendAlert(client, alert, clientDevice); err != nil {
			errs = append(errs, fmt.Sprintf("SMS: %v", err))
		}
	}

	// Send WhatsApp alert
	if am.whatsappService != nil {
		if err := am.whatsappService.SendAlert(client, alert, clientDevice); err != nil {
			errs = append(errs, fmt.Sprintf("WhatsApp: %v", err))
		}
	}

	// Send email alert
	if am.emailService != nil {
		if err := am.emailService.SendAlert(client, alert, clientDevice); err != nil {
			errs = append(errs, fmt.Sprintf("Email: %v", err))
		}
	}

	// Return combined errs if any
	if len(errs) > 0 {
		return fmt.Errorf("alert sending errs: %s", strings.Join(errs, "; "))
	}

	return nil
}

// SendAlertToClient sends alerts to a specific client based on their preferences
func SendAlertToClient(client *models.Client, alert *models.Alert, clientDevice *models.ClientDevice) error {
	alertManager, err := NewAlertManager()
	if err != nil {
		return fmt.Errorf("failed to initialize alert manager: %w", err)
	}

	return alertManager.SendAlert(client, alert, clientDevice)
}
