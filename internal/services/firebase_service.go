package services

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"firebase.google.com/go/v4/messaging"
	fcm "github.com/appleboy/go-fcm"
)

// FirebaseResponse represents the response from Firebase
type FirebaseResponse struct {
	Success int `json:"success"`
	Failure int `json:"failure"`
	Results []struct {
		MessageID string `json:"message_id,omitempty"`
		Error     string `json:"error,omitempty"`
	} `json:"results"`
}

// PushNotificationRequest represents the request structure for sending push notifications
type PushNotificationRequest struct {
	To             string                 `json:"to" binding:"required"`
	Title          string                 `json:"title" binding:"required"`
	Body           string                 `json:"body" binding:"required"`
	Type           string                 `json:"type" binding:"required"`
	Data           map[string]interface{} `json:"data,omitempty"`
	ClientDeviceID *uint                  `json:"client_device_id,omitempty"`
	Timestamp      *string                `json:"timestamp,omitempty"`
}

// FCMClient interface for Firebase Cloud Messaging
type FCMClient interface {
	Send(ctx context.Context, msgs ...*messaging.Message) (*messaging.BatchResponse, error)
}

// PushNotificationService handles Firebase push notifications
type PushNotificationService struct {
	Client FCMClient
}

// NewPushNotificationService creates a new push notification service
func NewPushNotificationService() *PushNotificationService {
	ctx := context.Background()

	// Try to use service account key file first
	client, err := fcm.NewClient(ctx, fcm.WithCredentialsFile("yotracker-firebase.json"))
	if err != nil {
		log.Printf("Failed to create FCM client with service account file: %v", err)
		// Fallback to server key if service account file is not available
		serverKey := os.Getenv("FIREBASE_SERVER_KEY")
		if serverKey != "" {
			// For legacy server key support, we'll create a basic client
			// Note: This is less secure than service account credentials
			client, err = fcm.NewClient(ctx, fcm.WithProjectID("yotracker"))
			if err != nil {
				log.Printf("Failed to create FCM client with project ID: %v", err)
			}
		} else {
			// For testing purposes, create a client with test project
			client, err = fcm.NewClient(ctx, fcm.WithProjectID("test-project"))
			if err != nil {
				log.Printf("Failed to create FCM client with test project: %v", err)
			}
		}
	}

	if client == nil {
		log.Printf("Warning: FCM client is nil - push notifications will not work")
	}

	return &PushNotificationService{
		Client: client,
	}
}

// SendPushNotification sends a push notification to a specific device
func (s *PushNotificationService) SendPushNotification(req PushNotificationRequest) (*FirebaseResponse, error) {
	if s.Client == nil {
		return nil, fmt.Errorf("FCM client is not initialized - check Firebase configuration")
	}

	// Set default timestamp if not provided
	if req.Timestamp == nil {
		now := time.Now().UTC().Format(time.RFC3339)
		req.Timestamp = &now
	}

	// Build the data payload
	data := make(map[string]string)
	data["type"] = req.Type
	data["timestamp"] = *req.Timestamp

	if req.ClientDeviceID != nil {
		data["client_device_id"] = fmt.Sprintf("%d", *req.ClientDeviceID)
	}

	// Add any additional data
	for key, value := range req.Data {
		data[key] = fmt.Sprintf("%v", value)
	}

	// Create the FCM message
	msg := &messaging.Message{
		Token: req.To,
		Notification: &messaging.Notification{
			Title: req.Title,
			Body:  req.Body,
		},
		Data: data,
	}

	// Send the message
	response, err := s.Client.Send(context.Background(), msg)
	if err != nil {
		return &FirebaseResponse{
			Success: 0,
			Failure: 1,
			Results: []struct {
				MessageID string `json:"message_id,omitempty"`
				Error     string `json:"error,omitempty"`
			}{
				{Error: err.Error()},
			},
		}, err
	}

	// Convert FCM response to our FirebaseResponse format
	firebaseResp := &FirebaseResponse{
		Success: 0,
		Failure: 0,
		Results: []struct {
			MessageID string `json:"message_id,omitempty"`
			Error     string `json:"error,omitempty"`
		}{},
	}

	if response.SuccessCount > 0 {
		firebaseResp.Success = 1
		firebaseResp.Results = append(firebaseResp.Results, struct {
			MessageID string `json:"message_id,omitempty"`
			Error     string `json:"error,omitempty"`
		}{
			MessageID: response.Responses[0].MessageID,
		})
	} else {
		firebaseResp.Failure = 1
		if len(response.Responses) > 0 && response.Responses[0].Error != nil {
			firebaseResp.Results = append(firebaseResp.Results, struct {
				MessageID string `json:"message_id,omitempty"`
				Error     string `json:"error,omitempty"`
			}{
				Error: response.Responses[0].Error.Error(),
			})
		}
	}

	return firebaseResp, nil
}

// SendBulkPushNotification sends push notifications to multiple devices
func (s *PushNotificationService) SendBulkPushNotification(requests []PushNotificationRequest) (*FirebaseResponse, error) {
	if len(requests) == 0 {
		return nil, fmt.Errorf("no notifications to send")
	}

	if s.Client == nil {
		return nil, fmt.Errorf("FCM client is not initialized - check Firebase configuration")
	}

	// For bulk notifications, we need to send them individually
	// Firebase doesn't support bulk notifications in a single request
	var totalSuccess, totalFailure int
	var allResults []struct {
		MessageID string `json:"message_id,omitempty"`
		Error     string `json:"error,omitempty"`
	}

	for _, req := range requests {
		response, err := s.SendPushNotification(req)
		if err != nil {
			totalFailure++
			allResults = append(allResults, struct {
				MessageID string `json:"message_id,omitempty"`
				Error     string `json:"error,omitempty"`
			}{
				Error: err.Error(),
			})
		} else {
			totalSuccess += response.Success
			totalFailure += response.Failure
			allResults = append(allResults, response.Results...)
		}
	}

	return &FirebaseResponse{
		Success: totalSuccess,
		Failure: totalFailure,
		Results: allResults,
	}, nil
}

// ValidateNotificationType validates if the notification type is supported
func ValidateNotificationType(notificationType string) bool {
	supportedTypes := map[string]bool{
		"ignition_alert":    true,
		"admin_message":     true,
		"geofence_alert":    true,
		"trip_summary":      true,
		"system_notice":     true,
		"new_chat_message":  true,
		"maintenance_alert": true,
		"speed_alert":       true,
		"fuel_alert":        true,
		"battery_alert":     true,
		"panic_alert":       true,
		"driver_alert":      true,
		"device_offline":    true,
		"device_online":     true,
	}

	return supportedTypes[notificationType]
}

// GetNotificationTypeDescription returns a description for the notification type
func GetNotificationTypeDescription(notificationType string) string {
	descriptions := map[string]string{
		"ignition_alert":    "Vehicle ignition status change",
		"admin_message":     "Administrative message",
		"geofence_alert":    "Geofence entry/exit alert",
		"trip_summary":      "Trip summary report",
		"system_notice":     "System notification",
		"new_chat_message":  "New chat message received",
		"maintenance_alert": "Vehicle maintenance reminder",
		"speed_alert":       "Speed limit violation",
		"fuel_alert":        "Fuel level alert",
		"battery_alert":     "Battery level alert",
		"panic_alert":       "Panic button activated",
		"driver_alert":      "Driver-related alert",
		"device_offline":    "Device went offline",
		"device_online":     "Device came online",
	}

	if desc, exists := descriptions[notificationType]; exists {
		return desc
	}
	return "Unknown notification type"
}
