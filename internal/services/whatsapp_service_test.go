package services

import (
	"net/http"
	"os"
	"strings"
	"testing"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/seed"
	"yotracker/internal/utils"
)

func TestWhatsAppService_NewWhatsAppService(t *testing.T) {
	// Set up test environment
	utils.ForceProjectRoot()

	// Set up test database
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}

	config.InitTestDB()
	seed.Seed()

	// Test with missing settings
	_, err := NewWhatsAppService()
	if err == nil {
		t.Error("Expected error when settings are missing")
	}

	// Note: This test would require setting up test database with settings
	// For now, we'll just test that it returns an error when settings are missing
	if err == nil {
		t.Error("Expected error when whatsapp_access_token setting is missing")
	}
}

func TestWhatsAppService_FormatPhoneNumber(t *testing.T) {
	// Mock the GetSetting function for testing
	// We'll test the phone number formatting logic directly
	// by creating a test function that doesn't rely on database
	testFormatPhoneNumber := func(phone string) string {
		// Remove any non-digit characters except +
		formatted := ""
		for _, char := range phone {
			if char >= '0' && char <= '9' || char == '+' {
				formatted += string(char)
			}
		}

		// If no country code, handle Zimbabwe format
		if !strings.HasPrefix(formatted, "+") {
			// Check if it's a Zimbabwe number (10 digits starting with 0)
			if len(formatted) == 10 && strings.HasPrefix(formatted, "0") {
				// Remove the first 0 and add Zimbabwe country code
				formatted = "+263" + formatted[1:]
			} else {
				// Use default country code for other formats
				defaultCountryCode := "263" // Default to Zimbabwe for testing
				formatted = "+" + defaultCountryCode + formatted
			}
		}

		return formatted
	}

	tests := []struct {
		input    string
		expected string
	}{
		// Zimbabwe numbers (10 digits starting with 0)
		{"0774175438", "+263774175438"},
		{"0712345678", "+263712345678"},
		{"0734567890", "+263734567890"},

		// Numbers with country code
		{"+1234567890", "+1234567890"},
		{"+44 20 7946 0958", "+442079460958"},
		{"******-123-4567", "+15551234567"},

		// Other formats (will use default country code 263)
		{"1234567890", "+2631234567890"},
		{"(*************", "+2635551234567"},
	}

	for _, test := range tests {
		result := testFormatPhoneNumber(test.input)
		if result != test.expected {
			t.Errorf("FormatPhoneNumber(%s) = %s, expected %s", test.input, result, test.expected)
		}
	}
}

func TestWhatsAppService_ValidatePhoneNumber(t *testing.T) {
	// Mock the validation logic for testing
	// We'll test the phone number validation logic directly
	testValidatePhoneNumber := func(phone string) bool {
		// Remove any non-digit characters except +
		formatted := ""
		for _, char := range phone {
			if char >= '0' && char <= '9' || char == '+' {
				formatted += string(char)
			}
		}

		// Basic validation: must have at least 10 digits
		digitCount := 0
		for _, char := range formatted {
			if char >= '0' && char <= '9' {
				digitCount++
			}
		}

		return digitCount >= 10
	}

	tests := []struct {
		input    string
		expected bool
	}{
		// Zimbabwe numbers
		{"0774175438", true}, // Will be formatted to +263774175438
		{"0712345678", true}, // Will be formatted to +263712345678

		// Numbers with country code
		{"+1234567890", true},
		{"+44 20 7946 0958", true},

		// Other valid formats
		{"1234567890", true}, // Will be formatted to +2631234567890

		// Invalid formats
		{"invalid", false},
		{"123", false}, // Too short
		{"", false},    // Empty
	}

	for _, test := range tests {
		result := testValidatePhoneNumber(test.input)
		if result != test.expected {
			t.Errorf("ValidatePhoneNumber(%s) = %t, expected %t", test.input, result, test.expected)
		}
	}
}

func TestWhatsAppService_IsEnabled(t *testing.T) {
	// Create a service instance directly for testing
	service := &WhatsAppService{
		AccessToken:   "test_token",
		PhoneNumberID: "test_phone_id",
		BaseURL:       "https://graph.facebook.com/v18.0",
		HTTPClient:    &http.Client{Timeout: 30 * time.Second},
	}

	// Test in test environment (should return false)
	os.Setenv("TESTING_DB_NAME", "test_db")
	defer os.Unsetenv("TESTING_DB_NAME")

	result := service.IsEnabled()
	if result {
		t.Error("Expected IsEnabled to return false in test environment")
	}

	// Test in non-test environment (depends on database setting)
	os.Unsetenv("TESTING_DB_NAME")
	os.Unsetenv("GO_ENV")

	result = service.IsEnabled()
	// The result depends on the whatsapp_alerts_enabled setting in the database
	// We can't assume it will be true, so we just test that it doesn't panic
	_ = result // Use the result to avoid unused variable warning
}

func TestWhatsAppService_SendTextMessage(t *testing.T) {
	os.Setenv("TEST_ENV", "true") // Enable test mode
	defer os.Unsetenv("TEST_ENV")

	// Create a service instance directly for testing
	service := &WhatsAppService{
		AccessToken:   "test_token",
		PhoneNumberID: "test_phone_id",
		BaseURL:       "https://graph.facebook.com/v18.0",
		HTTPClient:    &http.Client{Timeout: 30 * time.Second},
	}

	// Test sending text message in test mode
	err := service.SendTextMessage("+1234567890", "Test message")
	if err != nil {
		t.Errorf("Unexpected error sending text message: %v", err)
	}
}

func TestWhatsAppService_SendInteractiveMessage(t *testing.T) {
	os.Setenv("TEST_ENV", "true") // Enable test mode
	defer os.Unsetenv("TEST_ENV")

	// Create a service instance directly for testing
	service := &WhatsAppService{
		AccessToken:   "test_token",
		PhoneNumberID: "test_phone_id",
		BaseURL:       "https://graph.facebook.com/v18.0",
		HTTPClient:    &http.Client{Timeout: 30 * time.Second},
	}

	buttons := []Button{
		{
			Type: "reply",
			Reply: Reply{
				ID:    "view_device",
				Title: "View Device",
			},
		},
	}

	// Test sending interactive message in test mode
	err := service.SendInteractiveMessage("+1234567890", "Test Header", "Test Body", "Test Footer", buttons)
	if err != nil {
		t.Errorf("Unexpected error sending interactive message: %v", err)
	}
}

func TestWhatsAppService_SendAlert(t *testing.T) {
	os.Setenv("TEST_ENV", "true") // Enable test mode
	defer os.Unsetenv("TEST_ENV")

	// Create a service instance directly for testing
	service := &WhatsAppService{
		AccessToken:   "test_token",
		PhoneNumberID: "test_phone_id",
		BaseURL:       "https://graph.facebook.com/v18.0",
		HTTPClient:    &http.Client{Timeout: 30 * time.Second},
	}

	// Test client with WhatsApp alerts disabled
	client := &models.Client{
		Name:                  "Test Client",
		WhatsappAlertsEnabled: func() *bool { b := false; return &b }(),
		WhatsappPhoneNumber:   func() *string { s := "+1234567890"; return &s }(),
	}

	alert := &models.Alert{
		AlertType: "geofence",
		Message:   func() *string { s := "Test alert"; return &s }(),
	}

	clientDevice := &models.ClientDevice{
		Name: func() *string { s := "Test Device"; return &s }(),
	}

	// Should not send alert when disabled
	err := service.SendAlert(client, alert, clientDevice)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Test client with WhatsApp alerts enabled
	client.WhatsappAlertsEnabled = func() *bool { b := true; return &b }()

	// Should send alert when enabled
	err = service.SendAlert(client, alert, clientDevice)
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Test client without WhatsApp phone number
	client.WhatsappPhoneNumber = nil

	// Should not return error when no phone number (just skips sending)
	err = service.SendAlert(client, alert, clientDevice)
	if err != nil {
		t.Errorf("Unexpected error when client has no WhatsApp phone number: %v", err)
	}
}

func TestWhatsAppService_BuildMessageTypes(t *testing.T) {
	os.Setenv("WHATSAPP_ACCESS_TOKEN", "test_token")
	os.Setenv("WHATSAPP_PHONE_NUMBER_ID", "test_phone_id")
	defer func() {
		os.Unsetenv("WHATSAPP_ACCESS_TOKEN")
		os.Unsetenv("WHATSAPP_PHONE_NUMBER_ID")
	}()

	service, _ := NewWhatsAppService()

	alert := &models.Alert{
		AlertType: "geofence",
		Message:   func() *string { s := "Test alert message"; return &s }(),
	}

	clientDevice := &models.ClientDevice{
		Name: func() *string { s := "Test Device"; return &s }(),
	}

	// Test different alert types
	alertTypes := []string{"geofence", "speed", "maintenance", "device_offline"}

	for _, alertType := range alertTypes {
		alert.AlertType = alertType
		message := service.buildWhatsAppMessage(alert, clientDevice)

		if message.Type != "interactive" {
			t.Errorf("Expected message type to be 'interactive', got '%s'", message.Type)
		}

		if message.Interactive == nil {
			t.Errorf("Expected interactive content to be present for alert type '%s'", alertType)
		}

		if message.Interactive.Body == nil {
			t.Errorf("Expected body to be present for alert type '%s'", alertType)
		}

		if message.Interactive.Action == nil {
			t.Errorf("Expected action to be present for alert type '%s'", alertType)
		}

		if len(message.Interactive.Action.Buttons) == 0 {
			t.Errorf("Expected buttons to be present for alert type '%s'", alertType)
		}
	}
}

// Example usage functions for documentation
func ExampleWhatsAppService_SendTextMessage() {
	// Initialize WhatsApp service
	service, err := NewWhatsAppService()
	if err != nil {
		return
	}

	// Send a simple text message
	err = service.SendTextMessage("+1234567890", "Hello! This is a test message.")
	if err != nil {
		return
	}
}

func ExampleWhatsAppService_SendInteractiveMessage() {
	// Initialize WhatsApp service
	service, err := NewWhatsAppService()
	if err != nil {
		return
	}

	// Create interactive message with buttons
	buttons := []Button{
		{
			Type: "reply",
			Reply: Reply{
				ID:    "view_device",
				Title: "View Device",
			},
		},
		{
			Type: "reply",
			Reply: Reply{
				ID:    "dismiss",
				Title: "Dismiss",
			},
		},
	}

	// Send interactive message
	err = service.SendInteractiveMessage(
		"+1234567890",
		"🚨 Alert Notification",
		"A device has triggered an alert. Would you like to view the details?",
		"Click a button below to take action",
		buttons,
	)
	if err != nil {
		return
	}
}

func ExampleWhatsAppService_ValidatePhoneNumber() {
	// Initialize WhatsApp service
	service, err := NewWhatsAppService()
	if err != nil {
		return
	}

	// Validate phone numbers
	phoneNumbers := []string{"+1234567890", "1234567890", "invalid"}

	for _, phone := range phoneNumbers {
		isValid := service.ValidatePhoneNumber(phone)
		formatted := service.FormatPhoneNumber(phone)
		_ = isValid   // Use the result
		_ = formatted // Use the result
	}
}
