package services

import (
	"context"
	"fmt"
	"log"
	"strconv"
	"sync"
	"sync/atomic"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"
)

// RealtimeTripDetectionService handles real-time trip detection using worker pools
type RealtimeTripDetectionService struct {
	gpsQueue         chan *models.GPSData
	workerCount      int
	deviceStates     sync.Map // map[uint]*DeviceTripState
	settings         *TripDetectionSettings
	ctx              context.Context
	cancel           context.CancelFunc
	processedCount   int64
	tripsCreated     int64
	keyEventsCreated int64
	lastFlushTime    time.Time
	flushInterval    time.Duration
	mu               sync.RWMutex
}

// DeviceTripState maintains in-memory state for each device
type DeviceTripState struct {
	DeviceId           uint
	DeviceType         *models.DeviceType
	LastGPSPoint       *models.GPSData
	CurrentTrip        *models.Trip
	LastIgnitionStatus *bool
	LastFlushTime      time.Time
	PendingGPSPoints   []*models.GPSData
	mu                 sync.RWMutex
}

// TripDetectionSettings holds configuration for trip detection
type TripDetectionSettings struct {
	IdleTimeoutMinutes   int
	MinSpeedThreshold    float64
	MinDistanceMeters    float64
	LookaheadPoints      int
	BatchSize            int
	FlushIntervalSeconds int
	WorkerCount          int
	QueueBufferSize      int
}

// KeyEvent represents a key on/off event
type KeyEvent struct {
	DeviceId      uint
	EventType     string // "key_on" or "key_off"
	Timestamp     time.Time
	Latitude      float64
	Longitude     float64
	PreviousState *bool
	NewState      bool
}

// NewRealtimeTripDetectionService creates a new real-time trip detection service
func NewRealtimeTripDetectionService() *RealtimeTripDetectionService {
	ctx, cancel := context.WithCancel(context.Background())

	service := &RealtimeTripDetectionService{
		ctx:           ctx,
		cancel:        cancel,
		flushInterval: 30 * time.Second,
		lastFlushTime: time.Now(),
	}

	service.loadSettings()
	service.gpsQueue = make(chan *models.GPSData, service.settings.QueueBufferSize)

	log.Printf("Realtime trip detection service initialized: workers=%d, queue_size=%d, flush_interval=%ds",
		service.settings.WorkerCount, service.settings.QueueBufferSize, service.settings.FlushIntervalSeconds)

	return service
}

// loadSettings loads trip detection settings from database
func (s *RealtimeTripDetectionService) loadSettings() {
	settings := &TripDetectionSettings{
		IdleTimeoutMinutes:   5,
		MinSpeedThreshold:    1.0,
		MinDistanceMeters:    100.0,
		LookaheadPoints:      3,
		BatchSize:            1000,
		FlushIntervalSeconds: 30,
		WorkerCount:          4,
		QueueBufferSize:      10000,
	}

	// Load from database settings
	if timeoutStr := models.GetSetting("trip_detection_idle_timeout_minutes"); timeoutStr != "" {
		if timeout, err := strconv.Atoi(timeoutStr); err == nil {
			settings.IdleTimeoutMinutes = timeout
		}
	}

	if speedStr := models.GetSetting("trip_detection_min_speed_threshold"); speedStr != "" {
		if speed, err := strconv.ParseFloat(speedStr, 64); err == nil {
			settings.MinSpeedThreshold = speed
		}
	}

	if distStr := models.GetSetting("trip_detection_min_distance_meters"); distStr != "" {
		if dist, err := strconv.ParseFloat(distStr, 64); err == nil {
			settings.MinDistanceMeters = dist
		}
	}

	if lookaheadStr := models.GetSetting("trip_detection_lookahead_points"); lookaheadStr != "" {
		if lookahead, err := strconv.Atoi(lookaheadStr); err == nil {
			settings.LookaheadPoints = lookahead
		}
	}

	if batchStr := models.GetSetting("trip_detection_batch_size"); batchStr != "" {
		if batch, err := strconv.Atoi(batchStr); err == nil {
			settings.BatchSize = batch
		}
	}

	// Real-time specific settings
	if flushStr := models.GetSetting("realtime_trip_flush_interval_seconds"); flushStr != "" {
		if flush, err := strconv.Atoi(flushStr); err == nil {
			settings.FlushIntervalSeconds = flush
		}
	}

	if workersStr := models.GetSetting("realtime_trip_worker_count"); workersStr != "" {
		if workers, err := strconv.Atoi(workersStr); err == nil {
			settings.WorkerCount = workers
		}
	}

	if queueStr := models.GetSetting("realtime_trip_queue_size"); queueStr != "" {
		if queue, err := strconv.Atoi(queueStr); err == nil {
			settings.QueueBufferSize = queue
		}
	}

	s.settings = settings
	s.workerCount = settings.WorkerCount
	s.flushInterval = time.Duration(settings.FlushIntervalSeconds) * time.Second
}

// Start initializes and starts the real-time trip detection service
func (s *RealtimeTripDetectionService) Start() error {
	log.Println("Starting real-time trip detection service...")

	// Start worker pool
	for i := 0; i < s.workerCount; i++ {
		go s.worker(i)
	}

	// Start periodic flush routine
	go s.periodicFlush()

	// Start monitoring routine
	go s.monitor()

	log.Printf("Real-time trip detection service started with %d workers", s.workerCount)
	return nil
}

// Stop gracefully shuts down the service
func (s *RealtimeTripDetectionService) Stop() error {
	log.Println("Stopping real-time trip detection service...")

	s.cancel()
	close(s.gpsQueue)

	// Flush remaining data
	s.flushAllDevices()

	log.Println("Real-time trip detection service stopped")
	return nil
}

// ProcessGPSData adds GPS data to the processing queue
func (s *RealtimeTripDetectionService) ProcessGPSData(gpsData *models.GPSData) error {
	select {
	case s.gpsQueue <- gpsData:
		return nil
	case <-s.ctx.Done():
		return fmt.Errorf("service is shutting down")
	default:
		// Queue is full, log warning but don't block
		log.Printf("Warning: GPS queue is full, dropping GPS data for device %d", *gpsData.ClientDeviceId)
		return fmt.Errorf("queue is full")
	}
}

// worker processes GPS data from the queue
func (s *RealtimeTripDetectionService) worker(workerID int) {
	log.Printf("Trip detection worker %d started", workerID)

	for {
		select {
		case gpsData := <-s.gpsQueue:
			if gpsData == nil {
				log.Printf("Worker %d: GPS queue closed, exiting", workerID)
				return
			}

			s.processGPSPoint(gpsData)
			atomic.AddInt64(&s.processedCount, 1)

		case <-s.ctx.Done():
			log.Printf("Worker %d: Context cancelled, exiting", workerID)
			return
		}
	}
}

// processGPSPoint processes a single GPS point for trip detection
func (s *RealtimeTripDetectionService) processGPSPoint(gpsData *models.GPSData) {
	if gpsData.ClientDeviceId == nil {
		return
	}

	deviceId := *gpsData.ClientDeviceId

	// Get or create device state
	deviceState := s.getOrCreateDeviceState(deviceId)
	if deviceState == nil {
		return
	}

	deviceState.mu.Lock()
	defer deviceState.mu.Unlock()

	// Check for key events (ignition status changes)
	if s.detectKeyEvent(deviceState, gpsData) {
		atomic.AddInt64(&s.keyEventsCreated, 1)
	}

	// Check for speed alerts
	if s.detectSpeedAlert(deviceState, gpsData) {
		atomic.AddInt64(&s.keyEventsCreated, 1)
	}

	// Check for towing events
	if s.detectTowingEvent(deviceState, gpsData) {
		atomic.AddInt64(&s.keyEventsCreated, 1)
	}

	// Check for impact detection
	if s.detectImpactEvent(deviceState, gpsData) {
		atomic.AddInt64(&s.keyEventsCreated, 1)
	}

	// Add GPS point to pending list
	deviceState.PendingGPSPoints = append(deviceState.PendingGPSPoints, gpsData)

	// Process trip detection
	s.processTripDetection(deviceState, gpsData)

	// Update device state
	deviceState.LastGPSPoint = gpsData
	deviceState.LastIgnitionStatus = gpsData.IgnitionStatus

	// Update ignition status time in database if ignition status changed
	if deviceState.LastIgnitionStatus != nil && gpsData.IgnitionStatus != nil {
		if *deviceState.LastIgnitionStatus != *gpsData.IgnitionStatus {
			// Update ignition status time in database
			go func() {
				timeNow := time.Now()
				config.DB.Model(&models.ClientDevice{}).
					Where("id = ?", deviceId).
					Updates(map[string]interface{}{
						"ignition_status":      gpsData.IgnitionStatus,
						"ignition_status_time": timeNow,
					})
			}()
		}
	}
}

// getOrCreateDeviceState gets or creates device state for a device
func (s *RealtimeTripDetectionService) getOrCreateDeviceState(deviceId uint) *DeviceTripState {
	// Try to get existing state
	if state, exists := s.deviceStates.Load(deviceId); exists {
		return state.(*DeviceTripState)
	}

	// Create new state
	var device models.ClientDevice
	err := config.DB.Preload("DeviceType").First(&device, deviceId).Error
	if err != nil {
		log.Printf("Failed to load device %d: %v", deviceId, err)
		return nil
	}

	deviceState := &DeviceTripState{
		DeviceId:         deviceId,
		DeviceType:       &device.DeviceType,
		LastFlushTime:    time.Now(),
		PendingGPSPoints: make([]*models.GPSData, 0),
	}

	s.deviceStates.Store(deviceId, deviceState)
	return deviceState
}

// detectKeyEvent detects key on/off events based on ignition status changes
func (s *RealtimeTripDetectionService) detectKeyEvent(deviceState *DeviceTripState, gpsData *models.GPSData) bool {
	// Only detect key events if device supports ignition status
	if deviceState.DeviceType.IgnitionStatus == nil || !*deviceState.DeviceType.IgnitionStatus {
		return false
	}

	// Check if ignition status changed
	if deviceState.LastIgnitionStatus == nil || gpsData.IgnitionStatus == nil {
		return false
	}

	lastIgnition := *deviceState.LastIgnitionStatus
	currentIgnition := *gpsData.IgnitionStatus

	if lastIgnition == currentIgnition {
		return false
	}

	// Create key event
	eventType := "key_off"
	if currentIgnition {
		eventType = "key_on"
	}

	keyEvent := &KeyEvent{
		DeviceId:      deviceState.DeviceId,
		EventType:     eventType,
		Timestamp:     *gpsData.GPSTimestamp,
		Latitude:      gpsData.Latitude,
		Longitude:     gpsData.Longitude,
		PreviousState: deviceState.LastIgnitionStatus,
		NewState:      currentIgnition,
	}

	// Process key event (create alert, send notifications, etc.)
	s.processKeyEvent(keyEvent)

	log.Printf("Key event detected for device %d: %s at %v", deviceState.DeviceId, eventType, keyEvent.Timestamp)
	return true
}

// processKeyEvent handles key events (alerts, notifications, etc.)
func (s *RealtimeTripDetectionService) processKeyEvent(keyEvent *KeyEvent) {
	// Create alert for key event
	alert := models.Alert{
		ClientDeviceId: keyEvent.DeviceId,
		AlertType:      keyEvent.EventType,
		AlertName:      &keyEvent.EventType,
		AlertLevel:     realtimeStringPtr("info"),
		Message:        realtimeStringPtr(fmt.Sprintf("Key %s detected", keyEvent.EventType)),
		AlertTimestamp: keyEvent.Timestamp,
	}

	if err := config.DB.Create(&alert).Error; err != nil {
		log.Printf("Failed to create key event alert: %v", err)
	}

	// Send real-time notifications if needed
	go s.sendKeyEventNotifications(keyEvent, &alert)
}

// detectSpeedAlert detects speed limit violations
func (s *RealtimeTripDetectionService) detectSpeedAlert(deviceState *DeviceTripState, gpsData *models.GPSData) bool {
	// Get device info to check speed alert settings
	var device models.ClientDevice
	err := config.DB.First(&device, deviceState.DeviceId).Error
	if err != nil {
		return false
	}

	// Check if speed alerts are enabled
	if device.DeviceType.SpeedAlerts == nil || !*device.DeviceType.SpeedAlerts {
		return false
	}

	// Check if speed exceeds threshold
	if gpsData.Speed != nil && device.MaxSpeed != nil && *gpsData.Speed > *device.MaxSpeed {
		// Create speed alert with GPS data link
		alert := models.Alert{
			ClientDeviceId: deviceState.DeviceId,
			DeviceId:       &device.DeviceId,
			GPSDataId:      &gpsData.Id,
			AlertType:      "speed_alert",
			AlertName:      realtimeStringPtr("Speed Limit Exceeded"),
			AlertLevel:     realtimeStringPtr("warning"),
			Message:        realtimeStringPtr(fmt.Sprintf("Vehicle speed %.2f km/h exceeds limit of %.2f km/h", *gpsData.Speed, *device.MaxSpeed)),
			Speed:          gpsData.Speed,
			Direction:      gpsData.Direction,
			AlertTimestamp: *gpsData.GPSTimestamp,
		}

		if err := config.DB.Create(&alert).Error; err != nil {
			log.Printf("Failed to create speed alert: %v", err)
		} else {
			log.Printf("Speed alert created for device %d: %.2f km/h (limit: %.2f km/h)",
				deviceState.DeviceId, *gpsData.Speed, *device.MaxSpeed)
		}

		return true
	}

	return false
}

// detectTowingEvent detects towing events
func (s *RealtimeTripDetectionService) detectTowingEvent(deviceState *DeviceTripState, gpsData *models.GPSData) bool {
	// Get device info to check towing event settings
	var device models.ClientDevice
	err := config.DB.First(&device, deviceState.DeviceId).Error
	if err != nil {
		return false
	}

	// Check if towing events are enabled
	if !device.TowingEvents {
		return false
	}

	// Check for required fields
	if gpsData.GPSTimestamp == nil {
		return false
	}

	// Use previous GPS point from device state if available
	if deviceState.LastGPSPoint == nil {
		return false
	}

	prevGPS := deviceState.LastGPSPoint

	// Detect towing based on movement without ignition
	if s.isTowingDetected(gpsData, prevGPS, &device) {
		// Create towing alert with GPS data link
		alert := models.Alert{
			ClientDeviceId: deviceState.DeviceId,
			DeviceId:       &device.DeviceId,
			GPSDataId:      &gpsData.Id,
			AlertType:      "towing_event",
			AlertName:      realtimeStringPtr("Vehicle Towing Detected"),
			AlertLevel:     realtimeStringPtr("critical"),
			Message:        realtimeStringPtr("Vehicle appears to be towed"),
			Speed:          gpsData.Speed,
			Direction:      gpsData.Direction,
			AlertTimestamp: *gpsData.GPSTimestamp,
		}

		if err := config.DB.Create(&alert).Error; err != nil {
			log.Printf("Failed to create towing alert: %v", err)
		} else {
			log.Printf("Towing event detected for device %d", deviceState.DeviceId)
		}

		return true
	}

	return false
}

// isTowingDetected checks if towing is detected based on GPS data patterns
func (s *RealtimeTripDetectionService) isTowingDetected(current, prev *models.GPSData, device *models.ClientDevice) bool {
	// Check for required fields
	if current.GPSTimestamp == nil || prev.GPSTimestamp == nil {
		return false
	}

	// Check for required coordinate fields
	if prev.Latitude == 0 || prev.Longitude == 0 || current.Latitude == 0 || current.Longitude == 0 {
		return false
	}

	// Calculate distance moved
	distance := utils.HaversineDistance(prev.Latitude, prev.Longitude, current.Latitude, current.Longitude)

	// Calculate time difference
	timeDiff := current.GPSTimestamp.Sub(*prev.GPSTimestamp)

	// If significant movement without ignition or with unusual pattern
	if distance > 0.1 && timeDiff < 5*time.Minute { // 100m in 5 minutes
		if current.IgnitionStatus != nil && !*current.IgnitionStatus {
			log.Printf("[TOWING] Device %d: Movement %.2fkm in %v with ignition OFF",
				*current.ClientDeviceId, distance, timeDiff)
			return true
		}

		// Check for unusual speed patterns
		if current.Speed != nil && prev.Speed != nil {
			speedDiff := *current.Speed - *prev.Speed
			if speedDiff > 20 { // Sudden speed increase
				log.Printf("[TOWING] Device %d: Sudden speed increase %.2f km/h",
					*current.ClientDeviceId, speedDiff)
				return true
			}
		}
	}

	return false
}

// detectImpactEvent detects potential impact events in real-time
func (s *RealtimeTripDetectionService) detectImpactEvent(deviceState *DeviceTripState, gpsData *models.GPSData) bool {
	// Need previous GPS point for comparison
	if deviceState.LastGPSPoint == nil {
		return false
	}

	prevGPS := deviceState.LastGPSPoint
	currentGPS := gpsData

	// Check if impact is detected
	if s.isImpactDetected(currentGPS, prevGPS) {
		// Get device info to get DeviceId
		var device models.ClientDevice
		if err := config.DB.First(&device, deviceState.DeviceId).Error; err != nil {
			log.Printf("Failed to get device info for impact alert: %v", err)
			return false
		}

		// Create impact alert with GPS data link
		alert := models.Alert{
			ClientDeviceId: deviceState.DeviceId,
			DeviceId:       &device.DeviceId,
			GPSDataId:      &gpsData.Id,
			AlertType:      "impact_detection",
			AlertName:      realtimeStringPtr("Potential Impact Detected"),
			AlertLevel:     realtimeStringPtr("critical"),
			Message:        realtimeStringPtr("Unusual movement pattern suggests possible impact"),
			Speed:          gpsData.Speed,
			Direction:      gpsData.Direction,
			AlertTimestamp: *gpsData.GPSTimestamp,
		}

		if err := config.DB.Create(&alert).Error; err != nil {
			log.Printf("Failed to create impact alert: %v", err)
		} else {
			log.Printf("Impact alert created for device %d at %v",
				deviceState.DeviceId, *gpsData.GPSTimestamp)
		}

		return true
	}

	return false
}

// isImpactDetected checks if impact is detected based on GPS data patterns
func (s *RealtimeTripDetectionService) isImpactDetected(current, prev *models.GPSData) bool {
	if current.Speed == nil || prev.Speed == nil {
		return false
	}

	currentSpeed := *current.Speed
	prevSpeed := *prev.Speed

	// Sudden deceleration (impact)
	if prevSpeed > 20 && currentSpeed < prevSpeed*0.3 { // Speed drops by 70%+
		log.Printf("[IMPACT] Device %d: Sudden deceleration from %.2f to %.2f km/h",
			*current.ClientDeviceId, prevSpeed, currentSpeed)
		return true
	}

	// Sudden stop from high speed
	if prevSpeed > 30 && currentSpeed < 5 {
		log.Printf("[IMPACT] Device %d: Sudden stop from %.2f to %.2f km/h",
			*current.ClientDeviceId, prevSpeed, currentSpeed)
		return true
	}

	// Additional impact detection patterns
	// Check for rapid deceleration over short time
	if prev.GPSTimestamp != nil && current.GPSTimestamp != nil {
		timeDiff := current.GPSTimestamp.Sub(*prev.GPSTimestamp).Seconds()
		if timeDiff > 0 && timeDiff < 10 { // Within 10 seconds
			speedDiff := prevSpeed - currentSpeed
			deceleration := speedDiff / timeDiff // km/h per second

			// Very high deceleration (>50 km/h per second)
			if deceleration > 50 {
				log.Printf("[IMPACT] Device %d: High deceleration %.2f km/h/s over %.2fs",
					*current.ClientDeviceId, deceleration, timeDiff)
				return true
			}
		}
	}

	return false
}

// processTripDetection processes trip detection logic
func (s *RealtimeTripDetectionService) processTripDetection(deviceState *DeviceTripState, gpsData *models.GPSData) {
	// Determine if device uses ignition-based or speed-based trip detection
	useIgnitionStatus := deviceState.DeviceType.IgnitionStatus != nil && *deviceState.DeviceType.IgnitionStatus

	// Check if trip should start
	if deviceState.CurrentTrip == nil {
		if s.shouldStartTrip(deviceState, gpsData, useIgnitionStatus) {
			s.startTrip(deviceState, gpsData)
		}
		return
	}

	// Check if trip should end
	if s.shouldEndTrip(deviceState, gpsData, useIgnitionStatus) {
		s.endTrip(deviceState, gpsData)
	} else {
		// Update current trip
		s.updateCurrentTrip(deviceState, gpsData)
	}
}

// shouldStartTrip determines if a trip should start
func (s *RealtimeTripDetectionService) shouldStartTrip(deviceState *DeviceTripState, gpsData *models.GPSData, useIgnitionStatus bool) bool {
	if useIgnitionStatus {
		// Use ignition status for trip start
		if gpsData.IgnitionStatus != nil && *gpsData.IgnitionStatus {
			return true
		}
	} else {
		// Use speed-based detection
		if gpsData.Speed != nil && *gpsData.Speed > s.settings.MinSpeedThreshold {
			return true
		}
	}

	// Check for significant movement
	if deviceState.LastGPSPoint != nil {
		distance := utils.HaversineDistance(
			deviceState.LastGPSPoint.Latitude, deviceState.LastGPSPoint.Longitude,
			gpsData.Latitude, gpsData.Longitude,
		)
		distanceMeters := distance * 1000

		if distanceMeters > s.settings.MinDistanceMeters {
			timeDiff := gpsData.GPSTimestamp.Sub(*deviceState.LastGPSPoint.GPSTimestamp)
			if timeDiff <= 2*time.Minute {
				return true
			}
		}
	}

	return false
}

// shouldEndTrip determines if a trip should end
func (s *RealtimeTripDetectionService) shouldEndTrip(deviceState *DeviceTripState, gpsData *models.GPSData, useIgnitionStatus bool) bool {
	if useIgnitionStatus {
		// Use ignition status for trip end
		if gpsData.IgnitionStatus != nil && !*gpsData.IgnitionStatus {
			return true
		}
	}

	// Check for prolonged idle time
	if gpsData.Speed != nil && *gpsData.Speed == 0 {
		idleDuration := s.calculateIdleDuration(deviceState)
		if idleDuration >= time.Duration(s.settings.IdleTimeoutMinutes)*time.Minute {
			return true
		}
	}

	return false
}

// startTrip starts a new trip
func (s *RealtimeTripDetectionService) startTrip(deviceState *DeviceTripState, gpsData *models.GPSData) {
	trip := &models.Trip{
		ClientDeviceId: deviceState.DeviceId,
		StartTime:      *gpsData.GPSTimestamp,
		StartLatitude:  gpsData.Latitude,
		StartLongitude: gpsData.Longitude,
		Status:         "active",
	}

	deviceState.CurrentTrip = trip
	log.Printf("Trip started for device %d at %v", deviceState.DeviceId, trip.StartTime)
}

// endTrip ends the current trip
func (s *RealtimeTripDetectionService) endTrip(deviceState *DeviceTripState, gpsData *models.GPSData) {
	if deviceState.CurrentTrip == nil {
		return
	}

	trip := deviceState.CurrentTrip
	trip.EndTime = gpsData.GPSTimestamp
	trip.EndLatitude = &gpsData.Latitude
	trip.EndLongitude = &gpsData.Longitude
	trip.Status = "completed"

	// Calculate trip metrics
	s.calculateTripMetrics(trip, deviceState.PendingGPSPoints)

	// Save trip to database
	if err := s.saveTrip(trip, deviceState.PendingGPSPoints); err != nil {
		log.Printf("Failed to save trip for device %d: %v", deviceState.DeviceId, err)
	} else {
		atomic.AddInt64(&s.tripsCreated, 1)
		log.Printf("Trip completed for device %d: %.2fkm, %ds duration",
			deviceState.DeviceId, trip.Distance, *trip.Duration)
	}

	// Clear current trip and pending points
	deviceState.CurrentTrip = nil
	deviceState.PendingGPSPoints = make([]*models.GPSData, 0)
}

// updateCurrentTrip updates the current trip with new GPS data
func (s *RealtimeTripDetectionService) updateCurrentTrip(deviceState *DeviceTripState, gpsData *models.GPSData) {
	// Update trip end time and location
	if deviceState.CurrentTrip != nil {
		deviceState.CurrentTrip.EndTime = gpsData.GPSTimestamp
		deviceState.CurrentTrip.EndLatitude = &gpsData.Latitude
		deviceState.CurrentTrip.EndLongitude = &gpsData.Longitude
	}
}

// calculateTripMetrics calculates trip metrics from GPS points
func (s *RealtimeTripDetectionService) calculateTripMetrics(trip *models.Trip, gpsPoints []*models.GPSData) {
	if len(gpsPoints) < 2 {
		return
	}

	var totalDistance float64
	var maxSpeed float64
	var totalSpeed float64
	var speedCount int

	for i := 1; i < len(gpsPoints); i++ {
		prev := gpsPoints[i-1]
		curr := gpsPoints[i]

		// Calculate distance
		distance := utils.HaversineDistance(prev.Latitude, prev.Longitude, curr.Latitude, curr.Longitude)
		totalDistance += distance

		// Calculate speed metrics
		if curr.Speed != nil {
			speed := *curr.Speed
			if speed > maxSpeed {
				maxSpeed = speed
			}
			if speed > 0 {
				totalSpeed += speed
				speedCount++
			}
		}
	}

	// Set trip metrics
	trip.Distance = totalDistance
	trip.MaxSpeed = &maxSpeed

	if speedCount > 0 {
		avgSpeed := totalSpeed / float64(speedCount)
		trip.AvgSpeed = &avgSpeed
	}

	// Calculate duration
	if trip.EndTime != nil {
		duration := int(trip.EndTime.Sub(trip.StartTime).Seconds())
		trip.Duration = &duration
	}

	// Calculate idle time
	idleTime := s.calculateTripIdleTime(gpsPoints)
	trip.IdleTime = &idleTime
}

// calculateIdleDuration calculates how long the vehicle has been idle
func (s *RealtimeTripDetectionService) calculateIdleDuration(deviceState *DeviceTripState) time.Duration {
	if deviceState.CurrentTrip == nil || len(deviceState.PendingGPSPoints) == 0 {
		return 0
	}

	// Find last point with movement
	lastMovementTime := deviceState.CurrentTrip.StartTime
	for i := len(deviceState.PendingGPSPoints) - 1; i >= 0; i-- {
		point := deviceState.PendingGPSPoints[i]

		// Check for movement (ignition on or speed > threshold)
		hasMovement := false
		if point.IgnitionStatus != nil && *point.IgnitionStatus {
			hasMovement = true
		} else if point.Speed != nil && *point.Speed > s.settings.MinSpeedThreshold {
			hasMovement = true
		}

		if hasMovement {
			lastMovementTime = *point.GPSTimestamp
			break
		}
	}

	currentTime := *deviceState.PendingGPSPoints[len(deviceState.PendingGPSPoints)-1].GPSTimestamp
	return currentTime.Sub(lastMovementTime)
}

// calculateTripIdleTime calculates total idle time during a trip
func (s *RealtimeTripDetectionService) calculateTripIdleTime(gpsPoints []*models.GPSData) int {
	if len(gpsPoints) < 2 {
		return 0
	}

	var totalIdleTime time.Duration
	var idleStartTime *time.Time

	for _, point := range gpsPoints {
		if point.GPSTimestamp == nil {
			continue
		}

		// Determine if point is idle
		isIdle := false
		if point.IgnitionStatus != nil {
			isIdle = !*point.IgnitionStatus
		} else if point.Speed != nil {
			isIdle = *point.Speed == 0
		}

		if isIdle {
			if idleStartTime == nil {
				idleStartTime = point.GPSTimestamp
			}
		} else {
			if idleStartTime != nil {
				idleDuration := point.GPSTimestamp.Sub(*idleStartTime)
				totalIdleTime += idleDuration
				idleStartTime = nil
			}
		}
	}

	// Handle case where trip ends while still idle
	if idleStartTime != nil && len(gpsPoints) > 0 {
		lastPoint := gpsPoints[len(gpsPoints)-1]
		if lastPoint.GPSTimestamp != nil {
			idleDuration := lastPoint.GPSTimestamp.Sub(*idleStartTime)
			totalIdleTime += idleDuration
		}
	}

	return int(totalIdleTime.Seconds())
}

// saveTrip saves a trip to the database and updates GPS data
func (s *RealtimeTripDetectionService) saveTrip(trip *models.Trip, gpsPoints []*models.GPSData) error {
	// Start transaction
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get start and end locations
	startLocation, _ := s.getLocationName(trip.StartLatitude, trip.StartLongitude)
	endLocation, _ := s.getLocationName(*trip.EndLatitude, *trip.EndLongitude)

	trip.StartLocation = startLocation
	trip.EndLocation = endLocation

	// Get current driver assignment
	if driver, err := GetCurrentDriverForDevice(trip.ClientDeviceId, trip.StartTime); err == nil && driver != nil {
		trip.DriverId = &driver.Id
	}

	// Save the trip
	if err := tx.Create(trip).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create trip: %v", err)
	}

	// Update GPS data with trip_id
	if err := tx.Model(&models.GPSData{}).
		Where("client_device_id = ? AND gps_timestamp >= ? AND gps_timestamp <= ? AND trip_id IS NULL",
			trip.ClientDeviceId, trip.StartTime, *trip.EndTime).
		Update("trip_id", trip.Id).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update GPS data with trip_id: %v", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit trip transaction: %v", err)
	}

	// Send trip notifications
	go s.sendTripNotifications(*trip)

	// Process driving behavior
	go s.processDrivingBehavior(*trip)

	return nil
}

// getLocationName gets location name for coordinates
func (s *RealtimeTripDetectionService) getLocationName(latitude, longitude float64) (*string, error) {
	geocodingService := NewReverseGeocodingService()
	locationName, err := geocodingService.GetLocationName(latitude, longitude)
	if err != nil {
		return nil, err
	}
	return &locationName, nil
}

// sendKeyEventNotifications sends notifications for key events
func (s *RealtimeTripDetectionService) sendKeyEventNotifications(keyEvent *KeyEvent, alert *models.Alert) {
	// Get client device
	var clientDevice models.ClientDevice
	err := config.DB.Preload("Client").First(&clientDevice, keyEvent.DeviceId).Error
	if err != nil {
		log.Printf("Failed to load client device for key event notifications: %v", err)
		return
	}

	// Send Slack notification if enabled
	go func() {
		slackService, err := NewSlackService()
		if err != nil {
			log.Printf("Failed to initialize Slack service: %v", err)
			return
		}

		if err := slackService.SendAlert(alert, &clientDevice); err != nil {
			log.Printf("Failed to send Slack key event alert: %v", err)
		}
	}()
}

// sendTripNotifications sends trip notifications
func (s *RealtimeTripDetectionService) sendTripNotifications(trip models.Trip) {
	// Get client device
	var clientDevice models.ClientDevice
	err := config.DB.Preload("Client").First(&clientDevice, trip.ClientDeviceId).Error
	if err != nil {
		log.Printf("Failed to load client device for trip notifications: %v", err)
		return
	}

	// Create trip end alert if enabled
	if clientDevice.TripEndEvents && trip.EndTime != nil {
		endAlert := models.Alert{
			ClientDeviceId: trip.ClientDeviceId,
			DeviceId:       &clientDevice.DeviceId,
			AlertType:      "trip_end",
			AlertName:      realtimeStringPtr("Trip Ended"),
			AlertLevel:     realtimeStringPtr("info"),
			Message: realtimeStringPtr(fmt.Sprintf("Trip ended at %s. Distance: %.2fkm, Duration: %s",
				realtimeFormatLocationName(trip.EndLocation), trip.Distance, realtimeFormatDuration(trip.Duration))),
			AlertTimestamp: *trip.EndTime,
		}

		if err := config.DB.Create(&endAlert).Error; err != nil {
			log.Printf("Failed to create trip end alert: %v", err)
		}
	}
}

// processDrivingBehavior processes driving behavior for a trip
func (s *RealtimeTripDetectionService) processDrivingBehavior(trip models.Trip) {
	behaviorService := NewDrivingBehaviorService()
	err := behaviorService.ProcessTripBehavior(trip)
	if err != nil {
		log.Printf("Failed to process driving behavior for trip %d: %v", trip.Id, err)
	}
}

// periodicFlush periodically flushes pending data to database
func (s *RealtimeTripDetectionService) periodicFlush() {
	ticker := time.NewTicker(s.flushInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.flushAllDevices()
		case <-s.ctx.Done():
			return
		}
	}
}

// flushAllDevices flushes all pending data for all devices
func (s *RealtimeTripDetectionService) flushAllDevices() {
	s.deviceStates.Range(func(key, value interface{}) bool {
		deviceState := value.(*DeviceTripState)
		deviceState.mu.Lock()

		// Flush if enough time has passed or enough points accumulated
		shouldFlush := time.Since(deviceState.LastFlushTime) > s.flushInterval ||
			len(deviceState.PendingGPSPoints) >= s.settings.BatchSize

		if shouldFlush && len(deviceState.PendingGPSPoints) > 0 {
			// Update GPS data with current trip_id if there's an active trip
			if deviceState.CurrentTrip != nil && deviceState.CurrentTrip.Id != 0 {
				s.updateGPSDataWithTripId(deviceState)
			}

			deviceState.PendingGPSPoints = make([]*models.GPSData, 0)
			deviceState.LastFlushTime = time.Now()
		}

		deviceState.mu.Unlock()
		return true
	})
}

// updateGPSDataWithTripId updates GPS data with trip_id
func (s *RealtimeTripDetectionService) updateGPSDataWithTripId(deviceState *DeviceTripState) {
	if deviceState.CurrentTrip == nil || deviceState.CurrentTrip.Id == 0 {
		return
	}

	// Update GPS data points that don't have trip_id yet
	for _, gpsPoint := range deviceState.PendingGPSPoints {
		if gpsPoint.TripId == nil {
			config.DB.Model(gpsPoint).Update("trip_id", deviceState.CurrentTrip.Id)
		}
	}
}

// monitor monitors the service health and performance
func (s *RealtimeTripDetectionService) monitor() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			processed := atomic.LoadInt64(&s.processedCount)
			trips := atomic.LoadInt64(&s.tripsCreated)
			keyEvents := atomic.LoadInt64(&s.keyEventsCreated)
			queueLen := len(s.gpsQueue)

			log.Printf("Realtime trip detection stats: processed=%d, trips=%d, key_events=%d, queue_len=%d",
				processed, trips, keyEvents, queueLen)

			// Check for queue backpressure
			if queueLen > s.settings.QueueBufferSize*8/10 {
				log.Printf("Warning: GPS queue is %d%% full (%d/%d)",
					queueLen*100/s.settings.QueueBufferSize, queueLen, s.settings.QueueBufferSize)
			}

		case <-s.ctx.Done():
			return
		}
	}
}

// getIgnitionStatusDuration gets the duration since last ignition status change
func (s *RealtimeTripDetectionService) getIgnitionStatusDuration(deviceId uint) (time.Duration, error) {
	var device models.ClientDevice
	err := config.DB.First(&device, deviceId).Error
	if err != nil {
		return 0, err
	}

	if device.IgnitionStatusTime == nil {
		return 0, nil
	}

	now := time.Now()
	duration := now.Sub(*device.IgnitionStatusTime)
	return duration, nil
}

// Helper functions for real-time trip detection
func realtimeStringPtr(s string) *string {
	return &s
}

func realtimeFormatLocationName(location *string) string {
	if location != nil && *location != "" {
		return *location
	}
	return "Unknown Location"
}

func realtimeFormatDuration(durationPtr *int) string {
	if durationPtr == nil {
		return "Unknown"
	}
	duration := time.Duration(*durationPtr) * time.Second
	hours := int(duration.Hours())
	minutes := int(duration.Minutes()) % 60

	if hours > 0 {
		return fmt.Sprintf("%dh %dm", hours, minutes)
	}
	return fmt.Sprintf("%dm", minutes)
}
