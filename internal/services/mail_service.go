package services

import (
	"encoding/base64"
	"errors"
	"fmt"
	"log"
	"os"
	"strings"

	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
	"yotracker/internal/models"
)

// EmailTemplate represents the structure for email templates
type EmailTemplate struct {
	AppName    string
	AppURL     string
	Subject    string
	Greeting   string
	Content    []string
	ActionText string
	ActionURL  string
	Signature  string
	Year       string
}

// SetGreeting sets a custom greeting
func (t *EmailTemplate) SetGreeting(greeting string) *EmailTemplate {
	t.Greeting = greeting
	return t
}

// AddContent adds content paragraphs to the email
func (t *EmailTemplate) AddContent(content ...string) *EmailTemplate {
	t.Content = append(t.Content, content...)
	return t
}

// SetAction sets the action button text and URL
func (t *EmailTemplate) SetAction(text, url string) *EmailTemplate {
	t.ActionText = text
	t.ActionURL = url
	return t
}

// SetSignature sets a custom signature
func (t *EmailTemplate) SetSignature(signature string) *EmailTemplate {
	t.Signature = signature
	return t
}

// GenerateHTML generates the HTML email content
func (t *EmailTemplate) GenerateHTML() string {
	// Build content paragraphs
	contentHTML := ""
	for _, paragraph := range t.Content {
		contentHTML += fmt.Sprintf(`<p style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; font-size: 16px; line-height: 1.5em; margin-top: 0; text-align: left;">%s</p>`, paragraph)
	}

	// Build action button if provided
	actionHTML := ""
	subcopyHTML := ""
	if t.ActionText != "" && t.ActionURL != "" {
		actionHTML = fmt.Sprintf(`
<table class="action" align="center" width="100%%" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%%; margin: 30px auto; padding: 0; text-align: center; width: 100%%; float: unset;">
<tr>
<td align="center" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<table width="100%%" border="0" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<tr>
<td align="center" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<table border="0" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<tr>
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<a href="%s" class="button button-primary" target="_blank" rel="noopener" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -webkit-text-size-adjust: none; border-radius: 4px; color: #fff; display: inline-block; overflow: hidden; text-decoration: none; background-color: #2d3748; border-bottom: 8px solid #2d3748; border-left: 18px solid #2d3748; border-right: 18px solid #2d3748; border-top: 8px solid #2d3748;">%s</a>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>`, t.ActionURL, t.ActionText)

		subcopyHTML = fmt.Sprintf(`
<table class="subcopy" width="100%%" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; border-top: 1px solid #e8e5ef; margin-top: 25px; padding-top: 25px;">
<tr>
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<p style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; line-height: 1.5em; margin-top: 0; text-align: left; font-size: 14px;">If you're having trouble clicking the "%s" button, copy and paste the URL below
into your web browser: <span class="break-all" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; word-break: break-all;"><a href="%s" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; color: #3869d4;">%s</a></span></p>
</td>
</tr>
</table>`, t.ActionText, t.ActionURL, t.ActionURL)
	}

	return fmt.Sprintf(`<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>%s</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="color-scheme" content="light">
<meta name="supported-color-schemes" content="light">
<style>
@media only screen and (max-width: 600px) {
.inner-body {
width: 100%% !important;
}

.footer {
width: 100%% !important;
}
}

@media only screen and (max-width: 500px) {
.button {
width: 100%% !important;
}
}
</style>
</head>
<body style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -webkit-text-size-adjust: none; background-color: #ffffff; color: #718096; height: 100%%; line-height: 1.4; margin: 0; padding: 0; width: 100%% !important;">

<table class="wrapper" width="100%%" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%%; background-color: #edf2f7; margin: 0; padding: 0; width: 100%%;">
<tr>
<td align="center" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<table class="content" width="100%%" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%%; margin: 0; padding: 0; width: 100%%;">
<tr>
<td class="header" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; padding: 25px 0; text-align: center;">
<a href="%s" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; color: #3d4852; font-size: 19px; font-weight: bold; text-decoration: none; display: inline-block;">
%s
</a>
</td>
</tr>

<!-- Email Body -->
<tr>
<td class="body" width="100%%" cellpadding="0" cellspacing="0" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 100%%; background-color: #edf2f7; border-bottom: 1px solid #edf2f7; border-top: 1px solid #edf2f7; margin: 0; padding: 0; width: 100%%; border: hidden !important;">
<table class="inner-body" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px; background-color: #ffffff; border-color: #e8e5ef; border-radius: 2px; border-width: 1px; box-shadow: 0 2px 0 rgba(0, 0, 150, 0.025), 2px 4px 0 rgba(0, 0, 150, 0.015); margin: 0 auto; padding: 0; width: 570px;">
<!-- Body content -->
<tr>
<td class="content-cell" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; max-width: 100vw; padding: 32px;">
<h1 style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; color: #3d4852; font-size: 18px; font-weight: bold; margin-top: 0; text-align: left;">%s</h1>
%s
%s
<p style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; font-size: 16px; line-height: 1.5em; margin-top: 0; text-align: left;">Regards,<br>
%s</p>

%s
</td>
</tr>
</table>
</td>
</tr>

<tr>
<td style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative;">
<table class="footer" align="center" width="570" cellpadding="0" cellspacing="0" role="presentation" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; -premailer-cellpadding: 0; -premailer-cellspacing: 0; -premailer-width: 570px; margin: 0 auto; padding: 0; text-align: center; width: 570px;">
<tr>
<td class="content-cell" align="center" style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; max-width: 100vw; padding: 32px;">
<p style="box-sizing: border-box; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; position: relative; line-height: 1.5em; margin-top: 0; color: #b0adc5; font-size: 12px; text-align: center;">© %s %s. All rights reserved.</p>
</td>
</tr>
</table>
</td>
</tr>
</table>
</td>
</tr>
</table>
</body>
</html>`, t.AppName, t.AppURL, t.AppName, t.Greeting, contentHTML, actionHTML, t.Signature, subcopyHTML, t.Year, t.AppName)
}

// GenerateText generates the plain text email content
func (t *EmailTemplate) GenerateText() string {
	var textContent strings.Builder

	textContent.WriteString(fmt.Sprintf("%s: %s\n\n", t.AppName, t.AppURL))
	textContent.WriteString(fmt.Sprintf("# %s\n\n", t.Greeting))

	for _, paragraph := range t.Content {
		textContent.WriteString(fmt.Sprintf("%s\n\n", paragraph))
	}

	if t.ActionText != "" && t.ActionURL != "" {
		textContent.WriteString(fmt.Sprintf("%s: %s\n\n", t.ActionText, t.ActionURL))
	}

	textContent.WriteString(fmt.Sprintf("Regards,\n%s\n\n", t.Signature))

	if t.ActionText != "" && t.ActionURL != "" {
		textContent.WriteString(fmt.Sprintf("If you're having trouble clicking the \"%s\" button, copy and paste the URL below\ninto your web browser: [%s](%s)\n\n", t.ActionText, t.ActionURL, t.ActionURL))
	}

	textContent.WriteString(fmt.Sprintf("© %s %s. All rights reserved.\n", t.Year, t.AppName))

	return textContent.String()
}

// MailService handles email operations using SendGrid
type MailService struct {
	client    *sendgrid.Client
	fromName  string
	fromEmail string
}

// NewMailService creates a new instance of MailService
func NewMailService() (*MailService, error) {
	apiKey := os.Getenv("SENDGRID_API_KEY")
	if apiKey == "" {
		return nil, errors.New("SENDGRID_API_KEY environment variable is required")
	}

	fromEmail := os.Getenv("FROM_EMAIL")
	if fromEmail == "" {
		return nil, errors.New("FROM_EMAIL environment variable is required")
	}

	fromName := os.Getenv("FROM_NAME")
	if fromName == "" {
		fromName = "YoTracker" // Default name
	}

	client := sendgrid.NewSendClient(apiKey)

	return &MailService{
		client:    client,
		fromName:  fromName,
		fromEmail: fromEmail,
	}, nil
}

// SendMail sends an email using SendGrid
func (ms *MailService) SendMail(mailData *models.Mail) error {
	// Validate the mail data
	if err := mailData.Validate(); err != nil {
		return fmt.Errorf("mail validation failed: %w", err)
	}

	// Create the SendGrid message
	message := ms.buildSendGridMessage(mailData)

	// Send the email
	response, err := ms.client.Send(message)
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	// Check response status
	if response.StatusCode >= 400 {
		return fmt.Errorf("sendgrid returned error status %d: %s", response.StatusCode, response.Body)
	}

	log.Printf("Email sent successfully. Status: %d", response.StatusCode)
	return nil
}

// buildSendGridMessage constructs a SendGrid mail message from our Mail model
func (ms *MailService) buildSendGridMessage(mailData *models.Mail) *mail.SGMailV3 {
	// Create from address
	from := mail.NewEmail(ms.fromName, ms.fromEmail)

	// Create the message
	message := mail.NewV3Mail()
	message.SetFrom(from)
	message.Subject = mailData.Subject

	// Set reply-to if provided
	if mailData.ReplyTo != "" {
		replyTo := mail.NewEmail("", mailData.ReplyTo)
		message.SetReplyTo(replyTo)
	}

	// Create personalization (recipients)
	personalization := mail.NewPersonalization()

	// Add TO recipients
	for _, toEmail := range mailData.To {
		personalization.AddTos(mail.NewEmail("", toEmail))
	}

	// Add CC recipients
	for _, ccEmail := range mailData.CC {
		personalization.AddCCs(mail.NewEmail("", ccEmail))
	}

	// Add BCC recipients
	for _, bccEmail := range mailData.BCC {
		personalization.AddBCCs(mail.NewEmail("", bccEmail))
	}

	message.AddPersonalizations(personalization)

	// Set content
	if mailData.TextBody != "" {
		message.AddContent(mail.NewContent("text/plain", mailData.TextBody))
	}

	if mailData.HTMLBody != "" {
		message.AddContent(mail.NewContent("text/html", mailData.HTMLBody))
	}

	// Add attachments
	for _, attachment := range mailData.Attachments {
		sgAttachment := mail.NewAttachment()
		sgAttachment.SetFilename(attachment.Filename)
		sgAttachment.SetContent(base64.StdEncoding.EncodeToString(attachment.Content))

		if attachment.ContentType != "" {
			sgAttachment.SetType(attachment.ContentType)
		}

		message.AddAttachment(sgAttachment)
	}

	return message
}

// SendSimpleMail sends a simple email with basic parameters
func (ms *MailService) SendSimpleMail(to []string, subject, message string, isHTML bool) error {
	mailObj := models.NewMail(to, subject)

	if isHTML {
		mailObj.SetHTMLBody(message)
	} else {
		mailObj.SetTextBody(message)
	}

	return ms.SendMail(mailObj)
}

// SendMailWithCC sends an email with CC recipients
func (ms *MailService) SendMailWithCC(to []string, cc []string, subject, message string, isHTML bool) error {
	mailObj := models.NewMail(to, subject)
	mailObj.CC = cc

	if isHTML {
		mailObj.SetHTMLBody(message)
	} else {
		mailObj.SetTextBody(message)
	}

	return ms.SendMail(mailObj)
}

// SendMailWithAttachment sends an email with an attachment
func (ms *MailService) SendMailWithAttachment(to []string, subject, message string, isHTML bool, filename string, content []byte, contentType string) error {
	mailObj := models.NewMail(to, subject)

	if isHTML {
		mailObj.SetHTMLBody(message)
	} else {
		mailObj.SetTextBody(message)
	}

	mailObj.AddAttachment(filename, content, contentType)

	return ms.SendMail(mailObj)
}

// NewEmailTemplate creates a new email template with default values
func (ms *MailService) NewEmailTemplate() *EmailTemplate {
	appURL := os.Getenv("APP_URL")
	if appURL == "" {
		appURL = "https://yourdomain.com"
	}

	return &EmailTemplate{
		AppName:   ms.fromName,
		AppURL:    appURL,
		Greeting:  "Hello!",
		Signature: ms.fromName,
		Year:      "2025",
	}
}

// SendTemplatedEmail sends an email using the template system
func (ms *MailService) SendTemplatedEmail(to string, subject string, templateFunc func(*EmailTemplate) *EmailTemplate) error {
	template := ms.NewEmailTemplate()
	template = templateFunc(template)

	htmlBody := template.GenerateHTML()
	textBody := template.GenerateText()

	mailObj := models.NewMail([]string{to}, subject)
	mailObj.SetHTMLBody(htmlBody)
	mailObj.SetTextBody(textBody)

	return ms.SendMail(mailObj)
}

// SendWelcomeEmail sends a welcome email to new users using template
func (ms *MailService) SendWelcomeEmail(to string, name string) error {
	return ms.SendTemplatedEmail(to, "Welcome to YoTracker!", func(template *EmailTemplate) *EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Welcome to YoTracker, %s!", name)).
			AddContent(
				"Thank you for joining YoTracker. We're excited to have you on board.",
				"You can now start tracking your devices and managing your fleet.",
				"If you have any questions, please don't hesitate to contact our support team.",
			).
			SetAction("Get Started", template.AppURL+"/dashboard")
	})
}

// SendPasswordResetEmail sends a password reset email using template
func (ms *MailService) SendPasswordResetEmail(to string, name string, resetLink string) error {
	return ms.SendTemplatedEmail(to, "Password Reset Request - YoTracker", func(template *EmailTemplate) *EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Hello %s,", name)).
			AddContent(
				"We received a request to reset your password for your YoTracker account.",
				"If you didn't request this password reset, please ignore this email.",
				"This link will expire in 24 hours.",
			).
			SetAction("Reset Password", resetLink)
	})
}

// SendNewAccountEmail sends a new account creation notification with credentials
func (ms *MailService) SendNewAccountEmail(to string, name string, password string, loginURL string) error {
	return ms.SendTemplatedEmail(to, "Your New YoTracker Account", func(template *EmailTemplate) *EmailTemplate {
		return template.
			SetGreeting(fmt.Sprintf("Hello %s,", name)).
			AddContent(
				"A new YoTracker account has been created for you.",
				"Here are your login credentials:",
				fmt.Sprintf("Email: %s", to),
				fmt.Sprintf("Password: %s", password),
				"Please log in and change your password as soon as possible for security reasons.",
				"If you have any questions, please don't hesitate to contact our support team.",
			).
			SetAction("Login to YoTracker", loginURL)
	})
}
