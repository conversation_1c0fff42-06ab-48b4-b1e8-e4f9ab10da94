package services

import (
	"os"
	"strings"
	"testing"
)

func TestGoogleGeocodingService_GetLocationName(t *testing.T) {
	// Skip test if database is not available (e.g., in CI environment)
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		t.<PERSON><PERSON>("Skipping test in test environment - no database connection")
	}

	service := NewGoogleGeocodingService()

	// Test with known coordinates (Harare, Zimbabwe)
	latitude := -17.8216
	longitude := 31.0492

	locationName, err := service.GetLocationName(latitude, longitude)
	if err != nil {
		// Check if it's a configuration error (expected in test environment)
		if strings.Contains(err.<PERSON>rror(), "google_maps_api_key setting is not configured") {
			t.Logf("Expected error in test environment: %v", err)
			return
		}
		t.Fatalf("Failed to get location name: %v", err)
	}

	if locationName == "" {
		t.<PERSON><PERSON>("Expected non-empty location name")
	}

	t.Logf("Location name for coordinates (%.6f, %.6f): %s", latitude, longitude, locationName)
}

func TestGoogleGeocodingService_BuildLocationName(t *testing.T) {
	service := NewGoogleGeocodingService()

	// Test with sample Google Geocoding response
	sampleResult := struct {
		FormattedAddress  string `json:"formatted_address"`
		AddressComponents []struct {
			LongName  string   `json:"long_name"`
			ShortName string   `json:"short_name"`
			Types     []string `json:"types"`
		} `json:"address_components"`
	}{
		FormattedAddress: "123 Main St, Harare, Zimbabwe",
		AddressComponents: []struct {
			LongName  string   `json:"long_name"`
			ShortName string   `json:"short_name"`
			Types     []string `json:"types"`
		}{
			{LongName: "123", Types: []string{"street_number"}},
			{LongName: "Main St", Types: []string{"route"}},
			{LongName: "Harare", Types: []string{"locality"}},
			{LongName: "Zimbabwe", Types: []string{"country"}},
		},
	}

	locationName := service.buildLocationName(sampleResult)
	expected := "123 Main St, Harare, Zimbabwe"
	if locationName != expected {
		t.Errorf("Expected location name '%s', got '%s'", expected, locationName)
	}

	t.Logf("Built location name: %s", locationName)
}

func TestGoogleGeocodingService_GetAddressComponent(t *testing.T) {
	service := NewGoogleGeocodingService()

	components := []struct {
		LongName  string   `json:"long_name"`
		ShortName string   `json:"short_name"`
		Types     []string `json:"types"`
	}{
		{LongName: "123", Types: []string{"street_number"}},
		{LongName: "Main St", Types: []string{"route"}},
		{LongName: "Harare", Types: []string{"locality"}},
		{LongName: "Zimbabwe", Types: []string{"country"}},
	}

	// Test extracting street number
	streetNumber := service.getAddressComponent(components, []string{"street_number"})
	if streetNumber != "123" {
		t.Errorf("Expected street number '123', got '%s'", streetNumber)
	}

	// Test extracting route
	route := service.getAddressComponent(components, []string{"route"})
	if route != "Main St" {
		t.Errorf("Expected route 'Main St', got '%s'", route)
	}

	// Test extracting locality
	locality := service.getAddressComponent(components, []string{"locality"})
	if locality != "Harare" {
		t.Errorf("Expected locality 'Harare', got '%s'", locality)
	}

	// Test extracting country
	country := service.getAddressComponent(components, []string{"country"})
	if country != "Zimbabwe" {
		t.Errorf("Expected country 'Zimbabwe', got '%s'", country)
	}

	// Test non-existent component
	nonExistent := service.getAddressComponent(components, []string{"postal_code"})
	if nonExistent != "" {
		t.Errorf("Expected empty string for non-existent component, got '%s'", nonExistent)
	}
}
