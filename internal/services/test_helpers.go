package services

import (
	"log"
	"os"
	"yotracker/config"
	"yotracker/internal/seed"
	"yotracker/migrations"
)

// RefreshTestDatabase clears the database, runs migrations, and seeds data
// This ensures consistent test data and fixes issues with missing or inconsistent data
func RefreshTestDatabase() error {
	log.Println("Refreshing test database...")

	// Set test environment variable to disable external API calls
	os.Setenv("TEST_ENV", "true")

	// Initialize database if not already done
	if config.DB == nil {
		config.InitTestDB()
	}

	// First, try to drop all tables to start fresh
	// We'll ignore errors here since some tables might not exist
	config.DB.Migrator().DropTable(
		"trips",
		"alerts",
		"invoice_items",
		"invoice_payments",
		"invoices",
		"client_devices",
		"clients",
		"fleets",
		"device_types",
		"protocols",
		"currencies",
		"payment_types",
		"tax_rates",
		"settings",
		"roles",
		"users",
		"gps_data",
		"trip_replays",
		"migrations",
		// New report tables
		"customer_visits",
		"maintenance_schedules",
		"vehicle_health_metrics",
		"operating_costs",
		"emissions_data",
		"driver_training",
		"asset_performance",
		"route_optimization",
		"safety_compliance",
		"vehicle_lifecycle",
		"service_technicians",
		"construction_equipment",
		"sales_territories",
		"delivery_performance",
	)

	// Run migrations to recreate tables
	// We'll ignore errors here since migrations might fail if tables already exist
	migrations.Migrate()

	// Clear any existing data from key tables
	// Use Exec with error handling to ignore errors if tables don't exist
	config.DB.Exec("DELETE FROM trips")
	config.DB.Exec("DELETE FROM alerts")
	config.DB.Exec("DELETE FROM invoice_items")
	config.DB.Exec("DELETE FROM invoice_payments")
	config.DB.Exec("DELETE FROM invoices")
	config.DB.Exec("DELETE FROM client_devices")
	config.DB.Exec("DELETE FROM clients")
	config.DB.Exec("DELETE FROM fleets")
	config.DB.Exec("DELETE FROM device_types")
	config.DB.Exec("DELETE FROM protocols")
	config.DB.Exec("DELETE FROM currencies")
	config.DB.Exec("DELETE FROM payment_types")
	config.DB.Exec("DELETE FROM tax_rates")
	config.DB.Exec("DELETE FROM settings")
	config.DB.Exec("DELETE FROM roles")
	config.DB.Exec("DELETE FROM users")

	// Clean up new report tables
	config.DB.Exec("DELETE FROM customer_visits")
	config.DB.Exec("DELETE FROM maintenance_schedules")
	config.DB.Exec("DELETE FROM vehicle_health_metrics")
	config.DB.Exec("DELETE FROM operating_costs")
	config.DB.Exec("DELETE FROM emissions_data")
	config.DB.Exec("DELETE FROM driver_training")
	config.DB.Exec("DELETE FROM asset_performance")
	config.DB.Exec("DELETE FROM route_optimization")
	config.DB.Exec("DELETE FROM safety_compliance")
	config.DB.Exec("DELETE FROM vehicle_lifecycle")
	config.DB.Exec("DELETE FROM service_technicians")
	config.DB.Exec("DELETE FROM construction_equipment")
	config.DB.Exec("DELETE FROM sales_territories")
	config.DB.Exec("DELETE FROM delivery_performance")

	// Seed data
	seed.Seed()

	log.Println("Test database refreshed successfully")
	return nil
}

// FastCleanupTestData performs a lightweight cleanup of test-specific data
// This is much faster than RefreshTestDatabase and should be used for most tests
// Note: Database setup and seeding is now handled by TestMain in each package
func FastCleanupTestData() error {
	log.Println("Cleaning up test data...")

	// Initialize database if not already done
	if config.DB == nil {
		config.InitTestDB()
	}

	// Check if database connection is still valid
	if err := config.DB.Raw("SELECT 1").Error; err != nil {
		log.Println("Database connection lost, resetting connection...")
		ResetTestDBConnection()
	}

	// Disable foreign key checks during cleanup to avoid constraint issues
	config.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	defer config.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

	// Only clear test-specific data, keep seeded data
	// Use Exec with error handling to ignore errors if tables don't exist
	// Delete in proper order to avoid foreign key constraint issues
	// Start with child tables and work up to parent tables

	// First, delete data from tables that depend on other tables
	config.DB.Exec("DELETE FROM trips WHERE id > 0")
	config.DB.Exec("DELETE FROM alerts WHERE id > 0")
	config.DB.Exec("DELETE FROM invoice_items WHERE id > 0")
	config.DB.Exec("DELETE FROM invoice_payments WHERE id > 0")
	config.DB.Exec("DELETE FROM client_device_daily_stats WHERE id > 0")
	config.DB.Exec("DELETE FROM device_alert_preferences WHERE id > 0")
	config.DB.Exec("DELETE FROM fleet_alert_preferences WHERE id > 0")
	config.DB.Exec("DELETE FROM alert_preferences WHERE id > 0")
	config.DB.Exec("DELETE FROM support_tickets WHERE id > 0")
	config.DB.Exec("DELETE FROM file_uploads WHERE id > 0")
	config.DB.Exec("DELETE FROM driver_device_assignments WHERE id > 0")
	config.DB.Exec("DELETE FROM gps_data WHERE id > 0")
	config.DB.Exec("DELETE FROM trip_replays WHERE id > 0")

	// Then delete from intermediate tables
	config.DB.Exec("DELETE FROM invoices WHERE id > 0")
	config.DB.Exec("DELETE FROM client_devices WHERE id > 0")
	config.DB.Exec("DELETE FROM users WHERE id > 1") // Keep the first seeded admin user
	config.DB.Exec("DELETE FROM fleets WHERE id > 0")

	// Finally delete from parent tables
	config.DB.Exec("DELETE FROM clients WHERE id > 1") // Keep the first seeded client

	// Don't delete device_types, protocols, currencies, payment_types, tax_rates - keep seeded data
	config.DB.Exec("DELETE FROM settings WHERE setting_key LIKE 'test_%'")
	config.DB.Exec("DELETE FROM roles WHERE id > 2") // Keep seeded roles (Admin=1, Client=2)

	log.Println("Test data cleaned up successfully")
	return nil
}

// CloseTestDB closes the test database connection
func CloseTestDB() error {
	if config.DB != nil {
		sqlDB, err := config.DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// ResetTestDBConnection resets the database connection to handle connection pool exhaustion
func ResetTestDBConnection() {
	if config.DB != nil {
		sqlDB, err := config.DB.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
	config.DB = nil
	config.InitTestDB()
}

// CleanupTestConnections closes all database connections to prevent pool exhaustion
func CleanupTestConnections() {
	if config.DB != nil {
		sqlDB, err := config.DB.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
	config.DB = nil
}

// GetTableCount returns the count of records in a table
func GetTableCount(tableName string) (int64, error) {
	var count int64
	err := config.DB.Table(tableName).Count(&count).Error
	return count, err
}

// GetTableCountWithCondition returns the count of records in a table with a condition
func GetTableCountWithCondition(tableName, condition string, args ...interface{}) (int64, error) {
	var count int64
	err := config.DB.Table(tableName).Where(condition, args...).Count(&count).Error
	return count, err
}
