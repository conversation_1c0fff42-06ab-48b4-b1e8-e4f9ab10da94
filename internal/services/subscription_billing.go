package services

import (
	"bytes"
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/templates"
	"yotracker/internal/utils"

	"github.com/SebastiaanKlippert/go-wkhtmltopdf"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// SubscriptionBillingService handles subscription billing based on client NextBillingDate
type SubscriptionBillingService struct{}

// NewSubscriptionBillingService creates a new subscription billing service
func NewSubscriptionBillingService() *SubscriptionBillingService {
	return &SubscriptionBillingService{}
}

// ProcessSubscriptionBilling processes subscription billing for clients based on NextBillingDate
func (s *SubscriptionBillingService) ProcessSubscriptionBilling() error {
	// Get the generate_invoice_before_days setting
	generateBeforeDays := models.GetSetting("generate_invoice_before_days")
	if generateBeforeDays == "" {
		generateBeforeDays = "7" // default value
	}

	beforeDaysInt, err := strconv.Atoi(generateBeforeDays)
	if err != nil {
		return fmt.Errorf("invalid generate_invoice_before_days setting: %v", err)
	}

	// Calculate the target date (today + generate_invoice_before_days)
	targetDate := time.Now().AddDate(0, 0, beforeDaysInt)

	log.Printf("Processing subscription billing for clients with NextBillingDate <= %s (generate_invoice_before_days: %s)",
		targetDate.Format("2006-01-02"), generateBeforeDays)

	// Find all active clients where NextBillingDate <= targetDate
	var clients []models.Client
	err = config.DB.Where("status = ? AND next_billing_date <= ? AND next_billing_date IS NOT NULL",
		"active", targetDate).
		Preload("Currency").
		Find(&clients).Error

	if err != nil {
		return fmt.Errorf("failed to fetch clients for subscription billing: %v", err)
	}

	log.Printf("Found %d clients for subscription billing", len(clients))

	for _, client := range clients {
		err := s.processClientSubscription(client)
		if err != nil {
			log.Printf("Failed to process subscription for client ID %d (%s): %v", client.Id, client.Name, err)
			continue
		}
		log.Printf("Successfully processed subscription for client ID %d (%s)", client.Id, client.Name)
	}

	return nil
}

// processClientSubscription processes subscription billing for a single client
func (s *SubscriptionBillingService) processClientSubscription(client models.Client) error {
	// Check if client has any unpaid invoices
	hasUnpaidInvoices, err := s.hasUnpaidInvoices(client.Id)
	if err != nil {
		return fmt.Errorf("failed to check unpaid invoices: %v", err)
	}

	if hasUnpaidInvoices {
		log.Printf("Client %s (ID: %d) has unpaid invoices, skipping subscription billing", client.Name, client.Id)
		return nil
	}

	// Get all active client devices
	var clientDevices []models.ClientDevice
	err = config.DB.Where("client_id = ? AND status = ?", client.Id, "active").
		Preload("DeviceType").
		Find(&clientDevices).Error

	if err != nil {
		return fmt.Errorf("failed to fetch client devices: %v", err)
	}

	if len(clientDevices) == 0 {
		log.Printf("Client %s (ID: %d) has no active devices, skipping subscription billing", client.Name, client.Id)
		return nil
	}

	// Create subscription invoice
	return s.createSubscriptionInvoice(client, clientDevices)
}

// hasUnpaidInvoices checks if client has any unpaid subscription invoices (balance > 0)
// Only subscription invoices should block new subscription billing
func (s *SubscriptionBillingService) hasUnpaidInvoices(clientId uint) (bool, error) {
	var count int64
	err := config.DB.Model(&models.Invoice{}).
		Where("client_id = ? AND balance > 0 AND status != ? AND is_subscription = ?", clientId, "paid", true).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

// createSubscriptionInvoice creates a new subscription invoice for the client
func (s *SubscriptionBillingService) createSubscriptionInvoice(client models.Client, devices []models.ClientDevice) error {
	// Get invoice_due_after_days setting
	dueDays := models.GetSetting("invoice_due_after_days")
	if dueDays == "" {
		dueDays = "15" // default value
	}

	dueDaysInt, err := strconv.Atoi(dueDays)
	if err != nil {
		return fmt.Errorf("invalid invoice_due_after_days setting: %v", err)
	}

	// Calculate invoice date and due date
	invoiceDate := client.NextBillingDate
	dueDate := invoiceDate.AddDate(0, 0, dueDaysInt)

	// Get default payment type (prefer online, but fallback to any active payment type)
	var paymentType models.PaymentType
	err = config.DB.Where("active = ? AND is_online = ?", true, true).First(&paymentType).Error
	if err != nil {
		// Fallback to any active payment type if no online payment type is found
		err = config.DB.Where("active = ?", true).First(&paymentType).Error
		if err != nil {
			return fmt.Errorf("failed to get default payment type: %v", err)
		}
	}
	//get system currency
	systemCurrencyIdString := models.GetSetting("currency")
	//convert to int
	systemCurrencyIdInt, err := strconv.Atoi(systemCurrencyIdString)
	if err != nil {
		return fmt.Errorf("failed to convert system currency id to int: %v", err)
	}
	systemCurrencyId := uint(systemCurrencyIdInt)
	var currencyId uint

	if client.CurrencyId != nil {
		currencyId = *client.CurrencyId
	} else {
		currencyId = systemCurrencyId
	}
	//get client currency
	var currency models.Currency
	err = config.DB.Where("id = ?", currencyId).First(&currency).Error

	// Create invoice first to get ID for reference generation
	invoice := models.Invoice{
		ClientId:       client.Id,
		CurrencyId:     &currencyId,
		PaymentTypeId:  &paymentType.Id,
		Date:           invoiceDate,
		DueDate:        &dueDate,
		Status:         "draft",
		IsSubscription: func() *bool { b := true; return &b }(),
		Xrate:          &currency.Xrate,
	}

	// Start transaction
	tx := config.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Create invoice
	if err = tx.Create(&invoice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create invoice: %v", err)
	}

	// Generate reference after creating invoice to get ID
	reference := utils.GenerateReference(strconv.Itoa(int(invoice.Id)))
	invoice.Reference = &reference
	if err := tx.Save(&invoice).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update invoice reference: %v", err)
	}

	// Create invoice items from devices
	err = s.createInvoiceItemsFromDevices(tx, &invoice, devices)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to create invoice items: %v", err)
	}

	// Update client's next billing date
	err = s.updateClientNextBillingDate(tx, &client)
	if err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update client next billing date: %v", err)
	}

	// Commit transaction
	if err = tx.Commit().Error; err != nil {
		return fmt.Errorf("failed to commit transaction: %v", err)
	}

	log.Printf("Created subscription invoice %s for client %s with %d devices (Total: %.2f)",
		reference, client.Name, len(devices), *invoice.Amount)

	// Send invoice email with PDF attachment
	err = s.sendInvoiceEmail(invoice)
	if err != nil {
		log.Printf("Failed to send invoice email for %s: %v", reference, err)
		// Don't return error here as invoice creation was successful
	} else {
		log.Printf("Successfully sent invoice email for %s to %s", reference, client.Email)
	}

	return nil
}

// createInvoiceItemsFromDevices creates invoice items from client devices
func (s *SubscriptionBillingService) createInvoiceItemsFromDevices(tx *gorm.DB, invoice *models.Invoice, devices []models.ClientDevice) error {
	var err error
	var taxRate models.TaxRate

	// Get default tax rate by ID from settings
	vatSetting := models.GetSetting("vat")
	if vatSetting == "" {
		vatSetting = "1" // default to first tax rate
	}

	vatId, err := strconv.Atoi(vatSetting)
	if err != nil {
		return fmt.Errorf("invalid vat setting: %v", err)
	}

	err = config.DB.Session(&gorm.Session{Logger: config.DB.Logger.LogMode(logger.Silent)}).
		Where("id = ? AND active = ?", vatId, true).First(&taxRate).Error
	if err != nil {
		// Fallback to any active tax rate if the specified one is not found
		err = config.DB.Where("active = ?", true).First(&taxRate).Error
		if err != nil {
			return fmt.Errorf("failed to get default tax rate: %v", err)
		}
	}
	//get system currency
	systemCurrencyIdString := models.GetSetting("currency")
	//convert to int
	systemCurrencyIdInt, err := strconv.Atoi(systemCurrencyIdString)
	if err != nil {
		return fmt.Errorf("failed to convert system currency id to int: %v", err)
	}
	systemCurrencyId := uint(systemCurrencyIdInt)

	// Calculate total amount
	var totalAmount float64
	var totalBaseCurrencyAmount float64
	var totalBaseCurrencySubtotal float64
	var totalBaseCurrencyTaxAmount float64
	var totalTaxAmount float64
	var totalSubtotal float64
	for i, device := range devices {
		// Get device amount from the device type
		baseCurrencyUnitCost := 0.0
		baseCurrencyTaxAmount := 0.0
		baseCurrencyTotal := 0.0
		unitCost := 0.0
		taxAmount := 0.0
		if device.DeviceType.Amount != nil {
			baseCurrencyUnitCost = *device.DeviceType.Amount
		}
		if *taxRate.AmountType == "percentage" {
			baseCurrencyTaxAmount = baseCurrencyUnitCost * taxRate.Amount / 100
		} else {
			baseCurrencyTaxAmount = taxRate.Amount
		}
		if systemCurrencyId == *invoice.CurrencyId {
			unitCost = baseCurrencyUnitCost
			taxAmount = baseCurrencyTaxAmount
		} else {
			unitCost = baseCurrencyUnitCost * *invoice.Xrate
			taxAmount = baseCurrencyTaxAmount * *invoice.Xrate
		}
		total := unitCost + taxAmount
		baseCurrencyTotal = baseCurrencyUnitCost + baseCurrencyTaxAmount

		// Create item name: device name + plate number if available
		itemName := device.DeviceType.Name
		if device.Name != nil && *device.Name != "" {
			itemName = *device.Name
		}
		if device.PlateNumber != nil && *device.PlateNumber != "" {
			itemName += " (" + *device.PlateNumber + ")"
		}

		// Create invoice item
		invoiceItem := models.InvoiceItem{
			InvoiceId:             invoice.Id,
			TaxRateId:             &taxRate.Id,
			ClientDeviceId:        &device.Id,
			Name:                  &itemName,
			Description:           &itemName,
			Quantity:              func() *uint { q := uint(1); return &q }(),
			ItemPosition:          func() *uint { pos := uint(i + 1); return &pos }(),
			UnitCost:              &unitCost,
			Total:                 &total,
			TaxAmount:             &taxAmount,
			BaseCurrencyUnitCost:  &baseCurrencyUnitCost,
			BaseCurrencyTaxAmount: &baseCurrencyTaxAmount,
			BaseCurrencyTotal:     &baseCurrencyTotal,
		}
		totalAmount += total
		totalBaseCurrencyAmount += baseCurrencyTotal
		totalBaseCurrencySubtotal += baseCurrencyUnitCost
		totalBaseCurrencyTaxAmount += baseCurrencyTaxAmount
		totalTaxAmount += taxAmount
		totalSubtotal += unitCost
		if err := tx.Create(&invoiceItem).Error; err != nil {
			return fmt.Errorf("failed to create invoice item for device %d: %v", device.Id, err)
		}
	}
	//update invoice
	invoice.Amount = &totalAmount
	invoice.BaseCurrencyAmount = &totalBaseCurrencyAmount
	invoice.BaseCurrencySubtotal = &totalBaseCurrencySubtotal
	invoice.BaseCurrencyTaxAmount = &totalBaseCurrencyTaxAmount
	invoice.TaxAmount = &totalTaxAmount
	invoice.Subtotal = &totalSubtotal
	if err = tx.Save(&invoice).Error; err != nil {
		return fmt.Errorf("failed to update invoice: %v", err)
	}

	return nil
}

// updateClientNextBillingDate updates the client's next billing date based on billing cycle
func (s *SubscriptionBillingService) updateClientNextBillingDate(tx *gorm.DB, client *models.Client) error {
	if client.BillingCycle == nil {
		return fmt.Errorf("client billing cycle not set")
	}

	billingCycle := *client.BillingCycle
	billingDay := uint(5) // default billing day
	if client.BillingDay != nil {
		billingDay = *client.BillingDay
	}

	// Calculate next billing date
	nextBillingDate := s.calculateNextBillingDate(*client.NextBillingDate, billingCycle, billingDay)

	// Update client
	client.NextBillingDate = &nextBillingDate
	client.LastBilledAt = client.NextBillingDate

	if err := tx.Save(client).Error; err != nil {
		return fmt.Errorf("failed to update client billing dates: %v", err)
	}

	return nil
}

// calculateNextBillingDate calculates the next billing date based on current date, billing cycle and billing day
func (s *SubscriptionBillingService) calculateNextBillingDate(currentDate time.Time, billingCycle string, billingDay uint) time.Time {
	switch billingCycle {
	case "monthly":
		return s.calculateNextMonthlyDate(currentDate, int(billingDay))
	case "quarterly":
		return s.calculateNextQuarterlyDate(currentDate, int(billingDay))
	case "half_yearly":
		return s.calculateNextHalfYearlyDate(currentDate, int(billingDay))
	case "yearly":
		return s.calculateNextYearlyDate(currentDate, int(billingDay))
	default:
		// Default to monthly if unknown cycle
		return s.calculateNextMonthlyDate(currentDate, int(billingDay))
	}
}

// Helper functions for calculating next billing dates
func (s *SubscriptionBillingService) calculateNextMonthlyDate(currentDate time.Time, billingDay int) time.Time {
	year, month, _ := currentDate.Date()
	nextMonth := month + 1
	nextYear := year

	if nextMonth > 12 {
		nextMonth = 1
		nextYear++
	}

	// Handle cases where billing day doesn't exist in the target month
	daysInMonth := time.Date(nextYear, nextMonth+1, 0, 0, 0, 0, 0, time.UTC).Day()
	if billingDay > daysInMonth {
		billingDay = daysInMonth
	}

	return time.Date(nextYear, nextMonth, billingDay, 0, 0, 0, 0, time.UTC)
}

func (s *SubscriptionBillingService) calculateNextQuarterlyDate(currentDate time.Time, billingDay int) time.Time {
	year, month, _ := currentDate.Date()
	nextMonth := month + 3

	nextYear := year
	if nextMonth > 12 {
		nextMonth -= 12
		nextYear++
	}

	daysInMonth := time.Date(nextYear, nextMonth+1, 0, 0, 0, 0, 0, time.UTC).Day()
	if billingDay > daysInMonth {
		billingDay = daysInMonth
	}

	return time.Date(nextYear, nextMonth, billingDay, 0, 0, 0, 0, time.UTC)
}

func (s *SubscriptionBillingService) calculateNextHalfYearlyDate(currentDate time.Time, billingDay int) time.Time {
	year, month, _ := currentDate.Date()
	nextMonth := month + 6

	nextYear := year
	if nextMonth > 12 {
		nextMonth -= 12
		nextYear++
	}

	daysInMonth := time.Date(nextYear, nextMonth+1, 0, 0, 0, 0, 0, time.UTC).Day()
	if billingDay > daysInMonth {
		billingDay = daysInMonth
	}

	return time.Date(nextYear, nextMonth, billingDay, 0, 0, 0, 0, time.UTC)
}

func (s *SubscriptionBillingService) calculateNextYearlyDate(currentDate time.Time, billingDay int) time.Time {
	year, month, _ := currentDate.Date()
	nextYear := year + 1

	daysInMonth := time.Date(nextYear, month+1, 0, 0, 0, 0, 0, time.UTC).Day()
	if billingDay > daysInMonth {
		billingDay = daysInMonth
	}

	return time.Date(nextYear, month, billingDay, 0, 0, 0, 0, time.UTC)
}

// sendInvoiceEmail sends the invoice via email with PDF attachment
func (s *SubscriptionBillingService) sendInvoiceEmail(invoice models.Invoice) error {
	// Skip email sending in test environment
	if os.Getenv("TESTING_DB_NAME") != "" || os.Getenv("GO_ENV") == "test" {
		log.Printf("TEST MODE: Skipping subscription invoice email for invoice ID %d", invoice.Id)
		return nil
	}

	// Fetch complete invoice with all relations for email
	var completeInvoice models.Invoice
	err := config.DB.Preload("Client").
		Preload("InvoiceItems").
		Preload("Currency").
		Preload("PaymentType").
		First(&completeInvoice, invoice.Id).Error
	if err != nil {
		return fmt.Errorf("failed to fetch complete invoice: %v", err)
	}

	// Generate PDF content
	pdfContent, err := s.generateInvoicePDF(completeInvoice)
	if err != nil {
		return fmt.Errorf("failed to generate PDF: %v", err)
	}

	// Send email using the existing mail helper
	return SendInvoiceEmail(&completeInvoice, pdfContent)
}

// generateInvoicePDF generates a PDF for the given invoice
func (s *SubscriptionBillingService) generateInvoicePDF(invoice models.Invoice) ([]byte, error) {
	// Generate PDF using the same logic as in the mail helper
	tmpl, err := templates.GetInvoiceTemplate()
	if err != nil {
		return nil, fmt.Errorf("template error: %v", err)
	}

	var htmlBuffer bytes.Buffer
	invoiceView := models.ToInvoiceView(invoice)
	err = tmpl.Execute(&htmlBuffer, invoiceView)
	if err != nil {
		return nil, fmt.Errorf("template render error: %v", err)
	}

	// Generate PDF from HTML using wkhtmltopdf
	pdfg, err := wkhtmltopdf.NewPDFGenerator()
	if err != nil {
		return nil, fmt.Errorf("PDF generator error: %v", err)
	}

	page := wkhtmltopdf.NewPageReader(&htmlBuffer)
	pdfg.AddPage(page)

	err = pdfg.Create()
	if err != nil {
		return nil, fmt.Errorf("PDF creation failed: %v", err)
	}

	return pdfg.Bytes(), nil
}
