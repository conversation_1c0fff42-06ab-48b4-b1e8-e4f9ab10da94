package services

import (
	"testing"
	"time"

	"yotracker/config"
	"yotracker/internal/models"

	"github.com/stretchr/testify/suite"
)

type TripDetectionServiceTestSuite struct {
	suite.Suite
	service *TripDetectionService
}

func (suite *TripDetectionServiceTestSuite) SetupSuite() {
	// Initialize test database
	config.InitTestDB()
}

func (suite *TripDetectionServiceTestSuite) SetupTest() {
	// Clear test data before each test
	config.DB.Exec("DELETE FROM trips")
	config.DB.Exec("DELETE FROM gps_data")
	config.DB.Exec("DELETE FROM client_devices")
	config.DB.Exec("DELETE FROM device_types")
	config.DB.Exec("DELETE FROM protocols")
	config.DB.Exec("DELETE FROM clients")

	// Initialize the service
	suite.service = NewTripDetectionService()
}

func (suite *TripDetectionServiceTestSuite) TearDownTest() {
	// Clean up test data
	config.DB.Exec("DELETE FROM trips")
	config.DB.Exec("DELETE FROM gps_data")
	config.DB.Exec("DELETE FROM client_devices")
	config.DB.Exec("DELETE FROM device_types")
	config.DB.Exec("DELETE FROM protocols")
	config.DB.Exec("DELETE FROM clients")
}

func (suite *TripDetectionServiceTestSuite) createTestProtocol() models.Protocol {
	protocol := models.Protocol{
		Name:   "Test Protocol",
		Active: true,
	}
	config.DB.Create(&protocol)
	return protocol
}

func (suite *TripDetectionServiceTestSuite) createTestDeviceType(ignitionStatus bool) models.DeviceType {
	protocol := suite.createTestProtocol()
	deviceType := models.DeviceType{
		ProtocolId:     protocol.Id,
		Name:           "Test Device Type",
		Active:         true,
		IgnitionStatus: &ignitionStatus,
	}
	config.DB.Create(&deviceType)
	return deviceType
}

func (suite *TripDetectionServiceTestSuite) createTestClient() models.Client {
	client := models.Client{
		Name:        "Test Client",
		Email:       "<EMAIL>",
		Status:      "active",
		PhoneNumber: "1234567890",
	}
	config.DB.Create(&client)
	return client
}

func (suite *TripDetectionServiceTestSuite) createTestDevice(clientId, deviceTypeId uint) models.ClientDevice {
	device := models.ClientDevice{
		ClientId:     clientId,
		DeviceTypeId: deviceTypeId,
		AssetType:    "vehicle",
		Name:         stringPtrTest("Test Device"),
		DeviceId:     "TEST001",
		Status:       "active",
	}
	config.DB.Create(&device)
	return device
}

func (suite *TripDetectionServiceTestSuite) createTestGPSData(deviceId uint, timestamp time.Time, lat, lng float64, speed *float64, ignitionStatus *bool) models.GPSData {
	gpsData := models.GPSData{
		ClientDeviceId: &deviceId,
		GPSTimestamp:   &timestamp,
		Latitude:       lat,
		Longitude:      lng,
		Speed:          speed,
		IgnitionStatus: ignitionStatus,
	}
	config.DB.Create(&gpsData)
	return gpsData
}

func stringPtrTest(s string) *string {
	return &s
}

func boolPtrTest(b bool) *bool {
	return &b
}

func float64PtrTest(f float64) *float64 {
	return &f
}

// TestTripDetectionWithIgnitionStatus tests trip detection when DeviceType.IgnitionStatus = true
func (suite *TripDetectionServiceTestSuite) TestTripDetectionWithIgnitionStatus() {
	// Create device type that supports ignition status
	deviceType := suite.createTestDeviceType(true)
	client := suite.createTestClient()
	device := suite.createTestDevice(client.Id, deviceType.Id)

	// Create GPS data with ignition status changes
	baseTime := time.Now().UTC()

	// Point 1: Ignition OFF, stopped
	gps1 := suite.createTestGPSData(device.Id, baseTime, 40.7128, -74.0060, float64PtrTest(0.0), boolPtrTest(false))

	// Point 2: Ignition ON, moving (significant distance from point 1)
	gps2 := suite.createTestGPSData(device.Id, baseTime.Add(1*time.Minute), 40.7200, -74.0100, float64PtrTest(30.0), boolPtrTest(true))

	// Point 3: Ignition ON, moving (significant distance from point 2)
	gps3 := suite.createTestGPSData(device.Id, baseTime.Add(2*time.Minute), 40.7300, -74.0200, float64PtrTest(35.0), boolPtrTest(true))

	// Point 4: Ignition OFF, stopped (significant distance from point 3)
	gps4 := suite.createTestGPSData(device.Id, baseTime.Add(3*time.Minute), 40.7400, -74.0300, float64PtrTest(0.0), boolPtrTest(false))

	gpsData := []models.GPSData{gps1, gps2, gps3, gps4}

	// Test trip detection
	trips, err := suite.service.detectTrips(gpsData, &device)

	suite.NoError(err)
	suite.Len(trips, 1, "Should detect one trip")

	if len(trips) > 0 {
		trip := trips[0]
		suite.Equal(device.Id, trip.DeviceId)
		suite.Equal(1, trip.StartIndex, "Trip should start at index 1 (ignition ON)")
		suite.Equal(3, trip.EndIndex, "Trip should end at index 3 (ignition OFF)")
		suite.Equal(3, len(trip.GPSPoints), "Trip should have 3 GPS points")
	}
}

// TestTripDetectionWithoutIgnitionStatus tests trip detection when DeviceType.IgnitionStatus = false
func (suite *TripDetectionServiceTestSuite) TestTripDetectionWithoutIgnitionStatus() {
	// Create device type that doesn't support ignition status
	deviceType := suite.createTestDeviceType(false)
	client := suite.createTestClient()
	device := suite.createTestDevice(client.Id, deviceType.Id)

	// Create GPS data without ignition status, using speed-based detection
	baseTime := time.Now().UTC()

	// Point 1: Stopped, no ignition status
	gps1 := suite.createTestGPSData(device.Id, baseTime, 40.7128, -74.0060, float64PtrTest(0.0), nil)

	// Point 2: Moving, no ignition status (significant distance)
	gps2 := suite.createTestGPSData(device.Id, baseTime.Add(1*time.Minute), 40.7200, -74.0100, float64PtrTest(30.0), nil)

	// Point 3: Moving, no ignition status (significant distance)
	gps3 := suite.createTestGPSData(device.Id, baseTime.Add(2*time.Minute), 40.7300, -74.0200, float64PtrTest(35.0), nil)

	// Point 4: Stopped, no ignition status (significant distance)
	gps4 := suite.createTestGPSData(device.Id, baseTime.Add(3*time.Minute), 40.7400, -74.0300, float64PtrTest(0.0), nil)

	gpsData := []models.GPSData{gps1, gps2, gps3, gps4}

	// Test trip detection
	trips, err := suite.service.detectTrips(gpsData, &device)

	suite.NoError(err)
	suite.Len(trips, 1, "Should detect one trip using speed-based detection")

	if len(trips) > 0 {
		trip := trips[0]
		suite.Equal(device.Id, trip.DeviceId)
		suite.Equal(1, trip.StartIndex, "Trip should start at index 1 (speed > threshold)")
		suite.Equal(3, trip.EndIndex, "Trip should end at index 3 (speed = 0)")
	}
}

// TestTripDetectionMixedIgnitionData tests trip detection with mixed ignition status data
func (suite *TripDetectionServiceTestSuite) TestTripDetectionMixedIgnitionData() {
	// Create device type that supports ignition status
	deviceType := suite.createTestDeviceType(true)
	client := suite.createTestClient()
	device := suite.createTestDevice(client.Id, deviceType.Id)

	// Create GPS data with mixed ignition status (some points have it, some don't)
	baseTime := time.Now().UTC()

	// Point 1: No ignition status, stopped
	gps1 := suite.createTestGPSData(device.Id, baseTime, 40.7128, -74.0060, float64PtrTest(0.0), nil)

	// Point 2: Ignition ON, moving (significant distance)
	gps2 := suite.createTestGPSData(device.Id, baseTime.Add(1*time.Minute), 40.7200, -74.0100, float64PtrTest(30.0), boolPtrTest(true))

	// Point 3: Ignition ON, moving (significant distance)
	gps3 := suite.createTestGPSData(device.Id, baseTime.Add(2*time.Minute), 40.7300, -74.0200, float64PtrTest(35.0), boolPtrTest(true))

	// Point 4: No ignition status, stopped (significant distance)
	gps4 := suite.createTestGPSData(device.Id, baseTime.Add(3*time.Minute), 40.7400, -74.0300, float64PtrTest(0.0), nil)

	gpsData := []models.GPSData{gps1, gps2, gps3, gps4}

	// Test trip detection
	trips, err := suite.service.detectTrips(gpsData, &device)

	suite.NoError(err)
	suite.Len(trips, 1, "Should detect one trip")

	if len(trips) > 0 {
		trip := trips[0]
		suite.Equal(device.Id, trip.DeviceId)
		suite.Equal(1, trip.StartIndex, "Trip should start at index 1 (ignition ON)")
		suite.Equal(3, trip.EndIndex, "Trip should end at index 3 (last ignition ON point)")
	}
}

// TestTripDetectionNoTrips tests when no trips should be detected
func (suite *TripDetectionServiceTestSuite) TestTripDetectionNoTrips() {
	// Create device type that supports ignition status
	deviceType := suite.createTestDeviceType(true)
	client := suite.createTestClient()
	device := suite.createTestDevice(client.Id, deviceType.Id)

	// Create GPS data with no trip-indicating conditions
	baseTime := time.Now().UTC()

	// Point 1: Ignition OFF, stopped
	gps1 := suite.createTestGPSData(device.Id, baseTime, 40.7128, -74.0060, float64PtrTest(0.0), boolPtrTest(false))

	// Point 2: Ignition OFF, stopped
	gps2 := suite.createTestGPSData(device.Id, baseTime.Add(1*time.Minute), 40.7128, -74.0060, float64PtrTest(0.0), boolPtrTest(false))

	// Point 3: Ignition OFF, stopped
	gps3 := suite.createTestGPSData(device.Id, baseTime.Add(2*time.Minute), 40.7128, -74.0060, float64PtrTest(0.0), boolPtrTest(false))

	gpsData := []models.GPSData{gps1, gps2, gps3}

	// Test trip detection
	trips, err := suite.service.detectTrips(gpsData, &device)

	suite.NoError(err)
	suite.Len(trips, 0, "Should detect no trips when ignition is always OFF and no movement")
}

// TestTripDetectionMultipleTrips tests detection of multiple trips
func (suite *TripDetectionServiceTestSuite) TestTripDetectionMultipleTrips() {
	// Create device type that supports ignition status
	deviceType := suite.createTestDeviceType(true)
	client := suite.createTestClient()
	device := suite.createTestDevice(client.Id, deviceType.Id)

	// Create GPS data for multiple trips
	baseTime := time.Now().UTC()

	// Trip 1: Ignition ON -> OFF (significant distances)
	gps1 := suite.createTestGPSData(device.Id, baseTime, 40.7128, -74.0060, float64PtrTest(0.0), boolPtrTest(false))
	gps2 := suite.createTestGPSData(device.Id, baseTime.Add(1*time.Minute), 40.7200, -74.0100, float64PtrTest(30.0), boolPtrTest(true))
	gps3 := suite.createTestGPSData(device.Id, baseTime.Add(2*time.Minute), 40.7300, -74.0200, float64PtrTest(35.0), boolPtrTest(true))
	gps4 := suite.createTestGPSData(device.Id, baseTime.Add(3*time.Minute), 40.7400, -74.0300, float64PtrTest(0.0), boolPtrTest(false))

	// Trip 2: Ignition ON -> OFF (significant distances, longer gap for separation)
	gps5 := suite.createTestGPSData(device.Id, baseTime.Add(10*time.Minute), 40.7500, -74.0400, float64PtrTest(0.0), boolPtrTest(false))
	gps6 := suite.createTestGPSData(device.Id, baseTime.Add(11*time.Minute), 40.7600, -74.0500, float64PtrTest(25.0), boolPtrTest(true))
	gps7 := suite.createTestGPSData(device.Id, baseTime.Add(12*time.Minute), 40.7700, -74.0600, float64PtrTest(30.0), boolPtrTest(true))
	gps8 := suite.createTestGPSData(device.Id, baseTime.Add(13*time.Minute), 40.7800, -74.0700, float64PtrTest(0.0), boolPtrTest(false))

	gpsData := []models.GPSData{gps1, gps2, gps3, gps4, gps5, gps6, gps7, gps8}

	// Test trip detection
	trips, err := suite.service.detectTrips(gpsData, &device)

	suite.NoError(err)
	suite.Len(trips, 2, "Should detect two trips")

	if len(trips) >= 2 {
		// First trip
		trip1 := trips[0]
		suite.Equal(1, trip1.StartIndex, "First trip should start at index 1")
		suite.Equal(3, trip1.EndIndex, "First trip should end at index 3")

		// Second trip
		trip2 := trips[1]
		suite.Equal(5, trip2.StartIndex, "Second trip should start at index 5")
		suite.Equal(7, trip2.EndIndex, "Second trip should end at index 7")
	}
}

// TestTripDetectionEdgeCases tests various edge cases
func (suite *TripDetectionServiceTestSuite) TestTripDetectionEdgeCases() {
	// Create device type that supports ignition status
	deviceType := suite.createTestDeviceType(true)
	client := suite.createTestClient()
	device := suite.createTestDevice(client.Id, deviceType.Id)

	// Test with single GPS point (should not create a trip)
	baseTime := time.Now().UTC()
	gps1 := suite.createTestGPSData(device.Id, baseTime, 40.7128, -74.0060, float64PtrTest(30.0), boolPtrTest(true))

	gpsData := []models.GPSData{gps1}
	trips, err := suite.service.detectTrips(gpsData, &device)

	suite.NoError(err)
	suite.Len(trips, 0, "Single GPS point should not create a trip")

	// Test with empty GPS data
	emptyGPSData := []models.GPSData{}
	trips, err = suite.service.detectTrips(emptyGPSData, &device)

	suite.NoError(err)
	suite.Len(trips, 0, "Empty GPS data should not create trips")
}

// TestTripDetectionServiceIntegration tests the full service integration
func (suite *TripDetectionServiceTestSuite) TestTripDetectionServiceIntegration() {
	// Create device type that supports ignition status
	deviceType := suite.createTestDeviceType(true)
	client := suite.createTestClient()
	device := suite.createTestDevice(client.Id, deviceType.Id)

	// Create GPS data
	baseTime := time.Now().UTC()

	// Create a trip scenario (significant distances)
	suite.createTestGPSData(device.Id, baseTime, 40.7128, -74.0060, float64PtrTest(0.0), boolPtrTest(false))
	suite.createTestGPSData(device.Id, baseTime.Add(1*time.Minute), 40.7200, -74.0100, float64PtrTest(30.0), boolPtrTest(true))
	suite.createTestGPSData(device.Id, baseTime.Add(2*time.Minute), 40.7300, -74.0200, float64PtrTest(35.0), boolPtrTest(true))
	suite.createTestGPSData(device.Id, baseTime.Add(3*time.Minute), 40.7400, -74.0300, float64PtrTest(0.0), boolPtrTest(false))

	// Test the full service method
	err := suite.service.processDeviceGPSDataWithDateFilter(device.Id, "", "")

	suite.NoError(err, "Service should process GPS data without error")

	// Verify that trips were created
	var trips []models.Trip
	config.DB.Where("client_device_id = ?", device.Id).Find(&trips)

	suite.Len(trips, 1, "Service should create one trip")
}

func TestTripDetectionServiceTestSuite(t *testing.T) {
	suite.Run(t, new(TripDetectionServiceTestSuite))
}
