package services

import (
	"errors"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

type OperatingCostService struct{}

func NewOperatingCostService() *OperatingCostService {
	return &OperatingCostService{}
}

// GetOperatingCosts returns all operating costs with filtering and pagination
func (s *OperatingCostService) GetOperatingCosts(filters models.OperatingCostFilters) ([]models.OperatingCost, int64, error) {
	var costs []models.OperatingCost
	var total int64

	query := config.DB.Where("client_id = ?", filters.ClientId)

	// Apply filters
	if filters.CostType != "" {
		query = query.Where("cost_type = ?", filters.CostType)
	}
	if filters.ClientDeviceId != "" {
		query = query.Where("client_device_id = ?", filters.ClientDeviceId)
	}
	if filters.StartDate != nil {
		query = query.Where("cost_date >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("cost_date <= ?", filters.EndDate)
	}

	// Get total count
	query.Model(&models.OperatingCost{}).Count(&total)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := (filters.Page - 1) * filters.PerPage
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	// Order by cost date
	query = query.Order("cost_date DESC")

	err := query.Find(&costs).Error
	return costs, total, err
}

// GetOperatingCost returns a specific operating cost record
func (s *OperatingCostService) GetOperatingCost(id uint, clientId uint) (*models.OperatingCost, error) {
	var cost models.OperatingCost
	err := config.DB.Where("id = ? AND client_id = ?", id, clientId).First(&cost).Error
	if err != nil {
		return nil, err
	}
	return &cost, nil
}

// CreateOperatingCost creates a new operating cost record
func (s *OperatingCostService) CreateOperatingCost(cost models.OperatingCost) (*models.OperatingCost, error) {
	err := config.DB.Create(&cost).Error
	if err != nil {
		return nil, err
	}

	return &cost, nil
}

// UpdateOperatingCost updates an existing operating cost record
func (s *OperatingCostService) UpdateOperatingCost(cost models.OperatingCost) (*models.OperatingCost, error) {
	// Check if cost exists and belongs to client
	existingCost, err := s.GetOperatingCost(cost.Id, cost.ClientId)
	if err != nil {
		return nil, errors.New("operating cost record not found")
	}

	// Update fields
	existingCost.CostDate = cost.CostDate
	existingCost.CostType = cost.CostType
	existingCost.CostCategory = cost.CostCategory
	existingCost.Amount = cost.Amount
	existingCost.Description = cost.Description
	existingCost.InvoiceNumber = cost.InvoiceNumber
	existingCost.Vendor = cost.Vendor
	existingCost.TripId = cost.TripId
	existingCost.ClientDeviceId = cost.ClientDeviceId

	err = config.DB.Save(existingCost).Error
	if err != nil {
		return nil, err
	}

	return existingCost, nil
}

// DeleteOperatingCost deletes an operating cost record
func (s *OperatingCostService) DeleteOperatingCost(id uint, clientId uint) error {
	result := config.DB.Where("id = ? AND client_id = ?", id, clientId).Delete(&models.OperatingCost{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("operating cost record not found")
	}
	return nil
}

// GetOperatingCostStats returns operating cost statistics
func (s *OperatingCostService) GetOperatingCostStats(clientId uint) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total costs
	var total int64
	config.DB.Model(&models.OperatingCost{}).Where("client_id = ?", clientId).Count(&total)
	stats["total"] = total

	// Total amount
	var totalAmount float64
	config.DB.Model(&models.OperatingCost{}).
		Where("client_id = ?", clientId).
		Select("COALESCE(SUM(amount), 0)").Scan(&totalAmount)
	stats["total_amount"] = totalAmount

	// This month costs
	var thisMonth int64
	monthStart := time.Now().Truncate(24*time.Hour).AddDate(0, 0, -time.Now().Day()+1)
	config.DB.Model(&models.OperatingCost{}).Where("client_id = ? AND cost_date >= ?",
		clientId, monthStart).Count(&thisMonth)
	stats["this_month"] = thisMonth

	// This month amount
	var thisMonthAmount float64
	config.DB.Model(&models.OperatingCost{}).
		Where("client_id = ? AND cost_date >= ?", clientId, monthStart).
		Select("COALESCE(SUM(amount), 0)").Scan(&thisMonthAmount)
	stats["this_month_amount"] = thisMonthAmount

	// Average cost per day
	var avgDailyCost float64
	config.DB.Model(&models.OperatingCost{}).
		Where("client_id = ?", clientId).
		Select("COALESCE(SUM(amount) / COUNT(DISTINCT DATE(cost_date)), 0)").Scan(&avgDailyCost)
	stats["avg_daily_cost"] = avgDailyCost

	// Cost type breakdown
	var costTypeBreakdown []struct {
		CostType string  `json:"cost_type"`
		Count    int64   `json:"count"`
		Amount   float64 `json:"amount"`
	}
	config.DB.Model(&models.OperatingCost{}).
		Select("cost_type, COUNT(*) as count, COALESCE(SUM(amount), 0) as amount").
		Where("client_id = ?", clientId).
		Group("cost_type").
		Scan(&costTypeBreakdown)
	stats["cost_type_breakdown"] = costTypeBreakdown

	// Top vendors by amount
	var topVendors []struct {
		Vendor string  `json:"vendor"`
		Count  int64   `json:"count"`
		Amount float64 `json:"amount"`
	}
	config.DB.Model(&models.OperatingCost{}).
		Select("vendor, COUNT(*) as count, COALESCE(SUM(amount), 0) as amount").
		Where("client_id = ? AND vendor != ''", clientId).
		Group("vendor").
		Order("amount DESC").
		Limit(10).
		Scan(&topVendors)
	stats["top_vendors"] = topVendors

	// Monthly trend (last 12 months)
	var monthlyTrend []struct {
		Month  string  `json:"month"`
		Count  int64   `json:"count"`
		Amount float64 `json:"amount"`
	}
	config.DB.Model(&models.OperatingCost{}).
		Select("DATE_FORMAT(cost_date, '%Y-%m') as month, COUNT(*) as count, COALESCE(SUM(amount), 0) as amount").
		Where("client_id = ? AND cost_date >= DATE_SUB(NOW(), INTERVAL 12 MONTH)", clientId).
		Group("month").
		Order("month DESC").
		Scan(&monthlyTrend)
	stats["monthly_trend"] = monthlyTrend

	// High cost alerts (costs > $500)
	var highCostAlerts int64
	config.DB.Model(&models.OperatingCost{}).
		Where("client_id = ? AND amount > 500", clientId).
		Count(&highCostAlerts)
	stats["high_cost_alerts"] = highCostAlerts

	return stats, nil
}
