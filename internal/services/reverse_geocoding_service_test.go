package services

import (
	"testing"
	"time"
)

func TestReverseGeocodingService_GetLocationName(t *testing.T) {
	service := NewReverseGeocodingService()

	// Test with known coordinates (Harare, Zimbabwe)
	latitude := -17.8216
	longitude := 31.0492

	locationName, err := service.GetLocationName(latitude, longitude)
	if err != nil {
		t.Fatalf("Failed to get location name: %v", err)
	}

	if locationName == "" {
		t.<PERSON><PERSON>r("Expected non-empty location name")
	}

	t.Logf("Location name for coordinates (%.6f, %.6f): %s", latitude, longitude, locationName)
}

func TestReverseGeocodingService_Cache(t *testing.T) {
	service := NewReverseGeocodingService()

	// Test coordinates (Harare area)
	baseLat := -17.8216
	baseLon := 31.0492

	// First call - should hit API
	startTime := time.Now()
	locationName1, err := service.GetLocationName(baseLat, baseLon)
	if err != nil {
		t.Fatalf("Failed to get location name: %v", err)
	}
	firstCallTime := time.Since(startTime)

	// Second call with nearby coordinates - should hit cache
	startTime = time.Now()
	locationName2, err := service.GetLocationName(baseLat+0.0001, baseLon+0.0001) // ~11 meters away
	if err != nil {
		t.Fatalf("Failed to get location name for nearby coordinates: %v", err)
	}
	secondCallTime := time.Since(startTime)

	// Third call with same coordinates - should hit cache
	startTime = time.Now()
	locationName3, err := service.GetLocationName(baseLat, baseLon)
	if err != nil {
		t.Fatalf("Failed to get location name for same coordinates: %v", err)
	}
	thirdCallTime := time.Since(startTime)

	// Verify all location names are the same
	if locationName1 != locationName2 || locationName2 != locationName3 {
		t.Errorf("Location names should be the same: %s, %s, %s", locationName1, locationName2, locationName3)
	}

	// Verify cache calls are much faster than API calls
	if secondCallTime >= firstCallTime {
		t.Logf("Warning: Cache call (%v) should be faster than API call (%v)", secondCallTime, firstCallTime)
	}

	if thirdCallTime >= firstCallTime {
		t.Logf("Warning: Cache call (%v) should be faster than API call (%v)", thirdCallTime, firstCallTime)
	}

	t.Logf("API call time: %v", firstCallTime)
	t.Logf("Nearby cache call time: %v", secondCallTime)
	t.Logf("Exact cache call time: %v", thirdCallTime)
	t.Logf("Location name: %s", locationName1)
}

func TestReverseGeocodingService_CacheStats(t *testing.T) {
	service := NewReverseGeocodingService()

	// Get initial stats
	initialStats := service.GetCacheStats()
	if initialStats["total_entries"].(int) != 0 {
		t.Errorf("Expected 0 cache entries initially, got %d", initialStats["total_entries"])
	}

	// Make some API calls to populate cache
	coordinates := []struct {
		lat, lon float64
	}{
		{-17.8216, 31.0492},
		{-17.8250, 31.0500},
		{-17.8300, 31.0550},
	}

	for _, coord := range coordinates {
		_, err := service.GetLocationName(coord.lat, coord.lon)
		if err != nil {
			t.Fatalf("Failed to get location name: %v", err)
		}
	}

	// Get stats after populating cache
	stats := service.GetCacheStats()
	t.Logf("Cache stats: %+v", stats)

	if stats["total_entries"].(int) == 0 {
		t.Error("Expected cache to have entries after API calls")
	}

	// Test cache clearing
	service.ClearCache()
	clearedStats := service.GetCacheStats()
	if clearedStats["total_entries"].(int) != 0 {
		t.Errorf("Expected 0 cache entries after clearing, got %d", clearedStats["total_entries"])
	}
}

func TestReverseGeocodingService_RateLimit(t *testing.T) {
	service := NewReverseGeocodingService()

	// Test coordinates (different locations)
	testCoords := []struct {
		lat, lon float64
		name     string
	}{
		{-17.8216, 31.0492, "Harare"},
		{-17.8250, 31.0500, "Harare nearby"},
		{-17.8300, 31.0550, "Harare area"},
	}

	startTime := time.Now()

	for i, coord := range testCoords {
		locationName, err := service.GetLocationName(coord.lat, coord.lon)
		if err != nil {
			t.Fatalf("Failed to get location name for test %d: %v", i, err)
		}

		if locationName == "" {
			t.Errorf("Expected non-empty location name for test %d", i)
		}

		t.Logf("Test %d - Location name for coordinates (%.6f, %.6f): %s",
			i+1, coord.lat, coord.lon, locationName)

		// The service should automatically sleep 1 second between requests
		// We'll verify the total time is at least 2 seconds for 3 requests
		if i < len(testCoords)-1 {
			time.Sleep(1 * time.Second)
		}
	}

	elapsed := time.Since(startTime)
	t.Logf("Total time for %d requests: %v", len(testCoords), elapsed)

	// With rate limiting, 3 requests should take at least 2 seconds
	if elapsed < 2*time.Second {
		t.Logf("Warning: Rate limiting may not be working properly. Elapsed time: %v", elapsed)
	}
}

func TestReverseGeocodingService_CacheKeyGeneration(t *testing.T) {
	service := NewReverseGeocodingService()

	// Test that nearby coordinates generate the same cache key
	key1 := service.generateCacheKey(-17.8216, 31.0492)
	key2 := service.generateCacheKey(-17.8217, 31.0493) // Very close coordinates
	key3 := service.generateCacheKey(-17.8220, 31.0500) // Further coordinates

	t.Logf("Cache key 1: %s", key1)
	t.Logf("Cache key 2: %s", key2)
	t.Logf("Cache key 3: %s", key3)

	// Keys 1 and 2 should be the same (within rounding precision)
	// Note: With 4 decimal places, coordinates within ~11 meters should have same key
	if key1 != key2 {
		t.Logf("Note: Coordinates are too far apart for same cache key with 4 decimal precision")
		t.Logf("Distance between coordinates: ~11 meters")
	}

	// Test with coordinates that should definitely have the same key
	key4 := service.generateCacheKey(-17.8216, 31.0492)
	key5 := service.generateCacheKey(-17.8216, 31.0492) // Exact same coordinates
	if key4 != key5 {
		t.Errorf("Expected identical coordinates to have same cache key: %s vs %s", key4, key5)
	}

	// Test with coordinates that should definitely have different keys
	key6 := service.generateCacheKey(-17.8200, 31.0400) // Much further away
	if key1 == key6 {
		t.Errorf("Expected different coordinates to have different cache keys: %s vs %s", key1, key6)
	}
}
