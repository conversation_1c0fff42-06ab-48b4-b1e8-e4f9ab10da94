package services

import (
	"errors"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

type CustomerVisitService struct{}

func NewCustomerVisitService() *CustomerVisitService {
	return &CustomerVisitService{}
}

// GetCustomerVisits returns all customer visits with filtering and pagination
func (s *CustomerVisitService) GetCustomerVisits(filters models.CustomerVisitFilters) ([]models.CustomerVisit, int64, error) {
	var visits []models.CustomerVisit
	var total int64

	query := config.DB.Where("client_id = ?", filters.ClientId)

	// Apply filters
	if filters.VisitType != "" {
		query = query.Where("visit_type = ?", filters.VisitType)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.DriverId != "" {
		query = query.Where("driver_id = ?", filters.DriverId)
	}
	if filters.ClientDeviceId != "" {
		query = query.Where("client_device_id = ?", filters.ClientDeviceId)
	}
	if filters.StartDate != nil {
		query = query.Where("visit_date >= ?", filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("visit_date <= ?", filters.EndDate)
	}

	// Get total count
	query.Model(&models.CustomerVisit{}).Count(&total)

	// Apply pagination
	if filters.PerPage > 0 {
		offset := (filters.Page - 1) * filters.PerPage
		query = query.Offset(offset).Limit(filters.PerPage)
	}

	// Order by visit date
	query = query.Order("visit_date DESC")

	err := query.Find(&visits).Error
	return visits, total, err
}

// GetCustomerVisit returns a specific customer visit
func (s *CustomerVisitService) GetCustomerVisit(id uint, clientId uint) (*models.CustomerVisit, error) {
	var visit models.CustomerVisit
	err := config.DB.Where("id = ? AND client_id = ?", id, clientId).First(&visit).Error
	if err != nil {
		return nil, err
	}
	return &visit, nil
}

// CreateCustomerVisit creates a new customer visit
func (s *CustomerVisitService) CreateCustomerVisit(visit models.CustomerVisit) (*models.CustomerVisit, error) {
	// Set default status if not provided
	if visit.Status == "" {
		visit.Status = "scheduled"
	}

	// Set default visit type if not provided
	if visit.VisitType == "" {
		visit.VisitType = "other"
	}

	err := config.DB.Create(&visit).Error
	if err != nil {
		return nil, err
	}

	return &visit, nil
}

// UpdateCustomerVisit updates an existing customer visit
func (s *CustomerVisitService) UpdateCustomerVisit(visit models.CustomerVisit) (*models.CustomerVisit, error) {
	// Check if visit exists and belongs to client
	existingVisit, err := s.GetCustomerVisit(visit.Id, visit.ClientId)
	if err != nil {
		return nil, errors.New("customer visit not found")
	}

	// Update fields
	existingVisit.CustomerName = visit.CustomerName
	existingVisit.CustomerAddress = visit.CustomerAddress
	existingVisit.VisitDate = visit.VisitDate
	existingVisit.ArrivalTime = visit.ArrivalTime
	existingVisit.DepartureTime = visit.DepartureTime
	existingVisit.VisitDuration = visit.VisitDuration
	existingVisit.VisitType = visit.VisitType
	existingVisit.Status = visit.Status
	existingVisit.Notes = visit.Notes
	existingVisit.DriverId = visit.DriverId
	existingVisit.ClientDeviceId = visit.ClientDeviceId

	err = config.DB.Save(existingVisit).Error
	if err != nil {
		return nil, err
	}

	return existingVisit, nil
}

// DeleteCustomerVisit deletes a customer visit
func (s *CustomerVisitService) DeleteCustomerVisit(id uint, clientId uint) error {
	result := config.DB.Where("id = ? AND client_id = ?", id, clientId).Delete(&models.CustomerVisit{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return errors.New("customer visit not found")
	}
	return nil
}

// GetCustomerVisitStats returns customer visit statistics
func (s *CustomerVisitService) GetCustomerVisitStats(clientId uint) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total visits
	var total int64
	config.DB.Model(&models.CustomerVisit{}).Where("client_id = ?", clientId).Count(&total)
	stats["total"] = total

	// Visits this month
	var thisMonth int64
	monthStart := time.Now().Truncate(24*time.Hour).AddDate(0, 0, -time.Now().Day()+1)
	config.DB.Model(&models.CustomerVisit{}).Where("client_id = ? AND visit_date >= ?",
		clientId, monthStart).Count(&thisMonth)
	stats["this_month"] = thisMonth

	// Completed visits
	var completed int64
	config.DB.Model(&models.CustomerVisit{}).Where("client_id = ? AND status = ?",
		clientId, "completed").Count(&completed)
	stats["completed"] = completed

	// Scheduled visits
	var scheduled int64
	config.DB.Model(&models.CustomerVisit{}).Where("client_id = ? AND status = ?",
		clientId, "scheduled").Count(&scheduled)
	stats["scheduled"] = scheduled

	// Visit type breakdown
	var visitTypeBreakdown []struct {
		VisitType string `json:"visit_type"`
		Count     int64  `json:"count"`
	}
	config.DB.Model(&models.CustomerVisit{}).
		Select("visit_type, COUNT(*) as count").
		Where("client_id = ?", clientId).
		Group("visit_type").
		Scan(&visitTypeBreakdown)
	stats["visit_type_breakdown"] = visitTypeBreakdown

	// Status breakdown
	var statusBreakdown []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}
	config.DB.Model(&models.CustomerVisit{}).
		Select("status, COUNT(*) as count").
		Where("client_id = ?", clientId).
		Group("status").
		Scan(&statusBreakdown)
	stats["status_breakdown"] = statusBreakdown

	// Average visit duration
	var avgDuration float64
	config.DB.Model(&models.CustomerVisit{}).
		Where("client_id = ? AND visit_duration > 0", clientId).
		Select("AVG(visit_duration)").Scan(&avgDuration)
	stats["avg_duration_minutes"] = avgDuration / 60.0

	// Top customers by visit count
	var topCustomers []struct {
		CustomerName string `json:"customer_name"`
		VisitCount   int64  `json:"visit_count"`
	}
	config.DB.Model(&models.CustomerVisit{}).
		Select("customer_name, COUNT(*) as visit_count").
		Where("client_id = ?", clientId).
		Group("customer_name").
		Order("visit_count DESC").
		Limit(10).
		Scan(&topCustomers)
	stats["top_customers"] = topCustomers

	return stats, nil
}
