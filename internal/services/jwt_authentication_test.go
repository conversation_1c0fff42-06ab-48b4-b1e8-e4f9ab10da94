package services

import (
	"github.com/stretchr/testify/assert"
	"os"
	"testing"
	"yotracker/internal/models"
)

func TestGenerateToken(t *testing.T) {
	os.Setenv("APP_KEY", "secret")
	user := &models.User{
		Id:    1,
		Email: "<EMAIL>",
	}
	token, err := GenerateToken(user, "access")
	assert.NoError(t, err)
	assert.NotEmpty(t, token, "Access token should not be empty")
	refreshToken, err := GenerateToken(user, "refresh")
	assert.NoError(t, err)
	assert.NotEmpty(t, refreshToken, "Refresh token should not be empty")
}
func TestVerifyToken(t *testing.T) {
	os.Setenv("APP_KEY", "secret")
	user := &models.User{
		Id:    1,
		Email: "<EMAIL>",
	}
	token, err := GenerateToken(user, "access")
	assert.NoError(t, err)
	claims, err := VerifyToken(token, "access")
	assert.NoError(t, err)
	assert.Equal(t, user.Id, claims.UserId)
	refreshToken, err := GenerateToken(user, "refresh")
	assert.NoError(t, err)
	assert.NotEmpty(t, refreshToken, "Refresh token should not be empty")
}
func TestInvalidToken(t *testing.T) {
	os.Setenv("APP_KEY", "secret")
	_, err := VerifyToken("invalid.token.string", "access")
	assert.Error(t, err)
}

func TestPasswordResetToken(t *testing.T) {
	os.Setenv("APP_KEY", "secret")
	user := &models.User{
		Id:    1,
		Email: "<EMAIL>",
	}

	// Test generating password reset token
	resetToken, err := GenerateToken(user, "password_reset")
	assert.NoError(t, err)
	assert.NotEmpty(t, resetToken, "Password reset token should not be empty")

	// Test verifying password reset token
	claims, err := VerifyToken(resetToken, "password_reset")
	assert.NoError(t, err)
	assert.Equal(t, user.Id, claims.UserId)
	assert.Equal(t, "password_reset", claims.Subject)

	// Test that access token cannot be used as password reset token
	accessToken, err := GenerateToken(user, "access")
	assert.NoError(t, err)
	_, err = VerifyToken(accessToken, "password_reset")
	assert.Error(t, err)

	// Test that password reset token cannot be used as access token
	_, err = VerifyToken(resetToken, "access")
	assert.Error(t, err)
}
