package services

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"yotracker/config"
	"yotracker/internal/models"
)

// FlexibleNotificationService handles notifications based on client preferences
type FlexibleNotificationService struct {
	emailService            *EmailAlertService
	smsService              *TwilioSMSService
	whatsappService         *WhatsAppService
	slackService            *SlackService
	pushNotificationService *PushNotificationService
}

// NewFlexibleNotificationService creates a new flexible notification service
func NewFlexibleNotificationService() (*FlexibleNotificationService, error) {
	emailService, err := NewEmailAlertService()
	if err != nil {
		log.Printf("Warning: Failed to initialize email service: %v", err)
		emailService = nil
	}

	smsService, err := NewTwilioSMSService()
	if err != nil {
		log.Printf("Warning: Failed to initialize SMS service: %v", err)
		smsService = nil
	}

	whatsappService, err := NewWhatsAppService()
	if err != nil {
		log.Printf("Warning: Failed to initialize WhatsApp service: %v", err)
		whatsappService = nil
	}

	slackService, err := NewSlackService()
	if err != nil {
		log.Printf("Warning: Failed to initialize Slack service: %v", err)
		slackService = nil
	}

	pushNotificationService := NewPushNotificationService()

	return &FlexibleNotificationService{
		emailService:            emailService,
		smsService:              smsService,
		whatsappService:         whatsappService,
		slackService:            slackService,
		pushNotificationService: pushNotificationService,
	}, nil
}

// NotificationRequest represents a request to send notifications
type NotificationRequest struct {
	Alert         *models.Alert
	Client        *models.Client
	ClientDevice  *models.ClientDevice
	Fleet         *models.Fleet
	AlertType     models.AlertType
	ForceChannels []models.NotificationChannel // Override client preferences
}

// SendNotification sends notifications based on client preferences and alert type
func (s *FlexibleNotificationService) SendNotification(req *NotificationRequest) error {
	if req.Alert == nil || req.Client == nil || req.ClientDevice == nil {
		return fmt.Errorf("invalid notification request: missing required fields")
	}

	// Skip sending in test environment
	if os.Getenv("TEST_ENV") == "true" {
		log.Printf("[NOTIFICATION TEST] Would send notification for alert %d (type: %s) through configured channels", req.Alert.Id, req.AlertType)
		return nil
	}

	// Determine which channels to use
	channels := s.determineNotificationChannels(req)
	if len(channels) == 0 {
		log.Printf("No notification channels configured for alert type %s", req.AlertType)
		return nil
	}

	// Send notifications through each configured channel
	var errors []string
	for _, channel := range channels {
		if err := s.sendNotificationThroughChannel(req, channel); err != nil {
			errors = append(errors, fmt.Sprintf("%s: %v", channel, err))
		}
	}

	// Return combined errors if any
	if len(errors) > 0 {
		return fmt.Errorf("notification errors: %s", errors)
	}

	return nil
}

// determineNotificationChannels determines which channels to use for notifications
func (s *FlexibleNotificationService) determineNotificationChannels(req *NotificationRequest) []models.NotificationChannel {
	// If force channels are specified, use those
	if len(req.ForceChannels) > 0 {
		return req.ForceChannels
	}

	// Check device-level preferences first
	if deviceChannels := s.getDeviceLevelChannels(req); len(deviceChannels) > 0 {
		return deviceChannels
	}

	// Check fleet-level preferences
	if fleetChannels := s.getFleetLevelChannels(req); len(fleetChannels) > 0 {
		return fleetChannels
	}

	// Check client-level preferences
	if clientChannels := s.getClientLevelChannels(req); len(clientChannels) > 0 {
		return clientChannels
	}

	// Fall back to default channels based on client settings
	return s.getDefaultChannels(req)
}

// getDeviceLevelChannels gets notification channels from device-level preferences
func (s *FlexibleNotificationService) getDeviceLevelChannels(req *NotificationRequest) []models.NotificationChannel {
	var devicePref models.DeviceAlertPreference
	err := config.DB.Where("client_device_id = ? AND alert_type = ? AND enabled = ?",
		req.ClientDevice.Id, req.AlertType, true).First(&devicePref).Error
	if err != nil {
		return nil
	}

	return devicePref.GetChannelsAsSlice()
}

// getFleetLevelChannels gets notification channels from fleet-level preferences
func (s *FlexibleNotificationService) getFleetLevelChannels(req *NotificationRequest) []models.NotificationChannel {
	if req.Fleet == nil {
		return nil
	}

	var fleetPref models.FleetAlertPreference
	err := config.DB.Where("fleet_id = ? AND alert_type = ? AND enabled = ?",
		req.Fleet.Id, req.AlertType, true).First(&fleetPref).Error
	if err != nil {
		return nil
	}

	return fleetPref.GetChannelsAsSlice()
}

// getClientLevelChannels gets notification channels from client-level preferences
func (s *FlexibleNotificationService) getClientLevelChannels(req *NotificationRequest) []models.NotificationChannel {
	var clientPref models.AlertPreference
	err := config.DB.Where("client_id = ? AND alert_type = ? AND enabled = ?",
		req.Client.Id, req.AlertType, true).First(&clientPref).Error
	if err != nil {
		return nil
	}

	return clientPref.GetChannelsAsSlice()
}

// getDefaultChannels gets default notification channels based on client settings
func (s *FlexibleNotificationService) getDefaultChannels(req *NotificationRequest) []models.NotificationChannel {
	var channels []models.NotificationChannel

	// Check if client has email alerts enabled
	if req.Client.EmailAlertsEnabled != nil && *req.Client.EmailAlertsEnabled && req.Client.AlertsEmail != nil {
		channels = append(channels, models.NotificationChannelEmail)
	}

	// Check if client has SMS alerts enabled
	if req.Client.SmsAlertsEnabled != nil && *req.Client.SmsAlertsEnabled {
		channels = append(channels, models.NotificationChannelSMS)
	}

	// Check if client has WhatsApp alerts enabled
	if req.Client.WhatsappAlertsEnabled != nil && *req.Client.WhatsappAlertsEnabled {
		channels = append(channels, models.NotificationChannelWhatsApp)
	}

	return channels
}

// sendNotificationThroughChannel sends a notification through a specific channel
func (s *FlexibleNotificationService) sendNotificationThroughChannel(req *NotificationRequest, channel models.NotificationChannel) error {
	switch channel {
	case models.NotificationChannelEmail:
		return s.sendEmailNotification(req)
	case models.NotificationChannelSMS:
		return s.sendSMSNotification(req)
	case models.NotificationChannelWhatsApp:
		return s.sendWhatsAppNotification(req)
	case models.NotificationChannelSlack:
		return s.sendSlackNotification(req)
	case models.NotificationChannelPush:
		return s.sendPushNotification(req)
	default:
		return fmt.Errorf("unsupported notification channel: %s", channel)
	}
}

// sendEmailNotification sends an email notification
func (s *FlexibleNotificationService) sendEmailNotification(req *NotificationRequest) error {
	if s.emailService == nil {
		return fmt.Errorf("email service not available")
	}

	// Check if client has email alerts enabled
	if req.Client.EmailAlertsEnabled == nil || !*req.Client.EmailAlertsEnabled {
		return fmt.Errorf("email alerts not enabled for client")
	}

	// Check if client has email addresses configured
	if req.Client.AlertsEmail == nil {
		return fmt.Errorf("client has no email addresses configured")
	}

	// Parse email addresses from JSON
	var emailAddresses []string
	if err := json.Unmarshal([]byte(*req.Client.AlertsEmail), &emailAddresses); err != nil {
		return fmt.Errorf("failed to parse client email addresses: %v", err)
	}

	if len(emailAddresses) == 0 {
		return fmt.Errorf("client has no valid email addresses")
	}

	// Send email to all configured addresses
	for _, email := range emailAddresses {
		if err := s.emailService.SendAlert(req.Client, req.Alert, req.ClientDevice); err != nil {
			log.Printf("Failed to send email to %s: %v", email, err)
			// Continue with other emails
		}
	}

	return nil
}

// sendSMSNotification sends an SMS notification
func (s *FlexibleNotificationService) sendSMSNotification(req *NotificationRequest) error {
	if s.smsService == nil {
		return fmt.Errorf("SMS service not available")
	}

	// Check if client has SMS alerts enabled
	if req.Client.SmsAlertsEnabled == nil || !*req.Client.SmsAlertsEnabled {
		return fmt.Errorf("SMS alerts not enabled for client")
	}

	// Check if client has phone numbers configured
	if req.Client.SmsPhoneNumber == nil {
		return fmt.Errorf("client has no phone numbers configured")
	}

	// Parse phone numbers from JSON
	var phoneNumbers []string
	if err := json.Unmarshal([]byte(*req.Client.SmsPhoneNumber), &phoneNumbers); err != nil {
		return fmt.Errorf("failed to parse client phone numbers: %v", err)
	}

	if len(phoneNumbers) == 0 {
		return fmt.Errorf("client has no valid phone numbers")
	}

	// Send SMS to all configured numbers
	for _, phone := range phoneNumbers {
		if err := s.smsService.SendAlert(req.Client, req.Alert, req.ClientDevice); err != nil {
			log.Printf("Failed to send SMS to %s: %v", phone, err)
			// Continue with other numbers
		}
	}

	return nil
}

// sendWhatsAppNotification sends a WhatsApp notification
func (s *FlexibleNotificationService) sendWhatsAppNotification(req *NotificationRequest) error {
	if s.whatsappService == nil {
		return fmt.Errorf("WhatsApp service not available")
	}

	// Check if client has WhatsApp alerts enabled
	if req.Client.WhatsappAlertsEnabled == nil || !*req.Client.WhatsappAlertsEnabled {
		return fmt.Errorf("WhatsApp alerts not enabled for client")
	}

	// Check if client has WhatsApp phone numbers configured
	if req.Client.WhatsappPhoneNumber == nil {
		return fmt.Errorf("client has no WhatsApp phone numbers configured")
	}

	// Parse WhatsApp phone numbers from JSON
	var whatsappNumbers []string
	if err := json.Unmarshal([]byte(*req.Client.WhatsappPhoneNumber), &whatsappNumbers); err != nil {
		return fmt.Errorf("failed to parse client WhatsApp phone numbers: %v", err)
	}

	if len(whatsappNumbers) == 0 {
		return fmt.Errorf("client has no valid WhatsApp phone numbers")
	}

	// Send WhatsApp message to all configured numbers
	for _, phone := range whatsappNumbers {
		if err := s.whatsappService.SendAlert(req.Client, req.Alert, req.ClientDevice); err != nil {
			log.Printf("Failed to send WhatsApp message to %s: %v", phone, err)
			// Continue with other numbers
		}
	}

	return nil
}

// sendSlackNotification sends a Slack notification
func (s *FlexibleNotificationService) sendSlackNotification(req *NotificationRequest) error {
	if s.slackService == nil {
		return fmt.Errorf("Slack service not available")
	}

	// For now, send to default Slack channel
	// TODO: Add client-specific Slack configuration
	return s.slackService.SendAlert(req.Alert, req.ClientDevice)
}

// sendPushNotification sends push notifications to all users of the client
func (s *FlexibleNotificationService) sendPushNotification(req *NotificationRequest) error {
	if s.pushNotificationService == nil {
		return fmt.Errorf("push notification service not available")
	}

	// Get all users with Firebase tokens for this client
	var users []models.User
	err := config.DB.Where("client_id = ? AND firebase_push_token IS NOT NULL AND firebase_push_token != ''", req.Client.Id).Find(&users).Error
	if err != nil {
		return fmt.Errorf("failed to fetch users with Firebase tokens: %v", err)
	}

	if len(users) == 0 {
		log.Printf("No users with Firebase tokens found for client %d", req.Client.Id)
		return nil
	}

	// Convert users to individual push notification requests
	var individualRequests []PushNotificationRequest
	for _, user := range users {
		if user.FirebasePushToken != nil && *user.FirebasePushToken != "" {
			// Build notification data
			data := map[string]interface{}{
				"alert_id":         req.Alert.Id,
				"alert_type":       req.Alert.AlertType,
				"client_id":        req.Client.Id,
				"client_device_id": req.ClientDevice.Id,
				"timestamp":        req.Alert.CreatedAt.Format("2006-01-02T15:04:05Z"),
			}

			// Add device name if available
			if req.ClientDevice.Name != nil {
				data["device_name"] = *req.ClientDevice.Name
			}

			// Add plate number if available
			if req.ClientDevice.PlateNumber != nil {
				data["plate_number"] = *req.ClientDevice.PlateNumber
			}

			// Add alert name if available
			if req.Alert.AlertName != nil {
				data["alert_name"] = *req.Alert.AlertName
			}

			individualRequests = append(individualRequests, PushNotificationRequest{
				To:    *user.FirebasePushToken,
				Title: s.buildPushNotificationTitle(req),
				Body:  s.buildPushNotificationBody(req),
				Type:  string(req.AlertType),
				Data:  data,
			})
		}
	}

	if len(individualRequests) == 0 {
		log.Printf("No valid Firebase tokens found for client %d", req.Client.Id)
		return nil
	}

	// Send bulk notifications
	_, err = s.pushNotificationService.SendBulkPushNotification(individualRequests)
	if err != nil {
		return fmt.Errorf("failed to send push notifications: %v", err)
	}

	log.Printf("Sent push notifications to %d users for client %d", len(individualRequests), req.Client.Id)
	return nil
}

// buildPushNotificationTitle builds the title for push notifications
func (s *FlexibleNotificationService) buildPushNotificationTitle(req *NotificationRequest) string {
	deviceName := "Device"
	if req.ClientDevice.Name != nil {
		deviceName = *req.ClientDevice.Name
	}

	alertType := string(req.AlertType)
	switch req.AlertType {
	case models.AlertTypeSpeed:
		return fmt.Sprintf("⚡ %s - Speed Alert", deviceName)
	case models.AlertTypeShutdown:
		return fmt.Sprintf("🛑 %s - Shutdown Alert", deviceName)
	case models.AlertTypeTowing:
		return fmt.Sprintf("🚛 %s - Towing Alert", deviceName)
	case models.AlertTypeImpact:
		return fmt.Sprintf("💥 %s - Impact Alert", deviceName)
	case models.AlertTypeGeofence:
		return fmt.Sprintf("📍 %s - Geofence Alert", deviceName)
	case models.AlertTypeMaintenance:
		return fmt.Sprintf("🔧 %s - Maintenance Alert", deviceName)
	case models.AlertTypeBattery:
		return fmt.Sprintf("🔋 %s - Battery Alert", deviceName)
	case models.AlertTypeSOS:
		return fmt.Sprintf("🆘 %s - SOS Alert", deviceName)
	default:
		return fmt.Sprintf("📱 %s - %s Alert", deviceName, alertType)
	}
}

// buildPushNotificationBody builds the body for push notifications
func (s *FlexibleNotificationService) buildPushNotificationBody(req *NotificationRequest) string {
	deviceName := "Unknown Device"
	if req.ClientDevice.Name != nil {
		deviceName = *req.ClientDevice.Name
	}

	alertName := string(req.AlertType)
	if req.Alert.AlertName != nil && *req.Alert.AlertName != "" {
		alertName = *req.Alert.AlertName
	}

	return fmt.Sprintf("%s: %s", deviceName, alertName)
}

// GetNotificationStatus returns the status of all notification services
func (s *FlexibleNotificationService) GetNotificationStatus() map[string]bool {
	return map[string]bool{
		"email":    s.emailService != nil,
		"sms":      s.smsService != nil,
		"whatsapp": s.whatsappService != nil,
		"slack":    s.slackService != nil,
		"push":     s.pushNotificationService != nil,
	}
}
