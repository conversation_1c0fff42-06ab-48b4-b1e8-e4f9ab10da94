package models

import (
	"time"
)

// FileUpload represents an uploaded file
type FileUpload struct {
	Id           uint      `json:"id" gorm:"primaryKey"`
	ClientId     *uint     `json:"client_id" gorm:"index"` // Optional, for client-specific files
	CreatedById  uint      `json:"created_by_id" gorm:"index"`
	CreatedBy    User      `json:"created_by"`
	FileName     string    `json:"file_name" gorm:"type:varchar(255);not null"`
	OriginalName string    `json:"original_name" gorm:"type:varchar(255);not null"`
	FileSize     int64     `json:"file_size" gorm:"not null"`
	MimeType     string    `json:"mime_type" gorm:"type:varchar(100);not null"`
	FileUrl      string    `json:"file_url" gorm:"type:varchar(500);not null"`
	FilePath     string    `json:"file_path" gorm:"type:varchar(500);not null"`
	FileType     string    `json:"file_type" gorm:"type:enum('image','document','video','audio','other');default:'other'"`
	Description  *string   `json:"description" gorm:"type:text"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// FileUploadResponse represents the response for file upload
type FileUploadResponse struct {
	Id           uint    `json:"id"`
	FileName     string  `json:"file_name"`
	OriginalName string  `json:"original_name"`
	FileSize     int64   `json:"file_size"`
	MimeType     string  `json:"mime_type"`
	FileUrl      string  `json:"file_url"`
	FileType     string  `json:"file_type"`
	Description  *string `json:"description"`
}

// FileUploadFilters represents filters for file upload queries
type FileUploadFilters struct {
	ClientId    uint   `form:"client_id"`
	FileType    string `form:"file_type"`
	CreatedById *uint  `form:"created_by_id"`
	Page        int    `form:"page" binding:"omitempty,min=1"`
	PerPage     int    `form:"per_page" binding:"omitempty,min=1,max=100"`
}
