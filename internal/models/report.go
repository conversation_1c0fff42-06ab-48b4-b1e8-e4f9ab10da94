package models

import (
	"encoding/json"
	"time"
)

type Report struct {
	Id          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"unique;not null"`
	Description string `json:"description" gorm:"type:text"`
	Category    string `json:"category" gorm:"index"`        // Detail, Summary, Maintenance, Behavior, Management
	Status      string `json:"status" gorm:"default:active"` // active, inactive

	// Report configuration
	ReportType     string          `json:"report_type"`                      // position_log, trip_detail, etc.
	DefaultFilters json.RawMessage `json:"default_filters" gorm:"type:json"` // Default filter parameters
	ReportOrder    int             `json:"report_order" gorm:"default:999"`  // Order for display priority

	// Metadata
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type ScheduledReport struct {
	Id       uint   `json:"id" gorm:"primaryKey"`
	ReportId uint   `json:"report_id" gorm:"index"`
	Report   Report `json:"report"`

	// Scheduling details
	Name        string `json:"name"`
	Description string `json:"description"`
	Status      string `json:"status" gorm:"default:active"` // active, inactive, paused

	// Schedule configuration
	Frequency   string `json:"frequency"`    // daily, weekly, monthly, custom
	CronPattern string `json:"cron_pattern"` // For custom schedules
	Timezone    string `json:"timezone" gorm:"default:UTC"`

	// Report parameters
	Filters    json.RawMessage `json:"filters" gorm:"type:json"`    // Report-specific filters
	Recipients json.RawMessage `json:"recipients" gorm:"type:json"` // Email recipients
	Format     string          `json:"format" gorm:"default:pdf"`   // pdf, excel, csv

	// Execution tracking
	LastRunAt  *time.Time `json:"last_run_at"`
	NextRunAt  *time.Time `json:"next_run_at"`
	LastStatus string     `json:"last_status"` // success, failed, running
	LastError  *string    `json:"last_error"`
	RunCount   int        `json:"run_count" gorm:"default:0"`

	// Metadata
	CreatedBy uint      `json:"created_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Report filter structures
type ReportFilters struct {
	// Security filter - REQUIRED for client isolation (not exposed in JSON)
	ClientId uint `json:"-"`

	// Time filters
	StartDate *time.Time `json:"start_date"`
	EndDate   *time.Time `json:"end_date"`

	// Entity filters
	ClientDeviceIds []uint `json:"client_device_ids,omitempty"`
	DriverIds       []uint `json:"driver_ids,omitempty"`
	FleetIds        []uint `json:"fleet_ids,omitempty"`

	// Behavior filters
	EventTypes  []string `json:"event_types,omitempty"`
	MinSeverity *float64 `json:"min_severity,omitempty"`
	MaxSeverity *float64 `json:"max_severity,omitempty"`

	// Trip filters
	MinDistance *float64 `json:"min_distance,omitempty"`
	MaxDistance *float64 `json:"max_distance,omitempty"`
	MinDuration *int     `json:"min_duration,omitempty"`
	MaxDuration *int     `json:"max_duration,omitempty"`

	// Speed filters
	MinSpeed   *float64 `json:"min_speed,omitempty"`
	MaxSpeed   *float64 `json:"max_speed,omitempty"`
	SpeedLimit *float64 `json:"speed_limit,omitempty"`

	// Location filters
	GeofenceIds  []uint  `json:"geofence_ids,omitempty"`
	LocationName *string `json:"location_name,omitempty"`

	// Grouping options
	GroupBy  []string `json:"group_by,omitempty"` // device, driver, date, hour, etc.
	OrderBy  string   `json:"order_by,omitempty"`
	OrderDir string   `json:"order_dir,omitempty"` // asc, desc

	// Pagination
	Page    int `json:"page,omitempty"`
	PerPage int `json:"per_page,omitempty"`
}

// Customer visits tracking
type CustomerVisit struct {
	Id              uint       `json:"id" gorm:"primaryKey"`
	ClientId        uint       `json:"client_id" gorm:"index"`
	ClientDeviceId  *uint      `json:"client_device_id" gorm:"index"`
	DriverId        *uint      `json:"driver_id" gorm:"index"`
	CustomerName    string     `json:"customer_name" gorm:"not null"`
	CustomerAddress string     `json:"customer_address" gorm:"type:text"`
	VisitDate       time.Time  `json:"visit_date" gorm:"index"`
	ArrivalTime     *time.Time `json:"arrival_time"`
	DepartureTime   *time.Time `json:"departure_time"`
	VisitDuration   int        `json:"visit_duration"` // in seconds
	VisitType       string     `json:"visit_type" gorm:"type:enum('delivery','pickup','service','sales','other');default:'other'"`
	Status          string     `json:"status" gorm:"type:enum('scheduled','in_progress','completed','cancelled');default:'scheduled'"`
	Notes           string     `json:"notes" gorm:"type:text"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// Maintenance schedule and history
type MaintenanceSchedule struct {
	Id              uint       `json:"id" gorm:"primaryKey"`
	ClientId        uint       `json:"client_id" gorm:"index"`
	ClientDeviceId  uint       `json:"client_device_id" gorm:"index"`
	MaintenanceType string     `json:"maintenance_type" gorm:"type:enum('scheduled','preventive','corrective','emergency');default:'scheduled'"`
	ServiceType     string     `json:"service_type" gorm:"not null"`
	ScheduledDate   time.Time  `json:"scheduled_date"`
	DueDate         time.Time  `json:"due_date" gorm:"index"`
	CompletedDate   *time.Time `json:"completed_date"`
	Cost            float64    `json:"cost" gorm:"default:0.00"`
	ServiceProvider string     `json:"service_provider"`
	Description     string     `json:"description" gorm:"type:text"`
	Status          string     `json:"status" gorm:"type:enum('scheduled','in_progress','completed','overdue','cancelled');default:'scheduled'"`
	Priority        string     `json:"priority" gorm:"type:enum('low','medium','high','critical');default:'medium'"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// Vehicle health metrics
type VehicleHealthMetric struct {
	Id                 uint      `json:"id" gorm:"primaryKey"`
	ClientId           uint      `json:"client_id" gorm:"index"`
	ClientDeviceId     uint      `json:"client_device_id" gorm:"index"`
	MetricDate         time.Time `json:"metric_date" gorm:"index"`
	EngineHours        int       `json:"engine_hours" gorm:"default:0"`
	FuelEfficiency     float64   `json:"fuel_efficiency"` // km/L
	EngineTemperature  float64   `json:"engine_temperature"`
	OilPressure        float64   `json:"oil_pressure"`
	BatteryVoltage     float64   `json:"battery_voltage"`
	TirePressureFront  float64   `json:"tire_pressure_front"`
	TirePressureRear   float64   `json:"tire_pressure_rear"`
	BrakeCondition     string    `json:"brake_condition" gorm:"type:enum('good','fair','poor','critical');default:'good'"`
	OverallHealthScore float64   `json:"overall_health_score" gorm:"default:100.0"` // 0-100 scale
	AlertsCount        int       `json:"alerts_count" gorm:"default:0"`
	CreatedAt          time.Time `json:"created_at"`
}

// Operating costs tracking
type OperatingCost struct {
	Id             uint      `json:"id" gorm:"primaryKey"`
	ClientId       uint      `json:"client_id" gorm:"index"`
	ClientDeviceId *uint     `json:"client_device_id" gorm:"index"`
	CostDate       time.Time `json:"cost_date" gorm:"index"`
	CostType       string    `json:"cost_type" gorm:"type:enum('fuel','maintenance','labor','insurance','depreciation','other');not null"`
	CostCategory   string    `json:"cost_category"`
	Amount         float64   `json:"amount" gorm:"not null"`
	Description    string    `json:"description" gorm:"type:text"`
	InvoiceNumber  string    `json:"invoice_number"`
	Vendor         string    `json:"vendor"`
	TripId         *uint     `json:"trip_id"`
	CreatedAt      time.Time `json:"created_at"`
}

// Environmental impact data
type EmissionsData struct {
	Id                uint      `json:"id" gorm:"primaryKey"`
	ClientId          uint      `json:"client_id" gorm:"index"`
	ClientDeviceId    *uint     `json:"client_device_id" gorm:"index"`
	Date              time.Time `json:"date" gorm:"index"`
	FuelConsumed      float64   `json:"fuel_consumed"`                             // liters
	DistanceTraveled  float64   `json:"distance_traveled"`                         // km
	CO2Emissions      float64   `json:"co2_emissions" gorm:"column:co2_emissions"` // kg CO2
	NOxEmissions      float64   `json:"nox_emissions" gorm:"column:nox_emissions"` // g NOx
	ParticulateMatter float64   `json:"particulate_matter"`                        // g PM
	FuelEfficiency    float64   `json:"fuel_efficiency"`                           // km/L
	CarbonFootprint   float64   `json:"carbon_footprint"`                          // kg CO2/km
	CreatedAt         time.Time `json:"created_at"`
}

// Driver training records
type DriverTraining struct {
	Id               uint       `json:"id" gorm:"primaryKey"`
	ClientId         uint       `json:"client_id" gorm:"index"`
	DriverId         uint       `json:"driver_id" gorm:"index"`
	TrainingType     string     `json:"training_type" gorm:"not null"`
	TrainingDate     time.Time  `json:"training_date" gorm:"index"`
	CompletionDate   *time.Time `json:"completion_date"`
	Score            float64    `json:"score"` // percentage
	Status           string     `json:"status" gorm:"type:enum('scheduled','in_progress','completed','failed','expired');default:'scheduled'"`
	Trainer          string     `json:"trainer"`
	TrainingProvider string     `json:"training_provider"`
	Cost             float64    `json:"cost" gorm:"default:0.00"`
	Notes            string     `json:"notes" gorm:"type:text"`
	NextRefreshDate  *time.Time `json:"next_refresh_date"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
}

// Asset performance tracking
type AssetPerformance struct {
	Id               uint      `json:"id" gorm:"primaryKey"`
	ClientId         uint      `json:"client_id" gorm:"index"`
	ClientDeviceId   uint      `json:"client_device_id" gorm:"index"`
	PerformanceDate  time.Time `json:"performance_date" gorm:"index"`
	UtilizationRate  float64   `json:"utilization_rate"`  // percentage
	AvailabilityRate float64   `json:"availability_rate"` // percentage
	ReliabilityRate  float64   `json:"reliability_rate"`  // percentage
	EfficiencyScore  float64   `json:"efficiency_score"`  // 0-100 scale
	DowntimeHours    int       `json:"downtime_hours" gorm:"default:0"`
	ProductiveHours  int       `json:"productive_hours" gorm:"default:0"`
	TotalHours       int       `json:"total_hours" gorm:"default:0"`
	RevenueGenerated float64   `json:"revenue_generated" gorm:"default:0.00"`
	CostPerHour      float64   `json:"cost_per_hour" gorm:"default:0.00"`
	ROIPercentage    float64   `json:"roi_percentage" gorm:"column:roi_percentage;default:0.00"`
	CreatedAt        time.Time `json:"created_at"`
}

// Route optimization data
type RouteOptimization struct {
	Id                uint      `json:"id" gorm:"primaryKey"`
	ClientId          uint      `json:"client_id" gorm:"index"`
	RouteName         string    `json:"route_name" gorm:"not null"`
	StartLocation     string    `json:"start_location"`
	EndLocation       string    `json:"end_location"`
	OptimizedDistance float64   `json:"optimized_distance"`
	OriginalDistance  float64   `json:"original_distance"`
	TimeSaved         int       `json:"time_saved"` // in minutes
	FuelSaved         float64   `json:"fuel_saved"` // liters
	CostSaved         float64   `json:"cost_saved"`
	OptimizationDate  time.Time `json:"optimization_date" gorm:"index"`
	Status            string    `json:"status" gorm:"type:enum('proposed','implemented','rejected');default:'proposed'"`
	CreatedAt         time.Time `json:"created_at"`
}

// Safety compliance tracking
type SafetyCompliance struct {
	Id             uint       `json:"id" gorm:"primaryKey"`
	ClientId       uint       `json:"client_id" gorm:"index"`
	DriverId       *uint      `json:"driver_id" gorm:"index"`
	ComplianceDate time.Time  `json:"compliance_date" gorm:"index"`
	ComplianceType string     `json:"compliance_type" gorm:"type:enum('license','medical','training','vehicle_inspection','drug_test');not null"`
	Status         string     `json:"status" gorm:"type:enum('compliant','non_compliant','expiring_soon','expired');default:'compliant'"`
	ExpiryDate     *time.Time `json:"expiry_date"`
	LastCheckDate  *time.Time `json:"last_check_date"`
	NextCheckDate  *time.Time `json:"next_check_date"`
	Notes          string     `json:"notes" gorm:"type:text"`
	CreatedAt      time.Time  `json:"created_at"`
	UpdatedAt      time.Time  `json:"updated_at"`
}

// Vehicle lifecycle stages
type VehicleLifecycle struct {
	Id             uint       `json:"id" gorm:"primaryKey"`
	ClientId       uint       `json:"client_id" gorm:"index"`
	ClientDeviceId uint       `json:"client_device_id" gorm:"index"`
	LifecycleStage string     `json:"lifecycle_stage" gorm:"type:enum('acquisition','operation','maintenance','upgrade','disposal');not null"`
	StageDate      time.Time  `json:"stage_date" gorm:"index"`
	StageDuration  *int       `json:"stage_duration"` // in days
	Cost           float64    `json:"cost" gorm:"default:0.00"`
	Description    string     `json:"description" gorm:"type:text"`
	NextStageDate  *time.Time `json:"next_stage_date"`
	CreatedAt      time.Time  `json:"created_at"`
}

// Service technician assignments
type ServiceTechnician struct {
	Id                 uint      `json:"id" gorm:"primaryKey"`
	ClientId           uint      `json:"client_id" gorm:"index"`
	TechnicianName     string    `json:"technician_name" gorm:"not null"`
	Specialization     string    `json:"specialization"`
	ContactNumber      string    `json:"contact_number"`
	Email              string    `json:"email"`
	AvailabilityStatus string    `json:"availability_status" gorm:"type:enum('available','busy','off_duty','on_leave');default:'available'"`
	CurrentLocation    string    `json:"current_location"`
	Rating             float64   `json:"rating" gorm:"default:5.00"` // 0-5 scale
	TotalAssignments   int       `json:"total_assignments" gorm:"default:0"`
	SuccessfulRepairs  int       `json:"successful_repairs" gorm:"default:0"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// Construction equipment specific data
type ConstructionEquipment struct {
	Id                 uint      `json:"id" gorm:"primaryKey"`
	ClientId           uint      `json:"client_id" gorm:"index"`
	ClientDeviceId     uint      `json:"client_device_id" gorm:"index"`
	EquipmentType      string    `json:"equipment_type" gorm:"not null"`
	Model              string    `json:"model"`
	Capacity           string    `json:"capacity"`
	FuelType           string    `json:"fuel_type" gorm:"type:enum('diesel','gasoline','electric','hybrid','other');default:'diesel'"`
	OperatingHours     int       `json:"operating_hours" gorm:"default:0"`
	MaintenanceHours   int       `json:"maintenance_hours" gorm:"default:0"`
	IdleHours          int       `json:"idle_hours" gorm:"default:0"`
	EfficiencyRating   float64   `json:"efficiency_rating" gorm:"default:1.00"` // 0-1 scale
	ProjectAssignments int       `json:"project_assignments" gorm:"default:0"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// Sales territory data
type SalesTerritory struct {
	Id               uint      `json:"id" gorm:"primaryKey"`
	ClientId         uint      `json:"client_id" gorm:"index"`
	TerritoryName    string    `json:"territory_name" gorm:"not null"`
	TerritoryCode    string    `json:"territory_code"`
	Region           string    `json:"region"`
	AssignedDriverId *uint     `json:"assigned_driver_id" gorm:"column:assigned_driver_id;index"`
	TotalCustomers   int       `json:"total_customers" gorm:"default:0"`
	ActiveCustomers  int       `json:"active_customers" gorm:"default:0"`
	MonthlyRevenue   float64   `json:"monthly_revenue" gorm:"default:0.00"`
	VisitFrequency   int       `json:"visit_frequency" gorm:"default:0"` // visits per month
	TerritorySize    float64   `json:"territory_size"`                   // km²
	CreatedAt        time.Time `json:"created_at"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// Delivery performance tracking
type DeliveryPerformance struct {
	Id                    uint       `json:"id" gorm:"primaryKey"`
	ClientId              uint       `json:"client_id" gorm:"index"`
	DeliveryId            string     `json:"delivery_id" gorm:"not null;index"`
	ClientDeviceId        *uint      `json:"client_device_id" gorm:"index"`
	DriverId              *uint      `json:"driver_id" gorm:"index"`
	CustomerId            string     `json:"customer_id"`
	ScheduledDeliveryTime *time.Time `json:"scheduled_delivery_time"`
	ActualDeliveryTime    *time.Time `json:"actual_delivery_time"`
	DeliveryStatus        string     `json:"delivery_status" gorm:"type:enum('scheduled','in_transit','delivered','failed','returned');default:'scheduled'"`
	OnTimeDelivery        bool       `json:"on_time_delivery" gorm:"default:true"`
	DeliveryRating        float64    `json:"delivery_rating" gorm:"default:5.00"` // 0-5 scale
	CustomerFeedback      string     `json:"customer_feedback" gorm:"type:text"`
	CreatedAt             time.Time  `json:"created_at"`
}

// Maintenance Schedule Filters
type MaintenanceScheduleFilters struct {
	ClientId        uint       `json:"-"`
	Page            int        `json:"page,omitempty"`
	PerPage         int        `json:"per_page,omitempty"`
	Status          string     `json:"status,omitempty"`
	Priority        string     `json:"priority,omitempty"`
	MaintenanceType string     `json:"maintenance_type,omitempty"`
	ClientDeviceId  string     `json:"client_device_id,omitempty"`
	StartDate       *time.Time `json:"start_date,omitempty"`
	EndDate         *time.Time `json:"end_date,omitempty"`
}

// Customer Visit Filters
type CustomerVisitFilters struct {
	ClientId       uint       `json:"-"`
	Page           int        `json:"page,omitempty"`
	PerPage        int        `json:"per_page,omitempty"`
	VisitType      string     `json:"visit_type,omitempty"`
	Status         string     `json:"status,omitempty"`
	DriverId       string     `json:"driver_id,omitempty"`
	ClientDeviceId string     `json:"client_device_id,omitempty"`
	StartDate      *time.Time `json:"start_date,omitempty"`
	EndDate        *time.Time `json:"end_date,omitempty"`
}

// Driver Training Filters
type DriverTrainingFilters struct {
	ClientId     uint       `json:"-"`
	Page         int        `json:"page,omitempty"`
	PerPage      int        `json:"per_page,omitempty"`
	Status       string     `json:"status,omitempty"`
	DriverId     string     `json:"driver_id,omitempty"`
	TrainingType string     `json:"training_type,omitempty"`
	StartDate    *time.Time `json:"start_date,omitempty"`
	EndDate      *time.Time `json:"end_date,omitempty"`
}

// Operating Cost Filters
type OperatingCostFilters struct {
	ClientId       uint       `json:"-"`
	Page           int        `json:"page,omitempty"`
	PerPage        int        `json:"per_page,omitempty"`
	CostType       string     `json:"cost_type,omitempty"`
	ClientDeviceId string     `json:"client_device_id,omitempty"`
	StartDate      *time.Time `json:"start_date,omitempty"`
	EndDate        *time.Time `json:"end_date,omitempty"`
}

// Safety Compliance Filters
type SafetyComplianceFilters struct {
	ClientId       uint       `json:"-"`
	Page           int        `json:"page,omitempty"`
	PerPage        int        `json:"per_page,omitempty"`
	Status         string     `json:"status,omitempty"`
	ComplianceType string     `json:"compliance_type,omitempty"`
	DriverId       string     `json:"driver_id,omitempty"`
	StartDate      *time.Time `json:"start_date,omitempty"`
	EndDate        *time.Time `json:"end_date,omitempty"`
}

// Service Technician Filters
type ServiceTechnicianFilters struct {
	ClientId           uint   `json:"-"`
	Page               int    `json:"page,omitempty"`
	PerPage            int    `json:"per_page,omitempty"`
	AvailabilityStatus string `json:"availability_status,omitempty"`
	Specialization     string `json:"specialization,omitempty"`
}

// Report request structures
type CreateReportRequest struct {
	Name           string          `json:"name" binding:"required"`
	Description    string          `json:"description"`
	Category       string          `json:"category" binding:"required"`
	ReportType     string          `json:"report_type" binding:"required"`
	DefaultFilters json.RawMessage `json:"default_filters,omitempty"`
}

type UpdateReportRequest struct {
	Name           *string         `json:"name,omitempty"`
	Description    *string         `json:"description,omitempty"`
	Category       *string         `json:"category,omitempty"`
	Status         *string         `json:"status,omitempty"`
	DefaultFilters json.RawMessage `json:"default_filters,omitempty"`
}

type CreateScheduledReportRequest struct {
	ReportId    uint            `json:"report_id" binding:"required"`
	Name        string          `json:"name" binding:"required"`
	Description string          `json:"description"`
	Frequency   string          `json:"frequency" binding:"required"`
	CronPattern string          `json:"cron_pattern,omitempty"`
	Timezone    string          `json:"timezone"`
	Filters     json.RawMessage `json:"filters,omitempty"`
	Recipients  json.RawMessage `json:"recipients" binding:"required"`
	Format      string          `json:"format"`
}

type GenerateReportRequest struct {
	ReportId uint            `json:"report_id" binding:"required"`
	Format   string          `json:"format" binding:"required"` // pdf, excel, csv, json
	Filters  json.RawMessage `json:"filters,omitempty"`
}

// Report response structures
type ReportData struct {
	ReportInfo Report         `json:"report_info"`
	Filters    ReportFilters  `json:"filters"`
	Data       interface{}    `json:"data"`
	Summary    interface{}    `json:"summary,omitempty"`
	Metadata   ReportMetadata `json:"metadata"`
}

type ReportMetadata struct {
	GeneratedAt     time.Time `json:"generated_at"`
	GeneratedBy     *uint     `json:"generated_by,omitempty"`
	TotalRecords    int       `json:"total_records"`
	FilteredRecords int       `json:"filtered_records"`
	ExecutionTime   string    `json:"execution_time"`
	Format          string    `json:"format"`
}

// Recipient structure for scheduled reports
type ReportRecipient struct {
	Email string `json:"email"`
	Name  string `json:"name,omitempty"`
}

// Report categories
const (
	ReportCategoryDetail      = "Detail"
	ReportCategorySummary     = "Summary"
	ReportCategoryMaintenance = "Maintenance"
	ReportCategoryBehavior    = "Behavior"
	ReportCategoryManagement  = "Management"
)

// Report types
const (
	ReportTypePositionLog          = "position_log"
	ReportTypePositionLogDriver    = "position_log_driver"
	ReportTypeTripDetail           = "trip_detail"
	ReportTypeTripDetailDelta      = "trip_detail_delta"
	ReportTypeTripMileage          = "trip_mileage"
	ReportTypeStopDetail           = "stop_detail"
	ReportTypeIdleDetail           = "idle_detail"
	ReportTypeSpeedingDetail       = "speeding_detail"
	ReportTypeDoorDetail           = "door_detail"
	ReportTypeExceptionDetail      = "exception_detail"
	ReportTypeDestinationArrival   = "destination_arrival"
	ReportTypeDestinationZoneIn    = "destination_zone_in"
	ReportTypeLastLocation         = "last_location"
	ReportTypeLastLocationHistory  = "last_location_history"
	ReportTypeCargoDetail          = "cargo_detail"
	ReportTypeCargoFuelConsumption = "cargo_fuel_consumption"

	// Summary reports
	ReportTypeTripSummary                = "trip_summary"
	ReportTypeMileageDaySummary          = "mileage_day_summary"
	ReportTypeTripDaySummary             = "trip_day_summary"
	ReportTypeWorkingTimeDaySummary      = "working_time_day_summary"
	ReportTypeFleetProductiveDaySummary  = "fleet_productive_day_summary"
	ReportTypeAfterHourMonthSummary      = "after_hour_month_summary"
	ReportTypeMileageMonthSummary        = "mileage_month_summary"
	ReportTypeMileageAchievingSummary    = "mileage_achieving_summary"
	ReportTypeFuelEstimationMonthSummary = "fuel_estimation_month_summary"
	ReportTypeFuelEstimationHourSummary  = "fuel_estimation_hour_summary"
	ReportTypeVehicleUsageSummary        = "vehicle_usage_summary"
	ReportTypeExceptionSummary           = "exception_summary"
	ReportTypeDriverExceptionSummary     = "driver_exception_summary"
	ReportTypeSpeedingExceptionSummary   = "speeding_exception_summary"
	ReportTypeDriverPerformanceSummary   = "driver_performance_summary"
	ReportTypeVehiclePerformanceSummary  = "vehicle_performance_summary"
	ReportTypeVehicleOnlineSummary       = "vehicle_online_summary"
	ReportTypeCargoDaySummaryFuel        = "cargo_day_summary_fuel"
	ReportTypeOBDReport                  = "obd_report"

	// Maintenance reports
	ReportTypeFuelEstimation          = "fuel_estimation"
	ReportTypeFuelEstimationHour      = "fuel_estimation_hour"
	ReportTypeMaintenanceSchedule     = "maintenance_schedule"
	ReportTypeMaintenanceScheduleHour = "maintenance_schedule_hour"
	ReportTypeMaintenanceDetail       = "maintenance_detail"
	ReportTypeMaintenanceSummary      = "maintenance_summary"

	// Behavior reports
	ReportTypeGeofenceSpeedingDetail = "geofence_speeding_detail"
	ReportTypeFatigueReport          = "fatigue_report"
	ReportTypeViolationStayReport    = "violation_stay_report"
	ReportTypeRouteDeviationReport   = "route_deviation_report"
	ReportTypeRashDriveDetail        = "rash_drive_detail"

	// Management reports
	ReportTypeVehicleAliveReport  = "vehicle_alive_report"
	ReportTypeVehicleAliveTrackit = "vehicle_alive_trackit"
	ReportTypeLoginHistoryDetail  = "login_history_detail"
	ReportTypeLoginHistorySummary = "login_history_summary"
)

// Report statuses
const (
	ReportStatusActive   = "active"
	ReportStatusInactive = "inactive"
)

// Scheduled report statuses
const (
	ScheduledReportStatusActive   = "active"
	ScheduledReportStatusInactive = "inactive"
	ScheduledReportStatusPaused   = "paused"
)

// Report frequencies
const (
	ReportFrequencyDaily   = "daily"
	ReportFrequencyWeekly  = "weekly"
	ReportFrequencyMonthly = "monthly"
	ReportFrequencyCustom  = "custom"
)

// Report formats
const (
	ReportFormatPDF   = "pdf"
	ReportFormatExcel = "excel"
	ReportFormatCSV   = "csv"
	ReportFormatJSON  = "json"
)

// TableName methods for GORM to use correct table names
func (CustomerVisit) TableName() string {
	return "customer_visits"
}

func (MaintenanceSchedule) TableName() string {
	return "maintenance_schedule"
}

func (VehicleHealthMetric) TableName() string {
	return "vehicle_health_metrics"
}

func (OperatingCost) TableName() string {
	return "operating_costs"
}

func (EmissionsData) TableName() string {
	return "emissions_data"
}

func (DriverTraining) TableName() string {
	return "driver_training"
}

func (AssetPerformance) TableName() string {
	return "asset_performance"
}

func (RouteOptimization) TableName() string {
	return "route_optimization"
}

func (SafetyCompliance) TableName() string {
	return "safety_compliance"
}

func (VehicleLifecycle) TableName() string {
	return "vehicle_lifecycle"
}

func (ServiceTechnician) TableName() string {
	return "service_technicians"
}

func (ConstructionEquipment) TableName() string {
	return "construction_equipment"
}

func (SalesTerritory) TableName() string {
	return "sales_territories"
}

func (DeliveryPerformance) TableName() string {
	return "delivery_performance"
}
