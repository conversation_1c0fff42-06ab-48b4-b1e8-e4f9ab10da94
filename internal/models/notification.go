package models

import "time"

// PushNotificationRequest represents the request structure for sending push notifications to a specific FCM token
type PushNotificationRequest struct {
	To             string                 `json:"to" binding:"required"`
	Title          string                 `json:"title" binding:"required"`
	Body           string                 `json:"body" binding:"required"`
	Type           string                 `json:"type" binding:"required"`
	Data           map[string]interface{} `json:"data,omitempty"`
	ClientDeviceID *uint                  `json:"client_device_id,omitempty"`
	Timestamp      *string                `json:"timestamp,omitempty"`
}

// ClientNotificationRequest represents the request structure for sending notifications to specific clients
type ClientNotificationRequest struct {
	ClientIDs      []uint                 `json:"client_ids,omitempty"`       // If empty, send to all clients
	ClientDeviceID *uint                  `json:"client_device_id,omitempty"` // For device-specific notifications
	Title          string                 `json:"title" binding:"required"`
	Body           string                 `json:"body" binding:"required"`
	Type           string                 `json:"type" binding:"required"`
	Data           map[string]interface{} `json:"data,omitempty"`
	Timestamp      *string                `json:"timestamp,omitempty"`
}

// BulkPushNotificationRequest represents the request structure for sending bulk push notifications
type BulkPushNotificationRequest struct {
	Tokens []string               `json:"tokens" binding:"required"`
	Title  string                 `json:"title" binding:"required"`
	Body   string                 `json:"body" binding:"required"`
	Type   string                 `json:"type" binding:"required"`
	Data   map[string]interface{} `json:"data,omitempty"`
}

// PushNotificationResponse represents the response structure for push notifications
type PushNotificationResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Success int `json:"success"`
		Failure int `json:"failure"`
		Results []struct {
			MessageID string `json:"message_id,omitempty"`
			Error     string `json:"error,omitempty"`
		} `json:"results"`
	} `json:"data,omitempty"`
}

// NotificationLog represents a log entry for sent notifications
type NotificationLog struct {
	Id                uint      `json:"id" gorm:"primaryKey"`
	ClientId          uint      `json:"client_id" gorm:"index"`
	UserId            *uint     `json:"user_id" gorm:"index"`
	ClientDeviceId    *uint     `json:"client_device_id" gorm:"index"`
	FirebaseToken     string    `json:"firebase_token"`
	Title             string    `json:"title"`
	Body              string    `json:"body"`
	Type              string    `json:"type"`
	Data              string    `json:"data" gorm:"type:text"`
	Status            string    `json:"status" gorm:"default:'sent'"` // sent, failed, pending
	FirebaseMessageId *string   `json:"firebase_message_id"`
	ErrorMessage      *string   `json:"error_message"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`

	// Relations
	Client       Client        `json:"client"`
	User         *User         `json:"user,omitempty"`
	ClientDevice *ClientDevice `json:"client_device,omitempty"`
}

// CreateNotificationLogRequest represents the request structure for creating notification logs
type CreateNotificationLogRequest struct {
	ClientId          uint    `json:"client_id" binding:"required"`
	UserId            *uint   `json:"user_id,omitempty"`
	ClientDeviceId    *uint   `json:"client_device_id,omitempty"`
	FirebaseToken     string  `json:"firebase_token" binding:"required"`
	Title             string  `json:"title" binding:"required"`
	Body              string  `json:"body" binding:"required"`
	Type              string  `json:"type" binding:"required"`
	Data              string  `json:"data,omitempty"`
	Status            string  `json:"status,omitempty"`
	FirebaseMessageId *string `json:"firebase_message_id,omitempty"`
	ErrorMessage      *string `json:"error_message,omitempty"`
}
