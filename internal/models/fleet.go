package models

import (
	"encoding/json"
	"time"
)

type Fleet struct {
	Id           uint             `json:"id" gorm:"primaryKey"`
	ParentId     *uint            `json:"parent_id,omitempty"`
	ClientId     uint             `json:"client_id"`
	Name         string           `json:"name"`
	Description  *string          `json:"description" gorm:"type:text"`
	MaxSpeed     *float64         `json:"max_speed"`
	ShutdownStartHour *int             `json:"shutdown_start_hour" gorm:"default:22"` // 22:00
	ShutdownEndHour   *int             `json:"shutdown_end_hour" gorm:"default:6"`   // 06:00
	Emails       *json.RawMessage `json:"emails" gorm:"type:json"`
	PhoneNumbers *json.RawMessage `json:"phone_numbers" gorm:"type:json"`
	CreatedAt    time.Time        `json:"created_at"`
	UpdatedAt    time.Time        `json:"updated_at"`
}
type FleetRequest struct {
	ParentId     *uint            `json:"parent_id,omitempty"`
	Name         string           `json:"name" binding:"required"`
	Description  *string          `json:"description,omitempty"`
	MaxSpeed     *float64         `json:"max_speed,omitempty"`
	ShutdownStartHour *int             `json:"shutdown_start_hour,omitempty"`
	ShutdownEndHour   *int             `json:"shutdown_end_hour,omitempty"`
	Emails       *json.RawMessage `json:"emails,omitempty"`
	PhoneNumbers *json.RawMessage `json:"phone_numbers,omitempty"`
}
