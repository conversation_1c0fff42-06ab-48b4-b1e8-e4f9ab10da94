package models

import (
	"encoding/json"
	"time"
)

// AlertType represents different types of alerts
type AlertType string

const (
	AlertTypeSpeed       AlertType = "speed_alert"
	AlertTypeShutdown    AlertType = "shutdown_time_alert"
	AlertTypeTowing      AlertType = "towing_event"
	AlertTypeImpact      AlertType = "impact_detection"
	AlertTypeGeofence    AlertType = "geofence_event"
	AlertTypeMaintenance AlertType = "maintenance_alert"
	AlertTypeBattery     AlertType = "battery_alert"
	AlertTypeSOS         AlertType = "sos_alert"
)

// NotificationChannel represents different notification channels
type NotificationChannel string

const (
	NotificationChannelEmail    NotificationChannel = "email"
	NotificationChannelSMS      NotificationChannel = "sms"
	NotificationChannelWhatsApp NotificationChannel = "whatsapp"
	NotificationChannelSlack    NotificationChannel = "slack"
	NotificationChannelPush     NotificationChannel = "push"
)

// AlertPreference represents a client's preference for a specific alert type
type AlertPreference struct {
	Id        uint            `json:"id" gorm:"primaryKey"`
	ClientId  uint            `json:"client_id" gorm:"index"`
	Client    Client          `json:"client"`
	AlertType AlertType       `json:"alert_type" gorm:"type:varchar(50)"`
	Enabled   bool            `json:"enabled" gorm:"default:true"`
	Channels  json.RawMessage `json:"channels" gorm:"type:json"`                         // Array of NotificationChannel
	Priority  string          `json:"priority" gorm:"type:varchar(20);default:'normal'"` // low, normal, high, critical
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
}

// AlertPreferenceRequest represents the request structure for creating/updating alert preferences
type AlertPreferenceRequest struct {
	AlertType AlertType       `json:"alert_type" binding:"required"`
	Enabled   bool            `json:"enabled"`
	Channels  json.RawMessage `json:"channels"` // Array of NotificationChannel
	Priority  string          `json:"priority"`
}

// FleetAlertPreference represents fleet-level alert preferences
type FleetAlertPreference struct {
	Id        uint            `json:"id" gorm:"primaryKey"`
	FleetId   uint            `json:"fleet_id" gorm:"index"`
	Fleet     Fleet           `json:"fleet"`
	AlertType AlertType       `json:"alert_type" gorm:"type:varchar(50)"`
	Enabled   bool            `json:"enabled" gorm:"default:true"`
	Channels  json.RawMessage `json:"channels" gorm:"type:json"` // Array of NotificationChannel
	Priority  string          `json:"priority" gorm:"type:varchar(20);default:'normal'"`
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
}

// DeviceAlertPreference represents device-level alert preferences
type DeviceAlertPreference struct {
	Id             uint            `json:"id" gorm:"primaryKey"`
	ClientDeviceId uint            `json:"client_device_id" gorm:"index"`
	ClientDevice   ClientDevice    `json:"client_device"`
	AlertType      AlertType       `json:"alert_type" gorm:"type:varchar(50)"`
	Enabled        bool            `json:"enabled" gorm:"default:true"`
	Channels       json.RawMessage `json:"channels" gorm:"type:json"` // Array of NotificationChannel
	Priority       string          `json:"priority" gorm:"type:varchar(20);default:'normal'"`
	CreatedAt      time.Time       `json:"created_at"`
	UpdatedAt      time.Time       `json:"updated_at"`
}

// GetChannelsAsSlice returns the channels as a slice of NotificationChannel
func (ap *AlertPreference) GetChannelsAsSlice() []NotificationChannel {
	if ap.Channels == nil {
		return []NotificationChannel{}
	}

	var channels []NotificationChannel
	if err := json.Unmarshal(ap.Channels, &channels); err != nil {
		return []NotificationChannel{}
	}
	return channels
}

// SetChannelsFromSlice sets the channels from a slice of NotificationChannel
func (ap *AlertPreference) SetChannelsFromSlice(channels []NotificationChannel) error {
	data, err := json.Marshal(channels)
	if err != nil {
		return err
	}
	ap.Channels = data
	return nil
}

// GetChannelsAsSlice returns the channels as a slice of NotificationChannel
func (fap *FleetAlertPreference) GetChannelsAsSlice() []NotificationChannel {
	if fap.Channels == nil {
		return []NotificationChannel{}
	}

	var channels []NotificationChannel
	if err := json.Unmarshal(fap.Channels, &channels); err != nil {
		return []NotificationChannel{}
	}
	return channels
}

// SetChannelsFromSlice sets the channels from a slice of NotificationChannel
func (fap *FleetAlertPreference) SetChannelsFromSlice(channels []NotificationChannel) error {
	data, err := json.Marshal(channels)
	if err != nil {
		return err
	}
	fap.Channels = data
	return nil
}

// GetChannelsAsSlice returns the channels as a slice of NotificationChannel
func (dap *DeviceAlertPreference) GetChannelsAsSlice() []NotificationChannel {
	if dap.Channels == nil {
		return []NotificationChannel{}
	}

	var channels []NotificationChannel
	if err := json.Unmarshal(dap.Channels, &channels); err != nil {
		return []NotificationChannel{}
	}
	return channels
}

// SetChannelsFromSlice sets the channels from a slice of NotificationChannel
func (dap *DeviceAlertPreference) SetChannelsFromSlice(channels []NotificationChannel) error {
	data, err := json.Marshal(channels)
	if err != nil {
		return err
	}
	dap.Channels = data
	return nil
}
