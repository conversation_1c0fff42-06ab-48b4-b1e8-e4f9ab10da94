package models

import (
	"time"
)

// SupportTicket represents a customer support ticket
type SupportTicket struct {
	Id             uint          `json:"id" gorm:"primaryKey"`
	ClientId       uint          `json:"client_id" gorm:"index"`
	Client         Client        `json:"client"`
	CreatedById    uint          `json:"created_by_id" gorm:"index"`
	CreatedBy      User          `json:"created_by"`
	AssignedToId   *uint         `json:"assigned_to_id" gorm:"index"`
	AssignedTo     *User         `json:"assigned_to,omitempty"`
	ClientDeviceId *uint         `json:"client_device_id" gorm:"index"`
	ClientDevice   *ClientDevice `json:"client_device,omitempty"`
	Department     string        `json:"department" gorm:"type:enum('billing','technical','general','feature_request','bug_report');default:'general'"`
	Priority       string        `json:"priority" gorm:"type:enum('low','medium','high','urgent');default:'medium'"`
	Status         string        `json:"status" gorm:"type:enum('open','assigned','in_progress','waiting_for_customer','resolved','closed');default:'open'"`
	Subject        string        `json:"subject" gorm:"type:varchar(255);not null"`
	Description    string        `json:"description" gorm:"type:text;not null"`
	Resolution     *string       `json:"resolution" gorm:"type:text"`
	CreatedAt      time.Time     `json:"created_at"`
	UpdatedAt      time.Time     `json:"updated_at"`
	ResolvedAt     *time.Time    `json:"resolved_at"`
	ClosedAt       *time.Time    `json:"closed_at"`
}

// SupportTicketReply represents a reply to a support ticket
type SupportTicketReply struct {
	Id          uint          `json:"id" gorm:"primaryKey"`
	TicketId    uint          `json:"ticket_id" gorm:"index"`
	Ticket      SupportTicket `json:"ticket"`
	CreatedById uint          `json:"created_by_id" gorm:"index"`
	CreatedBy   User          `json:"created_by"`
	IsInternal  bool          `json:"is_internal" gorm:"default:false"` // Internal notes not visible to client
	Message     string        `json:"message" gorm:"type:text;not null"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
}

// CreateSupportTicketRequest represents the request to create a new ticket (backend/admin)
type CreateSupportTicketRequest struct {
	ClientId       uint   `json:"client_id" binding:"required"`
	CreatedById    uint   `json:"created_by_id" binding:"required"`
	ClientDeviceId *uint  `json:"client_device_id"`
	Department     string `json:"department" binding:"required"`
	Priority       string `json:"priority"`
	Subject        string `json:"subject" binding:"required"`
	Description    string `json:"description" binding:"required"`
}

// CreateSupportTicketClientRequest represents the request to create a new ticket (frontend/client)
type CreateSupportTicketClientRequest struct {
	ClientDeviceId *uint  `json:"client_device_id"`
	Department     string `json:"department" binding:"required"`
	Priority       string `json:"priority"`
	Subject        string `json:"subject" binding:"required"`
	Description    string `json:"description" binding:"required"`
}

// UpdateSupportTicketRequest represents the request to update a ticket
type UpdateSupportTicketRequest struct {
	AssignedToId *uint   `json:"assigned_to_id"`
	Priority     *string `json:"priority"`
	Status       *string `json:"status"`
	Subject      *string `json:"subject"`
	Description  *string `json:"description"`
	Resolution   *string `json:"resolution"`
}

// CreateSupportTicketReplyRequest represents the request to create a reply
type CreateSupportTicketReplyRequest struct {
	TicketId   uint   `json:"ticket_id"`
	IsInternal bool   `json:"is_internal"`
	Message    string `json:"message" binding:"required"`
}

// ChangeSupportTicketStatusRequest represents the request to change ticket status
type ChangeSupportTicketStatusRequest struct {
	Status string `json:"status" binding:"required,oneof=open assigned in_progress waiting_for_customer resolved closed"`
}

// AssignSupportTicketRequest represents the request to assign a ticket to a user
type AssignSupportTicketRequest struct {
	AssignedToId uint `json:"assigned_to_id" binding:"required"`
}

// SupportTicketFilters represents filters for ticket queries
type SupportTicketFilters struct {
	ClientId       uint   `form:"client_id"`
	Search         string `form:"s"`
	Department     string `form:"department"`
	Priority       string `form:"priority"`
	Status         string `form:"status"`
	AssignedToId   *uint  `form:"assigned_to_id"`
	ClientDeviceId *uint  `form:"client_device_id"`
	CreatedById    *uint  `form:"created_by_id"`
	Page           int    `form:"page" binding:"omitempty,min=1"`
	PerPage        int    `form:"per_page" binding:"omitempty,min=1,max=100"`
}

// SupportTicketStats represents ticket statistics
type SupportTicketStats struct {
	TotalTickets        int64 `json:"total_tickets"`
	OpenTickets         int64 `json:"open_tickets"`
	AssignedTickets     int64 `json:"assigned_tickets"`
	InProgressTickets   int64 `json:"in_progress_tickets"`
	ResolvedTickets     int64 `json:"resolved_tickets"`
	ClosedTickets       int64 `json:"closed_tickets"`
	UrgentTickets       int64 `json:"urgent_tickets"`
	HighPriorityTickets int64 `json:"high_priority_tickets"`
}
