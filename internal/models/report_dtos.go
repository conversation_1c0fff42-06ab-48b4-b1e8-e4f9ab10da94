package models

// ============================================================================
// CORE GPS & TRACKING REPORT DTOs
// ============================================================================

// PositionLogDTO represents GPS position data with headers
type PositionLogDTO struct {
	Headers []string         `json:"headers"`
	Data    []PositionLogRow `json:"data"`
}

type PositionLogRow struct {
	Timestamp    string  `json:"timestamp" csv:"Timestamp"`
	Name         string  `json:"name" csv:"Name"`
	DriverName   string  `json:"driver_name" csv:"Driver Name"`
	Latitude     float64 `json:"latitude" csv:"Latitude"`
	Longitude    float64 `json:"longitude" csv:"Longitude"`
	LocationName string  `json:"location_name" csv:"Location Name"`
	Speed        float64 `json:"speed" csv:"Speed (km/h)"`
	Direction    string  `json:"direction" csv:"Direction"`
	EngineStatus string  `json:"engine_status" csv:"Engine Status"`
}

// ============================================================================
// TRIP & MOVEMENT REPORT DTOs
// ============================================================================

// TripDetailDTO represents detailed trip information
type TripDetailDTO struct {
	Headers []string        `json:"headers"`
	Data    []TripDetailRow `json:"data"`
}

type TripDetailRow struct {
	TripID        uint    `json:"trip_id" csv:"Trip ID"`
	DeviceName    string  `json:"device_name" csv:"Device Name"`
	PlateNumber   string  `json:"plate_number" csv:"Plate Number"`
	DriverName    string  `json:"driver_name" csv:"Driver Name"`
	StartTime     string  `json:"start_time" csv:"Start Time"`
	EndTime       string  `json:"end_time" csv:"End Time"`
	Duration      string  `json:"duration" csv:"Duration"`
	Distance      float64 `json:"distance" csv:"Distance (km)"`
	AvgSpeed      float64 `json:"avg_speed" csv:"Avg Speed (km/h)"`
	MaxSpeed      float64 `json:"max_speed" csv:"Max Speed (km/h)"`
	StartLocation string  `json:"start_location" csv:"Start Location"`
	EndLocation   string  `json:"end_location" csv:"End Location"`
	FuelConsumed  float64 `json:"fuel_consumed" csv:"Fuel Consumed (L)"`
	FuelCost      float64 `json:"fuel_cost" csv:"Fuel Cost"`
	Status        string  `json:"status" csv:"Status"`
}

// TripSummaryDTO represents trip summary statistics
type TripSummaryDTO struct {
	Headers []string         `json:"headers"`
	Data    []TripSummaryRow `json:"data"`
	Summary TripSummaryStats `json:"summary"`
}

type TripSummaryRow struct {
	Date          string  `json:"date" csv:"Date"`
	DeviceName    string  `json:"device_name" csv:"Device Name"`
	PlateNumber   string  `json:"plate_number" csv:"Plate Number"`
	DriverName    string  `json:"driver_name" csv:"Driver Name"`
	TotalTrips    int     `json:"total_trips" csv:"Total Trips"`
	TotalDistance float64 `json:"total_distance" csv:"Total Distance (km)"`
	TotalDuration string  `json:"total_duration" csv:"Total Duration"`
	AvgSpeed      float64 `json:"avg_speed" csv:"Avg Speed (km/h)"`
	FuelConsumed  float64 `json:"fuel_consumed" csv:"Fuel Consumed (L)"`
	FuelCost      float64 `json:"fuel_cost" csv:"Fuel Cost"`
	Efficiency    float64 `json:"efficiency" csv:"Efficiency (km/L)"`
}

type TripSummaryStats struct {
	TotalTrips    int     `json:"total_trips"`
	TotalDistance float64 `json:"total_distance"`
	TotalDuration string  `json:"total_duration"`
	TotalFuelCost float64 `json:"total_fuel_cost"`
	AvgEfficiency float64 `json:"avg_efficiency"`
}

// ============================================================================
// DRIVER BEHAVIOR & SAFETY REPORT DTOs
// ============================================================================

// DriverSafetyScorecardDTO represents driver safety scoring
type DriverSafetyScorecardDTO struct {
	Headers []string               `json:"headers"`
	Data    []DriverSafetyScoreRow `json:"data"`
	Summary DriverSafetySummary    `json:"summary"`
}

type DriverSafetyScoreRow struct {
	DriverName        string  `json:"driver_name" csv:"Driver Name"`
	DeviceName        string  `json:"device_name" csv:"Device Name"`
	PlateNumber       string  `json:"plate_number" csv:"Plate Number"`
	SafetyScore       float64 `json:"safety_score" csv:"Safety Score"`
	SpeedingEvents    int     `json:"speeding_events" csv:"Speeding Events"`
	HarshBraking      int     `json:"harsh_braking" csv:"Harsh Braking"`
	HarshAcceleration int     `json:"harsh_acceleration" csv:"Harsh Acceleration"`
	HarshCornering    int     `json:"harsh_cornering" csv:"Harsh Cornering"`
	IdleTime          string  `json:"idle_time" csv:"Idle Time"`
	TotalTrips        int     `json:"total_trips" csv:"Total Trips"`
	TotalDistance     float64 `json:"total_distance" csv:"Total Distance (km)"`
	AvgSpeed          float64 `json:"avg_speed" csv:"Avg Speed (km/h)"`
	RiskLevel         string  `json:"risk_level" csv:"Risk Level"`
}

type DriverSafetySummary struct {
	TotalDrivers     int     `json:"total_drivers"`
	AvgSafetyScore   float64 `json:"avg_safety_score"`
	HighRiskDrivers  int     `json:"high_risk_drivers"`
	TotalViolations  int     `json:"total_violations"`
	TotalSpeeding    int     `json:"total_speeding"`
	TotalHarshEvents int     `json:"total_harsh_events"`
}

// SpeedingViolationsDTO represents speeding violation details
type SpeedingViolationsDTO struct {
	Headers []string               `json:"headers"`
	Data    []SpeedingViolationRow `json:"data"`
}

type SpeedingViolationRow struct {
	Timestamp     string  `json:"timestamp" csv:"Timestamp"`
	DeviceName    string  `json:"device_name" csv:"Device Name"`
	PlateNumber   string  `json:"plate_number" csv:"Plate Number"`
	DriverName    string  `json:"driver_name" csv:"Driver Name"`
	Location      string  `json:"location" csv:"Location"`
	SpeedLimit    float64 `json:"speed_limit" csv:"Speed Limit (km/h)"`
	ActualSpeed   float64 `json:"actual_speed" csv:"Actual Speed (km/h)"`
	SpeedExceeded float64 `json:"speed_exceeded" csv:"Speed Exceeded (km/h)"`
	Duration      string  `json:"duration" csv:"Duration"`
	Severity      string  `json:"severity" csv:"Severity"`
	RiskLevel     string  `json:"risk_level" csv:"Risk Level"`
}

// ============================================================================
// GEOFENCE & LOCATION REPORT DTOs
// ============================================================================

// GeofenceActivityDTO represents geofence entry/exit events
type GeofenceActivityDTO struct {
	Headers []string              `json:"headers"`
	Data    []GeofenceActivityRow `json:"data"`
}

type GeofenceActivityRow struct {
	Timestamp    string `json:"timestamp" csv:"Timestamp"`
	DeviceName   string `json:"device_name" csv:"Device Name"`
	PlateNumber  string `json:"plate_number" csv:"Plate Number"`
	DriverName   string `json:"driver_name" csv:"Driver Name"`
	GeofenceName string `json:"geofence_name" csv:"Geofence Name"`
	EventType    string `json:"event_type" csv:"Event Type"`
	Location     string `json:"location" csv:"Location"`
	Duration     string `json:"duration" csv:"Duration"`
	Authorized   bool   `json:"authorized" csv:"Authorized"`
	Purpose      string `json:"purpose" csv:"Purpose"`
}

// ============================================================================
// COST & EFFICIENCY REPORT DTOs
// ============================================================================

// FuelConsumptionDTO represents fuel consumption analysis
type FuelConsumptionDTO struct {
	Headers []string               `json:"headers"`
	Data    []FuelConsumptionRow   `json:"data"`
	Summary FuelConsumptionSummary `json:"summary"`
}

type FuelConsumptionRow struct {
	Date          string  `json:"date" csv:"Date"`
	DeviceName    string  `json:"device_name" csv:"Device Name"`
	PlateNumber   string  `json:"plate_number" csv:"Plate Number"`
	DriverName    string  `json:"driver_name" csv:"Driver Name"`
	FuelConsumed  float64 `json:"fuel_consumed" csv:"Fuel Consumed (L)"`
	FuelCost      float64 `json:"fuel_cost" csv:"Fuel Cost"`
	Distance      float64 `json:"distance" csv:"Distance (km)"`
	Efficiency    float64 `json:"efficiency" csv:"Efficiency (km/L)"`
	AvgSpeed      float64 `json:"avg_speed" csv:"Avg Speed (km/h)"`
	IdleTime      string  `json:"idle_time" csv:"Idle Time"`
	IdleFuelWaste float64 `json:"idle_fuel_waste" csv:"Idle Fuel Waste (L)"`
	CostPerKm     float64 `json:"cost_per_km" csv:"Cost per km"`
}

type FuelConsumptionSummary struct {
	TotalFuelConsumed float64 `json:"total_fuel_consumed"`
	TotalFuelCost     float64 `json:"total_fuel_cost"`
	TotalDistance     float64 `json:"total_distance"`
	AvgEfficiency     float64 `json:"avg_efficiency"`
	TotalIdleWaste    float64 `json:"total_idle_waste"`
	AvgCostPerKm      float64 `json:"avg_cost_per_km"`
}

// ============================================================================
// MAINTENANCE & VEHICLE HEALTH REPORT DTOs
// ============================================================================

// MaintenanceScheduleDTO represents maintenance scheduling
type MaintenanceScheduleDTO struct {
	Headers []string                 `json:"headers"`
	Data    []MaintenanceScheduleRow `json:"data"`
}

type MaintenanceScheduleRow struct {
	DeviceName         string  `json:"device_name" csv:"Device Name"`
	PlateNumber        string  `json:"plate_number" csv:"Plate Number"`
	MaintenanceType    string  `json:"maintenance_type" csv:"Maintenance Type"`
	DueDate            string  `json:"due_date" csv:"Due Date"`
	DueMileage         float64 `json:"due_mileage" csv:"Due Mileage (km)"`
	CurrentMileage     float64 `json:"current_mileage" csv:"Current Mileage (km)"`
	MileageRemaining   float64 `json:"mileage_remaining" csv:"Mileage Remaining (km)"`
	EstimatedCost      float64 `json:"estimated_cost" csv:"Estimated Cost"`
	Priority           string  `json:"priority" csv:"Priority"`
	Status             string  `json:"status" csv:"Status"`
	LastServiceDate    string  `json:"last_service_date" csv:"Last Service Date"`
	LastServiceMileage float64 `json:"last_service_mileage" csv:"Last Service Mileage (km)"`
}

// ============================================================================
// COMPLIANCE & SECURITY REPORT DTOs
// ============================================================================

// HoursOfServiceDTO represents driver hours of service compliance
type HoursOfServiceDTO struct {
	Headers []string              `json:"headers"`
	Data    []HoursOfServiceRow   `json:"data"`
	Summary HoursOfServiceSummary `json:"summary"`
}

type HoursOfServiceRow struct {
	Date          string `json:"date" csv:"Date"`
	DriverName    string `json:"driver_name" csv:"Driver Name"`
	DeviceName    string `json:"device_name" csv:"Device Name"`
	PlateNumber   string `json:"plate_number" csv:"Plate Number"`
	TotalHours    string `json:"total_hours" csv:"Total Hours"`
	DrivingHours  string `json:"driving_hours" csv:"Driving Hours"`
	RestHours     string `json:"rest_hours" csv:"Rest Hours"`
	OvertimeHours string `json:"overtime_hours" csv:"Overtime Hours"`
	Compliance    string `json:"compliance" csv:"Compliance"`
	Violations    int    `json:"violations" csv:"Violations"`
	RiskLevel     string `json:"risk_level" csv:"Risk Level"`
}

type HoursOfServiceSummary struct {
	TotalDrivers     int     `json:"total_drivers"`
	CompliantDrivers int     `json:"compliant_drivers"`
	TotalViolations  int     `json:"total_violations"`
	AvgHoursPerDay   float64 `json:"avg_hours_per_day"`
	OvertimeHours    string  `json:"overtime_hours"`
}

// ============================================================================
// EXECUTIVE & MANAGEMENT REPORT DTOs
// ============================================================================

// ExecutiveFleetSummaryDTO represents high-level fleet KPIs
type ExecutiveFleetSummaryDTO struct {
	Headers []string                   `json:"headers"`
	Data    []ExecutiveFleetSummaryRow `json:"data"`
	Summary ExecutiveFleetSummaryStats `json:"summary"`
}

type ExecutiveFleetSummaryRow struct {
	Month           string  `json:"month" csv:"Month"`
	TotalVehicles   int     `json:"total_vehicles" csv:"Total Vehicles"`
	ActiveVehicles  int     `json:"active_vehicles" csv:"Active Vehicles"`
	TotalTrips      int     `json:"total_trips" csv:"Total Trips"`
	TotalDistance   float64 `json:"total_distance" csv:"Total Distance (km)"`
	TotalFuelCost   float64 `json:"total_fuel_cost" csv:"Total Fuel Cost"`
	AvgSafetyScore  float64 `json:"avg_safety_score" csv:"Avg Safety Score"`
	UtilizationRate float64 `json:"utilization_rate" csv:"Utilization Rate (%)"`
	Efficiency      float64 `json:"efficiency" csv:"Efficiency (km/L)"`
	CostPerKm       float64 `json:"cost_per_km" csv:"Cost per km"`
	ROI             float64 `json:"roi" csv:"ROI (%)"`
}

type ExecutiveFleetSummaryStats struct {
	TotalVehicles  int     `json:"total_vehicles"`
	TotalTrips     int     `json:"total_trips"`
	TotalDistance  float64 `json:"total_distance"`
	TotalFuelCost  float64 `json:"total_fuel_cost"`
	AvgSafetyScore float64 `json:"avg_safety_score"`
	AvgUtilization float64 `json:"avg_utilization"`
	AvgEfficiency  float64 `json:"avg_efficiency"`
	AvgCostPerKm   float64 `json:"avg_cost_per_km"`
	TotalROI       float64 `json:"total_roi"`
}

// ============================================================================
// OPERATIONAL REPORT DTOs
// ============================================================================

// VehicleUtilizationDTO represents vehicle utilization analysis
type VehicleUtilizationDTO struct {
	Headers []string                `json:"headers"`
	Data    []VehicleUtilizationRow `json:"data"`
	Summary VehicleUtilizationStats `json:"summary"`
}

type VehicleUtilizationRow struct {
	DeviceName        string  `json:"device_name" csv:"Device Name"`
	PlateNumber       string  `json:"plate_number" csv:"Plate Number"`
	DriverName        string  `json:"driver_name" csv:"Driver Name"`
	TotalHours        string  `json:"total_hours" csv:"Total Hours"`
	ActiveHours       string  `json:"active_hours" csv:"Active Hours"`
	IdleHours         string  `json:"idle_hours" csv:"Idle Hours"`
	UtilizationRate   float64 `json:"utilization_rate" csv:"Utilization Rate (%)"`
	TotalTrips        int     `json:"total_trips" csv:"Total Trips"`
	TotalDistance     float64 `json:"total_distance" csv:"Total Distance (km)"`
	AvgSpeed          float64 `json:"avg_speed" csv:"Avg Speed (km/h)"`
	Efficiency        float64 `json:"efficiency" csv:"Efficiency (km/L)"`
	Status            string  `json:"status" csv:"Status"`
	IdleTimePercent   float64 `json:"idle_time_percent" csv:"Idle Time (%)"`
	ProductivityScore float64 `json:"productivity_score" csv:"Productivity Score"`
}

type VehicleUtilizationStats struct {
	TotalVehicles    int     `json:"total_vehicles"`
	AvgUtilization   float64 `json:"avg_utilization"`
	TotalActiveHours string  `json:"total_active_hours"`
	TotalIdleHours   string  `json:"total_idle_hours"`
	TotalTrips       int     `json:"total_trips"`
	TotalDistance    float64 `json:"total_distance"`
	AvgEfficiency    float64 `json:"avg_efficiency"`
	HighUtilization  int     `json:"high_utilization"`
	LowUtilization   int     `json:"low_utilization"`
}

// ============================================================================
// CUSTOM BUSINESS REPORT DTOs
// ============================================================================

// DeliveryPerformanceDTO represents delivery performance metrics
type DeliveryPerformanceDTO struct {
	Headers []string                   `json:"headers"`
	Data    []DeliveryPerformanceRow   `json:"data"`
	Summary DeliveryPerformanceSummary `json:"summary"`
}

type DeliveryPerformanceRow struct {
	Date             string  `json:"date" csv:"Date"`
	DriverName       string  `json:"driver_name" csv:"Driver Name"`
	DeviceName       string  `json:"device_name" csv:"Device Name"`
	PlateNumber      string  `json:"plate_number" csv:"Plate Number"`
	TotalDeliveries  int     `json:"total_deliveries" csv:"Total Deliveries"`
	OnTimeDeliveries int     `json:"on_time_deliveries" csv:"On-Time Deliveries"`
	LateDeliveries   int     `json:"late_deliveries" csv:"Late Deliveries"`
	OnTimeRate       float64 `json:"on_time_rate" csv:"On-Time Rate (%)"`
	TotalDistance    float64 `json:"total_distance" csv:"Total Distance (km)"`
	AvgDeliveryTime  string  `json:"avg_delivery_time" csv:"Avg Delivery Time"`
	CustomerRating   float64 `json:"customer_rating" csv:"Customer Rating"`
	Efficiency       float64 `json:"efficiency" csv:"Efficiency Score"`
}

type DeliveryPerformanceSummary struct {
	TotalDeliveries   int     `json:"total_deliveries"`
	OnTimeDeliveries  int     `json:"on_time_deliveries"`
	OverallOnTimeRate float64 `json:"overall_on_time_rate"`
	AvgCustomerRating float64 `json:"avg_customer_rating"`
	AvgEfficiency     float64 `json:"avg_efficiency"`
}

// ============================================================================
// ADDITIONAL SPECIALIZED REPORT DTOs
// ============================================================================

// FuelEstimationDTO represents fuel consumption estimation
type FuelEstimationDTO struct {
	Headers []string              `json:"headers"`
	Data    []FuelEstimationRow   `json:"data"`
	Summary FuelEstimationSummary `json:"summary"`
}

type FuelEstimationRow struct {
	Month                    string  `json:"month" csv:"Month"`
	DeviceName               string  `json:"device_name" csv:"Device Name"`
	PlateNumber              string  `json:"plate_number" csv:"Plate Number"`
	TotalDistance            float64 `json:"total_distance" csv:"Total Distance (km)"`
	RunningHours             float64 `json:"running_hours" csv:"Running Hours"`
	AvgSpeed                 float64 `json:"avg_speed" csv:"Avg Speed (km/h)"`
	TotalTrips               int     `json:"total_trips" csv:"Total Trips"`
	EstimatedFuelConsumption float64 `json:"estimated_fuel_consumption" csv:"Estimated Fuel (L)"`
	EstimatedFuelCost        float64 `json:"estimated_fuel_cost" csv:"Estimated Cost"`
	EfficiencyKmPerHour      float64 `json:"efficiency_km_per_hour" csv:"Efficiency (km/h)"`
	FuelEfficiencyLph        float64 `json:"fuel_efficiency_lph" csv:"Fuel Efficiency (L/h)"`
}

type FuelEstimationSummary struct {
	TotalDistance      float64 `json:"total_distance"`
	TotalRunningHours  float64 `json:"total_running_hours"`
	TotalEstimatedFuel float64 `json:"total_estimated_fuel"`
	TotalEstimatedCost float64 `json:"total_estimated_cost"`
	AvgEfficiency      float64 `json:"avg_efficiency"`
	AvgFuelEfficiency  float64 `json:"avg_fuel_efficiency"`
}

// DriverBehaviorAnalysisDTO represents comprehensive driver behavior analysis
type DriverBehaviorAnalysisDTO struct {
	Headers []string                      `json:"headers"`
	Data    []DriverBehaviorAnalysisRow   `json:"data"`
	Summary DriverBehaviorAnalysisSummary `json:"summary"`
}

type DriverBehaviorAnalysisRow struct {
	Month                string  `json:"month" csv:"Month"`
	DriverName           string  `json:"driver_name" csv:"Driver Name"`
	DeviceName           string  `json:"device_name" csv:"Device Name"`
	PlateNumber          string  `json:"plate_number" csv:"Plate Number"`
	BehaviorScore        float64 `json:"behavior_score" csv:"Behavior Score"`
	SpeedingEvents       int     `json:"speeding_events" csv:"Speeding Events"`
	HarshBrakingEvents   int     `json:"harsh_braking_events" csv:"Harsh Braking"`
	HarshAccelEvents     int     `json:"harsh_accel_events" csv:"Harsh Acceleration"`
	HarshCorneringEvents int     `json:"harsh_cornering_events" csv:"Harsh Cornering"`
	IdleTimePercentage   float64 `json:"idle_time_percentage" csv:"Idle Time (%)"`
	FuelEfficiency       float64 `json:"fuel_efficiency" csv:"Fuel Efficiency (km/L)"`
	SafetyViolations     int     `json:"safety_violations" csv:"Safety Violations"`
	RiskLevel            string  `json:"risk_level" csv:"Risk Level"`
	ImprovementAreas     string  `json:"improvement_areas" csv:"Improvement Areas"`
}

type DriverBehaviorAnalysisSummary struct {
	TotalDrivers      int     `json:"total_drivers"`
	AvgBehaviorScore  float64 `json:"avg_behavior_score"`
	HighRiskDrivers   int     `json:"high_risk_drivers"`
	TotalViolations   int     `json:"total_violations"`
	AvgFuelEfficiency float64 `json:"avg_fuel_efficiency"`
	AvgIdleTime       float64 `json:"avg_idle_time"`
}

// FleetProductivityDTO represents fleet productivity metrics
type FleetProductivityDTO struct {
	Headers []string                 `json:"headers"`
	Data    []FleetProductivityRow   `json:"data"`
	Summary FleetProductivitySummary `json:"summary"`
}

type FleetProductivityRow struct {
	Month             string  `json:"month" csv:"Month"`
	TotalVehicles     int     `json:"total_vehicles" csv:"Total Vehicles"`
	ActiveVehicles    int     `json:"active_vehicles" csv:"Active Vehicles"`
	TotalTrips        int     `json:"total_trips" csv:"Total Trips"`
	TotalDistance     float64 `json:"total_distance" csv:"Total Distance (km)"`
	TotalHours        string  `json:"total_hours" csv:"Total Hours"`
	ProductiveHours   string  `json:"productive_hours" csv:"Productive Hours"`
	ProductivityRate  float64 `json:"productivity_rate" csv:"Productivity Rate (%)"`
	AvgUtilization    float64 `json:"avg_utilization" csv:"Avg Utilization (%)"`
	FuelEfficiency    float64 `json:"fuel_efficiency" csv:"Fuel Efficiency (km/L)"`
	CostPerKm         float64 `json:"cost_per_km" csv:"Cost per km"`
	ProductivityScore float64 `json:"productivity_score" csv:"Productivity Score"`
}

type FleetProductivitySummary struct {
	TotalVehicles     int     `json:"total_vehicles"`
	TotalTrips        int     `json:"total_trips"`
	TotalDistance     float64 `json:"total_distance"`
	AvgProductivity   float64 `json:"avg_productivity"`
	AvgUtilization    float64 `json:"avg_utilization"`
	AvgFuelEfficiency float64 `json:"avg_fuel_efficiency"`
	TotalCost         float64 `json:"total_cost"`
	AvgCostPerKm      float64 `json:"avg_cost_per_km"`
}

// ============================================================================
// ADDITIONAL SPECIALIZED REPORT DTOs
// ============================================================================

// FuelEfficiencyTrendsDTO represents fuel efficiency trends analysis
type FuelEfficiencyTrendsDTO struct {
	Headers []string                    `json:"headers"`
	Data    []FuelEfficiencyTrendsRow   `json:"data"`
	Summary FuelEfficiencyTrendsSummary `json:"summary"`
}

type FuelEfficiencyTrendsRow struct {
	Month            string  `json:"month" csv:"Month"`
	DeviceName       string  `json:"device_name" csv:"Device Name"`
	PlateNumber      string  `json:"plate_number" csv:"Plate Number"`
	DriverName       string  `json:"driver_name" csv:"Driver Name"`
	TotalDistance    float64 `json:"total_distance" csv:"Total Distance (km)"`
	FuelConsumed     float64 `json:"fuel_consumed" csv:"Fuel Consumed (L)"`
	EfficiencyKmPerL float64 `json:"efficiency_km_per_l" csv:"Efficiency (km/L)"`
	EfficiencyLph    float64 `json:"efficiency_lph" csv:"Efficiency (L/h)"`
	AvgSpeed         float64 `json:"avg_speed" csv:"Avg Speed (km/h)"`
	IdleTime         string  `json:"idle_time" csv:"Idle Time"`
	CostPerKm        float64 `json:"cost_per_km" csv:"Cost per km"`
	Trend            string  `json:"trend" csv:"Trend"`
	Improvement      float64 `json:"improvement" csv:"Improvement (%)"`
}

type FuelEfficiencyTrendsSummary struct {
	TotalDistance     float64 `json:"total_distance"`
	TotalFuelConsumed float64 `json:"total_fuel_consumed"`
	AvgEfficiency     float64 `json:"avg_efficiency"`
	AvgCostPerKm      float64 `json:"avg_cost_per_km"`
	OverallTrend      string  `json:"overall_trend"`
	TotalImprovement  float64 `json:"total_improvement"`
}

// RealTimeDashboardDTO represents real-time fleet dashboard
type RealTimeDashboardDTO struct {
	Headers []string                 `json:"headers"`
	Data    []RealTimeDashboardRow   `json:"data"`
	Summary RealTimeDashboardSummary `json:"summary"`
}

type RealTimeDashboardRow struct {
	DeviceName    string  `json:"device_name" csv:"Device Name"`
	PlateNumber   string  `json:"plate_number" csv:"Plate Number"`
	DriverName    string  `json:"driver_name" csv:"Driver Name"`
	CurrentStatus string  `json:"current_status" csv:"Current Status"`
	Location      string  `json:"location" csv:"Location"`
	Speed         float64 `json:"speed" csv:"Speed (km/h)"`
	EngineStatus  string  `json:"engine_status" csv:"Engine Status"`
	LastUpdate    string  `json:"last_update" csv:"Last Update"`
	CurrentTrip   string  `json:"current_trip" csv:"Current Trip"`
	FuelLevel     float64 `json:"fuel_level" csv:"Fuel Level (%)"`
	Alerts        string  `json:"alerts" csv:"Alerts"`
}

type RealTimeDashboardSummary struct {
	TotalVehicles   int     `json:"total_vehicles"`
	ActiveVehicles  int     `json:"active_vehicles"`
	IdleVehicles    int     `json:"idle_vehicles"`
	OfflineVehicles int     `json:"offline_vehicles"`
	ActiveTrips     int     `json:"active_trips"`
	TotalAlerts     int     `json:"total_alerts"`
	AvgSpeed        float64 `json:"avg_speed"`
	UtilizationRate float64 `json:"utilization_rate"`
}

// DriverScorecardDTO represents comprehensive driver scorecard
type DriverScorecardDTO struct {
	Headers []string               `json:"headers"`
	Data    []DriverScorecardRow   `json:"data"`
	Summary DriverScorecardSummary `json:"summary"`
}

type DriverScorecardRow struct {
	DriverName      string  `json:"driver_name" csv:"Driver Name"`
	DeviceName      string  `json:"device_name" csv:"Device Name"`
	PlateNumber     string  `json:"plate_number" csv:"Plate Number"`
	OverallScore    float64 `json:"overall_score" csv:"Overall Score"`
	SafetyScore     float64 `json:"safety_score" csv:"Safety Score"`
	EfficiencyScore float64 `json:"efficiency_score" csv:"Efficiency Score"`
	ComplianceScore float64 `json:"compliance_score" csv:"Compliance Score"`
	TotalTrips      int     `json:"total_trips" csv:"Total Trips"`
	TotalDistance   float64 `json:"total_distance" csv:"Total Distance (km)"`
	AvgSpeed        float64 `json:"avg_speed" csv:"Avg Speed (km/h)"`
	FuelEfficiency  float64 `json:"fuel_efficiency" csv:"Fuel Efficiency (km/L)"`
	Violations      int     `json:"violations" csv:"Violations"`
	RiskLevel       string  `json:"risk_level" csv:"Risk Level"`
	Ranking         int     `json:"ranking" csv:"Ranking"`
}

type DriverScorecardSummary struct {
	TotalDrivers      int     `json:"total_drivers"`
	AvgOverallScore   float64 `json:"avg_overall_score"`
	TopPerformers     int     `json:"top_performers"`
	NeedsImprovement  int     `json:"needs_improvement"`
	TotalViolations   int     `json:"total_violations"`
	AvgFuelEfficiency float64 `json:"avg_fuel_efficiency"`
}

// FleetHealthMonitorDTO represents fleet health monitoring
type FleetHealthMonitorDTO struct {
	Headers []string                  `json:"headers"`
	Data    []FleetHealthMonitorRow   `json:"data"`
	Summary FleetHealthMonitorSummary `json:"summary"`
}

type FleetHealthMonitorRow struct {
	DeviceName      string  `json:"device_name" csv:"Device Name"`
	PlateNumber     string  `json:"plate_number" csv:"Plate Number"`
	HealthScore     float64 `json:"health_score" csv:"Health Score"`
	Status          string  `json:"status" csv:"Status"`
	LastMaintenance string  `json:"last_maintenance" csv:"Last Maintenance"`
	NextMaintenance string  `json:"next_maintenance" csv:"Next Maintenance"`
	CurrentMileage  float64 `json:"current_mileage" csv:"Current Mileage (km)"`
	EngineHours     float64 `json:"engine_hours" csv:"Engine Hours"`
	BatteryStatus   string  `json:"battery_status" csv:"Battery Status"`
	FaultCodes      string  `json:"fault_codes" csv:"Fault Codes"`
	HealthLevel     string  `json:"health_level" csv:"Health Level"`
	Recommendations string  `json:"recommendations" csv:"Recommendations"`
}

type FleetHealthMonitorSummary struct {
	TotalVehicles    int     `json:"total_vehicles"`
	HealthyVehicles  int     `json:"healthy_vehicles"`
	WarningVehicles  int     `json:"warning_vehicles"`
	CriticalVehicles int     `json:"critical_vehicles"`
	AvgHealthScore   float64 `json:"avg_health_score"`
	MaintenanceDue   int     `json:"maintenance_due"`
	TotalFaultCodes  int     `json:"total_fault_codes"`
}

// CostAnalysisDashboardDTO represents cost analysis dashboard
type CostAnalysisDashboardDTO struct {
	Headers []string                     `json:"headers"`
	Data    []CostAnalysisDashboardRow   `json:"data"`
	Summary CostAnalysisDashboardSummary `json:"summary"`
}

type CostAnalysisDashboardRow struct {
	DeviceName           string  `json:"device_name" csv:"Device Name"`
	PlateNumber          string  `json:"plate_number" csv:"Plate Number"`
	TotalCost            float64 `json:"total_cost" csv:"Total Cost"`
	FuelCost             float64 `json:"fuel_cost" csv:"Fuel Cost"`
	MaintenanceCost      float64 `json:"maintenance_cost" csv:"Maintenance Cost"`
	InsuranceCost        float64 `json:"insurance_cost" csv:"Insurance Cost"`
	DepreciationCost     float64 `json:"depreciation_cost" csv:"Depreciation Cost"`
	TotalDistance        float64 `json:"total_distance" csv:"Total Distance (km)"`
	CostPerKm            float64 `json:"cost_per_km" csv:"Cost per km"`
	FuelCostPerKm        float64 `json:"fuel_cost_per_km" csv:"Fuel Cost per km"`
	MaintenanceCostPerKm float64 `json:"maintenance_cost_per_km" csv:"Maintenance Cost per km"`
	ROI                  float64 `json:"roi" csv:"ROI (%)"`
	CostEfficiency       string  `json:"cost_efficiency" csv:"Cost Efficiency"`
}

type CostAnalysisDashboardSummary struct {
	TotalVehicles        int     `json:"total_vehicles"`
	TotalCost            float64 `json:"total_cost"`
	TotalFuelCost        float64 `json:"total_fuel_cost"`
	TotalMaintenanceCost float64 `json:"total_maintenance_cost"`
	AvgCostPerKm         float64 `json:"avg_cost_per_km"`
	AvgROI               float64 `json:"avg_roi"`
	TotalDistance        float64 `json:"total_distance"`
}

// ComplianceDashboardDTO represents compliance dashboard
type ComplianceDashboardDTO struct {
	Headers []string                   `json:"headers"`
	Data    []ComplianceDashboardRow   `json:"data"`
	Summary ComplianceDashboardSummary `json:"summary"`
}

type ComplianceDashboardRow struct {
	DriverName          string  `json:"driver_name" csv:"Driver Name"`
	DeviceName          string  `json:"device_name" csv:"Device Name"`
	PlateNumber         string  `json:"plate_number" csv:"Plate Number"`
	ComplianceScore     float64 `json:"compliance_score" csv:"Compliance Score"`
	HoursOfService      string  `json:"hours_of_service" csv:"Hours of Service"`
	RestCompliance      string  `json:"rest_compliance" csv:"Rest Compliance"`
	SpeedCompliance     string  `json:"speed_compliance" csv:"Speed Compliance"`
	SafetyViolations    int     `json:"safety_violations" csv:"Safety Violations"`
	LicenseStatus       string  `json:"license_status" csv:"License Status"`
	CertificationStatus string  `json:"certification_status" csv:"Certification Status"`
	LastAudit           string  `json:"last_audit" csv:"Last Audit"`
	RiskLevel           string  `json:"risk_level" csv:"Risk Level"`
	ComplianceStatus    string  `json:"compliance_status" csv:"Compliance Status"`
}

type ComplianceDashboardSummary struct {
	TotalDrivers        int     `json:"total_drivers"`
	CompliantDrivers    int     `json:"compliant_drivers"`
	NonCompliantDrivers int     `json:"non_compliant_drivers"`
	AvgComplianceScore  float64 `json:"avg_compliance_score"`
	TotalViolations     int     `json:"total_violations"`
	HighRiskDrivers     int     `json:"high_risk_drivers"`
	AuditDue            int     `json:"audit_due"`
}

// RouteAnalysisDTO represents route analysis
type RouteAnalysisDTO struct {
	Headers []string             `json:"headers"`
	Data    []RouteAnalysisRow   `json:"data"`
	Summary RouteAnalysisSummary `json:"summary"`
}

type RouteAnalysisRow struct {
	RouteName         string  `json:"route_name" csv:"Route Name"`
	DeviceName        string  `json:"device_name" csv:"Device Name"`
	PlateNumber       string  `json:"plate_number" csv:"Plate Number"`
	DriverName        string  `json:"driver_name" csv:"Driver Name"`
	TotalTrips        int     `json:"total_trips" csv:"Total Trips"`
	AvgDistance       float64 `json:"avg_distance" csv:"Avg Distance (km)"`
	AvgDuration       string  `json:"avg_duration" csv:"Avg Duration"`
	AvgSpeed          float64 `json:"avg_speed" csv:"Avg Speed (km/h)"`
	FuelEfficiency    float64 `json:"fuel_efficiency" csv:"Fuel Efficiency (km/L)"`
	TrafficDelays     string  `json:"traffic_delays" csv:"Traffic Delays"`
	RouteEfficiency   float64 `json:"route_efficiency" csv:"Route Efficiency (%)"`
	OptimizationScore float64 `json:"optimization_score" csv:"Optimization Score"`
	Recommendations   string  `json:"recommendations" csv:"Recommendations"`
}

type RouteAnalysisSummary struct {
	TotalRoutes          int     `json:"total_routes"`
	TotalTrips           int     `json:"total_trips"`
	AvgRouteEfficiency   float64 `json:"avg_route_efficiency"`
	AvgOptimizationScore float64 `json:"avg_optimization_score"`
	TotalDistance        float64 `json:"total_distance"`
	TotalFuelSavings     float64 `json:"total_fuel_savings"`
}

// PredictiveMaintenanceDTO represents predictive maintenance
type PredictiveMaintenanceDTO struct {
	Headers []string                     `json:"headers"`
	Data    []PredictiveMaintenanceRow   `json:"data"`
	Summary PredictiveMaintenanceSummary `json:"summary"`
}

type PredictiveMaintenanceRow struct {
	DeviceName       string  `json:"device_name" csv:"Device Name"`
	PlateNumber      string  `json:"plate_number" csv:"Plate Number"`
	Component        string  `json:"component" csv:"Component"`
	CurrentHealth    float64 `json:"current_health" csv:"Current Health (%)"`
	PredictedFailure string  `json:"predicted_failure" csv:"Predicted Failure"`
	RiskLevel        string  `json:"risk_level" csv:"Risk Level"`
	LastInspection   string  `json:"last_inspection" csv:"Last Inspection"`
	NextInspection   string  `json:"next_inspection" csv:"Next Inspection"`
	MaintenanceCost  float64 `json:"maintenance_cost" csv:"Maintenance Cost"`
	DowntimeRisk     string  `json:"downtime_risk" csv:"Downtime Risk"`
	Recommendations  string  `json:"recommendations" csv:"Recommendations"`
	Priority         string  `json:"priority" csv:"Priority"`
}

type PredictiveMaintenanceSummary struct {
	TotalVehicles        int     `json:"total_vehicles"`
	HighRiskVehicles     int     `json:"high_risk_vehicles"`
	MediumRiskVehicles   int     `json:"medium_risk_vehicles"`
	LowRiskVehicles      int     `json:"low_risk_vehicles"`
	TotalComponents      int     `json:"total_components"`
	AvgHealthScore       float64 `json:"avg_health_score"`
	PredictedFailures    int     `json:"predicted_failures"`
	TotalMaintenanceCost float64 `json:"total_maintenance_cost"`
}

// FleetOptimizationDTO represents fleet optimization
type FleetOptimizationDTO struct {
	Headers []string                 `json:"headers"`
	Data    []FleetOptimizationRow   `json:"data"`
	Summary FleetOptimizationSummary `json:"summary"`
}

type FleetOptimizationRow struct {
	DeviceName        string  `json:"device_name" csv:"Device Name"`
	PlateNumber       string  `json:"plate_number" csv:"Plate Number"`
	UtilizationRate   float64 `json:"utilization_rate" csv:"Utilization Rate (%)"`
	EfficiencyScore   float64 `json:"efficiency_score" csv:"Efficiency Score"`
	CostPerKm         float64 `json:"cost_per_km" csv:"Cost per km"`
	ROI               float64 `json:"roi" csv:"ROI (%)"`
	OptimizationScore float64 `json:"optimization_score" csv:"Optimization Score"`
	Recommendations   string  `json:"recommendations" csv:"Recommendations"`
	RightSizing       string  `json:"right_sizing" csv:"Right Sizing"`
	ReplacementValue  float64 `json:"replacement_value" csv:"Replacement Value"`
	SavingsPotential  float64 `json:"savings_potential" csv:"Savings Potential"`
	Status            string  `json:"status" csv:"Status"`
}

type FleetOptimizationSummary struct {
	TotalVehicles         int     `json:"total_vehicles"`
	OverUtilized          int     `json:"over_utilized"`
	WellUtilized          int     `json:"well_utilized"`
	UnderUtilized         int     `json:"under_utilized"`
	AvgOptimizationScore  float64 `json:"avg_optimization_score"`
	TotalSavingsPotential float64 `json:"total_savings_potential"`
	AvgROI                float64 `json:"avg_roi"`
	Recommendations       string  `json:"recommendations"`
}

// DriverTrainingNeedsDTO represents driver training needs
type DriverTrainingNeedsDTO struct {
	Headers []string                   `json:"headers"`
	Data    []DriverTrainingNeedsRow   `json:"data"`
	Summary DriverTrainingNeedsSummary `json:"summary"`
}

type DriverTrainingNeedsRow struct {
	DriverName          string  `json:"driver_name" csv:"Driver Name"`
	DeviceName          string  `json:"device_name" csv:"Device Name"`
	PlateNumber         string  `json:"plate_number" csv:"Plate Number"`
	SafetyScore         float64 `json:"safety_score" csv:"Safety Score"`
	Violations          int     `json:"violations" csv:"Violations"`
	TrainingPriority    string  `json:"training_priority" csv:"Training Priority"`
	SkillGaps           string  `json:"skill_gaps" csv:"Skill Gaps"`
	RecommendedTraining string  `json:"recommended_training" csv:"Recommended Training"`
	LastTraining        string  `json:"last_training" csv:"Last Training"`
	TrainingDue         string  `json:"training_due" csv:"Training Due"`
	RiskLevel           string  `json:"risk_level" csv:"Risk Level"`
	ImprovementAreas    string  `json:"improvement_areas" csv:"Improvement Areas"`
}

type DriverTrainingNeedsSummary struct {
	TotalDrivers    int     `json:"total_drivers"`
	HighPriority    int     `json:"high_priority"`
	MediumPriority  int     `json:"medium_priority"`
	LowPriority     int     `json:"low_priority"`
	AvgSafetyScore  float64 `json:"avg_safety_score"`
	TotalViolations int     `json:"total_violations"`
	TrainingDue     int     `json:"training_due"`
}

// EnvironmentalDashboardDTO represents environmental dashboard
type EnvironmentalDashboardDTO struct {
	Headers []string                      `json:"headers"`
	Data    []EnvironmentalDashboardRow   `json:"data"`
	Summary EnvironmentalDashboardSummary `json:"summary"`
}

type EnvironmentalDashboardRow struct {
	Month               string  `json:"month" csv:"Month"`
	DeviceName          string  `json:"device_name" csv:"Device Name"`
	PlateNumber         string  `json:"plate_number" csv:"Plate Number"`
	FuelConsumed        float64 `json:"fuel_consumed" csv:"Fuel Consumed (L)"`
	CO2Emissions        float64 `json:"co2_emissions" csv:"CO2 Emissions (kg)"`
	CarbonFootprint     float64 `json:"carbon_footprint" csv:"Carbon Footprint (kg/km)"`
	FuelEfficiency      float64 `json:"fuel_efficiency" csv:"Fuel Efficiency (km/L)"`
	IdleTime            string  `json:"idle_time" csv:"Idle Time"`
	IdleEmissions       float64 `json:"idle_emissions" csv:"Idle Emissions (kg)"`
	TotalDistance       float64 `json:"total_distance" csv:"Total Distance (km)"`
	EnvironmentalScore  float64 `json:"environmental_score" csv:"Environmental Score"`
	SustainabilityLevel string  `json:"sustainability_level" csv:"Sustainability Level"`
}

type EnvironmentalDashboardSummary struct {
	TotalVehicles         int     `json:"total_vehicles"`
	TotalFuelConsumed     float64 `json:"total_fuel_consumed"`
	TotalCO2Emissions     float64 `json:"total_co2_emissions"`
	AvgCarbonFootprint    float64 `json:"avg_carbon_footprint"`
	AvgEnvironmentalScore float64 `json:"avg_environmental_score"`
	TotalDistance         float64 `json:"total_distance"`
	SustainabilityRating  string  `json:"sustainability_rating"`
}

// ============================================================================
// ROUTE DEVIATION REPORT DTOs
// ============================================================================

// RouteDeviationDTO represents route deviation analysis
type RouteDeviationDTO struct {
	Headers []string              `json:"headers"`
	Data    []RouteDeviationRow   `json:"data"`
	Summary RouteDeviationSummary `json:"summary"`
}

type RouteDeviationRow struct {
	TripID            uint    `json:"trip_id" csv:"Trip ID"`
	DeviceName        string  `json:"device_name" csv:"Device Name"`
	PlateNumber       string  `json:"plate_number" csv:"Plate Number"`
	DriverName        string  `json:"driver_name" csv:"Driver Name"`
	StartTime         string  `json:"start_time" csv:"Start Time"`
	EndTime           string  `json:"end_time" csv:"End Time"`
	StartLocation     string  `json:"start_location" csv:"Start Location"`
	EndLocation       string  `json:"end_location" csv:"End Location"`
	ActualDistance    float64 `json:"actual_distance" csv:"Actual Distance (km)"`
	ExpectedDistance  float64 `json:"expected_distance" csv:"Expected Distance (km)"`
	Deviation         float64 `json:"deviation" csv:"Deviation (km)"`
	DeviationPercent  float64 `json:"deviation_percent" csv:"Deviation (%)"`
	RouteEfficiency   float64 `json:"route_efficiency" csv:"Route Efficiency (%)"`
	CommonRoute       string  `json:"common_route" csv:"Common Route"`
	OptimizationScore float64 `json:"optimization_score" csv:"Optimization Score"`
	Recommendations   string  `json:"recommendations" csv:"Recommendations"`
}

type RouteDeviationSummary struct {
	TotalTrips            int     `json:"total_trips"`
	TotalActualDistance   float64 `json:"total_actual_distance"`
	TotalExpectedDistance float64 `json:"total_expected_distance"`
	TotalDeviation        float64 `json:"total_deviation"`
	AvgOptimizationScore  float64 `json:"avg_optimization_score"`
	OverallEfficiency     float64 `json:"overall_efficiency"`
	TotalFuelSavings      float64 `json:"total_fuel_savings"`
}

// ============================================================================
// DOOR DETAIL REPORT DTOs
// ============================================================================

// DoorDetailDTO represents door open/close events with security analysis
type DoorDetailDTO struct {
	Headers []string          `json:"headers"`
	Data    []DoorDetailRow   `json:"data"`
	Summary DoorDetailSummary `json:"summary"`
}

type DoorDetailRow struct {
	DeviceName    string  `json:"device_name" csv:"Device Name"`
	PlateNumber   string  `json:"plate_number" csv:"Plate Number"`
	DriverName    string  `json:"driver_name" csv:"Driver Name"`
	EventType     string  `json:"event_type" csv:"Event Type"`
	Timestamp     string  `json:"timestamp" csv:"Timestamp"`
	Location      string  `json:"location" csv:"Location"`
	Coordinates   string  `json:"coordinates" csv:"Coordinates"`
	Duration      string  `json:"duration" csv:"Duration"`
	Severity      float64 `json:"severity" csv:"Severity"`
	SecurityLevel string  `json:"security_level" csv:"Security Level"`
	AccessType    string  `json:"access_type" csv:"Access Type"`
	Notes         string  `json:"notes" csv:"Notes"`
}

type DoorDetailSummary struct {
	TotalEvents       int      `json:"total_events"`
	TotalDoorOpens    int      `json:"total_door_opens"`
	TotalDoorCloses   int      `json:"total_door_closes"`
	SecurityIncidents int      `json:"security_incidents"`
	AvgSeverity       float64  `json:"avg_severity"`
	SecurityRiskLevel string   `json:"security_risk_level"`
	Recommendations   []string `json:"recommendations"`
}

// ============================================================================
// MILEAGE MONTH SUMMARY REPORT DTOs
// ============================================================================

// MileageMonthSummaryDTO represents monthly mileage summary
type MileageMonthSummaryDTO struct {
	Headers []string                 `json:"headers"`
	Data    []MileageMonthSummaryRow `json:"data"`
	Summary MileageMonthSummaryStats `json:"summary"`
}

type MileageMonthSummaryRow struct {
	Month             string  `json:"month" csv:"Month"`
	DeviceName        string  `json:"device_name" csv:"Device Name"`
	PlateNumber       string  `json:"plate_number" csv:"Plate Number"`
	DriverName        string  `json:"driver_name" csv:"Driver Name"`
	TotalMileage      float64 `json:"total_mileage" csv:"Total Mileage (km)"`
	TotalTrips        int     `json:"total_trips" csv:"Total Trips"`
	AvgMileagePerTrip float64 `json:"avg_mileage_per_trip" csv:"Avg Mileage per Trip (km)"`
	TotalFuelCost     float64 `json:"total_fuel_cost" csv:"Total Fuel Cost"`
	CostPerKm         float64 `json:"cost_per_km" csv:"Cost per km"`
	MaintenanceDue    string  `json:"maintenance_due" csv:"Maintenance Due"`
	Efficiency        float64 `json:"efficiency" csv:"Efficiency (km/L)"`
	Recommendations   string  `json:"recommendations" csv:"Recommendations"`
}

type MileageMonthSummaryStats struct {
	TotalDevices      int     `json:"total_devices"`
	TotalMileage      float64 `json:"total_mileage"`
	TotalTrips        int     `json:"total_trips"`
	TotalFuelCost     float64 `json:"total_fuel_cost"`
	AvgMileagePerTrip float64 `json:"avg_mileage_per_trip"`
	AvgCostPerKm      float64 `json:"avg_cost_per_km"`
	AvgEfficiency     float64 `json:"avg_efficiency"`
	MaintenanceDue    int     `json:"maintenance_due"`
}

// ============================================================================
// ADDITIONAL DETAIL REPORT DTOs
// ============================================================================

// TripDetailDeltaDTO represents trip detail with delta analysis
type TripDetailDeltaDTO struct {
	Headers []string             `json:"headers"`
	Data    []TripDetailDeltaRow `json:"data"`
	Summary TripDetailDeltaStats `json:"summary"`
}

type TripDetailDeltaRow struct {
	TripID           uint    `json:"trip_id" csv:"Trip ID"`
	DeviceName       string  `json:"device_name" csv:"Device Name"`
	PlateNumber      string  `json:"plate_number" csv:"Plate Number"`
	DriverName       string  `json:"driver_name" csv:"Driver Name"`
	StartTime        string  `json:"start_time" csv:"Start Time"`
	EndTime          string  `json:"end_time" csv:"End Time"`
	Duration         string  `json:"duration" csv:"Duration"`
	Distance         float64 `json:"distance" csv:"Distance (km)"`
	AvgSpeed         float64 `json:"avg_speed" csv:"Avg Speed (km/h)"`
	MaxSpeed         float64 `json:"max_speed" csv:"Max Speed (km/h)"`
	StartLocation    string  `json:"start_location" csv:"Start Location"`
	EndLocation      string  `json:"end_location" csv:"End Location"`
	PreviousDistance float64 `json:"previous_distance" csv:"Previous Distance (km)"`
	DistanceDelta    float64 `json:"distance_delta" csv:"Distance Delta (km)"`
	DeltaPercentage  float64 `json:"delta_percentage" csv:"Delta (%)"`
	Improvement      string  `json:"improvement" csv:"Improvement"`
	Trend            string  `json:"trend" csv:"Trend"`
}

type TripDetailDeltaStats struct {
	TotalTrips     int     `json:"total_trips"`
	TotalDistance  float64 `json:"total_distance"`
	AvgDistance    float64 `json:"avg_distance"`
	ImprovingTrips int     `json:"improving_trips"`
	DecliningTrips int     `json:"declining_trips"`
	AvgDelta       float64 `json:"avg_delta"`
	OverallTrend   string  `json:"overall_trend"`
}

// PositionLogDriverDTO represents driver-specific position log
type PositionLogDriverDTO struct {
	Headers []string               `json:"headers"`
	Data    []PositionLogDriverRow `json:"data"`
	Summary PositionLogDriverStats `json:"summary"`
}

type PositionLogDriverRow struct {
	Timestamp    string  `json:"timestamp" csv:"Timestamp"`
	DriverName   string  `json:"driver_name" csv:"Driver Name"`
	DeviceName   string  `json:"device_name" csv:"Device Name"`
	PlateNumber  string  `json:"plate_number" csv:"Plate Number"`
	Latitude     float64 `json:"latitude" csv:"Latitude"`
	Longitude    float64 `json:"longitude" csv:"Longitude"`
	LocationName string  `json:"location_name" csv:"Location Name"`
	Speed        float64 `json:"speed" csv:"Speed (km/h)"`
	Direction    string  `json:"direction" csv:"Direction"`
	EngineStatus string  `json:"engine_status" csv:"Engine Status"`
	CurrentTrip  string  `json:"current_trip" csv:"Current Trip"`
	DriverStatus string  `json:"driver_status" csv:"Driver Status"`
}

type PositionLogDriverStats struct {
	TotalRecords  int     `json:"total_records"`
	TotalDrivers  int     `json:"total_drivers"`
	TotalDevices  int     `json:"total_devices"`
	AvgSpeed      float64 `json:"avg_speed"`
	ActiveDrivers int     `json:"active_drivers"`
	IdleDrivers   int     `json:"idle_drivers"`
	OnTripDrivers int     `json:"on_trip_drivers"`
}

// TripMileageDTO represents trip mileage analysis
type TripMileageDTO struct {
	Headers []string         `json:"headers"`
	Data    []TripMileageRow `json:"data"`
	Summary TripMileageStats `json:"summary"`
}

type TripMileageRow struct {
	TripID            uint    `json:"trip_id" csv:"Trip ID"`
	DeviceName        string  `json:"device_name" csv:"Device Name"`
	PlateNumber       string  `json:"plate_number" csv:"Plate Number"`
	DriverName        string  `json:"driver_name" csv:"Driver Name"`
	StartTime         string  `json:"start_time" csv:"Start Time"`
	EndTime           string  `json:"end_time" csv:"End Time"`
	Duration          string  `json:"duration" csv:"Duration"`
	Distance          float64 `json:"distance" csv:"Distance (km)"`
	AvgSpeed          float64 `json:"avg_speed" csv:"Avg Speed (km/h)"`
	MaxSpeed          float64 `json:"max_speed" csv:"Max Speed (km/h)"`
	StartLocation     string  `json:"start_location" csv:"Start Location"`
	EndLocation       string  `json:"end_location" csv:"End Location"`
	MileageEfficiency float64 `json:"mileage_efficiency" csv:"Mileage Efficiency (%)"`
	FuelConsumed      float64 `json:"fuel_consumed" csv:"Fuel Consumed (L)"`
	CostPerKm         float64 `json:"cost_per_km" csv:"Cost per km"`
	Efficiency        float64 `json:"efficiency" csv:"Efficiency (km/L)"`
}

type TripMileageStats struct {
	TotalTrips    int     `json:"total_trips"`
	TotalDistance float64 `json:"total_distance"`
	TotalFuel     float64 `json:"total_fuel"`
	AvgDistance   float64 `json:"avg_distance"`
	AvgEfficiency float64 `json:"avg_efficiency"`
	AvgCostPerKm  float64 `json:"avg_cost_per_km"`
	TotalCost     float64 `json:"total_cost"`
}

// ============================================================================
// ASSET TRACKING REPORT DTOs
// ============================================================================

// LastLocationDTO represents asset tracking with last known positions
type LastLocationDTO struct {
	Headers []string          `json:"headers"`
	Data    []LastLocationRow `json:"data"`
	Summary LastLocationStats `json:"summary"`
}

type LastLocationRow struct {
	DeviceName     string  `json:"device_name" csv:"Device Name"`
	PlateNumber    string  `json:"plate_number" csv:"Plate Number"`
	DriverName     string  `json:"driver_name" csv:"Driver Name"`
	LastLocation   string  `json:"last_location" csv:"Last Location"`
	Latitude       float64 `json:"latitude" csv:"Latitude"`
	Longitude      float64 `json:"longitude" csv:"Longitude"`
	LastSeen       string  `json:"last_seen" csv:"Last Seen"`
	Status         string  `json:"status" csv:"Status"`
	CurrentTrip    string  `json:"current_trip" csv:"Current Trip"`
	Speed          float64 `json:"speed" csv:"Speed (km/h)"`
	Direction      string  `json:"direction" csv:"Direction"`
	EngineStatus   string  `json:"engine_status" csv:"Engine Status"`
	BatteryLevel   string  `json:"battery_level" csv:"Battery Level"`
	SignalStrength string  `json:"signal_strength" csv:"Signal Strength"`
}

type LastLocationStats struct {
	TotalDevices   int     `json:"total_devices"`
	OnlineDevices  int     `json:"online_devices"`
	OfflineDevices int     `json:"offline_devices"`
	OnTripDevices  int     `json:"on_trip_devices"`
	IdleDevices    int     `json:"idle_devices"`
	AvgSpeed       float64 `json:"avg_speed"`
	ActiveDrivers  int     `json:"active_drivers"`
}

// ============================================================================
// LOCATION HISTORY REPORT DTOs
// ============================================================================

// LocationHistoryDTO represents location history for devices over time
type LocationHistoryDTO struct {
	Headers []string             `json:"headers"`
	Data    []LocationHistoryRow `json:"data"`
	Summary LocationHistoryStats `json:"summary"`
}

type LocationHistoryRow struct {
	DeviceName   string  `json:"device_name" csv:"Device Name"`
	PlateNumber  string  `json:"plate_number" csv:"Plate Number"`
	DriverName   string  `json:"driver_name" csv:"Driver Name"`
	LocationName string  `json:"location_name" csv:"Location"`
	Latitude     float64 `json:"latitude" csv:"Latitude"`
	Longitude    float64 `json:"longitude" csv:"Longitude"`
	GPSTimestamp string  `json:"gps_timestamp" csv:"GPS Timestamp"`
	Speed        float64 `json:"speed" csv:"Speed (km/h)"`
	Direction    string  `json:"direction" csv:"Direction"`
	EngineStatus string  `json:"engine_status" csv:"Engine Status"`
	TripInfo     string  `json:"trip_info" csv:"Trip Info"`
	Status       string  `json:"status" csv:"Status"`
}

type LocationHistoryStats struct {
	TotalRecords   int     `json:"total_records"`
	OnlineDevices  int     `json:"online_devices"`
	OfflineDevices int     `json:"offline_devices"`
	OnTripDevices  int     `json:"on_trip_devices"`
	IdleDevices    int     `json:"idle_devices"`
	AvgSpeed       float64 `json:"avg_speed"`
	ActiveDrivers  int     `json:"active_drivers"`
}

// ============================================================================
// DESTINATION ZONE REPORT DTOs
// ============================================================================

// DestinationZoneDTO represents destination zone entry analysis
type DestinationZoneDTO struct {
	Headers []string             `json:"headers"`
	Data    []DestinationZoneRow `json:"data"`
	Summary DestinationZoneStats `json:"summary"`
}

type DestinationZoneRow struct {
	DeviceName   string  `json:"device_name" csv:"Device Name"`
	PlateNumber  string  `json:"plate_number" csv:"Plate Number"`
	DriverName   string  `json:"driver_name" csv:"Driver Name"`
	ZoneName     string  `json:"zone_name" csv:"Zone Name"`
	EntryTime    string  `json:"entry_time" csv:"Entry Time"`
	ExitTime     string  `json:"exit_time" csv:"Exit Time"`
	Duration     float64 `json:"duration" csv:"Duration (min)"`
	LocationName string  `json:"location_name" csv:"Location"`
	Latitude     float64 `json:"latitude" csv:"Latitude"`
	Longitude    float64 `json:"longitude" csv:"Longitude"`
	TripInfo     string  `json:"trip_info" csv:"Trip Info"`
	Status       string  `json:"status" csv:"Status"`
}

type DestinationZoneStats struct {
	TotalEntries   int     `json:"total_entries"`
	ActiveZones    int     `json:"active_zones"`
	CompletedZones int     `json:"completed_zones"`
	AvgDuration    float64 `json:"avg_duration"`
	ActiveDrivers  int     `json:"active_drivers"`
}

// ============================================================================
// HARSH DRIVING EVENTS REPORT DTOs
// ============================================================================

// HarshDrivingEventsDTO represents harsh driving events analysis
type HarshDrivingEventsDTO struct {
	Headers []string                  `json:"headers"`
	Data    []HarshDrivingEventRow    `json:"data"`
	Summary HarshDrivingEventsSummary `json:"summary"`
}

type HarshDrivingEventRow struct {
	EventID        uint   `json:"event_id" csv:"Event ID"`
	EventType      string `json:"event_type" csv:"Event Type"`
	Timestamp      string `json:"timestamp" csv:"Timestamp"`
	Location       string `json:"location" csv:"Location"`
	Speed          string `json:"speed" csv:"Speed"`
	Severity       string `json:"severity" csv:"Severity"`
	DeviceName     string `json:"device_name" csv:"Device Name"`
	PlateNumber    string `json:"plate_number" csv:"Plate Number"`
	DriverName     string `json:"driver_name" csv:"Driver Name"`
	RiskLevel      string `json:"risk_level" csv:"Risk Level"`
	Recommendation string `json:"recommendation" csv:"Recommendation"`
}

type HarshDrivingEventsSummary struct {
	TotalEvents      int            `json:"total_events"`
	AvgSeverity      float64        `json:"avg_severity"`
	EventsByType     map[string]int `json:"events_by_type"`
	EventsBySeverity map[string]int `json:"events_by_severity"`
	SafetyScore      float64        `json:"safety_score"`
	RiskAssessment   string         `json:"risk_assessment"`
}
