package utils

import (
	"time"
	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// BuildClientDeviceQueryWithLastGPS builds a query that efficiently includes last GPS data
// using a LEFT JOIN with a subquery to get the latest GPS record for each device
func BuildClientDeviceQueryWithLastGPS() *gorm.DB {
	// Create a subquery to get the latest GPS data for each device
	latestGPSSubquery := config.DB.Table("gps_data g1").
		Select("g1.client_device_id, g1.latitude, g1.longitude, g1.gps_timestamp, g1.speed, g1.location_name, g1.ignition_status, g1.vehicle_status").
		Joins("INNER JOIN (SELECT client_device_id, MAX(gps_timestamp) as max_timestamp FROM gps_data GROUP BY client_device_id) g2 ON g1.client_device_id = g2.client_device_id AND g1.gps_timestamp = g2.max_timestamp")

	// Build the main query with LEFT JOIN to the latest GPS data
	return config.DB.Table("client_devices").
		Select(`client_devices.*,
			latest_gps.latitude as last_latitude,
			latest_gps.longitude as last_longitude,
			latest_gps.gps_timestamp as last_gps_timestamp,
			latest_gps.speed as last_speed,
			latest_gps.location_name as last_location_name,
			latest_gps.ignition_status as last_ignition_status,
			latest_gps.vehicle_status as last_vehicle_status`).
		Joins("LEFT JOIN (?) as latest_gps ON client_devices.id = latest_gps.client_device_id", latestGPSSubquery)
}

// GetClientDevicesWithLastGPS retrieves client devices with their last GPS data efficiently
func GetClientDevicesWithLastGPS(filter map[string]interface{}, preloads []string, pagination bool, c interface{}, searchTerm string) ([]models.ClientDevice, error) {
	var clientDevices []models.ClientDevice

	// Use the optimized query with GPS data (single query for performance)
	query := BuildClientDeviceQueryWithLastGPS()

	// Add search conditions
	if searchTerm != "" {
		query = query.Where("client_devices.name like ? or client_devices.id like ? or client_devices.device_id like ? or client_devices.phone_number like ? or client_devices.engine_number like ? or client_devices.chassis_number like ? or client_devices.vin like ? or client_devices.plate_number like ?",
			"%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%")
	}

	// Add pagination if requested
	if pagination {
		if ginContext, ok := c.(*gin.Context); ok {
			query = query.Scopes(Paginate(ginContext))
		}
	}

	// Apply filters
	if len(filter) > 0 {
		query = query.Where(filter)
	}

	// Use a struct that includes both ClientDevice and GPS fields
	type ClientDeviceWithGPS struct {
		models.ClientDevice
		LastLatitude       *float64   `json:"last_latitude"`
		LastLongitude      *float64   `json:"last_longitude"`
		LastGPSTimestamp   *time.Time `json:"last_gps_timestamp"`
		LastSpeed          *float64   `json:"last_speed"`
		LastLocationName   *string    `json:"last_location_name"`
		LastIgnitionStatus *bool      `json:"last_ignition_status"`
		LastVehicleStatus  *string    `json:"last_vehicle_status"`
	}

	var results []ClientDeviceWithGPS
	err := query.Scan(&results).Error
	if err != nil {
		return nil, err
	}

	// Convert to ClientDevice and map GPS fields
	for _, result := range results {
		device := result.ClientDevice
		device.LastLatitude = result.LastLatitude
		device.LastLongitude = result.LastLongitude
		device.LastGPSTimestamp = result.LastGPSTimestamp
		device.LastSpeed = result.LastSpeed
		device.LastLocationName = result.LastLocationName
		device.LastIgnitionStatus = result.LastIgnitionStatus
		device.LastVehicleStatus = result.LastVehicleStatus
		clientDevices = append(clientDevices, device)
	}

	// Load preloaded relationships efficiently in batch
	if len(clientDevices) > 0 && len(preloads) > 0 {
		// Get device IDs for preloading
		deviceIds := make([]uint, len(clientDevices))
		for i, device := range clientDevices {
			deviceIds[i] = device.Id
		}

		// Load preloaded relationships in batch
		preloadQuery := config.DB.Where("id IN ?", deviceIds)
		for _, preload := range preloads {
			preloadQuery = preloadQuery.Preload(preload)
		}

		var preloadedDevices []models.ClientDevice
		err = preloadQuery.Find(&preloadedDevices).Error
		if err != nil {
			return nil, err
		}

		// Create a map for quick lookup of preloaded data
		preloadMap := make(map[uint]models.ClientDevice)
		for _, device := range preloadedDevices {
			preloadMap[device.Id] = device
		}

		// Map preloaded relationships to our devices
		for i := range clientDevices {
			if preloaded, exists := preloadMap[clientDevices[i].Id]; exists {
				clientDevices[i].Client = preloaded.Client
				clientDevices[i].DeviceType = preloaded.DeviceType
				clientDevices[i].Fleet = preloaded.Fleet
				clientDevices[i].Driver = preloaded.Driver
			}
		}
	}

	return clientDevices, nil
}

// GetClientDeviceByIdWithLastGPS retrieves a single client device with last GPS data
func GetClientDeviceByIdWithLastGPS(id string, preloads []string) (models.ClientDevice, error) {
	var clientDevice models.ClientDevice

	// Use the optimized query with GPS data
	query := BuildClientDeviceQueryWithLastGPS()
	query = query.Where("client_devices.id = ?", id)

	// Use a struct that includes both ClientDevice and GPS fields
	type ClientDeviceWithGPS struct {
		models.ClientDevice
		LastLatitude       *float64   `json:"last_latitude"`
		LastLongitude      *float64   `json:"last_longitude"`
		LastGPSTimestamp   *time.Time `json:"last_gps_timestamp"`
		LastSpeed          *float64   `json:"last_speed"`
		LastLocationName   *string    `json:"last_location_name"`
		LastIgnitionStatus *bool      `json:"last_ignition_status"`
		LastVehicleStatus  *string    `json:"last_vehicle_status"`
	}

	var result ClientDeviceWithGPS
	err := query.Scan(&result).Error
	if err != nil {
		return models.ClientDevice{}, err
	}

	// Map the GPS fields to the struct
	clientDevice = result.ClientDevice
	clientDevice.LastLatitude = result.LastLatitude
	clientDevice.LastLongitude = result.LastLongitude
	clientDevice.LastGPSTimestamp = result.LastGPSTimestamp
	clientDevice.LastSpeed = result.LastSpeed
	clientDevice.LastLocationName = result.LastLocationName
	clientDevice.LastIgnitionStatus = result.LastIgnitionStatus
	clientDevice.LastVehicleStatus = result.LastVehicleStatus

	// Load preloaded relationships if needed
	if len(preloads) > 0 {
		preloadQuery := config.DB.Where("id = ?", clientDevice.Id)
		for _, preload := range preloads {
			preloadQuery = preloadQuery.Preload(preload)
		}

		var preloadedDevice models.ClientDevice
		err = preloadQuery.First(&preloadedDevice).Error
		if err != nil {
			return models.ClientDevice{}, err
		}

		// Map preloaded relationships
		clientDevice.Client = preloadedDevice.Client
		clientDevice.DeviceType = preloadedDevice.DeviceType
		clientDevice.Fleet = preloadedDevice.Fleet
		clientDevice.Driver = preloadedDevice.Driver
	}

	return clientDevice, nil
}

// SearchClientDevicesWithLastGPS searches client devices with last GPS data
func SearchClientDevicesWithLastGPS(filter map[string]interface{}, searchTerm string, preloads []string) ([]models.ClientDevice, error) {
	var clientDevices []models.ClientDevice

	// Use the optimized query with GPS data
	query := BuildClientDeviceQueryWithLastGPS()

	// Add search conditions
	if searchTerm != "" {
		query = query.Where("client_devices.name like ? or client_devices.id like ? or client_devices.device_id like ? or client_devices.phone_number like ? or client_devices.engine_number like ? or client_devices.chassis_number like ? or client_devices.vin like ? or client_devices.plate_number like ?",
			"%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%", "%"+searchTerm+"%")
	}

	// Apply filters
	if len(filter) > 0 {
		query = query.Where(filter)
	}

	// Use a struct that includes both ClientDevice and GPS fields
	type ClientDeviceWithGPS struct {
		models.ClientDevice
		LastLatitude       *float64   `json:"last_latitude"`
		LastLongitude      *float64   `json:"last_longitude"`
		LastGPSTimestamp   *time.Time `json:"last_gps_timestamp"`
		LastSpeed          *float64   `json:"last_speed"`
		LastLocationName   *string    `json:"last_location_name"`
		LastIgnitionStatus *bool      `json:"last_ignition_status"`
		LastVehicleStatus  *string    `json:"last_vehicle_status"`
	}

	var results []ClientDeviceWithGPS
	err := query.Scan(&results).Error
	if err != nil {
		return nil, err
	}

	// Convert to ClientDevice and map GPS fields
	for _, result := range results {
		device := result.ClientDevice
		device.LastLatitude = result.LastLatitude
		device.LastLongitude = result.LastLongitude
		device.LastGPSTimestamp = result.LastGPSTimestamp
		device.LastSpeed = result.LastSpeed
		device.LastLocationName = result.LastLocationName
		device.LastIgnitionStatus = result.LastIgnitionStatus
		device.LastVehicleStatus = result.LastVehicleStatus
		clientDevices = append(clientDevices, device)
	}

	// Load preloaded relationships efficiently in batch
	if len(clientDevices) > 0 && len(preloads) > 0 {
		// Get device IDs for preloading
		deviceIds := make([]uint, len(clientDevices))
		for i, device := range clientDevices {
			deviceIds[i] = device.Id
		}

		// Load preloaded relationships in batch
		preloadQuery := config.DB.Where("id IN ?", deviceIds)
		for _, preload := range preloads {
			preloadQuery = preloadQuery.Preload(preload)
		}

		var preloadedDevices []models.ClientDevice
		err = preloadQuery.Find(&preloadedDevices).Error
		if err != nil {
			return nil, err
		}

		// Create a map for quick lookup of preloaded data
		preloadMap := make(map[uint]models.ClientDevice)
		for _, device := range preloadedDevices {
			preloadMap[device.Id] = device
		}

		// Map preloaded relationships to our devices
		for i := range clientDevices {
			if preloaded, exists := preloadMap[clientDevices[i].Id]; exists {
				clientDevices[i].Client = preloaded.Client
				clientDevices[i].DeviceType = preloaded.DeviceType
				clientDevices[i].Fleet = preloaded.Fleet
				clientDevices[i].Driver = preloaded.Driver
			}
		}
	}

	return clientDevices, nil
}
