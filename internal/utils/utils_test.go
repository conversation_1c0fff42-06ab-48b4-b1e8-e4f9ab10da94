package utils

import (
	"os"
	"testing"
	"yotracker/config"
	"yotracker/internal/seed"
)

func SetupTestDBWithSeed(t *testing.T) {
	ForceProjectRoot()
	t.Helper()

	// Set up test environment variables
	if os.<PERSON>env("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.<PERSON>env("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.<PERSON>env("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.<PERSON>env("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}

	config.InitTestDB()
	seed.Seed()
}

func TestGenerateReference(t *testing.T) {
	// Initialize test database
	SetupTestDBWithSeed(t)

	//test for INV-2025/12/001
	result := GenerateReference("1")

	// Check if result is not empty
	if result == "" {
		t.Errorf("GenerateReference() returned empty string")
		return
	}

	// Check if result has minimum length (at least 3 characters for sequence number)
	if len(result) < 3 {
		t.Errorf("GenerateReference() result too short: %v", result)
		return
	}

	// If the result starts with a prefix (like "INV-"), check it
	if len(result) >= 4 && result[:4] == "INV-" {
		// Should contain a slash for year format
		found := false
		for _, char := range result {
			if char == '/' {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("GenerateReference() with INV- prefix should contain '/', got: %v", result)
		}
	} else {
		// For other formats, just ensure it's not empty and has reasonable length
		if len(result) < 3 {
			t.Errorf("GenerateReference() result too short: %v", result)
		}
	}
}
func TestGenerateUniqueID(t *testing.T) {
	id1 := generateUniqueID()
	id2 := generateUniqueID()

	if id1 == id2 {
		t.Errorf("generateUniqueID() should generate unique IDs, got same: %v", id1)
	}

	if len(id1) != 16 { // 8 bytes = 16 hex characters
		t.Errorf("generateUniqueID() should return 16 characters, got %v: %v", len(id1), id1)
	}
}
