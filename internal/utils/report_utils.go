package utils

import (
	"fmt"
	"reflect"
	"strings"
	"time"
)

// GetHeadersFromStruct extracts CSV headers from struct tags
func GetHeadersFromStruct(data interface{}) []string {
	var headers []string

	// Get the type of the data
	t := reflect.TypeOf(data)

	// If it's a slice, get the element type
	if t.Kind() == reflect.Slice {
		t = t.Elem()
	}

	// If it's a pointer, get the element type
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	// If it's a struct, extract headers from tags
	if t.Kind() == reflect.Struct {
		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			if csvTag := field.Tag.Get("csv"); csvTag != "" {
				headers = append(headers, csvTag)
			}
		}
	}

	return headers
}

// ConvertStructToCSV converts a slice of structs to CSV format
func ConvertStructToCSV(data interface{}) [][]string {
	var rows [][]string

	// Get headers
	headers := GetHeadersFromStruct(data)
	rows = append(rows, headers)

	// Get the value of the data
	v := reflect.ValueOf(data)

	// If it's a pointer, get the element
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}

	// If it's a slice, iterate through elements
	if v.Kind() == reflect.Slice {
		for i := 0; i < v.Len(); i++ {
			row := structToCSVRow(v.Index(i))
			rows = append(rows, row)
		}
	}

	return rows
}

// structToCSVRow converts a single struct to a CSV row
func structToCSVRow(v reflect.Value) []string {
	var row []string

	// If it's a pointer, get the element
	if v.Kind() == reflect.Ptr {
		if v.IsNil() {
			return []string{}
		}
		v = v.Elem()
	}

	// If it's a struct, extract values
	if v.Kind() == reflect.Struct {
		t := v.Type()
		for i := 0; i < t.NumField(); i++ {
			field := t.Field(i)
			fieldValue := v.Field(i)

			// Skip if no CSV tag
			if field.Tag.Get("csv") == "" {
				continue
			}

			// Convert field value to string
			row = append(row, valueToString(fieldValue))
		}
	}

	return row
}

// valueToString converts a reflect.Value to string
func valueToString(v reflect.Value) string {
	// Handle nil values
	if !v.IsValid() {
		return ""
	}

	// Handle pointers
	if v.Kind() == reflect.Ptr {
		if v.IsNil() {
			return ""
		}
		v = v.Elem()
	}

	// Convert based on type
	switch v.Kind() {
	case reflect.String:
		return v.String()
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return fmt.Sprintf("%d", v.Int())
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return fmt.Sprintf("%d", v.Uint())
	case reflect.Float32, reflect.Float64:
		return fmt.Sprintf("%.2f", v.Float())
	case reflect.Bool:
		return fmt.Sprintf("%t", v.Bool())
	case reflect.Struct:
		// Handle time.Time
		if v.Type() == reflect.TypeOf(time.Time{}) {
			t := v.Interface().(time.Time)
			return t.Format("2006-01-02 15:04:05")
		}
		return fmt.Sprintf("%v", v.Interface())
	default:
		return fmt.Sprintf("%v", v.Interface())
	}
}

// FormatDuration formats duration in a human-readable format
func FormatDuration(seconds int) string {
	if seconds < 60 {
		return fmt.Sprintf("%ds", seconds)
	} else if seconds < 3600 {
		minutes := seconds / 60
		remainingSeconds := seconds % 60
		return fmt.Sprintf("%dm %ds", minutes, remainingSeconds)
	} else {
		hours := seconds / 3600
		remainingMinutes := (seconds % 3600) / 60
		return fmt.Sprintf("%dh %dm", hours, remainingMinutes)
	}
}

// FormatDistance formats distance with appropriate units
func FormatDistance(km float64) string {
	if km < 1 {
		return fmt.Sprintf("%.0fm", km*1000)
	}
	return fmt.Sprintf("%.1fkm", km)
}

// FormatSpeed formats speed with units
func FormatSpeed(kmh float64) string {
	return fmt.Sprintf("%.1f km/h", kmh)
}

// FormatFuel formats fuel consumption with units
func FormatFuel(liters float64) string {
	return fmt.Sprintf("%.1f L", liters)
}

// FormatCost formats cost with currency
func FormatCost(cost float64) string {
	return fmt.Sprintf("$%.2f", cost)
}

// FormatPercentage formats percentage
func FormatPercentage(value float64) string {
	return fmt.Sprintf("%.1f%%", value)
}

// CalculateRiskLevel calculates risk level based on safety score
func CalculateRiskLevel(safetyScore float64) string {
	switch {
	case safetyScore >= 90:
		return "Low"
	case safetyScore >= 70:
		return "Medium"
	case safetyScore >= 50:
		return "High"
	default:
		return "Critical"
	}
}

// CalculateSeverity calculates severity based on speed exceeded
func CalculateSeverity(speedExceeded float64) string {
	switch {
	case speedExceeded >= 30:
		return "Critical"
	case speedExceeded >= 20:
		return "High"
	case speedExceeded >= 10:
		return "Medium"
	default:
		return "Low"
	}
}

// CalculateEfficiency calculates fuel efficiency
func CalculateEfficiency(distance, fuelConsumed float64) float64 {
	if fuelConsumed <= 0 {
		return 0
	}
	return distance / fuelConsumed
}

// CalculateUtilizationRate calculates vehicle utilization rate
func CalculateUtilizationRate(activeHours, totalHours float64) float64 {
	if totalHours <= 0 {
		return 0
	}
	return (activeHours / totalHours) * 100
}

// CalculateSafetyScore calculates driver safety score
func CalculateSafetyScore(speedingEvents, harshBraking, harshAcceleration, harshCornering, totalTrips int) float64 {
	if totalTrips == 0 {
		return 0
	}

	// Base score starts at 100
	score := 100.0

	// Deduct points for violations
	score -= float64(speedingEvents) * 5
	score -= float64(harshBraking) * 3
	score -= float64(harshAcceleration) * 3
	score -= float64(harshCornering) * 2

	// Ensure score doesn't go below 0
	if score < 0 {
		score = 0
	}

	return score
}

// CalculateBehaviorScore calculates comprehensive behavior score
func CalculateBehaviorScore(safetyScore, fuelEfficiency, idleTimePercentage float64) float64 {
	// Weighted average of different factors
	safetyWeight := 0.4
	efficiencyWeight := 0.3
	idleWeight := 0.3

	// Normalize fuel efficiency (assume 0-20 km/L range)
	normalizedEfficiency := (fuelEfficiency / 20.0) * 100
	if normalizedEfficiency > 100 {
		normalizedEfficiency = 100
	}

	// Invert idle time percentage (lower is better)
	idleScore := 100 - idleTimePercentage

	// Calculate weighted score
	behaviorScore := (safetyScore * safetyWeight) +
		(normalizedEfficiency * efficiencyWeight) +
		(idleScore * idleWeight)

	return behaviorScore
}

// GetImprovementAreas identifies areas for driver improvement
func GetImprovementAreas(speedingEvents, harshBraking, harshAcceleration, harshCornering int, idleTimePercentage float64) string {
	var areas []string

	if speedingEvents > 0 {
		areas = append(areas, "Speed Management")
	}
	if harshBraking > 0 {
		areas = append(areas, "Smooth Braking")
	}
	if harshAcceleration > 0 {
		areas = append(areas, "Smooth Acceleration")
	}
	if harshCornering > 0 {
		areas = append(areas, "Cornering Technique")
	}
	if idleTimePercentage > 20 {
		areas = append(areas, "Idle Time Reduction")
	}

	if len(areas) == 0 {
		return "No major improvements needed"
	}

	return strings.Join(areas, ", ")
}
