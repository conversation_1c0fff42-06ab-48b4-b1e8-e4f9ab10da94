package utils

import (
	"fmt"
	"time"
	"yotracker/internal/models"
)

// GetIgnitionStatusDuration calculates how long the ignition has been in its current state
func GetIgnitionStatusDuration(device *models.ClientDevice) (time.Duration, error) {
	if device.IgnitionStatusTime == nil {
		// If no ignition status time is recorded, return 0 duration
		return 0, nil
	}

	now := time.Now()
	duration := now.Sub(*device.IgnitionStatusTime)
	return duration, nil
}

// GetIgnitionStatusDurationMinutes returns the duration in minutes since last ignition status change
func GetIgnitionStatusDurationMinutes(device *models.ClientDevice) (float64, error) {
	duration, err := GetIgnitionStatusDuration(device)
	if err != nil {
		return 0, err
	}

	return duration.Minutes(), nil
}

// GetIgnitionStatusDurationHours returns the duration in hours since last ignition status change
func GetIgnitionStatusDurationHours(device *models.ClientDevice) (float64, error) {
	duration, err := GetIgnitionStatusDuration(device)
	if err != nil {
		return 0, err
	}

	return duration.Hours(), nil
}

// IsIgnitionOnForDuration checks if ignition has been on for a specific duration
func IsIgnitionOnForDuration(device *models.ClientDevice, minDuration time.Duration) (bool, error) {
	if device.IgnitionStatus == nil || !*device.IgnitionStatus {
		return false, nil // Ignition is off
	}

	duration, err := GetIgnitionStatusDuration(device)
	if err != nil {
		return false, err
	}

	return duration >= minDuration, nil
}

// IsIgnitionOffForDuration checks if ignition has been off for a specific duration
func IsIgnitionOffForDuration(device *models.ClientDevice, minDuration time.Duration) (bool, error) {
	if device.IgnitionStatus == nil || *device.IgnitionStatus {
		return false, nil // Ignition is on
	}

	duration, err := GetIgnitionStatusDuration(device)
	if err != nil {
		return false, err
	}

	return duration >= minDuration, nil
}

// FormatIgnitionDuration formats the ignition duration into a human-readable string
func FormatIgnitionDuration(device *models.ClientDevice) (string, error) {
	duration, err := GetIgnitionStatusDuration(device)
	if err != nil {
		return "", err
	}

	if duration == 0 {
		return "Unknown", nil
	}

	hours := int(duration.Hours())
	minutes := int(duration.Minutes()) % 60
	seconds := int(duration.Seconds()) % 60

	if hours > 0 {
		return fmt.Sprintf("%dh %dm %ds", hours, minutes, seconds), nil
	} else if minutes > 0 {
		return fmt.Sprintf("%dm %ds", minutes, seconds), nil
	} else {
		return fmt.Sprintf("%ds", seconds), nil
	}
}
