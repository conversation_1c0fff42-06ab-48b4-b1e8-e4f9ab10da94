package seed

import (
	"context"
	"log"
	"os"
	"strings"
	"time"
	"yotracker/config"
	"yotracker/migrations"
)

func runSqlFile(filePath string) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		log.Printf("Failed to read file: %s, %v", filePath, err)
		return
	}
	queries := strings.Split(string(content), ";")
	for _, query := range queries {
		if strings.TrimSpace(query) != "" {
			// Replace INSERT with INSERT IGNORE to handle duplicates gracefully
			modifiedQuery := strings.ReplaceAll(query, "insert into", "INSERT IGNORE INTO")
			modifiedQuery = strings.ReplaceAll(modifiedQuery, "INSERT INTO", "INSERT IGNORE INTO")

			// Execute query with timeout to prevent hanging
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			err := config.DB.WithContext(ctx).Exec(modifiedQuery).Error
			cancel()

			if err != nil {
				// Log error but don't fail - this allows for duplicate entries
				log.Printf("Failed to execute query: %s, Error: %v", modifiedQuery, err)
			}
		}
	}
}

func Seed() {
	migrations.Migrate()

	// Disable foreign key checks during seeding
	config.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	defer config.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

	// Use INSERT IGNORE or handle duplicates gracefully
	runSqlFile("migrations/seeders/settings.sql")
	runSqlFile("migrations/seeders/countries.sql")
	runSqlFile("migrations/seeders/protocols.sql")
	runSqlFile("migrations/seeders/device_types.sql")
	runSqlFile("migrations/seeders/roles.sql")
	//runSqlFile("migrations/seeders/permissions.sql")
	//runSqlFile("migrations/seeders/role_permissions.sql")
	runSqlFile("migrations/seeders/currencies.sql")
	runSqlFile("migrations/seeders/payment_types.sql")
	runSqlFile("migrations/seeders/tax_rates.sql")
	runSqlFile("migrations/seeders/users.sql")
	runSqlFile("migrations/seeders/reports.sql")

	// Run sample data for new report tables
	runSqlFile("migrations/seeders/report_data.sql")
}
