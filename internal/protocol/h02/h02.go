package h02

import (
	"bufio"
	"bytes"
	"encoding/binary"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"
)

type Parser struct {
	Conn   net.Conn
	Client *Client
}
type errUnsupportedPacket struct {
	packetType string
}

func (e errUnsupportedPacket) Is(err error) bool {
	_, isa := err.(*errUnsupportedPacket)
	return isa
}
func (e errUnsupportedPacket) Error() string {
	return fmt.Sprintf("unable to parse %q is an unsupported packet type", e.packetType)
}

const LOCATION_DATA_PACKET_V1 = "V1"
const LOCATION_DATA_PACKET_V2 = "V2"
const HEARTBEAT_PACKET = "XT"
const MSG_COMMAND_RESPONSE = "V4"

type Packet struct {
	DeviceId       string    `json:"device_id"`
	Timestamp      time.Time `json:"timestamp"`
	Latitude       float64   `json:"latitude"`
	Longitude      float64   `json:"longitude"`
	Heading        float64   `json:"heading"`
	Speed          float64   `json:"speed"`
	Position       bool      `json:"position"`
	Battery        float64   `json:"battery"`
	packetType     string
	RawData        string
	AdditionalData json.RawMessage
}
type GpsData struct {
	IMEI           string
	Timestamp      time.Time
	Latitude       float64
	Longitude      float64
	Speed          float64
	Course         uint16
	Satellites     uint8
	SerialNumber   uint16
	CellId         string
	Mcc            string
	Mnc            string
	Lac            string
	Direction      string
	IgnitionStatus bool
	BatteryLevel   string
}
type AlarmStatus struct {
	GPSMistake        bool
	MoveAlarm         bool
	BlindRecordAlarm  bool
	OilCutOff         bool
	BatteryDemolition bool
	HomeSOS           bool
	OfficeSOS         bool
	LowLevelSensor1   bool
	IllegalDoorOpen   bool
	ShockAlarm        bool
	TiltAlarm         bool
	BackupBattery     bool
	BatteryRemove     bool
	GPSAntennaDisc    bool
	GPSAntennaShort   bool
	LowLevelSensor2   bool
	IllegalIgnition   bool
	ArmedMode         bool
	ACCOff            bool
	SpeedAlarm        bool
	CrashAlarm        bool
	EnteringAlarm     bool
	PumpAlarm         bool
	CustomAlarm       bool
	SOSAlarm          bool
	GeofenceAlarm     bool
	TowAlarm          bool
	DoorOpenAlarm     bool
	OutAlarm          bool
	RawAlarmBits      string
}
type Client struct {
	Conn         net.Conn
	IMEI         string
	Protocol     string
	Device       models.ClientDevice
	CommandChan  chan Command
	ResponseChan chan Message
	ExitChan     chan struct{}
}
type Command struct {
	Data         []byte
	ResponseChan chan []byte
	Timeout      time.Duration
}
type Message struct {
	Type string
	Data []byte
}

// Global slice to store connected clients
var clients []Client
var mu sync.Mutex // Mutex to protect the clients slice
func (p *Parser) HandleConnection() {
	defer func() {
		p.removeClient()
		err := p.Conn.Close()
		if err != nil {
			return
		}
	}()
	fmt.Println("H02 Connection accepted")
	client := Client{
		Conn:         p.Conn,
		CommandChan:  make(chan Command),
		ExitChan:     make(chan struct{}),
		ResponseChan: make(chan Message),
	}
	p.Client = &client
	go p.readConnection()
	for {
		select {
		case <-client.ExitChan:
			p.removeClient()
			return
		case msg := <-client.ResponseChan:
			p.processMessage(msg)
		case cmd := <-client.CommandChan:
			fmt.Println("H02 Command received", string(cmd.Data))
			err := p.sendToDevice(cmd.Data)
			if err != nil {
				fmt.Println("Error sending h02 command:", err.Error())
				data := fmt.Sprintf("*HQ,%s,V4,FAIL#", p.Client.IMEI)
				cmd.ResponseChan <- []byte(data)
				continue
			}
			//wait for response in a separate routine
			go func(cmd Command) {
				timer := time.NewTimer(cmd.Timeout)
				defer timer.Stop()
				for {
					select {
					case msg := <-client.ResponseChan:
						if msg.Type == MSG_COMMAND_RESPONSE {
							cmd.ResponseChan <- msg.Data
							return
						} else {
							p.processMessage(msg)
						}
					case <-timer.C:
						fmt.Println("H02 Command timed out")
						data := fmt.Sprintf("*HQ,%s,V4,FAIL#", p.Client.IMEI)
						cmd.ResponseChan <- []byte(data)
						return
					}
				}
			}(cmd)
		}

		reader := bufio.NewReader(p.Conn)
		data, err := reader.ReadBytes('#')
		if err != nil {
			fmt.Println("Error reading data:", err)
			return
		}
		if data[0] == '*' {
			p.readAsciiPacket(data)
			if err != nil {
				fmt.Println("ascii error:", err)
				return
			}
		} else if data[0] == '$' {
			p.readBinaryPacket(data)
		} else {
			fmt.Println("Unknown packet.")
			//return
		}
	}
}
func (p *Parser) readConnection() {
	reader := bufio.NewReader(p.Conn)
	for {
		select {
		case <-p.Client.ExitChan:
			return
		default:
			//continue reading
		}
		p.Conn.SetReadDeadline(time.Now().Add(30 * time.Second))
		peek, err := reader.Peek(3)
		if err != nil {
			fmt.Println("Error peeking data:", err.Error())
			p.Client.ExitChan <- struct{}{}
			return
		}
		if len(peek) < 3 {
			continue //not enough data, continue reading
		}
		var data []byte
		var msgType string
		if isTextMessage(peek) {
			data, err = reader.ReadBytes('#')
			str := strings.TrimSuffix(string(data), "#")
			parts := strings.Split(str, ",")
			msgType = parts[2]
		} else {
			data := make([]byte, 42)
			_, err = reader.Read(data)
		}
		if err != nil {
			fmt.Println("Error reading text message:", err.Error())
			p.Client.ExitChan <- struct{}{}
		}
		if len(data) > 0 {
			p.Client.ResponseChan <- Message{
				Type: msgType,
				Data: data,
			}
		}

	}
}
func (p *Parser) processMessage(msg Message) {
	switch msg.Data[0] {
	case '*':
		p.readAsciiPacket(msg.Data)
		return
	case '$':
		p.readBinaryPacket(msg.Data)
		return
	default:
		fmt.Println("Unknown packet.")
	}
}
func (p *Parser) processPendingCommands() {
	// Check if there are any pending commands for this device
	var commandLogs []models.CommandLog
	config.DB.Where("client_device_id = ? AND status = ?", p.Client.Device.Id, "pending").Find(&commandLogs)
	for _, commandLog := range commandLogs {
		// Process the command
		var parameters map[string]interface{}
		currentTime := time.Now().UTC()
		commandContent := ""
		name := commandLog.Name
		switch name {
		case "cutoff_oil":
			commandContent = formatCommand(currentTime, p.Client.IMEI, "S20", "1", "1")
		case "enable_oil":
			commandContent = formatCommand(currentTime, p.Client.IMEI, "S20", "1", "0")
		case "speed_alert":
			err := json.Unmarshal([]byte(commandLog.Parameters), &parameters)
			if err != nil {
				fmt.Println("Error unmarshalling speed alert parameters:", err)
				commandLog.Status = "failed"
				config.DB.Save(commandLog)
				return
			}
			speed := int(parameters["speed"].(float64))
			commandContent = formatCommand(currentTime, p.Client.IMEI, "S33", strconv.Itoa(speed))
		case "set_alarm":
			commandContent = formatCommand(currentTime, p.Client.IMEI, "SCF", "1", "1")
		case "disable_alarm":
			commandContent = formatCommand(currentTime, p.Client.IMEI, "SCF", "0", "0")
		case "restart":
			commandContent = formatCommand(currentTime, p.Client.IMEI, "R1")
		case "geofence":

		case "custom":
			commandContent = formatCommand(currentTime, p.Client.IMEI, *commandLog.CommandContent)
		}
		if commandContent != "" {
			go p.sendCommand(&commandLog, commandContent)
		}

	}
}
func isTextMessage(data []byte) bool {
	return bytes.HasPrefix(data, []byte("*HQ"))
}

func (p *Parser) isClientInSlice() bool {
	mu.Lock()
	defer mu.Unlock()
	for _, client := range clients {
		if client.Conn == p.Client.Conn {
			return true
		}
	}
	return false
}
func (p *Parser) addClientIfNotExists(imei string) {
	if !p.isClientInSlice() {
		mu.Lock()
		defer mu.Unlock()
		clients = append(clients, *p.Client)
		fmt.Println("Client connected with IMEI:", imei)
		//set device as online
		utils.SetDeviceAsOnline(&p.Client.Device)
		p.processPendingCommands()
	}
}
func (p *Parser) readAsciiPacket(data []byte) {
	str := strings.TrimSuffix(string(data), "#")
	fmt.Println("reading ascii packet:", str)
	parts := strings.Split(str, ",")
	device, err := utils.FindDevice(parts[1])
	if err != nil {
		fmt.Println("Device not found")
		p.Client.ExitChan <- struct{}{}
		return
	}
	p.Client.IMEI = parts[1]
	p.Client.Device = *device
	// Add the client to the global clients slice and map
	p.addClientIfNotExists(p.Client.IMEI)
	switch parts[0] + ":" + parts[2] {
	case "*HQ:V1": // Location
		p.handleLocationPacket(str)
		return
	case "*HQ:V5": // Location
		p.handleLocationPacket(str)
		return
	case "*HQ:V19": // Sim data
		return
	case "*HQ:V4":
		fmt.Println("Instruction acknowledgment")
		return
	case "*HQ:ALRM":
		fmt.Println("alarm message received")
		return
	case "*HQ:XT":
		fmt.Println("Heartbeat packet")
		return
	case "*HQ:VI1":
		fmt.Println("Location Response packet")
		return
	}

	return
}
func (p *Parser) handleLocationPacket(data string) {
	gpsDataPacket := GpsData{
		IMEI: p.Client.IMEI,
	}
	parts := strings.Split(data, ",")
	ts := parts[11] + parts[3]
	var err error
	if gpsDataPacket.Timestamp, err = time.Parse("020106150405", ts); err != nil {
		fmt.Printf("%s (%s != 020106150405)", err, ts)
		return
	}

	if gpsDataPacket.Latitude, err = strLatitude(parts[5], strings.EqualFold(parts[6], "S")); err != nil {
		fmt.Printf("failed to parse latitude (%s): %s", parts[6], err)
		return
	}

	if gpsDataPacket.Longitude, err = strLongitude(parts[7], strings.EqualFold(parts[8], "W")); err != nil {
		fmt.Printf("failed to parse longitude (%s): %s", parts[8], err)
		return
	}

	if gpsDataPacket.Speed, err = strconv.ParseFloat(parts[9], 64); err != nil {
		fmt.Printf("failed to parse speed (%v): %s", data[9], err)
		return
	}
	gpsDataPacket.Speed *= 1.852 // Convert from knots to km/hr
	directionPart, _ := strconv.Atoi(parts[10])
	directionInt := uint16(directionPart)
	gpsDataPacket.Direction = utils.GetDirection(directionInt)
	gpsDataPacket.Mcc = parts[13]
	gpsDataPacket.Mnc = parts[14]
	gpsDataPacket.Lac = parts[15]
	gpsDataPacket.CellId = parts[16]
	alarmStatus, _ := decodeAlarmBits(parts[12])
	gpsDataPacket.IgnitionStatus = alarmStatus.ACCOff == false
	alarmJSON, err := json.Marshal(alarmStatus)
	if err != nil {
		fmt.Println("Error marshalling JSON:", err)
		return
	}
	//save packet
	gpsData := services.GPSData{
		Timestamp:      gpsDataPacket.Timestamp,
		DeviceId:       gpsDataPacket.IMEI,
		Latitude:       gpsDataPacket.Latitude,
		Longitude:      gpsDataPacket.Longitude,
		Speed:          gpsDataPacket.Speed,
		Direction:      gpsDataPacket.Direction,
		Mcc:            gpsDataPacket.Mcc,
		Mnc:            gpsDataPacket.Mnc,
		Lac:            gpsDataPacket.Lac,
		CellID:         gpsDataPacket.CellId,
		IgnitionStatus: gpsDataPacket.IgnitionStatus,
		AdditionalData: json.RawMessage(alarmJSON),
	}
	gpsData.SaveGPSData()
	p.processPendingCommands()
	go func() {
		slackService, err := services.NewSlackService()
		if err != nil {
			log.Printf("Failed to initialize Slack service: %v", err)
			return
		}
		if err := slackService.SendRawDataLog(*p.Client.Device.Name, p.Client.Device.DeviceId, data); err != nil {
			log.Printf("Failed to send Slack alert: %v", err)
		}
	}()
	if err := p.Respond(); err != nil {
		fmt.Println(err.Error())
	}

}
func (p *Parser) readBinaryPacket(data []byte) {

	fmt.Println("reading binary packet", data, "with hex:", hex.EncodeToString(data))

	if len(data) < 50 {
		fmt.Println("Not enough data")
		return
		//return nil, errors.New("not enough data")
	}
	fmt.Println("Device id from binary", binary.BigEndian.Uint64(data[1:6]))
	return
	deviceId := binary.BigEndian.Uint64(data[1:6])
	var timestamp time.Time
	var err error
	var longitude float64
	var latitude float64
	var speed float64

	ts := hex.EncodeToString(data[5:11])
	if timestamp, err = time.Parse("150405060102", ts); err != nil {
		fmt.Printf("%s (%s != 150405060102)", err, ts)
		return
	}

	west := data[20]&8 == 8
	south := data[20]&4 == 4
	hexLongitude := hex.EncodeToString(data[16:21])

	if longitude, err = strLongitude(hexLongitude[0:5]+"."+hexLongitude[5:9], south); err != nil {
		fmt.Printf("failed to parse longitude (%s): %s", hexLongitude[0:5]+"."+hexLongitude[5:9], err)
		return
	}

	if latitude, err = strLatitude(hex.EncodeToString(data[11:13])+"."+hex.EncodeToString(data[13:15]), west); err != nil {
		fmt.Printf("failed to parse latitude (%s): %s", hex.EncodeToString(data[11:13])+"."+hex.EncodeToString(data[13:15]), err)
		return
	}

	hexSpeedDir := hex.EncodeToString(data[21:24])
	if speed, err = strconv.ParseFloat(hexSpeedDir[0:3], 64); err != nil {
		fmt.Printf("failed to parse speed (%s): %s", hexSpeedDir[0:3], err)
		return
	}

	speed *= 1.852 // Convert from knots to km/hr

	device, err := utils.FindDevice(fmt.Sprint(deviceId))
	if err != nil {
		fmt.Println("Device not found")
		return
	}
	// Add the client to the global clients slice and map
	mu.Lock()
	p.Client.IMEI = fmt.Sprint(deviceId)
	p.Client.Device = *device
	p.addClientIfNotExists(fmt.Sprint(deviceId))
	mu.Unlock()
	fmt.Println(timestamp, longitude, latitude, speed)
	return
}
func strLongitude(pos string, west bool) (float64, error) {
	return strDDMtoDD(2, pos, west)
}

func strLatitude(pos string, south bool) (float64, error) {
	return strDDMtoDD(2, pos, south)
}

// strDDMtoDD will convert the string representation of Decimal Degrees and Minutes to
// Decimal Degrees, x is 2 for Latitude or 3 for Longitude.
func strDDMtoDD(degreeDigits int, pos string, negative bool) (float64, error) {
	if len(pos) <= degreeDigits {
		return 0, errors.New("invalid ddm data")
	}

	degreesStr := pos[:degreeDigits]
	minutesStr := pos[degreeDigits:]

	degrees, err := strconv.ParseFloat(degreesStr, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse degrees (%s): %w", degreesStr, err)
	}

	minutes, err := strconv.ParseFloat(minutesStr, 64)
	if err != nil {
		return 0, fmt.Errorf("failed to parse minutes (%s): %w", minutesStr, err)
	}

	dd := ddmtodd(degrees, minutes)

	if negative {
		dd *= -1.0
	}

	return dd, nil
}

func ddmtodd(d, dm float64) float64 {
	return d + (dm / 60)
}

func batteryConversion(in int) float64 {
	if in == 0 {
		return 0
	}

	if in <= 3 {
		return float64((in - 1) * 10)
	}

	if in <= 6 {
		return float64((in - 1) * 20)
	}

	if in <= 100 {
		return float64(in)
	}

	if in >= 0xF1 && in <= 0xF6 {
		return float64(in - 0xF0)
	}

	return 0
}

// decodeAlarmBits converts a 4-byte hex string to an AlarmStatus struct
func decodeAlarmBits(alarmHex string) (*AlarmStatus, error) {
	// Ensure the alarm hex is 8 characters (4 bytes)
	if len(alarmHex) != 8 {
		return nil, fmt.Errorf("invalid alarm hex length: expected 8, got %d", len(alarmHex))
	}

	// Convert hex string to bytes
	alarmBytes, err := hex.DecodeString(alarmHex)
	if err != nil {
		return nil, fmt.Errorf("invalid hex format: %s", alarmHex)
	}

	// Convert bytes to a 32-bit integer
	alarmValue := (uint32(alarmBytes[0]) << 24) | (uint32(alarmBytes[1]) << 16) | (uint32(alarmBytes[2]) << 8) | uint32(alarmBytes[3])

	// Create the AlarmStatus struct with bitwise checks
	alarmStatus := &AlarmStatus{
		GPSMistake:        (alarmValue & (1 << 0)) != 0,
		MoveAlarm:         (alarmValue & (1 << 1)) != 0,
		BlindRecordAlarm:  (alarmValue & (1 << 2)) != 0,
		OilCutOff:         (alarmValue & (1 << 3)) != 0,
		BatteryDemolition: (alarmValue & (1 << 4)) != 0,
		HomeSOS:           (alarmValue & (1 << 5)) != 0,
		OfficeSOS:         (alarmValue & (1 << 6)) != 0,
		LowLevelSensor1:   (alarmValue & (1 << 7)) != 0,
		IllegalDoorOpen:   (alarmValue & (1 << 8)) != 0,
		ShockAlarm:        (alarmValue & (1 << 9)) != 0,
		TiltAlarm:         (alarmValue & (1 << 10)) != 0,
		BackupBattery:     (alarmValue & (1 << 11)) != 0,
		BatteryRemove:     (alarmValue & (1 << 12)) != 0,
		GPSAntennaDisc:    (alarmValue & (1 << 13)) != 0,
		GPSAntennaShort:   (alarmValue & (1 << 14)) != 0,
		LowLevelSensor2:   (alarmValue & (1 << 15)) != 0,
		IllegalIgnition:   (alarmValue & (1 << 16)) != 0,
		ArmedMode:         (alarmValue & (1 << 17)) != 0,
		ACCOff:            (alarmValue & (1 << 18)) != 0,
		SpeedAlarm:        (alarmValue & (1 << 19)) != 0,
		CrashAlarm:        (alarmValue & (1 << 20)) != 0,
		EnteringAlarm:     (alarmValue & (1 << 21)) != 0,
		PumpAlarm:         (alarmValue & (1 << 22)) != 0,
		CustomAlarm:       (alarmValue & (1 << 23)) != 0,
		SOSAlarm:          (alarmValue & (1 << 24)) != 0,
		GeofenceAlarm:     (alarmValue & (1 << 25)) != 0,
		TowAlarm:          (alarmValue & (1 << 26)) != 0,
		DoorOpenAlarm:     (alarmValue & (1 << 27)) != 0,
		OutAlarm:          (alarmValue & (1 << 28)) != 0,
		RawAlarmBits:      strconv.FormatUint(uint64(alarmValue), 2), // Convert integer to binary
	}

	return alarmStatus, nil
}
func (p *Packet) Device() string {
	return p.DeviceId
}
func (p *Parser) Respond() error {
	data := fmt.Sprintf(`*HQ,%s,V4,V1,%s#`, p.Client.IMEI, time.Now().In(time.UTC).Format(`20060102150405`))
	err := p.sendToDevice([]byte(data))
	return err
}

func (p *Packet) WantsResponse() bool {
	return p.packetType == "HQ:V1"
}

func (p *Packet) Valid() bool {
	return true
}

// Function to remove a client
func (p *Parser) removeClient() {
	fmt.Println("H02 Client disconnected")
	mu.Lock()
	defer mu.Unlock()
	for i, client := range clients {
		if client.Conn == p.Conn {
			utils.SetDeviceAsOffline(&client.Device)
			clients = append(clients[:i], clients[i+1:]...)
			break
		}
	}
}

// formatCommand creates a formatted command string for H02 protocol
// Parameters:
//   - t: time to use in the command
//   - uniqueId: device unique identifier
//   - commandType: type of command
//   - params: variable number of additional parameters
//
// Returns:
//   - formatted command string
func formatCommand(t time.Time, uniqueId string, commandType string, params ...string) string {
	// Start with the base format: *MARKER,uniqueId,commandType,HHMMSS
	result := fmt.Sprintf("*HQ,%s,%s,%s", uniqueId, commandType, t.Format("150405"))

	// Append any additional parameters
	for _, param := range params {
		result += "," + param
	}

	// Add the terminating character
	result += "#"

	return result
}
func (p *Parser) sendCommand(commandLog *models.CommandLog, commandContent string) {
	responseChan := make(chan []byte)
	commandLog.Status = "processing"
	config.DB.Save(commandLog)
	fmt.Println("Sending command", commandContent)
	p.Client.CommandChan <- Command{
		Data:         []byte(commandContent),
		ResponseChan: responseChan,
		Timeout:      60 * time.Second,
	}
	for {
		select {
		case response := <-responseChan:
			fmt.Println("Received response from h02 custom command", response)
			// Check if the response contains "failed" or "FAIL"
			responseStr := string(response)
			commandLog.Description = &responseStr
			if strings.Contains(strings.ToLower(responseStr), "fail") || strings.Contains(strings.ToLower(responseStr), "failed") {
				commandLog.Status = "failed"
			} else {
				commandLog.Status = "completed"
			}
			config.DB.Save(&commandLog)
			close(responseChan)
			return
		}

	}
}
func (p *Parser) sendToDevice(data []byte) error {
	if p.Conn == nil {
		return errors.New("no active connection")
	}
	mu.Lock()
	defer mu.Unlock()
	_, err := p.Conn.Write(data)
	if err != nil {
		return err
	}
	return nil
}
