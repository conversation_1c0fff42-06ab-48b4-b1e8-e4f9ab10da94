package payment_gateways

import (
	"crypto/sha512"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
)

var PaynowFieldOrder = []string{
	"id",
	"reference",
	"amount",
	"additionalinfo",
	"returnurl",
	"resulturl",
	"status",
	"authemail",
	"authphone",
	"authname",
	"method",
	"phone",
	"token",
	"merchanttrace",
}

const INITIATE_TRANSACTION_URL = "https://www.paynow.co.zw/interface/initiatetransaction"
const INITIATE_MOBILE_TRANSACTION_URL = "https://www.paynow.co.zw/interface/remotetransaction"

type Paynow struct {
	IntegrationId  int
	IntegrationKey string
	ReturnUrl      string
	ResultUrl      string
	Reference      string
	Amount         float64
	AdditionalInfo string
	UserEmail      string
	UserName       string
	UserPhone      string
	Status         string
	Hash           string
	Method         string
	Token          string
	Phone          string
	MerchantTrace  string
	Items          []PaynowItem
}
type PaynowResponse struct {
	PollUrl              string
	BrowserUrl           string
	Instructions         string
	Status               string
	Error                string
	Hash                 string
	AuthorizationCode    string
	AuthorizationExpires string
	OtpReference         string
}
type PaynowItem struct {
	Name   string
	Amount float64
}

// GenerateHash constructs the hash string in the specific field order required by Paynow.
func (p *Paynow) GenerateHash(data []string) string {
	fmt.Println("Generating hash for data:", data)
	hashString := ""
	for _, val := range data {
		hashString += val
	}
	hashString += p.IntegrationKey
	hash := sha512.Sum512([]byte(hashString))
	return strings.ToUpper(hex.EncodeToString(hash[:]))
}
func (p *Paynow) VerifyHash(data []string, hash string) bool {
	return p.GenerateHash(data) == hash
}
func (p *Paynow) AddItem(name string, amount float64) {
	p.Items = append(p.Items, PaynowItem{Name: name, Amount: amount})
	//recalculate amount
	p.calculateTotalAmount()

}
func (p *Paynow) RemoveItem(name string) {
	for i, item := range p.Items {
		if item.Name == name {
			p.Items = append(p.Items[:i], p.Items[i+1:]...)
			break
		}
	}
	//recalculate amount
	p.calculateTotalAmount()
}
func (p *Paynow) calculateTotalAmount() {
	totalAmount := 0.0
	for _, item := range p.Items {
		totalAmount += item.Amount
	}
	p.Amount = totalAmount
}
func (p *Paynow) SendPaynowPayment() (*PaynowResponse, error) {
	if len(p.Items) == 0 {
		return nil, errors.New("no items to pay for")
	}
	if p.Reference == "" {
		return nil, errors.New("reference is required")
	}
	if p.Amount == 0 {
		return nil, errors.New("amount is required")
	}
	if p.IntegrationId == 0 {
		return nil, errors.New("integration id is required")
	}
	if p.IntegrationKey == "" {
		return nil, errors.New("integration key is required")
	}
	if p.ReturnUrl == "" {
		return nil, errors.New("return url is required")
	}
	if p.ResultUrl == "" {
		return nil, errors.New("result url is required")
	}
	if p.Status == "" {
		p.Status = "Message"
	}
	if p.UserName == "" {
		p.UserName = ""
	}
	hash := p.GenerateHash(p.generateHashData())
	p.Hash = hash
	requestData := map[string]interface{}{
		"id":        p.IntegrationId,
		"reference": p.Reference,
		"amount":    p.Amount,
		"returnurl": p.ReturnUrl,
		"resulturl": p.ResultUrl,
		"status":    p.Status,
	}
	if p.UserName != "" {
		requestData["authname"] = p.UserName
	}
	if p.UserEmail != "" {
		requestData["authemail"] = p.UserEmail
	}
	if p.UserPhone != "" {
		requestData["authphone"] = p.UserPhone
	}
	if p.AdditionalInfo != "" {
		requestData["additionalinfo"] = p.AdditionalInfo
	}
	requestData["hash"] = hash
	fmt.Println("Hash:", hash)
	return p.SendPaynowRequest(INITIATE_TRANSACTION_URL, requestData)
}

// generateHashData returns the hash data in the correct order for hash generation.
func (p *Paynow) generateHashData() []string {
	return []string{
		fmt.Sprintf("%v", p.IntegrationId),
		p.Reference,
		strconv.FormatFloat(p.Amount, 'f', 2, 64),
		p.AdditionalInfo,
		p.ReturnUrl,
		p.ResultUrl,
		p.Status,
		p.UserEmail,
		p.UserPhone,
		p.UserName,
	}
}

func (p *Paynow) SendPaynowRequest(apiUrl string, data map[string]interface{}) (*PaynowResponse, error) {
	var sb strings.Builder
	for _, key := range PaynowFieldOrder {
		val, ok := data[key]
		if !ok || val == nil {
			continue
		}
		var strVal string
		if key == "amount" {
			switch v := val.(type) {
			case float64:
				strVal = strconv.FormatFloat(v, 'f', 2, 64)
			case string:
				strVal = v
			default:
				strVal = fmt.Sprintf("%v", v)
			}
		} else {
			strVal = fmt.Sprintf("%v", val)
		}
		sb.WriteString(url.QueryEscape(key))
		sb.WriteString("=")
		sb.WriteString(url.QueryEscape(strVal))
		sb.WriteString("&")
	}
	sb.WriteString("hash=" + url.QueryEscape(fmt.Sprintf("%v", data["hash"])))
	encoded := sb.String()

	fmt.Println("Sending form data:", encoded)

	response, err := http.Post(apiUrl, "application/x-www-form-urlencoded", strings.NewReader(encoded))
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer response.Body.Close()

	body, err := io.ReadAll(response.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	values, err := url.ParseQuery(string(body))
	if err != nil {
		return nil, fmt.Errorf("failed to parse response body: %w", err)
	}

	fmt.Println("Parsed response:")
	fmt.Println("Status:", values.Get("status"))
	fmt.Println("Error:", values.Get("error"))

	if values.Get("status") == "Error" {
		return nil, fmt.Errorf("paynow error: %s", values.Get("error"))
	}

	return &PaynowResponse{
		PollUrl:              values.Get("pollurl"),
		BrowserUrl:           values.Get("browserurl"),
		Instructions:         values.Get("instructions"),
		Status:               values.Get("status"),
		Error:                values.Get("error"),
		Hash:                 values.Get("hash"),
		AuthorizationCode:    values.Get("authorizationcode"),
		AuthorizationExpires: values.Get("authorizationexpires"),
		OtpReference:         values.Get("otpreference"),
	}, nil

}
