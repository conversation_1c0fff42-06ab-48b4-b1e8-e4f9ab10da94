package response

// NotificationLogResponse represents the response structure for notification logs
type NotificationLogResponse struct {
	Id                uint                  `json:"id"`
	ClientId          uint                  `json:"client_id"`
	UserId            *uint                 `json:"user_id"`
	ClientDeviceId    *uint                 `json:"client_device_id"`
	FirebaseToken     string                `json:"firebase_token"`
	Title             string                `json:"title"`
	Body              string                `json:"body"`
	Type              string                `json:"type"`
	Data              string                `json:"data"`
	Status            string                `json:"status"`
	FirebaseMessageId *string               `json:"firebase_message_id"`
	ErrorMessage      *string               `json:"error_message"`
	CreatedAt         string                `json:"created_at"`
	UpdatedAt         string                `json:"updated_at"`
	User              *UserResponse         `json:"user,omitempty"`
	ClientDevice      *ClientDeviceResponse `json:"client_device,omitempty"`
}

// ClientDeviceResponse represents the response structure for client devices
type ClientDeviceResponse struct {
	Id          uint    `json:"id"`
	Name        *string `json:"name"`
	DeviceId    string  `json:"device_id"`
	PhoneNumber *string `json:"phone_number"`
	Status      string  `json:"status"`
}
