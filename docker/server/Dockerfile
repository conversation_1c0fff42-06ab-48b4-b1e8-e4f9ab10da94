# Build stage
FROM golang:1.24-alpine AS builder

WORKDIR /app/yotracker

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY cmd ./cmd
COPY internal ./internal
COPY config ./config

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o server ./cmd/server

# Final stage
FROM alpine:latest

WORKDIR /app

# Copy the binary from builder
COPY --from=builder /app/yotracker/server .

# Expose the TCP ports for GT06 and H02 protocols
EXPOSE 5022 5010

# Run the server
CMD ["./server"]
