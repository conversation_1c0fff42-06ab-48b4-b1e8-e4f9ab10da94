FROM golang:1.25

WORKDIR /app/yotracker

# Install wkhtmltopdf and fonts
RUN apt-get update && apt-get install -y \
    wkhtmltopdf \
    build-essential \
    fonts-freefont-ttf \
    && apt-get clean

# Install Air for hot reloading
RUN go install github.com/cosmtrek/air@v1.49.0

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY cmd ./cmd
COPY internal ./internal
COPY config ./config
COPY migrations ./migrations
COPY ./docker/web/backend/.air.toml ./.air.toml
COPY .env ./

# Expose the backend port
EXPOSE ${BACKEND_PORT:-9000}

# Run Air for hot reloading
CMD ["air", "-c", "/app/yotracker/.air.toml"]