FROM golang:1.25

WORKDIR /app/yotracker

# Install dependencies and wkhtmltopdf from source
RUN apt-get update && apt-get install -y \
    build-essential \
    fonts-freefont-ttf \
    wget \
    xvfb \
    && apt-get clean

# Install wkhtmltopdf from official releases
RUN wget -q https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6.1-3/wkhtmltox_0.12.6.1-3.bullseye_amd64.deb \
    && dpkg -i wkhtmltox_0.12.6.1-3.bullseye_amd64.deb || apt-get install -f -y \
    && rm wkhtmltox_0.12.6.1-3.bullseye_amd64.deb

# Install Air for hot reloading
RUN go install github.com/cosmtrek/air@v1.49.0

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY cmd ./cmd
COPY internal ./internal
COPY config ./config
COPY ./docker/cron/.air.toml ./.air.toml
COPY .env ./


# Run Air for hot reloading
CMD ["air", "-c", "/app/yotracker/.air.toml"]