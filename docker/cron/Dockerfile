# Build stage
FROM golang:1.24-alpine AS builder

WORKDIR /app/yotracker

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY cmd ./cmd
COPY internal ./internal
COPY config ./config

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o backend ./cmd/cron

# Final stage
FROM debian:bullseye-slim

WORKDIR /app

# Install wkhtmltopdf and dependencies
RUN apt-get update && apt-get install -y \
    fonts-freefont-ttf \
    wget \
    xvfb \
    ca-certificates \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install wkhtmltopdf from official releases
RUN wget -q https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6.1-3/wkhtmltox_0.12.6.1-3.bullseye_amd64.deb \
    && dpkg -i wkhtmltox_0.12.6.1-3.bullseye_amd64.deb || apt-get install -f -y \
    && rm wkhtmltox_0.12.6.1-3.bullseye_amd64.deb

# Copy the binary from builder
COPY --from=builder /app/yotracker/cron .

# Run the backend
CMD ["./cron"]
