version: '3.8'

services:
  realistic-gt06-simulator:
    build:
      context: ../../../
      dockerfile: docker/simulator/gt06/realistic/Dockerfile
    container_name: realistic-gt06-simulator
    environment:
      - GT06_SERVER_HOST=tcp-server-dev
      - GT06_SERVER_PORT=5022
      - GT06_IMEI=123456789012345
      - GT06_DEVICE_ID=TEST001
    depends_on:
      - tcp-server-dev
    networks:
      - yotracker-network
    restart: unless-stopped
    volumes:
      - ../../../logs:/app/logs

networks:
  yotracker-network:
    external: true
