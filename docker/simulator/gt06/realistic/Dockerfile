FROM golang:1.19-alpine AS builder

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY . .

# Build the realistic GT06 simulator
RUN CGO_ENABLED=0 GOOS=linux go build -o realistic_gt06_simulator ./cmd/simulator/gt06/realistic_gt06_simulator.go

# Final stage
FROM alpine:latest

RUN apk --no-cache add ca-certificates
WORKDIR /root/

# Copy the binary
COPY --from=builder /app/realistic_gt06_simulator .

# Run the simulator
CMD ["./realistic_gt06_simulator"]
