#!/bin/bash

# Test script for realistic trip detection
# This script tests the real-time trip detection service with realistic data

set -e

echo "🚀 Starting Realistic Trip Detection Test"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if the main docker-compose is running
if ! docker-compose ps | grep -q "tcp-server-dev"; then
    print_warning "Main YoTracker services are not running. Starting them..."
    docker-compose up -d tcp-server-dev mysql redis
    sleep 10
fi

print_status "Building and running realistic trip detection test..."

# Build and run the test
docker-compose -f docker/simulator/gt06/realistic/docker-compose.yml up --build

print_status "Test completed. Checking results..."

# Run the Go test to verify trip detection
print_status "Running trip detection verification..."
docker-compose exec tcp-server-dev go run cmd/test-realistic-trip-detection/main.go

print_success "Realistic trip detection test completed!"
print_status "Check the database for:"
print_status "  - GPS data with trip_id assigned"
print_status "  - Trips created with proper start/end times"
print_status "  - Key on/off events detected"
print_status "  - Alerts generated for events"

echo ""
print_status "To check the database results:"
echo "  docker-compose exec mysql mysql -u root -pyotracker yotracker"
echo "  SELECT COUNT(*) as gps_with_trip FROM gps_data WHERE trip_id IS NOT NULL AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR);"
echo "  SELECT COUNT(*) as trips_created FROM trips WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR);"
echo "  SELECT COUNT(*) as alerts_created FROM alerts WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR);"
