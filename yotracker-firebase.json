{"type": "service_account", "project_id": "yotracker", "private_key_id": "afe1f59e4376875552be096aa8f0a957ab3a6a3f", "private_key": "-----B<PERSON><PERSON> PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCvumTwv6iL1Ea3\n3PETvGghj81sA2UF529s5LG/ZhSm+iFEZcCJ5e01AXKgNbexMv820TW/ffcMxZp1\nwxr8fjmscNcBApDjoZMzeuI6jOhxEL8zemR8k9uehHmGWOmvP1IFJa1NHmb3G2IQ\n6tTxwJaQfeWWJpZrEHwoNJaG4hXk0R1u/GjIn6NW9m6C5TrioJprhEUdFdx1pA1G\nYKPg6ZCAwuAtQhjv0p4zCnRuW9JF7/5MTbGKiyJ81Fwu8oIvP75QF1cJi6SDZ0LS\n7PU6InjRyzZDQc3iaF/hGN70Mq5/UM86khWdQEuuSGx5Os1pKe71QoxMvPrxWY1f\nJuTgrfkJAgMBAAECggEAButTcylQxSS4HiA8e9b00LF9WK3vieJ5gDOnDg7CbGq3\n6Z4KDUP46ZfjgAxtbbRA2VM2hxI6Ko4TTWS/yMZdlf0hciA8PUJM7f9+LekDdARB\nSxEcKM2Xna/AqzoQBX78R0ibAMjcLAmvfFQKy3QWWZ0jb5yYWqHdCaY8cRYw34AN\nrNQaDWeJulx43QGhyM1UJSRHy3MZmL0W4k+1V1//5nRFnvfyZbzPR7n2aNQ2Q07j\nkH5SBijC0Ej4Gg3nW8dOIlFxACUzLnaSpQ4nNvnKFZzTJCRq4i4PuQf5azu2bkZ/\nHVPhsmFUmwrE5XQlMd1BJ4eqo2kmMHSX/xdLErwsWwKBgQDd4ExLFcjm65fhfFV6\nto7rY+27I4I6oqwv48IQ/Tzv7hP85g8KqllIaYlBTx+jNkeYLm59UPmz0eGAEueN\nSCG4DmeODOA6r9ZGXAzCuz9DoUYClhqobvqHdn5xlRWERm9HG2SG+GUiwctxLSLe\nMSBy96BuUdACPRvvJ/0f5zeIzwKBgQDKwSf32d4DtWgNZlhQxVSaZdc6MNg6axTe\nyLFbrFVZP648SpDRfVsTXYuA63zB7tr0705pZq2oss616b65zsuxRleCl48oL43m\nbZILyu2eTrzICqX6YtFPAev6q1ciYtom3j+Pgsa5lAJeoSh8r2Lqk6Zdwv6rdk7v\nJh7j38smpwKBgE1tNnKFWaRr1oaTcujtyBYQuycxDwMTPW9A1bpEBP3dYRBB4EHi\njGGeoo0t5WZ5TKaonVoR5fGAbmbZMgC1iT7HmA+UIrOYJPhMqw9RdJRPNTU12Huq\nrraS/TD5y5ZrVe3Ee2I2UGS+qBYIoEBVeHNmQo/88M+3L4ZXuBdoApM1AoGBAJBj\nU6I2vdtoQYxhWP1UObamVcpz5Fhdz4QD6gH9zN388oivFUiKe+Rjlqgdiz/UaItL\nLEz1OTBM/dYgepl5wichj8pJQkJ88xGo7/uD7Qnyyo+uvZp86s/X74QYprmvKenK\nFfuORxT7aWCYZeu9PrUMpspYxeG3I+k8/XyL7/E7AoGAQY8ZT3G0OWLPuUyUkI6M\n+TuGtc7p/TtLCfZrLAcE3EV/J9S10Ue8SkeIxwNsbJeDdaWgn0NTrHM9L/HquvGH\nnPuhWuFHE3yDOqghz3MlIVlQRjfY9XLQ2P2EVm0KKGYK0Bbv8AnVD3A+gNRNgxgV\nGNEtLWUzUJ3o4bzXH/PO/Ek=\n-----END PRIVATE KEY-----\n", "client_email": "*******", "client_id": "115581245697586083186", "auth_uri": "https://accounts.google.com/o/oauth2/auth", "token_uri": "https://oauth2.googleapis.com/token", "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs", "client_x509_cert_url": "https://www.googleapis.com/robot/v1/metadata/x509/yotracker-firebase%40yotracker.iam.gserviceaccount.com", "universe_domain": "googleapis.com"}