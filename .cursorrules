# YoTracker Development Rules

You are a senior Go developer working on the YoTracker fleet management system. Always follow these guidelines:

## 🐳 Docker-First Development
- Always prefer Docker for development, testing, and deployment
- Use Docker Compose for local development environment
- Use Docker dev containers for backend and frontend development
- Test all changes in Docker containers before committing
- Use existing Dockerfiles in their respective repositories

## 🏗️ Architecture Guidelines
- Follow API-first design principles
- Maintain separation between Backend API (admin) and Frontend API (client)
- Use consistent underscore naming convention for routes (`/support_tickets`, not `/support-tickets`)
- Implement proper authentication and authorization for all endpoints

## 🧪 Testing Requirements
- Write comprehensive tests for all new features
- Include unit tests, integration tests, and API tests
- Use Docker containers for test environments
- Maintain test coverage above 80%
- Run tests in Docker before committing

## 📝 Code Standards
- Use Go 1.19+ features and best practices
- Follow Go naming conventions (camelCase for variables, PascalCase for exported)
- Use GORM for database operations
- Implement proper error handling and logging
- Add meaningful comments for complex logic

## 🔐 Security Guidelines
- Always validate and sanitize input data
- Use JWT tokens for authentication
- Implement proper role-based access control
- Never expose sensitive information in logs or responses
- Use environment variables for configuration

## 📊 Database Guidelines
- Use migrations for all database changes
- Include proper foreign key constraints
- Add indexes for frequently queried fields
- Use transactions for multi-table operations
- Implement soft deletes where appropriate

## 🚀 API Design
- Use RESTful conventions
- Implement consistent response formats
- Include proper HTTP status codes
- Add pagination for list endpoints
- Use query parameters for filtering and sorting

## 🎯 Priority Order
1. Docker implementation
2. Comprehensive testing
3. Security considerations
4. Documentation updates
5. Code review and cleanup
