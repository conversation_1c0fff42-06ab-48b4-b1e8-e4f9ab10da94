#!/bin/bash

# Firebase Push Notifications Test Script
# This script provides sample requests for testing Firebase push notifications

# Configuration
BASE_URL="http://localhost:9000"
API_BASE="/api/v1/backend"
JWT_TOKEN="your-jwt-token-here"  # Replace with actual JWT token

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔥 Firebase Push Notifications Test Script${NC}"
echo -e "${BLUE}==========================================${NC}"
echo ""

# Function to make API request
make_request() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    echo -e "${YELLOW}📤 $description${NC}"
    echo -e "${BLUE}Request:${NC} $method $endpoint"
    
    if [ -n "$data" ]; then
        echo -e "${BLUE}Data:${NC}"
        echo "$data" | jq '.' 2>/dev/null || echo "$data"
    fi
    
    echo ""
    echo -e "${GREEN}Response:${NC}"
    
    if [ -n "$data" ]; then
        curl -s -X $method \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $JWT_TOKEN" \
            -d "$data" \
            "$BASE_URL$API_BASE$endpoint" | jq '.' 2>/dev/null || \
        curl -s -X $method \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer $JWT_TOKEN" \
            -d "$data" \
            "$BASE_URL$API_BASE$endpoint"
    else
        curl -s -X $method \
            -H "Authorization: Bearer $JWT_TOKEN" \
            "$BASE_URL$API_BASE$endpoint" | jq '.' 2>/dev/null || \
        curl -s -X $method \
            -H "Authorization: Bearer $JWT_TOKEN" \
            "$BASE_URL$API_BASE$endpoint"
    fi
    
    echo ""
    echo -e "${BLUE}----------------------------------------${NC}"
    echo ""
}

# Check if jq is installed
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}⚠️  jq is not installed. JSON responses will not be formatted.${NC}"
    echo -e "${YELLOW}   Install jq for better output formatting: brew install jq${NC}"
    echo ""
fi

# Check if server is running
echo -e "${YELLOW}🔍 Checking if server is running...${NC}"
if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Server is running at $BASE_URL${NC}"
else
    echo -e "${RED}❌ Server is not running at $BASE_URL${NC}"
    echo -e "${YELLOW}   Please start the server first:${NC}"
    echo -e "${YELLOW}   docker-compose up web-backend-dev${NC}"
    exit 1
fi

echo ""

# Test 1: Get supported notification types
make_request "GET" "/notifications/types" "" "Get supported notification types"

# Test 2: Send single push notification
SINGLE_NOTIFICATION='{
  "to": "test-fcm-token-12345",
  "title": "🚗 Engine ON Alert",
  "body": "Vehicle ABC123 just turned on at Main Street.",
  "type": "ignition_alert",
  "client_device_id": 1234,
  "data": {
    "vehicle_id": "ABC123",
    "location": "Main Street, Downtown",
    "timestamp": "2023-12-01T10:30:00Z"
  }
}'

make_request "POST" "/notifications/push" "$SINGLE_NOTIFICATION" "Send single push notification"

# Test 3: Send bulk push notifications
BULK_NOTIFICATION='{
  "tokens": [
    "test-fcm-token-1",
    "test-fcm-token-2",
    "test-fcm-token-3"
  ],
  "title": "🔧 System Maintenance Notice",
  "body": "Scheduled maintenance will begin in 30 minutes. Please save your work.",
  "type": "system_notice",
  "data": {
    "maintenance_id": "MAINT-2023-001",
    "estimated_duration": "2 hours",
    "affected_services": ["GPS tracking", "Notifications"]
  }
}'

make_request "POST" "/notifications/push/bulk" "$BULK_NOTIFICATION" "Send bulk push notifications"

# Test 4: Send geofence alert
GEOFENCE_ALERT='{
  "to": "test-fcm-token-geofence",
  "title": "🚨 Geofence Alert",
  "body": "Vehicle XYZ789 has entered the restricted area.",
  "type": "geofence_alert",
  "client_device_id": 5678,
  "data": {
    "vehicle_id": "XYZ789",
    "geofence_name": "Restricted Area A",
    "alert_type": "entry",
    "coordinates": {
      "lat": -17.8252,
      "lng": 31.0335
    }
  }
}'

make_request "POST" "/notifications/push" "$GEOFENCE_ALERT" "Send geofence alert notification"

# Test 5: Send speed alert
SPEED_ALERT='{
  "to": "test-fcm-token-speed",
  "title": "⚡ Speed Alert",
  "body": "Vehicle DEF456 is exceeding speed limit by 20 km/h.",
  "type": "speed_alert",
  "client_device_id": 9012,
  "data": {
    "vehicle_id": "DEF456",
    "current_speed": 120,
    "speed_limit": 100,
    "location": "Highway A1, KM 45"
  }
}'

make_request "POST" "/notifications/push" "$SPEED_ALERT" "Send speed alert notification"

# Test 6: Send panic alert
PANIC_ALERT='{
  "to": "test-fcm-token-panic",
  "title": "🚨 PANIC ALERT",
  "body": "Driver has activated panic button in vehicle GHI789!",
  "type": "panic_alert",
  "client_device_id": 3456,
  "data": {
    "vehicle_id": "GHI789",
    "driver_name": "John Doe",
    "location": "123 Main Street, Harare",
    "timestamp": "2023-12-01T14:45:00Z",
    "priority": "high"
  }
}'

make_request "POST" "/notifications/push" "$PANIC_ALERT" "Send panic alert notification"

# Test 7: Send notification to specific client
CLIENT_NOTIFICATION='{
  "client_ids": [1],
  "title": "📢 Client Announcement",
  "body": "Important update for your fleet management system.",
  "type": "admin_message",
  "data": {
    "announcement_id": "ANN-2023-001",
    "priority": "medium"
  }
}'

make_request "POST" "/notifications/client" "$CLIENT_NOTIFICATION" "Send notification to specific client"

# Test 8: Send notification to multiple specific clients
MULTIPLE_CLIENTS_NOTIFICATION='{
  "client_ids": [1, 2, 3],
  "title": "📢 Multi-Client Announcement",
  "body": "Important update for selected fleet management systems.",
  "type": "admin_message",
  "data": {
    "announcement_id": "ANN-2023-002",
    "priority": "high",
    "target_clients": [1, 2, 3]
  }
}'

make_request "POST" "/notifications/client" "$MULTIPLE_CLIENTS_NOTIFICATION" "Send notification to multiple specific clients"

# Test 9: Send notification to all clients
ALL_CLIENTS_NOTIFICATION='{
  "title": "🌍 System Maintenance Notice",
  "body": "Scheduled system maintenance will begin in 2 hours. Please save your work.",
  "type": "system_notice",
  "data": {
    "maintenance_id": "MAINT-2023-002",
    "estimated_duration": "4 hours",
    "affected_services": ["GPS tracking", "Notifications", "Reports"]
  }
}'

make_request "POST" "/notifications/client" "$ALL_CLIENTS_NOTIFICATION" "Send notification to all clients"

# Test 10: Get notification logs
make_request "GET" "/notifications/logs" "" "Get notification logs"

# Test 11: Get notification logs with pagination
make_request "GET" "/notifications/logs?page=1&limit=5" "" "Get notification logs with pagination"

# Test 12: Test invalid notification type (should fail)
INVALID_NOTIFICATION='{
  "to": "test-fcm-token-invalid",
  "title": "Invalid Test",
  "body": "This should fail",
  "type": "invalid_type"
}'

make_request "POST" "/notifications/push" "$INVALID_NOTIFICATION" "Test invalid notification type (should fail)"

# Test 13: Test missing required fields (should fail)
INCOMPLETE_NOTIFICATION='{
  "to": "test-fcm-token-incomplete",
  "title": "Incomplete Test"
}'

make_request "POST" "/notifications/push" "$INCOMPLETE_NOTIFICATION" "Test incomplete notification (should fail)"

# Test 14: Test alert system integration (simulate alert notification)
ALERT_NOTIFICATION='{
  "client_ids": [1],
  "title": "🚨 Speed Alert",
  "body": "Vehicle ABC123 exceeded speed limit",
  "type": "speed_alert",
  "data": {
    "alert_id": 12345,
    "device_name": "Vehicle ABC123",
    "plate_number": "ABC123",
    "speed": "85 km/h",
    "speed_limit": "60 km/h",
    "location": "Harare CBD"
  }
}'

make_request "POST" "/notifications/client" "$ALERT_NOTIFICATION" "Test alert system integration"

echo -e "${GREEN}🎉 Firebase push notification tests completed!${NC}"
echo ""
echo -e "${YELLOW}📝 Notes:${NC}"
echo -e "${YELLOW}   - Replace 'your-jwt-token-here' with a valid JWT token${NC}"
echo -e "${YELLOW}   - FCM tokens used are test tokens and won't send real notifications${NC}"
echo -e "${YELLOW}   - Check the notification logs to verify requests were processed${NC}"
echo -e "${YELLOW}   - For real Firebase integration, place yotracker-firebase.json in project root${NC}"
echo -e "${YELLOW}   - Or set FIREBASE_SERVER_KEY environment variable (legacy method)${NC}"
echo -e "${YELLOW}   - Now using go-fcm library with service account key support${NC}"
