package config

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"time"

	"github.com/joho/godotenv"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var DB *gorm.DB

const projectDirName = "yotracker"

func InitDB() {
	// Get the absolute path to the .env file
	rootDir, _ := filepath.Abs(filepath.Join(filepath.Dir("."), ".."))
	envPath := filepath.Join(rootDir+"/"+projectDirName, ".env")
	err := godotenv.Load(envPath)

	if err != nil {
		log.Fatalf("Error loading .env file")
	}
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local", os.Getenv("DB_USERNAME"), os.<PERSON>("DB_PASSWORD"), os.<PERSON>en<PERSON>("DB_HOST"), os.Getenv("DB_PORT"), os.Getenv("DB_NAME"))
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		fmt.Println("Failed to connect to database")
		log.Fatal("Database connection error:", err)
	}

	// Configure connection pooling
	sqlDB, err := DB.DB()
	if err != nil {
		log.Fatal("Failed to get underlying sql.DB:", err)
	}

	// Set connection pool settings - more conservative for testing
	sqlDB.SetMaxIdleConns(5)
	sqlDB.SetMaxOpenConns(20)
	sqlDB.SetConnMaxLifetime(30 * time.Minute)
}
func InitTestDB() {
	rootDir, _ := filepath.Abs(filepath.Join(filepath.Dir("."), ".."))
	envPath := filepath.Join(rootDir+"/"+projectDirName, ".env.testing")
	err := godotenv.Load(envPath)
	if err != nil {
		fmt.Println("Error loading .env file, using exported variables")
	}

	// Use TESTING_DB_NAME for test database, fallback to "testing" if not set
	testDBName := os.Getenv("TESTING_DB_NAME")
	if testDBName == "" {
		testDBName = "testing"
	}

	// Set default values for CI environment if not provided
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True&loc=Local", os.Getenv("DB_USERNAME"), os.Getenv("DB_PASSWORD"), os.Getenv("DB_HOST"), os.Getenv("DB_PORT"), testDBName)
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		fmt.Println("Failed to connect to test database")
		log.Fatal("Test database connection error:", err)
	}

	// Configure connection pooling for tests (more conservative settings)
	sqlDB, err := DB.DB()
	if err != nil {
		log.Fatal("Failed to get underlying sql.DB:", err)
	}

	// Set connection pool settings for tests - increased for test suite
	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(50)
	sqlDB.SetConnMaxLifetime(10 * time.Minute)

	// Disable foreign key constraints for testing
	DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
}

// CloseTestDB closes the test database connection
func CloseTestDB() {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
}
func loadEnv() {
	projectName := regexp.MustCompile(`^(.*` + projectDirName + `)`)
	currentWorkDirectory, _ := os.Getwd()
	rootPath := projectName.Find([]byte(currentWorkDirectory))

	err := godotenv.Load(string(rootPath) + `/.env`)

	if err != nil {
		log.Fatalf("Error loading .env file")
	}
}
func GetDB() *gorm.DB {
	return DB
}

// CloseDB closes the database connection
func CloseDB() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}
