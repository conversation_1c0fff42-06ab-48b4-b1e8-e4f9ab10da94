#!/bin/bash
set -e

echo "Starting real-time impact detection tests..."

# Ensure Docker services are up
docker-compose up -d mysql

# Wait for MySQL to be ready
echo "Waiting for MySQL to be ready..."
./scripts/wait-for-it.sh localhost:3309 --timeout=60 -- echo "MySQL is up!"

# Run migrations
echo "Running database migrations..."
go run cmd/server/server.go migrate

# Run the impact detection test
echo "Running impact detection test..."
go run cmd/test-impact-detection/main.go

echo "Impact detection tests completed."

# Optional: Add database verification queries here
echo "Checking for impact alerts in database..."
docker-compose exec -T mysql mysql -u root -ppassword yotracker -e "SELECT COUNT(*) as impact_alerts FROM alerts WHERE alert_type = 'impact_detection';"
