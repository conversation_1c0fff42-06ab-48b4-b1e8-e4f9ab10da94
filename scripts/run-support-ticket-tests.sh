#!/bin/bash

# Support Ticket System Test Runner
# This script runs all tests related to the support ticket system

echo "🧪 Running Support Ticket System Tests..."
echo "=========================================="

# Set test environment
export TESTING=true

# Run service tests
echo ""
echo "📦 Testing Support Ticket Service..."
go test ./internal/services -run TestSupportTicketService -v

if [ $? -eq 0 ]; then
    echo "✅ Service tests passed"
else
    echo "❌ Service tests failed"
    exit 1
fi

# Run backend controller tests
echo ""
echo "🔧 Testing Backend Support Ticket Controllers..."
go test ./cmd/web/backend/controllers -run TestBackendSupportTicketControllers -v

if [ $? -eq 0 ]; then
    echo "✅ Backend controller tests passed"
else
    echo "❌ Backend controller tests failed"
    exit 1
fi

# Run frontend controller tests
echo ""
echo "🌐 Testing Frontend Support Ticket Controllers..."
go test ./cmd/web/frontend/controllers -run TestFrontendSupportTicketControllers -v

if [ $? -eq 0 ]; then
    echo "✅ Frontend controller tests passed"
else
    echo "❌ Frontend controller tests failed"
    exit 1
fi

echo ""
echo "🎉 All Support Ticket System Tests Passed!"
echo "=========================================="
echo ""
echo "Test Coverage Summary:"
echo "- Service Layer: CRUD operations, filtering, statistics, notifications"
echo "- Backend Controllers: Admin/staff access, full permissions"
echo "- Frontend Controllers: Client access, restricted permissions"
echo "- Error Handling: Invalid inputs, unauthorized access, missing data"
echo "- Data Isolation: Client data separation, cross-client access prevention"
