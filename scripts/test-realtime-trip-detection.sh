#!/bin/bash

# Test script for real-time trip detection system
# This script runs comprehensive tests for the new hybrid real-time trip detection

echo "🚀 Starting Real-Time Trip Detection Tests"
echo "=========================================="

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if containers are running
if ! docker-compose ps | grep -q "Up"; then
    echo "📦 Starting Docker containers..."
    docker-compose up -d
    sleep 10
fi

echo "🔧 Building test applications..."

# Build the test applications
echo "Building basic real-time test..."
cd cmd/test-realtime-trip-detection
go build -o ../../tmp/test-realtime main.go
if [ $? -ne 0 ]; then
    echo "❌ Failed to build basic real-time test"
    exit 1
fi

echo "Building GT06 real-time test..."
cd ../test-gt06-realtime
go build -o ../../tmp/test-gt06-realtime main.go
if [ $? -ne 0 ]; then
    echo "❌ Failed to build GT06 real-time test"
    exit 1
fi

cd ../..

echo "🧪 Running Real-Time Trip Detection Tests"
echo "========================================="

# Test 1: Basic real-time trip detection
echo ""
echo "📋 Test 1: Basic Real-Time Trip Detection"
echo "----------------------------------------"
./tmp/test-realtime

# Wait a moment for processing
sleep 2

# Test 2: GT06 protocol real-time trip detection
echo ""
echo "📋 Test 2: GT06 Protocol Real-Time Trip Detection"
echo "------------------------------------------------"
./tmp/test-gt06-realtime

# Wait for processing
sleep 2

echo ""
echo "📊 Checking Results"
echo "=================="

# Check for created trips
echo "🔍 Checking for created trips..."
TRIP_COUNT=$(docker-compose exec -T mysql mysql -u root -ppassword yotracker -e "SELECT COUNT(*) as trip_count FROM trips WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR);" | tail -n 1)
echo "   Trips created in last hour: $TRIP_COUNT"

# Check for created alerts
echo "🔍 Checking for created alerts..."
ALERT_COUNT=$(docker-compose exec -T mysql mysql -u root -ppassword yotracker -e "SELECT COUNT(*) as alert_count FROM alerts WHERE alert_timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR);" | tail -n 1)
echo "   Alerts created in last hour: $ALERT_COUNT"

# Check for key events
echo "🔍 Checking for key events..."
KEY_EVENT_COUNT=$(docker-compose exec -T mysql mysql -u root -ppassword yotracker -e "SELECT COUNT(*) as key_event_count FROM alerts WHERE alert_type IN ('key_on', 'key_off') AND alert_timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR);" | tail -n 1)
echo "   Key events created in last hour: $KEY_EVENT_COUNT"

# Check for speed alerts
echo "🔍 Checking for speed alerts..."
SPEED_ALERT_COUNT=$(docker-compose exec -T mysql mysql -u root -ppassword yotracker -e "SELECT COUNT(*) as speed_alert_count FROM alerts WHERE alert_type = 'speed_alert' AND alert_timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR);" | tail -n 1)
echo "   Speed alerts created in last hour: $SPEED_ALERT_COUNT"

# Check for towing events
echo "🔍 Checking for towing events..."
TOWING_EVENT_COUNT=$(docker-compose exec -T mysql mysql -u root -ppassword yotracker -e "SELECT COUNT(*) as towing_event_count FROM alerts WHERE alert_type = 'towing_event' AND alert_timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR);" | tail -n 1)
echo "   Towing events created in last hour: $TOWING_EVENT_COUNT"

echo ""
echo "📈 Real-Time Service Metrics"
echo "============================"

# Check real-time service settings
echo "🔧 Real-time service configuration:"
docker-compose exec -T mysql mysql -u root -ppassword yotracker -e "SELECT setting_key, setting_value FROM settings WHERE setting_key LIKE 'realtime_trip%';"

echo ""
echo "✅ Test Summary"
echo "==============="
echo "   • Basic real-time test: ✅ Completed"
echo "   • GT06 protocol test: ✅ Completed"
echo "   • Trips detected: $TRIP_COUNT"
echo "   • Alerts created: $ALERT_COUNT"
echo "   • Key events: $KEY_EVENT_COUNT"
echo "   • Speed alerts: $SPEED_ALERT_COUNT"
echo "   • Towing events: $TOWING_EVENT_COUNT"

echo ""
echo "🎯 Next Steps:"
echo "   1. Check the database for detailed results"
echo "   2. Monitor real-time processing in logs"
echo "   3. Test with actual GT06 devices"
echo "   4. Verify alert notifications are working"

echo ""
echo "🔍 To view detailed results, run:"
echo "   docker-compose exec mysql mysql -u root -ppassword yotracker"
echo "   SELECT * FROM trips ORDER BY start_time DESC LIMIT 10;"
echo "   SELECT * FROM alerts ORDER BY alert_timestamp DESC LIMIT 10;"

# Clean up test binaries
echo ""
echo "🧹 Cleaning up test binaries..."
rm -f tmp/test-realtime tmp/test-gt06-realtime

echo ""
echo "🎉 Real-Time Trip Detection Tests Completed!"
