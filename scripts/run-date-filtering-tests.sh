#!/bin/bash

# Test runner script for date filtering functionality
# This script runs all the date filtering tests for both backend and frontend

set -e

echo "🚀 Starting Date Filtering Tests..."
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if required environment variables are set
if [ -z "$DB_HOST" ]; then
    export DB_HOST="localhost"
    print_warning "DB_HOST not set, using default: localhost"
fi

if [ -z "$DB_PORT" ]; then
    export DB_PORT="3306"
    print_warning "DB_PORT not set, using default: 3306"
fi

if [ -z "$DB_USERNAME" ]; then
    export DB_USERNAME="admin"
    print_warning "DB_USERNAME not set, using default: admin"
fi

if [ -z "$DB_PASSWORD" ]; then
    export DB_PASSWORD="password"
    print_warning "DB_PASSWORD not set, using default: password"
fi

if [ -z "$TESTING_DB_NAME" ]; then
    export TESTING_DB_NAME="testing"
    print_warning "TESTING_DB_NAME not set, using default: testing"
fi

if [ -z "$APP_KEY" ]; then
    export APP_KEY="test-secret-key"
    print_warning "APP_KEY not set, using default: test-secret-key"
fi

print_status "Environment variables configured"

# Function to run tests for a specific package
run_tests() {
    local package_path=$1
    local test_name=$2
    
    print_status "Running $test_name tests..."
    
    cd "$package_path"
    
    # Run tests with verbose output
    if go test -v -timeout=5m ./...; then
        print_success "$test_name tests passed!"
    else
        print_error "$test_name tests failed!"
        return 1
    fi
    
    cd - > /dev/null
}

# Function to run specific test files
run_specific_tests() {
    local test_file=$1
    local test_name=$2
    
    print_status "Running $test_name tests..."
    
    if go test -v -timeout=5m "$test_file"; then
        print_success "$test_name tests passed!"
    else
        print_error "$test_name tests failed!"
        return 1
    fi
}

# Main test execution
main() {
    local failed_tests=0
    
    print_status "Starting comprehensive date filtering tests..."
    
    # Test 1: Backend date filtering tests
    if run_specific_tests "./cmd/web/backend/controllers/date_filtering_test.go" "Backend Date Filtering"; then
        print_success "✅ Backend date filtering tests completed successfully"
    else
        print_error "❌ Backend date filtering tests failed"
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 2: Frontend date filtering tests
    if run_specific_tests "./cmd/web/frontend/controllers/date_filtering_test.go" "Frontend Date Filtering"; then
        print_success "✅ Frontend date filtering tests completed successfully"
    else
        print_error "❌ Frontend date filtering tests failed"
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 3: Database connection test
    print_status "Testing database connection..."
    if go test -v ./config/database_test.go; then
        print_success "✅ Database connection test passed"
    else
        print_error "❌ Database connection test failed"
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 4: Backend integration tests
    print_status "Running backend integration tests..."
    if run_tests "./cmd/web/backend" "Backend Integration"; then
        print_success "✅ Backend integration tests completed successfully"
    else
        print_error "❌ Backend integration tests failed"
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 5: Frontend integration tests
    print_status "Running frontend integration tests..."
    if run_tests "./cmd/web/frontend" "Frontend Integration"; then
        print_success "✅ Frontend integration tests completed successfully"
    else
        print_error "❌ Frontend integration tests failed"
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 6: Utility tests
    print_status "Running utility tests..."
    if go test -v ./internal/utils/...; then
        print_success "✅ Utility tests completed successfully"
    else
        print_error "❌ Utility tests failed"
        failed_tests=$((failed_tests + 1))
    fi
    
    # Test 7: Service tests
    print_status "Running service tests..."
    if go test -v ./internal/service/...; then
        print_success "✅ Service tests completed successfully"
    else
        print_error "❌ Service tests failed"
        failed_tests=$((failed_tests + 1))
    fi
    
    # Summary
    echo ""
    echo "=================================="
    echo "📊 Test Summary"
    echo "=================================="
    
    if [ $failed_tests -eq 0 ]; then
        print_success "🎉 All tests passed! ($failed_tests failed)"
        echo ""
        print_status "Date filtering functionality is working correctly across all endpoints:"
        echo "  • Invoices (GetAll & Search)"
        echo "  • Payments (GetAll & Search)"
        echo "  • Trips (GetAll & Search)"
        echo "  • Alerts (GetAll & Search)"
        echo "  • Geofence Events (GetAll)"
        echo "  • Command Logs (GetAll)"
        echo ""
        print_status "Date filtering supports:"
        echo "  • Single date filtering (start_date only)"
        echo "  • Date range filtering (start_date + end_date)"
        echo "  • Combined with search functionality"
        echo "  • Combined with other filters"
        echo "  • Pagination integration"
        echo "  • Edge case handling"
        exit 0
    else
        print_error "💥 $failed_tests test suite(s) failed!"
        echo ""
        print_warning "Please check the test output above for details on what failed."
        print_warning "Common issues:"
        echo "  • Database connection problems"
        echo "  • Missing environment variables"
        echo "  • Docker not running"
        echo "  • Test data not properly seeded"
        exit 1
    fi
}

# Run the main function
main "$@"
