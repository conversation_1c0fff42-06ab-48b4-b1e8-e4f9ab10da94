package main

import (
	"fmt"
	"os"
	"yotracker/config"
	"yotracker/internal/seed"
	"yotracker/migrations"
)

func main() {
	fmt.Println("Initializing test database...")

	// Set up environment variables for testing
	// Use localhost for local testing, mysql for CI
	dbHost := os.Getenv("DB_HOST")
	if dbHost == "" {
		dbHost = "localhost"
	}

	os.Setenv("DB_HOST", dbHost)
	os.Setenv("DB_PORT", "3306")
	os.Setenv("DB_USERNAME", "admin")
	os.Setenv("DB_PASSWORD", "password")
	os.Setenv("TESTING_DB_NAME", "testing")
	os.Setenv("APP_KEY", "secret")

	// Initialize database
	config.InitTestDB()

	// Run migrations
	fmt.Println("Running migrations...")
	migrations.Migrate()

	// Seed data
	fmt.Println("Seeding data...")
	seed.Seed()

	fmt.Println("Test database initialization completed successfully!")
}
