#!/bin/bash

echo "Running tests with connection cleanup..."

# Function to run tests for a specific package
run_package_tests() {
    local package=$1
    echo "Testing package: $package"
    
    # Clean up any existing connections
    go run scripts/cleanup_connections.go
    
    # Run tests for the package
    go test ./$package -timeout=2m -v
    
    # Clean up connections after tests
    go run scripts/cleanup_connections.go
    
    echo "Completed testing package: $package"
    echo "----------------------------------------"
}

# Clean up connections before starting
go run scripts/cleanup_connections.go

# Run tests for each package
run_package_tests "cmd/web/backend/controllers"
run_package_tests "cmd/web/frontend/controllers"
run_package_tests "internal/services"
run_package_tests "internal/service"
run_package_tests "config"
run_package_tests "internal/models"
run_package_tests "internal/utils"
run_package_tests "internal/mail"
run_package_tests "internal/templates"

echo "All tests completed!"
