#!/bin/bash

# Simple test runner for real-time trip detection
echo "🚀 Real-Time Trip Detection Test Runner"
echo "======================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Start containers if not running
if ! docker-compose ps | grep -q "Up"; then
    echo "📦 Starting Docker containers..."
    docker-compose up -d
    sleep 5
fi

echo "🧪 Available Tests:"
echo "1. Manual Interactive Test"
echo "2. GT06 Protocol Test"
echo "3. Basic Real-Time Test"
echo "4. Run All Tests"
echo ""

read -p "Select test (1-4): " choice

case $choice in
    1)
        echo "🔧 Running Manual Interactive Test..."
        cd cmd/manual-test-realtime
        go run main.go
        ;;
    2)
        echo "🔧 Running GT06 Protocol Test..."
        cd cmd/test-gt06-realtime
        go run main.go
        ;;
    3)
        echo "🔧 Running Basic Real-Time Test..."
        cd cmd/test-realtime-trip-detection
        go run main.go
        ;;
    4)
        echo "🔧 Running All Tests..."
        echo ""
        echo "1️⃣ Manual Interactive Test..."
        cd cmd/manual-test-realtime
        go run main.go
        echo ""
        echo "2️⃣ GT06 Protocol Test..."
        cd ../test-gt06-realtime
        go run main.go
        echo ""
        echo "3️⃣ Basic Real-Time Test..."
        cd ../test-realtime-trip-detection
        go run main.go
        ;;
    *)
        echo "❌ Invalid choice. Please run the script again."
        exit 1
        ;;
esac

echo ""
echo "✅ Test completed!"
echo ""
echo "🔍 To check results, run:"
echo "   docker-compose exec mysql mysql -u root -ppassword yotracker"
echo "   SELECT * FROM trips ORDER BY start_time DESC LIMIT 5;"
echo "   SELECT * FROM alerts ORDER BY alert_timestamp DESC LIMIT 10;"
