#!/bin/bash
set -e

echo "Starting alert-GPS data linking tests..."

# Ensure Docker services are up
docker-compose up -d mysql

# Wait for MySQL to be ready
echo "Waiting for MySQL to be ready..."
./scripts/wait-for-it.sh localhost:3309 --timeout=60 -- echo "MySQL is up!"

# Run migrations
echo "Running database migrations..."
go run cmd/server/server.go migrate

# Run the alert location linking test
echo "Running alert-GPS data linking test..."
go run cmd/test-alert-location-linking/main.go

echo "Alert-GPS data linking tests completed."

# Optional: Add database verification queries here
echo "Checking for alerts with GPS data in database..."
docker-compose exec -T mysql mysql -u root -ppassword yotracker -e "SELECT COUNT(*) as alerts_with_gps_data FROM alerts WHERE gps_data_id IS NOT NULL;"
docker-compose exec -T mysql mysql -u root -ppassword yotracker -e "SELECT alert_type, COUNT(*) as count FROM alerts WHERE gps_data_id IS NOT NULL GROUP BY alert_type;"
