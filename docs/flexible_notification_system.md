# Flexible Notification System

## Overview

The Flexible Notification System is a comprehensive alert management solution that allows customers to configure exactly which alerts they want to receive and through which notification channels. The system supports a hierarchical preference system that can be configured at the client, fleet, and device levels.

## Architecture

### Core Components

1. **Alert Preferences Models** (`internal/models/alert_preferences.go`)
   - `AlertPreference`: Client-level alert preferences
   - `FleetAlertPreference`: Fleet-level alert preferences  
   - `DeviceAlertPreference`: Device-level alert preferences

2. **Flexible Notification Service** (`internal/services/flexible_notification_service.go`)
   - Manages notification dispatch based on preferences
   - Integrates with existing notification services
   - Handles hierarchical preference resolution

3. **Alert Detection Service** (`internal/services/alert_detection_service.go`)
   - Detects real-time alerts from GPS data
   - Triggers notifications automatically
   - Supports speed, shutdown time, towing, and impact detection

### Supported Alert Types

| Alert Type | Description | Default Priority |
|------------|-------------|------------------|
| `speed_alert` | Vehicle exceeds speed limit | High |
| `shutdown_time_alert` | Vehicle active during shutdown hours | Warning |
| `towing_event` | Vehicle being towed without ignition | Critical |
| `impact_detection` | Sudden deceleration/impact detected | Critical |
| `geofence_event` | Vehicle enters/exits geofence | Normal |
| `maintenance_alert` | Maintenance due/overdue | Normal |
| `battery_alert` | Low battery warning | Warning |
| `sos_alert` | Emergency SOS signal | Critical |

### Supported Notification Channels

| Channel | Service | Configuration |
|---------|---------|---------------|
| `email` | Email Alert Service | Client email addresses |
| `sms` | Twilio SMS Service | Client phone numbers |
| `whatsapp` | WhatsApp Service | Client WhatsApp numbers |
| `slack` | Slack Service | Fleet/Client Slack webhooks |

## Preference Hierarchy

The system uses a hierarchical preference system where more specific settings override general ones:

```
Device Level (Highest Priority)
    ↓
Fleet Level
    ↓
Client Level
    ↓
Default Settings (Lowest Priority)
```

### Example Hierarchy

```json
// Client Level (Default for all devices)
{
  "alert_type": "speed_alert",
  "enabled": true,
  "channels": ["email", "sms"],
  "priority": "high"
}

// Fleet Level (Overrides client for this fleet)
{
  "alert_type": "speed_alert", 
  "enabled": true,
  "channels": ["whatsapp", "slack"],
  "priority": "critical"
}

// Device Level (Overrides fleet for this device)
{
  "alert_type": "speed_alert",
  "enabled": true,
  "channels": ["sms"],
  "priority": "normal"
}
```

## Database Schema

### AlertPreference Table

```sql
CREATE TABLE alert_preferences (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    alert_type VARCHAR(50) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    channels JSON,
    priority VARCHAR(20) DEFAULT 'normal',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_client_alert (client_id, alert_type, enabled)
);
```

### FleetAlertPreference Table

```sql
CREATE TABLE fleet_alert_preferences (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    fleet_id BIGINT UNSIGNED NOT NULL,
    alert_type VARCHAR(50) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    channels JSON,
    priority VARCHAR(20) DEFAULT 'normal',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_fleet_alert (fleet_id, alert_type, enabled)
);
```

### DeviceAlertPreference Table

```sql
CREATE TABLE device_alert_preferences (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_device_id BIGINT UNSIGNED NOT NULL,
    alert_type VARCHAR(50) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    channels JSON,
    priority VARCHAR(20) DEFAULT 'normal',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_device_alert (client_device_id, alert_type, enabled)
);
```

## Usage Examples

### Creating Alert Preferences

```go
// Client-level preference
clientPref := &models.AlertPreference{
    ClientId:  clientId,
    AlertType: models.AlertTypeSpeed,
    Enabled:   true,
    Channels:  []byte(`["email", "whatsapp"]`),
    Priority:  "high",
}
config.DB.Create(clientPref)

// Fleet-level preference
fleetPref := &models.FleetAlertPreference{
    FleetId:   fleetId,
    AlertType: models.AlertTypeSpeed,
    Enabled:   true,
    Channels:  []byte(`["sms", "slack"]`),
    Priority:  "critical",
}
config.DB.Create(fleetPref)

// Device-level preference
devicePref := &models.DeviceAlertPreference{
    ClientDeviceId: deviceId,
    AlertType:      models.AlertTypeSpeed,
    Enabled:        true,
    Channels:       []byte(`["whatsapp"]`),
    Priority:       "normal",
}
config.DB.Create(devicePref)
```

### Sending Notifications

```go
// Create notification service
notificationService, err := NewFlexibleNotificationService()
if err != nil {
    return err
}

// Create notification request
req := &NotificationRequest{
    Alert:        alert,
    Client:       client,
    ClientDevice: device,
    Fleet:        fleet,
    AlertType:    models.AlertTypeSpeed,
}

// Send notifications
err = notificationService.SendNotification(req)
if err != nil {
    log.Printf("Failed to send notifications: %v", err)
}
```

### Integration with Alert Detection

The system automatically integrates with the alert detection service:

```go
// In alert_detection_service.go
func (s *AlertDetectionService) createAlert(alert *models.Alert) error {
    // Save alert to database
    if err := config.DB.Create(alert).Error; err != nil {
        return err
    }

    // Send notifications through flexible notification service
    if err := s.sendNotifications(alert); err != nil {
        log.Printf("Failed to send notifications for alert %d: %v", alert.Id, err)
        // Don't fail the alert creation if notifications fail
    }

    return nil
}
```

## Configuration

### Client Configuration

Clients can configure their default notification settings:

```go
client := &models.Client{
    EmailAlertsEnabled:    &emailEnabled,
    SmsAlertsEnabled:      &smsEnabled,
    WhatsappAlertsEnabled: &whatsappEnabled,
    AlertsEmail:           &emailsJSON,        // JSON array of email addresses
    SmsPhoneNumber:        &phoneNumbersJSON,  // JSON array of phone numbers
    WhatsappPhoneNumber:   &whatsappNumbersJSON, // JSON array of WhatsApp numbers
}
```

### Fleet Configuration

Fleets can have their own shutdown hours and notification settings:

```go
fleet := &models.Fleet{
    ShutdownStartHour: &startHour,  // e.g., 22 (10 PM)
    ShutdownEndHour:   &endHour,    // e.g., 6 (6 AM)
    Emails:            &emailsJSON, // JSON array of fleet email addresses
}
```

### Device Configuration

Individual devices can have specific settings:

```go
device := &models.ClientDevice{
    ShutdownStartHour: &startHour,  // Device-specific shutdown hours
    ShutdownEndHour:   &endHour,
}
```

## Testing

The system includes comprehensive integration tests:

```bash
# Run notification system tests
docker exec yotracker-web-backend-dev-1 go test ./internal/services/flexible_notification_service_integration_test.go -v
```

### Test Coverage

- ✅ Alert preference creation and storage
- ✅ Hierarchical preference resolution
- ✅ Channel configuration validation
- ✅ JSON serialization/deserialization
- ✅ Alert type and notification channel constants
- ✅ Client configuration validation

## Error Handling

### Notification Failures

The system is designed to be resilient to notification failures:

1. **Partial Failures**: If one channel fails, others continue
2. **Non-blocking**: Alert creation doesn't fail if notifications fail
3. **Logging**: All failures are logged for debugging
4. **Retry Logic**: Can be implemented for transient failures

### Example Error Handling

```go
func (s *FlexibleNotificationService) SendNotification(req *NotificationRequest) error {
    var errors []string
    
    for _, channel := range channels {
        if err := s.sendToChannel(channel, req); err != nil {
            errors = append(errors, fmt.Sprintf("%s: %v", channel, err))
        }
    }
    
    if len(errors) > 0 {
        return fmt.Errorf("notification failures: %s", strings.Join(errors, "; "))
    }
    
    return nil
}
```

## Performance Considerations

### Database Optimization

- **Indexes**: All preference tables have composite indexes for efficient lookups
- **JSON Storage**: Channel preferences stored as JSON for flexibility
- **Caching**: Preferences can be cached to reduce database queries

### Scalability

- **Async Processing**: Notifications sent asynchronously to avoid blocking
- **Batch Processing**: Multiple alerts can be batched for efficiency
- **Rate Limiting**: Can implement rate limiting per channel

## Security

### Data Protection

- **Input Validation**: All preference data is validated before storage
- **SQL Injection**: Uses GORM parameterized queries
- **Access Control**: Preferences respect client/device ownership

### Privacy

- **Contact Information**: Stored securely and used only for notifications
- **Audit Trail**: All preference changes can be logged
- **Data Retention**: Preferences can be purged when devices/clients are deleted

## Future Enhancements

### Planned Features

1. **Webhook Support**: Allow custom webhook endpoints
2. **Template Customization**: Customizable notification templates
3. **Scheduling**: Time-based notification preferences
4. **Escalation**: Automatic escalation for critical alerts
5. **Analytics**: Notification delivery and engagement metrics

### API Endpoints

Future API endpoints for managing preferences:

```go
// GET /api/alert-preferences
// POST /api/alert-preferences
// PUT /api/alert-preferences/{id}
// DELETE /api/alert-preferences/{id}

// GET /api/fleets/{id}/alert-preferences
// POST /api/fleets/{id}/alert-preferences

// GET /api/devices/{id}/alert-preferences
// POST /api/devices/{id}/alert-preferences
```

## Troubleshooting

### Common Issues

1. **Tables Not Found**: Ensure migrations are run
2. **Notifications Not Sent**: Check channel configuration
3. **Preferences Not Applied**: Verify hierarchy order
4. **JSON Parsing Errors**: Validate channel array format

### Debug Commands

```bash
# Check if tables exist
docker exec yotracker-web-backend-dev-1 mysql -u admin -ppassword testing -e "SHOW TABLES LIKE '%alert_preference%';"

# Check preference data
docker exec yotracker-web-backend-dev-1 mysql -u admin -ppassword testing -e "SELECT * FROM alert_preferences LIMIT 5;"

# Run tests with verbose output
docker exec yotracker-web-backend-dev-1 go test ./internal/services/flexible_notification_service_integration_test.go -v -count=1
```

## Conclusion

The Flexible Notification System provides a robust, scalable solution for managing fleet alerts with complete customer control over notification preferences. The hierarchical design ensures flexibility while maintaining simplicity, and the integration with existing services ensures a smooth deployment experience.
