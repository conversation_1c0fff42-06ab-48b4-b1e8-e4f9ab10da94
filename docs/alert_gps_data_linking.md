# Alert-GPS Data Linking System

## 📍 Overview

The Alert-GPS Data Linking System provides a direct relationship between alerts and GPS data, enabling location-based alert visualization, mapping, and analysis. This system allows you to know exactly where each alert occurred and provides rich context for incident analysis.

## 🎯 Key Features

### **Direct GPS Data Linking**
- **GPS Data ID**: Each alert is linked to the specific GPS data point that triggered it
- **Location Context**: Access to exact coordinates, speed, direction, and timestamp
- **Rich Metadata**: Includes location name, cell tower info, and additional GPS data

### **Map Visualization Support**
- **Optimized Queries**: Specialized endpoints for map rendering
- **Geographic Filtering**: Query alerts within specific geographic areas
- **Location Statistics**: Coverage and distribution analysis

### **Comprehensive API**
- **Multiple Query Types**: By device, location, time range, alert type
- **Flexible Filtering**: Combine multiple criteria for precise results
- **Statistics & Analytics**: Coverage percentages and alert distribution

## 🏗️ Database Schema

### **Alert Model Updates**
```sql
-- Added GPS data relationship
ALTER TABLE alerts 
ADD COLUMN gps_data_id BIGINT UNSIGNED NULL,
ADD INDEX idx_alerts_gps_data_id (gps_data_id),
ADD CONSTRAINT fk_alerts_gps_data 
    FOREIGN KEY (gps_data_id) REFERENCES gps_data(id) 
    ON DELETE SET NULL ON UPDATE CASCADE;
```

### **Model Structure**
```go
type Alert struct {
    Id             uint            `json:"id"`
    ClientDeviceId uint            `json:"client_device_id"`
    DeviceId       *string         `json:"device_id"`
    GPSDataId      *uint           `json:"gps_data_id"`      // NEW: GPS data link
    AlertType      string          `json:"alert_type"`
    AlertName      *string         `json:"alert_name"`
    AlertLevel     *string         `json:"alert_level"`
    Message        *string         `json:"message"`
    Speed          *float64        `json:"speed"`            // From GPS data
    Direction      *string         `json:"direction"`        // From GPS data
    AlertTimestamp time.Time       `json:"alert_timestamp"`
    // ... other fields
    GPSData        *GPSData        `json:"gps_data,omitempty"` // NEW: GPS data relationship
}
```

## 🚀 API Endpoints

### **1. Get Alerts with Location Data**
```http
GET /api/v1/alerts/with_location
```

**Query Parameters:**
- `client_device_id` (optional): Filter by device ID
- `alert_type` (optional): Filter by alert type
- `start_date` (optional): Start date (YYYY-MM-DD)
- `end_date` (optional): End date (YYYY-MM-DD)
- `limit` (optional): Maximum number of results

**Response:**
```json
{
  "success": true,
  "message": "Alerts with location retrieved successfully",
  "data": [
    {
      "id": 123,
      "alert_type": "impact_detection",
      "alert_name": "Potential Impact Detected",
      "alert_level": "critical",
      "message": "Unusual movement pattern suggests possible impact",
      "alert_timestamp": "2025-01-15T10:30:00Z",
      "speed": 5.0,
      "direction": "N",
      "gps_data": {
        "id": 456,
        "latitude": 40.7128,
        "longitude": -74.0060,
        "speed": 5.0,
        "direction": "N",
        "location_name": "New York, NY",
        "gps_timestamp": "2025-01-15T10:30:00Z"
      }
    }
  ],
  "count": 1
}
```

### **2. Get Specific Alert with Location**
```http
GET /api/v1/alerts/{alertId}/with_location
```

### **3. Get Alerts by Geographic Area**
```http
GET /api/v1/alerts/by_location?min_lat=40.7&max_lat=40.8&min_lng=-74.1&max_lng=-74.0
```

**Query Parameters:**
- `min_lat`, `max_lat`: Latitude bounds
- `min_lng`, `max_lng`: Longitude bounds
- `alert_type` (optional): Filter by alert type
- `limit` (optional): Maximum number of results

### **4. Get Alerts by Device with Location**
```http
GET /api/v1/alerts/by_device/{deviceId}/with_location
```

### **5. Get Alert Statistics with Location**
```http
GET /api/v1/alerts/statistics/with_location
```

**Response:**
```json
{
  "success": true,
  "message": "Alert statistics with location retrieved successfully",
  "data": {
    "total_alerts": 150,
    "alerts_with_location": 145,
    "location_coverage_percentage": 96.7,
    "alerts_by_type": {
      "speed_alert": 45,
      "impact_detection": 12,
      "towing_event": 8,
      "geofence_event": 25
    }
  }
}
```

### **6. Get Alerts for Map View (Optimized)**
```http
GET /api/v1/alerts/map_view
```

**Response:**
```json
{
  "success": true,
  "message": "Alerts for map view retrieved successfully",
  "data": [
    {
      "id": 123,
      "alert_type": "impact_detection",
      "alert_name": "Potential Impact Detected",
      "alert_level": "critical",
      "message": "Unusual movement pattern suggests possible impact",
      "alert_timestamp": "2025-01-15T10:30:00Z",
      "latitude": 40.7128,
      "longitude": -74.0060,
      "speed": 5.0,
      "direction": "N",
      "location_name": "New York, NY",
      "device_id": "DEVICE_001",
      "client_device_id": 1
    }
  ],
  "count": 1
}
```

## 🗺️ Map Integration Examples

### **Google Maps Integration**
```javascript
// Fetch alerts for map view
const response = await fetch('/api/v1/alerts/map_view?client_device_id=1');
const data = await response.json();

// Create map markers
data.data.forEach(alert => {
  const marker = new google.maps.Marker({
    position: { lat: alert.latitude, lng: alert.longitude },
    map: map,
    title: alert.alert_name,
    icon: getAlertIcon(alert.alert_type, alert.alert_level)
  });

  // Add info window
  const infoWindow = new google.maps.InfoWindow({
    content: `
      <div>
        <h3>${alert.alert_name}</h3>
        <p><strong>Type:</strong> ${alert.alert_type}</p>
        <p><strong>Level:</strong> ${alert.alert_level}</p>
        <p><strong>Time:</strong> ${new Date(alert.alert_timestamp).toLocaleString()}</p>
        <p><strong>Speed:</strong> ${alert.speed} km/h</p>
        <p><strong>Location:</strong> ${alert.location_name}</p>
      </div>
    `
  });

  marker.addListener('click', () => {
    infoWindow.open(map, marker);
  });
});
```

### **Leaflet Integration**
```javascript
// Fetch alerts for map view
const response = await fetch('/api/v1/alerts/map_view?alert_type=impact_detection');
const data = await response.json();

// Create map markers
data.data.forEach(alert => {
  const marker = L.marker([alert.latitude, alert.longitude])
    .addTo(map)
    .bindPopup(`
      <b>${alert.alert_name}</b><br>
      Type: ${alert.alert_type}<br>
      Level: ${alert.alert_level}<br>
      Time: ${new Date(alert.alert_timestamp).toLocaleString()}<br>
      Speed: ${alert.speed} km/h<br>
      Location: ${alert.location_name}
    `);
});
```

## 📊 Use Cases

### **1. Fleet Management Dashboard**
- **Real-time Alert Map**: Show all active alerts on a map
- **Incident Analysis**: Click on alert markers to see detailed information
- **Geographic Patterns**: Identify problem areas or routes

### **2. Driver Behavior Analysis**
- **Speed Violation Mapping**: Visualize where speed limits are exceeded
- **Impact Event Clustering**: Identify accident-prone locations
- **Route Optimization**: Avoid areas with frequent alerts

### **3. Insurance & Compliance**
- **Incident Documentation**: Precise location data for claims
- **Risk Assessment**: Geographic risk analysis
- **Compliance Reporting**: Location-based compliance metrics

### **4. Emergency Response**
- **SOS Alert Mapping**: Immediate location of emergency alerts
- **Impact Detection**: Real-time collision alerts with exact coordinates
- **Towing Events**: Track unauthorized vehicle movement

## 🔧 Implementation Details

### **Real-Time Alert Creation**
```go
// In real-time trip detection service
alert := models.Alert{
    ClientDeviceId: deviceState.DeviceId,
    DeviceId:       &device.DeviceId,
    GPSDataId:      &gpsData.Id,        // Link to GPS data
    AlertType:      "impact_detection",
    AlertName:      realtimeStringPtr("Potential Impact Detected"),
    AlertLevel:     realtimeStringPtr("critical"),
    Message:        realtimeStringPtr("Unusual movement pattern suggests possible impact"),
    Speed:          gpsData.Speed,      // Copy from GPS data
    Direction:      gpsData.Direction,  // Copy from GPS data
    AlertTimestamp: *gpsData.GPSTimestamp,
}
```

### **Service Layer**
```go
// AlertWithLocationService provides specialized methods
type AlertWithLocationService struct{}

// Get alerts with GPS data preloaded
func (s *AlertWithLocationService) GetAlertsWithLocation(
    clientDeviceId *uint, 
    alertType *string, 
    startDate, endDate *time.Time, 
    limit int
) ([]models.Alert, error)

// Get alerts optimized for map visualization
func (s *AlertWithLocationService) GetAlertsForMapView(
    clientDeviceId *uint, 
    alertType *string, 
    startDate, endDate *time.Time
) ([]MapAlert, error)
```

## 📈 Performance Considerations

### **Database Optimization**
- **Indexes**: `gps_data_id` field is indexed for fast lookups
- **Foreign Key**: Maintains referential integrity
- **Preloading**: GPS data is preloaded to avoid N+1 queries

### **Query Optimization**
- **Selective Loading**: Only load GPS data when needed
- **Pagination**: Limit results to prevent large data transfers
- **Filtering**: Multiple filter options to reduce result sets

### **Caching Strategy**
- **Map View Caching**: Cache map view results for frequently accessed areas
- **Statistics Caching**: Cache alert statistics for dashboard performance
- **Location Caching**: Cache reverse geocoding results

## 🧪 Testing

### **Test File**: `cmd/test-alert-location-linking/main.go`
- **Comprehensive Testing**: Tests all alert types with GPS data linking
- **Verification**: Confirms alerts are properly linked to GPS data
- **API Testing**: Tests all new API endpoints
- **Statistics Testing**: Verifies alert statistics calculations

### **Run Tests**
```bash
# Run the alert location linking test
go run cmd/test-alert-location-linking/main.go
```

## 🚀 Benefits

### **Enhanced User Experience**
- **Visual Context**: See exactly where alerts occurred
- **Rich Information**: Access to speed, direction, and location details
- **Interactive Maps**: Click on alerts to see detailed information

### **Improved Analytics**
- **Geographic Patterns**: Identify problem areas and routes
- **Incident Analysis**: Precise location data for investigation
- **Risk Assessment**: Location-based risk analysis

### **Better Fleet Management**
- **Real-time Monitoring**: Live alert mapping
- **Driver Coaching**: Location-specific feedback
- **Route Optimization**: Avoid problem areas

### **Compliance & Reporting**
- **Accurate Documentation**: Precise location data for reports
- **Insurance Claims**: Detailed incident information
- **Regulatory Compliance**: Location-based compliance tracking

## 📋 Migration Guide

### **1. Run Database Migration**
```sql
-- Apply the migration
source migrations/add_gps_data_id_to_alerts.sql;
```

### **2. Update Existing Alerts (Optional)**
```sql
-- Link existing alerts to GPS data based on timestamp and device
UPDATE alerts a
JOIN gps_data g ON a.client_device_id = g.client_device_id 
    AND a.alert_timestamp = g.gps_timestamp
SET a.gps_data_id = g.id
WHERE a.gps_data_id IS NULL;
```

### **3. Update Frontend Code**
- **Replace Alert Queries**: Use new location-aware endpoints
- **Add Map Integration**: Implement map visualization
- **Update UI Components**: Show location information in alert lists

## 🎯 Summary

The Alert-GPS Data Linking System provides:

✅ **Direct GPS Data Relationships** - Every alert linked to its GPS data point
✅ **Rich Location Context** - Coordinates, speed, direction, and location names
✅ **Map Visualization Support** - Optimized endpoints for map rendering
✅ **Comprehensive API** - Multiple query types and filtering options
✅ **Performance Optimized** - Indexed relationships and efficient queries
✅ **Real-time Integration** - Works seamlessly with existing real-time services

This system transforms your alert system from simple notifications to a comprehensive location-aware incident management platform! 🗺️🚨
