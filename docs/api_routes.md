# YoTracker API Routes Documentation

This document provides a comprehensive list of all available API routes in the YoTracker system. The API is organized into two main sections: **Backend** (for admin/staff) and **Frontend** (for clients).

## Table of Contents

- [Authentication](#authentication)
- [Backend API Routes](#backend-api-routes)
- [Frontend API Routes](#frontend-api-routes)
- [Route Naming Convention](#route-naming-convention)
- [Common Response Format](#common-response-format)
- [Error Handling](#error-handling)

## Authentication

All routes require authentication unless specified otherwise. Use the following headers:

```http
Authorization: Bearer <your_jwt_token>
Content-Type: application/json
```

## Backend API Routes

Base URL: `/api/v1/backend`

### Authentication & User Management

#### Login & Registration
- `POST /login` - User login
- `POST /register` - User registration
- `POST /password-reset/request` - Request password reset
- `POST /password-reset/verify` - Verify password reset token
- `POST /password-reset/confirm` - Confirm password reset

#### User Profile
- `GET /users/profile` - Get current user profile
- `PUT /users/profile` - Update current user profile
- `PUT /users/change_password` - Change user password

#### User Management (Admin)
- `GET /users` - List all users
- `GET /users/search` - Search users
- `GET /users/:id` - Get user by ID
- `POST /users` - Create new user
- `PUT /users/:id` - Update user
- `DELETE /users/:id` - Delete user

#### Roles
- `GET /roles` - List all roles
- `GET /roles/search` - Search roles
- `GET /roles/:id` - Get role by ID
- `POST /roles` - Create new role
- `PUT /roles/:id` - Update role
- `DELETE /roles/:id` - Delete role

### Client Management

#### Clients
- `GET /clients` - List all clients
- `GET /clients/search` - Search clients
- `GET /clients/:id` - Get client by ID
- `POST /clients` - Create new client
- `PUT /clients/:id` - Update client
- `DELETE /clients/:id` - Delete client

#### Client Users
- `POST /clients/:id/users` - Create client user
- `PUT /clients/:id/users` - Update client user
- `DELETE /users/:id` - Delete client user

#### Fleets
- `GET /clients/:id/fleets` - Get client fleets
- `GET /fleets/:id` - Get fleet by ID
- `POST /clients/:id/fleets` - Create fleet
- `PUT /fleets/:id` - Update fleet
- `DELETE /fleets/:id` - Delete fleet

### Device Management

#### Protocols
- `GET /protocols` - List all protocols
- `GET /protocols/search` - Search protocols
- `GET /protocols/:id` - Get protocol by ID
- `POST /protocols` - Create new protocol
- `PUT /protocols/:id` - Update protocol
- `DELETE /protocols/:id` - Delete protocol

#### Device Types
- `GET /device_types` - List all device types
- `GET /device_types/search` - Search device types
- `GET /device_types/:id` - Get device type by ID
- `POST /device_types` - Create new device type
- `PUT /device_types/:id` - Update device type
- `DELETE /device_types/:id` - Delete device type

#### Client Devices
- `GET /client_devices` - List all client devices
- `GET /client_devices/search` - Search client devices
- `GET /client_devices/:id` - Get client device by ID
- `POST /client_devices` - Create new client device
- `PUT /client_devices/:id` - Update client device
- `DELETE /client_devices/:id` - Delete client device
- `POST /client_devices/command_log/:id` - Create command log
- `GET /client_devices/command_log/:id` - Get command log

### GPS & Location Data

#### GPS Data
- `GET /gps_data/search` - Search GPS data
- `GET /gps_data/last_location` - Get last location
- `GET /gps_data/:id` - Get GPS data by ID
- `POST /gps_data` - Create GPS data
- `PUT /gps_data/:id` - Update GPS data
- `DELETE /gps_data/:id` - Delete GPS data

### Alerts & Monitoring

#### Alerts
- `GET /alerts` - List all alerts
- `GET /alerts/search` - Search alerts
- `GET /alerts/:id` - Get alert by ID
- `POST /alerts` - Create new alert
- `PUT /alerts/:id` - Update alert
- `DELETE /alerts/:id` - Delete alert
- `PATCH /alerts/:id/mark-read` - Mark alert as read
- `PATCH /alerts/:id/mark-unread` - Mark alert as unread
- `PATCH /alerts/bulk/mark-read` - Bulk mark alerts as read
- `PATCH /alerts/mark-all-read` - Mark all alerts as read

#### Geofences
- `GET /geofences` - List all geofences
- `GET /geofences/search` - Search geofences
- `GET /geofences/:id` - Get geofence by ID
- `POST /geofences` - Create new geofence
- `PUT /geofences/:id` - Update geofence
- `DELETE /geofences/:id` - Delete geofence
- `GET /geofences/events` - Get geofence events
- `GET /geofences/device/:device_id` - Get geofences by device

### Driver Management

#### Drivers
- `GET /drivers` - List all drivers
- `GET /drivers/search` - Search drivers
- `GET /drivers/:id` - Get driver by ID
- `POST /drivers` - Create new driver
- `PUT /drivers/:id` - Update driver
- `DELETE /drivers/:id` - Delete driver

#### Driver Device Assignments
- `GET /driver_device_assignments` - List all assignments
- `GET /driver_device_assignments/search` - Search assignments
- `GET /driver_device_assignments/:id` - Get assignment by ID
- `POST /driver_device_assignments` - Create new assignment
- `POST /driver_device_assignments/rfid` - Create assignment by RFID
- `PUT /driver_device_assignments/:id` - Update assignment
- `DELETE /driver_device_assignments/:id` - Delete assignment
- `GET /driver_device_assignments/current` - Get current assignment

### Command Logs

#### Command Logs
- `GET /command_logs` - List all command logs
- `GET /command_logs/search` - Search command logs
- `GET /command_logs/:id` - Get command log by ID
- `POST /command_logs` - Create new command log
- `PUT /command_logs/:id` - Update command log
- `DELETE /command_logs/:id` - Delete command log

### Financial Management

#### Invoices
- `GET /invoices` - List all invoices
- `GET /invoices/search` - Search invoices
- `GET /invoices/:id` - Get invoice by ID
- `POST /invoices` - Create new invoice
- `PUT /invoices/:id` - Update invoice
- `DELETE /invoices/:id` - Delete invoice
- `GET /invoices/:id/pdf` - Generate invoice PDF
- `POST /invoices/:id/email` - Send invoice email

#### Payments
- `GET /payments` - List all payments
- `GET /payments/search` - Search payments
- `GET /payments/:id` - Get payment by ID
- `POST /payments` - Create new payment
- `PUT /payments/:id` - Update payment
- `DELETE /payments/:id` - Delete payment

#### Payment Types
- `GET /payment_types` - List all payment types
- `GET /payment_types/search` - Search payment types
- `GET /payment_types/:id` - Get payment type by ID
- `POST /payment_types` - Create new payment type
- `PUT /payment_types/:id` - Update payment type
- `DELETE /payment_types/:id` - Delete payment type

#### Tax Rates
- `GET /tax_rates` - List all tax rates
- `GET /tax_rates/search` - Search tax rates
- `GET /tax_rates/:id` - Get tax rate by ID
- `POST /tax_rates` - Create new tax rate
- `PUT /tax_rates/:id` - Update tax rate
- `DELETE /tax_rates/:id` - Delete tax rate

### Trip Management

#### Trips
- `GET /trips` - List all trips
- `GET /trips/search` - Search trips
- `GET /trips/:id` - Get trip by ID
- `POST /trips` - Create new trip
- `PUT /trips/:id` - Update trip
- `DELETE /trips/:id` - Delete trip
- `GET /trips/replay_simplified/:id` - Get simplified trip replay

### System Configuration

#### Currencies
- `GET /currencies` - List all currencies
- `GET /currencies/search` - Search currencies
- `GET /currencies/:id` - Get currency by ID
- `POST /currencies` - Create new currency
- `PUT /currencies/:id` - Update currency
- `DELETE /currencies/:id` - Delete currency

#### Countries
- `GET /countries` - List all countries

#### Settings
- `GET /settings` - List all settings
- `GET /settings/allowed` - Get allowed settings
- `GET /settings/:key` - Get setting by key
- `GET /settings/:key/value` - Get setting value
- `POST /settings` - Create new setting
- `PUT /settings` - Update all settings
- `PUT /settings/:key` - Update setting
- `DELETE /settings/:key` - Delete setting

### Communication

#### Communication Campaign Logs
- `GET /communication_campaign_logs` - List all campaign logs
- `GET /communication_campaign_logs/statistics` - Get campaign statistics
- `GET /communication_campaign_logs/:id` - Get campaign log by ID
- `POST /communication_campaign_logs` - Create new campaign log
- `PUT /communication_campaign_logs/:id` - Update campaign log
- `DELETE /communication_campaign_logs/:id` - Delete campaign log
- `GET /communication_campaign_logs/client/:clientId` - Get logs by client

### Support System

#### Support Tickets
- `GET /support_tickets` - List all support tickets
- `GET /support_tickets/search` - Search support tickets
- `GET /support_tickets/stats` - Get ticket statistics
- `GET /support_tickets/:id` - Get support ticket by ID
- `GET /support_tickets/:id/replies` - Get ticket replies
- `POST /support_tickets` - Create new support ticket
- `PUT /support_tickets/:id` - Update support ticket
- `DELETE /support_tickets/:id` - Delete support ticket
- `POST /support_tickets/:id/replies` - Add reply to ticket

### Reports

#### Reports (Admin)
- `GET /reports` - List all reports
- `POST /reports` - Create new report
- `PUT /reports/:id` - Update report
- `DELETE /reports/:id` - Delete report

#### Scheduled Reports
- `GET /reports/scheduled` - List all scheduled reports
- `GET /reports/scheduled/:id` - Get scheduled report by ID
- `POST /reports/scheduled/:id/run` - Run scheduled report

#### Report Analytics
- `GET /reports/categories` - Get report categories
- `GET /reports/stats` - Get report statistics
- `GET /reports/execution-history` - Get report execution history

### Dashboard

#### Dashboard
- `GET /dashboard/dashboard_stats` - Get dashboard statistics
- `GET /dashboard/server_stats` - Get server statistics

### Webhooks

#### Webhooks
- `ANY /webhook/paynow` - PayNow webhook handler

### WebSocket

#### WebSocket
- `GET /ws/:channel` - WebSocket connection

---

## Frontend API Routes

Base URL: `/api/v1/frontend`

### User Management

#### User Profile
- `GET /users/profile` - Get current user profile
- `PUT /users/profile` - Update current user profile
- `PUT /users/change_password` - Change user password

#### Roles
- `GET /roles` - List all roles
- `GET /roles/search` - Search roles
- `GET /roles/:id` - Get role by ID

### Client Management

#### Clients
- `GET /clients` - List all clients
- `GET /clients/search` - Search clients
- `GET /clients/:id` - Get client by ID

#### Fleets
- `GET /fleets` - List all fleets
- `GET /fleets/search` - Search fleets
- `GET /fleets/:id` - Get fleet by ID

### Device Management

#### Protocols
- `GET /protocols` - List all protocols
- `GET /protocols/search` - Search protocols
- `GET /protocols/:id` - Get protocol by ID

#### Devices
- `GET /devices` - List all devices
- `GET /devices/search` - Search devices
- `GET /devices/:id` - Get device by ID
- `POST /devices` - Create new device
- `PUT /devices/:id` - Update device
- `DELETE /devices/:id` - Delete device

### Alerts & Monitoring

#### Alerts
- `GET /alerts` - List all alerts
- `GET /alerts/search` - Search alerts
- `GET /alerts/:id` - Get alert by ID
- `PATCH /alerts/:id/mark-read` - Mark alert as read
- `PATCH /alerts/:id/mark-unread` - Mark alert as unread
- `PATCH /alerts/bulk/mark-read` - Bulk mark alerts as read
- `PATCH /alerts/mark-all-read` - Mark all alerts as read

#### Geofences
- `GET /geofences` - List all geofences
- `GET /geofences/search` - Search geofences
- `GET /geofences/:id` - Get geofence by ID
- `POST /geofences` - Create new geofence
- `PUT /geofences/:id` - Update geofence
- `DELETE /geofences/:id` - Delete geofence
- `GET /geofences/events` - Get geofence events
- `GET /geofences/device/:device_id` - Get geofences by device

### Driver Management

#### Drivers
- `GET /drivers` - List all drivers
- `GET /drivers/search` - Search drivers
- `GET /drivers/:id` - Get driver by ID
- `POST /drivers` - Create new driver
- `PUT /drivers/:id` - Update driver
- `DELETE /drivers/:id` - Delete driver

#### Driver Device Assignments
- `GET /driver_device_assignments` - List all assignments
- `GET /driver_device_assignments/search` - Search assignments
- `GET /driver_device_assignments/:id` - Get assignment by ID
- `POST /driver_device_assignments` - Create new assignment
- `PUT /driver_device_assignments/:id` - Update assignment
- `DELETE /driver_device_assignments/:id` - Delete assignment
- `GET /driver_device_assignments/current` - Get current assignment

### Command Logs

#### Command Logs
- `GET /command_logs` - List all command logs
- `GET /command_logs/search` - Search command logs
- `GET /command_logs/:id` - Get command log by ID
- `POST /command_logs` - Create new command log
- `PUT /command_logs/:id` - Update command log
- `DELETE /command_logs/:id` - Delete command log

### Financial Management

#### Invoices
- `GET /invoices` - List all invoices
- `GET /invoices/search` - Search invoices
- `GET /invoices/:id` - Get invoice by ID
- `GET /invoices/:id/pdf` - Generate invoice PDF
- `POST /invoices/:id/email` - Send invoice email

#### Payments
- `GET /payments` - List all payments
- `GET /payments/search` - Search payments
- `GET /payments/:id` - Get payment by ID
- `POST /payments` - Create new payment
- `PUT /payments/:id` - Update payment
- `DELETE /payments/:id` - Delete payment

#### Payment Types
- `GET /payment_types` - List all payment types
- `GET /payment_types/search` - Search payment types
- `GET /payment_types/:id` - Get payment type by ID

### Trip Management

#### Trips
- `GET /trips` - List all trips
- `GET /trips/search` - Search trips
- `GET /trips/:id` - Get trip by ID
- `GET /trips/replay_simplified/:id` - Get simplified trip replay

### System Configuration

#### Currencies
- `GET /currencies` - List all currencies
- `GET /currencies/search` - Search currencies
- `GET /currencies/:id` - Get currency by ID

#### Countries
- `GET /countries` - List all countries

#### Settings
- `GET /settings` - Get allowed settings
- `GET /settings/:key` - Get setting by key
- `GET /settings/:key/value` - Get setting value

### Support System

#### Support Tickets
- `GET /support_tickets` - List all support tickets
- `GET /support_tickets/search` - Search support tickets
- `GET /support_tickets/stats` - Get ticket statistics
- `GET /support_tickets/:id` - Get support ticket by ID
- `GET /support_tickets/:id/replies` - Get ticket replies
- `POST /support_tickets` - Create new support ticket
- `PUT /support_tickets/:id` - Update support ticket
- `POST /support_tickets/:id/replies` - Add reply to ticket

### Reports

#### Reports
- `GET /reports` - List all reports
- `GET /reports/:id` - Get report details

#### Report Generation
- `POST /reports/:id/generate` - Generate report
- `POST /reports/:id/export` - Export report

#### Scheduled Reports
- `GET /reports/scheduled` - List scheduled reports
- `POST /reports/scheduled` - Create scheduled report
- `PUT /reports/scheduled/:id` - Update scheduled report
- `DELETE /reports/scheduled/:id` - Delete scheduled report

### Fleet Management

#### Maintenance Schedules
- `GET /maintenance_schedules` - List maintenance schedules
- `GET /maintenance_schedules/:id` - Get maintenance schedule
- `POST /maintenance_schedules` - Create maintenance schedule
- `PUT /maintenance_schedules/:id` - Update maintenance schedule
- `DELETE /maintenance_schedules/:id` - Delete maintenance schedule
- `GET /maintenance_schedules/upcoming` - Get upcoming maintenance
- `GET /maintenance_schedules/stats` - Get maintenance statistics

#### Customer Visits
- `GET /customer_visits` - List customer visits
- `GET /customer_visits/:id` - Get customer visit
- `POST /customer_visits` - Create customer visit
- `PUT /customer_visits/:id` - Update customer visit
- `DELETE /customer_visits/:id` - Delete customer visit
- `GET /customer_visits/stats` - Get customer visit statistics

#### Driver Training
- `GET /driver_training` - List driver trainings
- `GET /driver_training/:id` - Get driver training
- `POST /driver_training` - Create driver training
- `PUT /driver_training/:id` - Update driver training
- `DELETE /driver_training/:id` - Delete driver training
- `GET /driver_training/expiring` - Get expiring trainings
- `GET /driver_training/stats` - Get driver training statistics

#### Operating Costs
- `GET /operating_costs` - List operating costs
- `GET /operating_costs/:id` - Get operating cost
- `POST /operating_costs` - Create operating cost
- `PUT /operating_costs/:id` - Update operating cost
- `DELETE /operating_costs/:id` - Delete operating cost
- `GET /operating_costs/stats` - Get operating cost statistics

#### Safety Compliance
- `GET /safety_compliance` - List safety compliances
- `GET /safety_compliance/:id` - Get safety compliance
- `POST /safety_compliance` - Create safety compliance
- `PUT /safety_compliance/:id` - Update safety compliance
- `DELETE /safety_compliance/:id` - Delete safety compliance
- `GET /safety_compliance/expiring` - Get expiring compliances
- `GET /safety_compliance/stats` - Get safety compliance statistics

#### Service Technicians
- `GET /service_technicians` - List service technicians
- `GET /service_technicians/:id` - Get service technician
- `POST /service_technicians` - Create service technician
- `PUT /service_technicians/:id` - Update service technician
- `DELETE /service_technicians/:id` - Delete service technician
- `GET /service_technicians/stats` - Get service technician statistics

### Dashboard

#### Dashboard
- `GET /dashboard/dashboard_stats` - Get dashboard statistics

---

## Route Naming Convention

All routes follow a consistent **underscore naming convention**:

- ✅ `/support_tickets` (correct)
- ✅ `/client_devices` (correct)
- ✅ `/device_types` (correct)
- ❌ `/support-tickets` (incorrect)
- ❌ `/client-devices` (incorrect)

## Common Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information"
}
```

### Paginated Response
```json
{
  "success": true,
  "message": "Data retrieved successfully",
  "data": [
    // Array of items
  ],
  "pagination": {
    "current_page": 1,
    "per_page": 10,
    "total": 100,
    "total_pages": 10
  }
}
```

## Error Handling

### Common HTTP Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `204 No Content` - Request successful, no content to return
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Access denied
- `404 Not Found` - Resource not found
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server error

### Validation Errors
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "field_name": ["Error message"],
    "another_field": ["Another error message"]
  }
}
```

## Authentication

### JWT Token Format
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Token Expiration
- Access tokens expire after 24 hours
- Refresh tokens expire after 7 days

## Rate Limiting

- **Backend API**: 1000 requests per hour per user
- **Frontend API**: 500 requests per hour per user
- **WebSocket**: No rate limiting

## WebSocket Events

### Available Channels
- `device_updates` - Real-time device status updates
- `alerts` - Real-time alert notifications
- `geofence_events` - Geofence entry/exit events
- `trip_updates` - Trip progress updates

### Connection
```javascript
const ws = new WebSocket('ws://localhost:8080/api/v1/backend/ws/alerts');
```

---

## Quick Reference

### Most Common Routes

#### Backend
- `GET /users` - List users
- `GET /clients` - List clients
- `GET /devices` - List devices
- `GET /alerts` - List alerts
- `GET /support_tickets` - List support tickets

#### Frontend
- `GET /devices` - List client devices
- `GET /alerts` - List client alerts
- `GET /support_tickets` - List client tickets
- `GET /trips` - List client trips

### Development Tips

1. **Always use underscores** in route names
2. **Include Authorization header** for authenticated requests
3. **Handle pagination** for list endpoints
4. **Check response status** before processing data
5. **Use WebSocket** for real-time updates
6. **Test with Postman** or similar tools during development

---

*Last updated: December 2024*
*Version: 1.0*
