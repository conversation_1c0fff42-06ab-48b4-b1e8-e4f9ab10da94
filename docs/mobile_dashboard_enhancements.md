# Mobile Dashboard Enhancements

## Overview

This document outlines the enhancements made to the `GetDashboardStats` function in `cmd/web/frontend/controllers/dashboard.go` to support the mobile dashboard requirements.

## Mobile Dashboard Requirements Analysis

Based on the mobile dashboard image, the following key elements were identified:

### 1. Devices Overview Card
- **Devices**: Total device count
- **Trips**: Total trip count
- **Online**: Online device count
- **Offline**: Offline device count

### 2. Financial Summary Card
- **Invoiced**: Total amount invoiced
- **Paid**: Total amount paid
- **Balance**: Outstanding balance

### 3. Distance Travelled Card
- **Today**: Distance covered today
- **This Week**: Distance covered this week
- **This Month**: Distance covered this month
- **Total**: Total distance covered

### 4. Latest Alerts Section
- Recent alerts with device information

## Implemented Enhancements

### New DashboardStats Struct Fields

```go
type DashboardStats struct {
    // Existing fields...
    
    // Financial Summary
    TotalInvoiced        float64 `json:"total_invoiced"`
    TotalPaid            float64 `json:"total_paid"`
    TotalBalance         float64 `json:"total_balance"`
    
    // Distance by time periods
    DistanceToday        float64 `json:"distance_today"`
    DistanceThisWeek     float64 `json:"distance_this_week"`
    DistanceThisMonth    float64 `json:"distance_this_month"`
    DistanceTotal        float64 `json:"distance_total"`
    
    // Additional useful metrics
    TotalGeofences       uint64  `json:"total_geofences"`
    ActiveTrips          uint64  `json:"active_trips"`
    CompletedTrips       uint64  `json:"completed_trips"`
    AverageTripDistance  float64 `json:"average_trip_distance"`
    AverageTripDuration  float64 `json:"average_trip_duration"`
    
    // Geofence event statistics
    TotalGeofenceEvents    uint64                `json:"total_geofence_events"`
    TotalGeofenceEntries   uint64                `json:"total_geofence_entries"`
    TotalGeofenceExits     uint64                `json:"total_geofence_exits"`
    GeofenceEventsToday    uint64                `json:"geofence_events_today"`
    LatestGeofenceEvents   []models.GeofenceEvent `json:"latest_geofence_events"`
}
```

### New Database Queries Added

#### 1. Trip Statistics
```go
// Total trips count
var totalTrips int64
config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
    Where("client_devices.client_id = ?", clientId).
    Model(&models.Trip{}).Count(&totalTrips)

// Active trips count
var activeTrips int64
config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
    Where("client_devices.client_id = ? AND trips.status = ?", clientId, "active").
    Model(&models.Trip{}).Count(&activeTrips)

// Completed trips count
var completedTrips int64
config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
    Where("client_devices.client_id = ? AND trips.status = ?", clientId, "completed").
    Model(&models.Trip{}).Count(&completedTrips)
```

#### 2. Distance by Time Periods
```go
// Distance today
var distanceToday float64
today := time.Now().Truncate(24 * time.Hour)
config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
    Where("client_devices.client_id = ? AND DATE(trips.start_time) = DATE(?)", clientId, today).
    Model(&models.Trip{}).Select("COALESCE(SUM(distance), 0)").Scan(&distanceToday)

// Distance this week
var distanceThisWeek float64
weekStart := time.Now().AddDate(0, 0, -int(time.Now().Weekday()))
weekStart = weekStart.Truncate(24 * time.Hour)
config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
    Where("client_devices.client_id = ? AND trips.start_time >= ?", clientId, weekStart).
    Model(&models.Trip{}).Select("COALESCE(SUM(distance), 0)").Scan(&distanceThisWeek)

// Distance this month
var distanceThisMonth float64
monthStart := time.Now().AddDate(0, 0, -time.Now().Day()+1)
monthStart = monthStart.Truncate(24 * time.Hour)
config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
    Where("client_devices.client_id = ? AND trips.start_time >= ?", clientId, monthStart).
    Model(&models.Trip{}).Select("COALESCE(SUM(distance), 0)").Scan(&distanceThisMonth)
```

#### 3. Financial Statistics
```go
// Total invoiced amount (in base currency)
var totalInvoiced float64
config.DB.Where("client_id = ?", clientId).Model(&models.Invoice{}).Select("COALESCE(SUM(base_currency_amount), 0)").Scan(&totalInvoiced)

// Total paid amount (in base currency)
var totalPaid float64
config.DB.Joins("JOIN invoices ON invoice_payments.invoice_id = invoices.id").
    Where("invoices.client_id = ?", clientId).
    Model(&models.InvoicePayment{}).Select("COALESCE(SUM(invoice_payments.base_currency_amount), 0)").Scan(&totalPaid)

// Calculate balance (in base currency)
totalBalance := totalInvoiced - totalPaid
```

#### 4. Trip Averages
```go
// Average trip distance
var avgTripDistance float64
config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
    Where("client_devices.client_id = ? AND trips.status = ?", clientId, "completed").
    Model(&models.Trip{}).Select("COALESCE(AVG(distance), 0)").Scan(&avgTripDistance)

// Average trip duration
var avgTripDuration float64
config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
    Where("client_devices.client_id = ? AND trips.status = ? AND trips.duration IS NOT NULL", clientId, "completed").
    Model(&models.Trip{}).Select("COALESCE(AVG(duration), 0)").Scan(&avgTripDuration)
```

#### 5. Geofence Statistics
```go
// Total geofences
var totalGeofences int64
config.DB.Where("client_id = ?", clientId).Model(&models.Geofence{}).Count(&totalGeofences)
```

#### 6. Geofence Event Statistics
```go
// Total geofence events
var totalGeofenceEvents int64
config.DB.Joins("JOIN geofences ON geofence_events.geofence_id = geofences.id").
    Where("geofences.client_id = ?", clientId).
    Model(&models.GeofenceEvent{}).Count(&totalGeofenceEvents)

// Total geofence entries
var totalGeofenceEntries int64
config.DB.Joins("JOIN geofences ON geofence_events.geofence_id = geofences.id").
    Where("geofences.client_id = ? AND geofence_events.event_type = ?", clientId, "entry").
    Model(&models.GeofenceEvent{}).Count(&totalGeofenceEntries)

// Total geofence exits
var totalGeofenceExits int64
config.DB.Joins("JOIN geofences ON geofence_events.geofence_id = geofences.id").
    Where("geofences.client_id = ? AND geofence_events.event_type = ?", clientId, "exit").
    Model(&models.GeofenceEvent{}).Count(&totalGeofenceExits)

// Geofence events today
var geofenceEventsToday int64
config.DB.Joins("JOIN geofences ON geofence_events.geofence_id = geofences.id").
    Where("geofences.client_id = ? AND DATE(geofence_events.event_timestamp) = DATE(?)", clientId, today).
    Model(&models.GeofenceEvent{}).Count(&geofenceEventsToday)

// Latest geofence events
var latestGeofenceEvents []models.GeofenceEvent
config.DB.Joins("JOIN geofences ON geofence_events.geofence_id = geofences.id").
    Where("geofences.client_id = ?", clientId).
    Order("geofence_events.event_timestamp desc").
    Preload("Geofence").
    Preload("ClientDevice").
    Limit(5).
    Find(&latestGeofenceEvents)
```

### Improvements Made

1. **Added Missing Trip Count**: The mobile dashboard shows "34 Trips" but this was missing from the original function.

2. **Added Financial Summary**: Complete financial tracking including invoiced, paid, and balance amounts (all in base currency for consistency).

3. **Added Time-Based Distance Tracking**: Distance metrics broken down by today, this week, this month, and total.

4. **Enhanced Trip Analytics**: Added active trips, completed trips, and average trip metrics.

5. **Added Geofence Count**: Total number of geofences for the client.

6. **Added Geofence Event Statistics**: Complete tracking of geofence entry/exit events including totals, today's events, and latest events.

7. **Optimized Latest Alerts**: Limited to 5 alerts for mobile display instead of 10.

8. **Improved Code Organization**: Better structured queries with clear comments and logical grouping.

## API Response Format

The enhanced API now returns:

```json
{
  "data": {
    "total_devices": 12,
    "total_online_devices": 9,
    "total_offline_devices": 3,
    "total_moving_devices": 5,
    "total_idling_devices": 4,
    "total_stopped_devices": 3,
    "total_users": 8,
    "total_active_users": 7,
    "total_inactive_users": 1,
    "total_distance_covered": 8450.5,
    "total_alerts": 25,
    "total_warning_alerts": 15,
    "total_critical_alerts": 10,
    "total_trips": 34,
    "latest_alerts": [...],
    "total_invoiced": 120.0,
    "total_paid": 90.0,
    "total_balance": 30.0,
    "distance_today": 25.0,
    "distance_this_week": 180.0,
    "distance_this_month": 720.0,
    "distance_total": 8450.5,
    "total_geofences": 5,
    "active_trips": 2,
    "completed_trips": 32,
    "average_trip_distance": 15.2,
    "average_trip_duration": 1800.0,
    "total_geofence_events": 45,
    "total_geofence_entries": 25,
    "total_geofence_exits": 20,
    "geofence_events_today": 3,
    "latest_geofence_events": [...]
  },
  "timestamp": 1703123456
}
```

## Performance Considerations

1. **Efficient Joins**: All queries use proper JOINs to filter by client_id for security and performance.

2. **Indexed Queries**: Queries leverage existing database indexes on client_id, status, and date fields.

3. **Single Function Call**: All statistics are calculated in one function call to minimize API requests.

4. **Proper NULL Handling**: All SUM and AVG operations use COALESCE to handle NULL values gracefully.

## Future Enhancements

Potential additional metrics that could be added:

1. **Fuel Efficiency**: Average fuel consumption per trip
2. **Driver Performance**: Driver-specific statistics
3. **Maintenance Alerts**: Upcoming maintenance reminders
4. **Weather Integration**: Weather conditions during trips
5. **Cost Analysis**: Cost per kilometer, cost per trip
6. **Real-time Status**: Current device locations and status
7. **Predictive Analytics**: Estimated arrival times, route optimization

## Testing

The enhanced function maintains backward compatibility while adding new features. All existing dashboard functionality remains intact, with new metrics providing additional value for mobile users.
