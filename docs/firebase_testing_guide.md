# Firebase Push Notifications Testing Guide

This guide provides comprehensive instructions for testing Firebase push notifications in the YoTracker system.

## Prerequisites

### 1. Environment Setup

Before testing Firebase functionality, ensure you have:

- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- YoTracker backend server running
- Valid JWT authentication token
- Firebase project with FCM enabled (for real testing)
- Go 1.19+ (for running tests)

### 2. Firebase Configuration

The system now supports two authentication methods:

#### Method 1: Service Account Key (Recommended)
Place your `yotracker-firebase.json` service account key file in the project root directory.

**To get your Service Account Key:**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to Project Settings → Service Accounts
4. Click "Generate new private key"
5. Save the downloaded JSON file as `yotracker-firebase.json` in your project root

#### Method 2: Server Key (Legacy)
Add the following to your `.env` file:

```bash
# Firebase Configuration (Legacy method)
FIREBASE_SERVER_KEY=your_firebase_server_key_here
```

**To get your Firebase Server Key:**
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project
3. Go to Project Settings → Cloud Messaging
4. Copy the "Server key" from the Cloud Messaging tab

### 3. Alert System Integration

The Firebase push notification service is now integrated into the alert system:

- **Automatic Push Notifications**: When alerts are triggered, push notifications are automatically sent to all users with Firebase tokens for the affected client
- **Client-Based Targeting**: Notifications are sent to all users of a client, not individual devices
- **Alert Type Support**: All alert types (speed, geofence, impact, maintenance, etc.) are supported
- **Rich Data**: Push notifications include device information, alert details, and timestamps
- **Channel Preferences**: Push notifications can be enabled/disabled per client through alert preferences

**How it works:**
1. When an alert is triggered (speed, geofence, impact, etc.)
2. The `FlexibleNotificationService` checks if push notifications are enabled for that client
3. It finds all users with Firebase tokens for that client
4. Sends push notifications to all those users with alert details
5. Logs the notification attempts for tracking

### 4. Docker Compose Configuration

For Docker deployment, you have two options:

#### Option 1: Service Account File (Recommended)
Mount the service account file into your container:

```yaml
web-backend-dev:
  volumes:
    - ./yotracker-firebase.json:/app/yotracker/yotracker-firebase.json
    # ... other volumes
```

#### Option 2: Environment Variable (Legacy)
Add the `FIREBASE_SERVER_KEY` environment variable to your docker-compose.yml:

```yaml
web-backend-dev:
  environment:
    - FIREBASE_SERVER_KEY=${FIREBASE_SERVER_KEY}
    # ... other environment variables
```

## Testing Methods

### Method 1: Automated Test Script

Use the provided test script for quick testing:

```bash
# Make the script executable (already done)
chmod +x test-firebase-notifications.sh

# Edit the script to add your JWT token
nano test-firebase-notifications.sh

# Run the test script
./test-firebase-notifications.sh
```

### Method 2: Manual API Testing

#### Using curl

**1. Get Supported Notification Types:**
```bash
curl -X GET \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:9000/api/v1/backend/notifications/types
```

**2. Send Single Push Notification:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "to": "test-fcm-token-12345",
    "title": "🚗 Engine ON Alert",
    "body": "Vehicle ABC123 just turned on at Main Street.",
    "type": "ignition_alert",
    "client_device_id": 1234,
    "data": {
      "vehicle_id": "ABC123",
      "location": "Main Street, Downtown"
    }
  }' \
  http://localhost:9000/api/v1/backend/notifications/push
```

**3. Send Client-Based Notifications:**

**Send to specific client:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "client_ids": [1],
    "title": "📢 Client Announcement",
    "body": "Important update for your fleet management system.",
    "type": "admin_message",
    "data": {
      "announcement_id": "ANN-2023-001",
      "priority": "medium"
    }
  }' \
  http://localhost:9000/api/v1/backend/notifications/client
```

**Send to multiple specific clients:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "client_ids": [1, 2, 3],
    "title": "📢 Multi-Client Announcement",
    "body": "Important update for selected fleet management systems.",
    "type": "admin_message",
    "data": {
      "announcement_id": "ANN-2023-002",
      "priority": "high",
      "target_clients": [1, 2, 3]
    }
  }' \
  http://localhost:9000/api/v1/backend/notifications/client
```

**Send to all clients:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "🌍 System Maintenance Notice",
    "body": "Scheduled system maintenance will begin in 2 hours.",
    "type": "system_notice",
    "data": {
      "maintenance_id": "MAINT-2023-002",
      "estimated_duration": "4 hours"
    }
  }' \
  http://localhost:9000/api/v1/backend/notifications/client
```

**4. Send Bulk Push Notifications:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "tokens": [
      "test-fcm-token-1",
      "test-fcm-token-2",
      "test-fcm-token-3"
    ],
    "title": "🔧 System Maintenance Notice",
    "body": "Scheduled maintenance will begin in 30 minutes.",
    "type": "system_notice",
    "data": {
      "maintenance_id": "MAINT-2023-001"
    }
  }' \
  http://localhost:9000/api/v1/backend/notifications/push/bulk
```

**4. Get Notification Logs:**
```bash
curl -X GET \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:9000/api/v1/backend/notifications/logs
```

#### Using Postman

Import the following collection or create requests manually:

**Collection: Firebase Push Notifications**

1. **Get Notification Types**
   - Method: GET
   - URL: `{{base_url}}/api/v1/backend/notifications/types`
   - Headers: `Authorization: Bearer {{jwt_token}}`

2. **Send Single Notification**
   - Method: POST
   - URL: `{{base_url}}/api/v1/backend/notifications/push`
   - Headers: 
     - `Authorization: Bearer {{jwt_token}}`
     - `Content-Type: application/json`
   - Body (JSON):
   ```json
   {
     "to": "{{fcm_token}}",
     "title": "Test Notification",
     "body": "This is a test notification",
     "type": "ignition_alert",
     "client_device_id": 1234,
     "data": {
       "custom_field": "custom_value"
     }
   }
   ```

3. **Send Bulk Notifications**
   - Method: POST
   - URL: `{{base_url}}/api/v1/backend/notifications/push/bulk`
   - Headers: 
     - `Authorization: Bearer {{jwt_token}}`
     - `Content-Type: application/json`
   - Body (JSON):
   ```json
   {
     "tokens": ["{{fcm_token_1}}", "{{fcm_token_2}}"],
     "title": "Bulk Test",
     "body": "This is a bulk test",
     "type": "system_notice"
   }
   ```

4. **Get Notification Logs**
   - Method: GET
   - URL: `{{base_url}}/api/v1/backend/notifications/logs?page=1&limit=10`
   - Headers: `Authorization: Bearer {{jwt_token}}`

### Method 3: Unit and Integration Tests

Run the comprehensive test suite:

```bash
# Run Firebase service unit tests (now using go-fcm library)
go test ./internal/services/ -v -run "TestNewPushNotificationService|TestSendPushNotification|TestSendBulkPushNotification|TestValidateNotificationType"

# Run all Firebase-related tests
go test ./internal/services/ -v -run "Firebase|PushNotification"

# Run all tests
go test ./... -v
```

**Note:** The tests now use the `github.com/appleboy/go-fcm` library with proper mocking for reliable testing without requiring real Firebase credentials.

## Test Scenarios

### 1. Valid Notification Types

Test all supported notification types:

- `ignition_alert` - Vehicle ignition on/off
- `admin_message` - Admin messages
- `geofence_alert` - Geofence entry/exit
- `trip_summary` - Trip completion summary
- `system_notice` - System announcements
- `new_chat_message` - New chat messages
- `maintenance_alert` - Maintenance reminders
- `speed_alert` - Speed limit violations
- `fuel_alert` - Low fuel warnings
- `battery_alert` - Low battery warnings
- `panic_alert` - Emergency panic button
- `driver_alert` - Driver-specific alerts
- `device_offline` - Device connectivity issues
- `device_online` - Device reconnection

### 2. Error Scenarios

Test error handling:

- Invalid notification type
- Missing required fields
- Invalid FCM token
- Unauthorized requests
- Malformed JSON
- Empty bulk token arrays

### 3. Data Validation

Test data payload handling:

- Custom data fields
- Timestamp handling
- Client device ID association
- Large data payloads
- Special characters in title/body

## Expected Responses

### Success Response
```json
{
  "success": true,
  "message": "Notification sent successfully",
  "data": {
    "success": 1,
    "failure": 0,
    "results": [
      {
        "message_id": "firebase_message_id_here"
      }
    ]
  }
}
```

### Error Response
```json
{
  "message": "Invalid notification type"
}
```

## Troubleshooting

### Common Issues

1. **"Unauthorized" Error**
   - Ensure JWT token is valid and not expired
   - Check Authorization header format: `Bearer <token>`

2. **"Invalid notification type" Error**
   - Use only supported notification types
   - Check the types endpoint for valid options

3. **Firebase Connection Errors**
   - Verify FIREBASE_SERVER_KEY is set correctly
   - Check Firebase project configuration
   - Ensure FCM is enabled in Firebase Console

4. **Database Errors**
   - Ensure database is running and accessible
   - Check notification_logs table exists
   - Verify user and client relationships

### Debug Mode

Enable debug logging by setting:

```bash
export GIN_MODE=debug
export LOG_LEVEL=debug
```

### Logs

Check application logs for detailed error information:

```bash
# Docker logs
docker-compose logs web-backend-dev

# Or if running locally
tail -f logs/app.log
```

## Real Device Testing

For testing with real devices:

1. **Get Real FCM Tokens**
   - Implement FCM token registration in your mobile app
   - Store tokens in the database
   - Use real tokens in test requests

2. **Test with Real Firebase Project**
   - Set up a Firebase project
   - Configure FCM
   - Use real server key in environment

3. **Monitor Firebase Console**
   - Check message delivery statistics
   - Monitor error rates
   - Verify message formatting

## Performance Testing

For load testing:

```bash
# Install hey (HTTP load testing tool)
go install github.com/rakyll/hey@latest

# Test single notification endpoint
hey -n 100 -c 10 -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -D test-notification.json \
  http://localhost:9000/api/v1/backend/notifications/push

# Test bulk notification endpoint
hey -n 50 -c 5 -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -D test-bulk-notification.json \
  http://localhost:9000/api/v1/backend/notifications/push/bulk
```

## Security Considerations

1. **Token Security**
   - Never expose FCM tokens in logs
   - Use HTTPS in production
   - Implement token rotation

2. **Rate Limiting**
   - Implement rate limiting for notification endpoints
   - Monitor for abuse patterns
   - Set reasonable limits per user/client

3. **Data Privacy**
   - Sanitize notification content
   - Avoid sensitive data in notification payloads
   - Implement proper access controls

## Next Steps

After successful testing:

1. **Production Setup**
   - Configure production Firebase project
   - Set up monitoring and alerting
   - Implement proper error handling

2. **Mobile App Integration**
   - Implement FCM token registration
   - Handle notification display
   - Implement notification actions

3. **Monitoring**
   - Set up Firebase Analytics
   - Monitor delivery rates
   - Track user engagement

4. **Optimization**
   - Implement notification scheduling
   - Add notification preferences
   - Optimize payload sizes
