# Support Ticket Notification Bug Fix

## Issue Description

**Problem**: Clients were receiving email notifications saying "A staff member has replied" when they themselves replied to their own support tickets.

**Root Cause**: The notification system was sending client notifications for ALL non-internal replies, regardless of who created the reply. The email message was also hardcoded to say "A staff member has replied" even when the reply was from the client.

## Technical Details

### Original Logic (Buggy)
```go
// sendTicketReplyNotifications sends notifications when a reply is added
func (s *SupportTicketService) sendTicketReplyNotifications(reply *models.SupportTicketReply) {
	// Send email to client if reply is not internal
	if !reply.IsInternal {
		s.sendClientReplyNotification(reply) // ❌ Always sent for non-internal replies
	}
	// ...
}

// sendClientReplyNotification sends email notification to client when staff replies
func (s *SupportTicketService) sendClientReplyNotification(reply *models.SupportTicketReply) {
	emailContent := fmt.Sprintf(`
		<p>A staff member has replied to your support ticket:</p> // ❌ Always said "staff member"
		// ...
	`)
}
```

### Fixed Logic
```go
// sendTicketReplyNotifications sends notifications when a reply is added
func (s *SupportTicketService) sendTicketReplyNotifications(reply *models.SupportTicketReply) {
	// Send email to client if reply is not internal AND from staff (not from client)
	if !reply.IsInternal && reply.CreatedBy.ClientId == nil {
		s.sendClientReplyNotification(reply) // ✅ Only sent for staff replies
	}
	// ...
}

// sendClientReplyNotification sends email notification to client when staff replies
func (s *SupportTicketService) sendClientReplyNotification(reply *models.SupportTicketReply) {
	emailContent := fmt.Sprintf(`
		<p>A new reply has been added to your support ticket:</p> // ✅ More generic message
		// ...
	`)
}
```

## User Types and ClientId Field

The fix relies on the `ClientId` field in the `User` model to distinguish between client users and staff users:

- **Client Users**: Have `ClientId` set to their client's ID
- **Staff/Admin Users**: Have `ClientId` set to `nil`

### User Model Structure
```go
type User struct {
	Id       uint   `json:"id" gorm:"primaryKey"`
	ClientId *uint  `json:"client_id"` // nil for staff, set for clients
	Email    string `json:"email"`
	UserType string `json:"user_type"` // "frontend" for clients, "backend" for staff
	// ... other fields
}
```

## Notification Logic

### When Client Notifications Are Sent
✅ **Staff replies** (non-internal): `reply.CreatedBy.ClientId == nil && !reply.IsInternal`
✅ **Staff replies** (internal): No client notification (as expected)

❌ **Client replies**: No client notification (fixed)

### Email Message Changes
- **Before**: "A staff member has replied to your support ticket"
- **After**: "A new reply has been added to your support ticket"

## Testing

Comprehensive tests were added to verify the fix:

```go
func TestSupportTicketReplyNotifications(t *testing.T) {
	// Test 1: Client reply should not trigger client notification
	// Test 2: Staff reply should trigger client notification  
	// Test 3: Internal reply should not trigger client notification
}
```

### Test Scenarios

1. **Client Reply**: Verify no client notification is sent
2. **Staff Reply**: Verify client notification is sent
3. **Internal Reply**: Verify no client notification is sent

## Impact

### Before Fix
- Clients received confusing emails when they replied to their own tickets
- Email message was misleading ("staff member" when it was the client)
- Unnecessary email notifications

### After Fix
- Clients only receive notifications when staff actually replies
- Email message is more accurate
- Reduced email noise for clients

## Files Modified

1. **`internal/services/support_ticket_service.go`**
   - Updated `sendTicketReplyNotifications` logic
   - Updated `sendClientReplyNotification` email message

2. **`cmd/web/frontend/controllers/support_tickets_test.go`**
   - Added `TestSupportTicketReplyNotifications` test

## Verification

The fix was verified by:
1. ✅ Running existing tests to ensure no regressions
2. ✅ Adding new tests to verify the notification logic
3. ✅ Testing all scenarios (client reply, staff reply, internal reply)

## Related Issues

This fix also addresses potential issues with:
- Email spam for clients
- Confusing notification messages
- Incorrect notification logic for different user types

## Future Considerations

- Consider making notification preferences configurable per client
- Add notification history for audit purposes
- Consider different notification channels (SMS, push notifications)
