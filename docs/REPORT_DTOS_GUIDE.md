# Report DTOs (Data Transfer Objects) Guide

## Overview

This guide explains the new DTO (Data Transfer Object) system for reports in YoTracker. The DTOs provide clean, structured data with proper headers for frontend consumption and CSV export functionality.

## Key Benefits

✅ **Clean Data Structure**: No more complex model relationships in API responses  
✅ **Proper Headers**: Each DTO includes CSV headers for easy export  
✅ **Type Safety**: Strongly typed structures for better development experience  
✅ **Consistent Format**: Standardized format across all reports  
✅ **CSV Export Ready**: Built-in CSV conversion utilities  
✅ **Frontend Friendly**: Simplified data structure for frontend consumption  

## DTO Structure

Each DTO follows this pattern:

```go
type ReportNameDTO struct {
    Headers []string        `json:"headers"`  // CSV headers
    Data    []ReportNameRow `json:"data"`     // Actual data rows
    Summary SummaryType     `json:"summary"`  // Optional summary stats
}
```

## Available DTOs

### 1. Core GPS & Tracking Reports

#### PositionLogDTO
```go
type PositionLogDTO struct {
    Headers []string        `json:"headers"`
    Data    []PositionLogRow `json:"data"`
}
```

**Headers**: Timestamp, Device Name, Plate Number, Driver Name, Latitude, Longitude, Speed (km/h), Heading, Altitude, Satellites, Accuracy, Fuel Level (%), Engine Status, Trip ID

**Use Case**: Raw GPS tracking data with device and driver information

#### TripDetailDTO
```go
type TripDetailDTO struct {
    Headers []string      `json:"headers"`
    Data    []TripDetailRow `json:"data"`
}
```

**Headers**: Trip ID, Device Name, Plate Number, Driver Name, Start Time, End Time, Duration, Distance (km), Avg Speed (km/h), Max Speed (km/h), Start Location, End Location, Fuel Consumed (L), Fuel Cost, Status

**Use Case**: Detailed trip information with start/end locations and metrics

### 2. Driver Behavior & Safety Reports

#### DriverSafetyScorecardDTO
```go
type DriverSafetyScorecardDTO struct {
    Headers []string                `json:"headers"`
    Data    []DriverSafetyScoreRow  `json:"data"`
    Summary DriverSafetySummary     `json:"summary"`
}
```

**Headers**: Driver Name, Device Name, Plate Number, Safety Score, Speeding Events, Harsh Braking, Harsh Acceleration, Harsh Cornering, Idle Time, Total Trips, Total Distance (km), Avg Speed (km/h), Risk Level

**Summary**: Total Drivers, Avg Safety Score, High Risk Drivers, Total Violations, Total Speeding, Total Harsh Events

**Use Case**: Driver safety scoring and risk assessment

#### SpeedingViolationsDTO
```go
type SpeedingViolationsDTO struct {
    Headers []string              `json:"headers"`
    Data    []SpeedingViolationRow `json:"data"`
}
```

**Headers**: Timestamp, Device Name, Plate Number, Driver Name, Location, Speed Limit (km/h), Actual Speed (km/h), Speed Exceeded (km/h), Duration, Severity, Risk Level

**Use Case**: Detailed speeding violation analysis

### 3. Cost & Efficiency Reports

#### FuelConsumptionDTO
```go
type FuelConsumptionDTO struct {
    Headers []string              `json:"headers"`
    Data    []FuelConsumptionRow  `json:"data"`
    Summary FuelConsumptionSummary `json:"summary"`
}
```

**Headers**: Date, Device Name, Plate Number, Driver Name, Fuel Consumed (L), Fuel Cost, Distance (km), Efficiency (km/L), Avg Speed (km/h), Idle Time, Idle Fuel Waste (L), Cost per km

**Summary**: Total Fuel Consumed, Total Fuel Cost, Total Distance, Avg Efficiency, Total Idle Waste, Avg Cost per km

**Use Case**: Fuel consumption analysis and cost tracking

### 4. Additional Specialized Reports

#### FuelEstimationDTO
```go
type FuelEstimationDTO struct {
    Headers []string              `json:"headers"`
    Data    []FuelEstimationRow   `json:"data"`
    Summary FuelEstimationSummary `json:"summary"`
}
```

**Headers**: Month, Device Name, Plate Number, Total Distance (km), Running Hours, Avg Speed (km/h), Total Trips, Estimated Fuel (L), Estimated Cost, Efficiency (km/h), Fuel Efficiency (L/h)

**Use Case**: Fuel consumption estimation based on running hours

#### DriverBehaviorAnalysisDTO
```go
type DriverBehaviorAnalysisDTO struct {
    Headers []string                    `json:"headers"`
    Data    []DriverBehaviorAnalysisRow `json:"data"`
    Summary DriverBehaviorAnalysisSummary `json:"summary"`
}
```

**Headers**: Month, Driver Name, Device Name, Plate Number, Behavior Score, Speeding Events, Harsh Braking, Harsh Acceleration, Harsh Cornering, Idle Time (%), Fuel Efficiency (km/L), Safety Violations, Risk Level, Improvement Areas

**Use Case**: Comprehensive driver behavior analysis

#### FleetProductivityDTO
```go
type FleetProductivityDTO struct {
    Headers []string                  `json:"headers"`
    Data    []FleetProductivityRow    `json:"data"`
    Summary FleetProductivitySummary  `json:"summary"`
}
```

**Headers**: Month, Total Vehicles, Active Vehicles, Total Trips, Total Distance (km), Total Hours, Productive Hours, Productivity Rate (%), Avg Utilization (%), Fuel Efficiency (km/L), Cost per km, Productivity Score

**Use Case**: Fleet productivity metrics and optimization

## Usage Examples

### 1. Basic DTO Usage

```go
// Generate DTO data
dto, totalCount, filteredCount, err := s.generatePositionLogDTOExample(filters)
if err != nil {
    return nil, err
}

// Access headers
headers := dto.Headers
// ["Timestamp", "Device Name", "Plate Number", "Driver Name", ...]

// Access data
data := dto.Data
// []PositionLogRow{...}
```

### 2. CSV Export

```go
// Convert DTO to CSV format
csvData := s.convertDTOToCSV(dto.Data)
// Returns [][]string with headers as first row

// Get headers separately
headers := s.getDTOHeaders(dto.Data)
// Returns []string of headers
```

### 3. Frontend Response

```json
{
  "success": true,
  "data": {
    "report_info": {
      "name": "Position Log Report",
      "description": "Raw GPS location data...",
      "category": "Detail"
    },
    "filters": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31"
    },
    "data": {
      "headers": ["Timestamp", "Device Name", "Plate Number", ...],
      "data": [
        {
          "timestamp": "2024-01-15 10:30:00",
          "device_name": "Truck-001",
          "plate_number": "ABC123",
          "driver_name": "John Doe",
          "latitude": -17.8252,
          "longitude": 31.0335,
          "speed": 65.5,
          "heading": 180.0,
          "altitude": 1480.0,
          "satellites": 8,
          "accuracy": 5.0,
          "fuel_level": 75.5,
          "engine_status": "On",
          "trip_id": null
        }
      ]
    },
    "summary": null,
    "metadata": {
      "generated_at": "2024-01-15T10:30:00Z",
      "total_records": 1000,
      "filtered_records": 50,
      "execution_time": "150ms",
      "format": "json"
    }
  }
}
```

## Utility Functions

### Header Extraction
```go
// Extract headers from any DTO struct
headers := GetHeadersFromStruct([]PositionLogRow{})
// Returns: ["Timestamp", "Device Name", "Plate Number", ...]
```

### CSV Conversion
```go
// Convert DTO data to CSV format
csvData := ConvertStructToCSV(dtoData)
// Returns [][]string with headers as first row
```

### Formatting Functions
```go
// Duration formatting
duration := FormatDuration(3665) // "1h 1m 5s"

// Distance formatting
distance := FormatDistance(0.5) // "500m"
distance := FormatDistance(1.5) // "1.5km"

// Speed formatting
speed := FormatSpeed(65.5) // "65.5 km/h"

// Fuel formatting
fuel := FormatFuel(25.5) // "25.5 L"

// Cost formatting
cost := FormatCost(38.25) // "$38.25"

// Percentage formatting
percentage := FormatPercentage(85.5) // "85.5%"
```

### Calculation Functions
```go
// Risk level calculation
riskLevel := CalculateRiskLevel(85.5) // "Medium"

// Severity calculation
severity := CalculateSeverity(25.0) // "High"

// Efficiency calculation
efficiency := CalculateEfficiency(250.5, 25.5) // 9.82

// Utilization rate calculation
utilization := CalculateUtilizationRate(6.5, 8.0) // 81.25

// Safety score calculation
safetyScore := CalculateSafetyScore(3, 2, 1, 0, 25) // 85.0

// Behavior score calculation
behaviorScore := CalculateBehaviorScore(85.0, 9.8, 15.5) // 82.3

// Improvement areas identification
areas := GetImprovementAreas(3, 2, 1, 0, 15.5) 
// "Speed Management, Smooth Braking, Smooth Acceleration"
```

## Implementation Guide

### 1. Create DTO Structure
```go
// Define your DTO row structure with CSV tags
type MyReportRow struct {
    Date        string  `json:"date" csv:"Date"`
    DeviceName  string  `json:"device_name" csv:"Device Name"`
    Value       float64 `json:"value" csv:"Value"`
    Unit        string  `json:"unit" csv:"Unit"`
}

// Define your DTO structure
type MyReportDTO struct {
    Headers []string      `json:"headers"`
    Data    []MyReportRow `json:"data"`
    Summary MySummary     `json:"summary,omitempty"`
}
```

### 2. Generate DTO Data
```go
func (s *ReportService) generateMyReportDTO(filters models.ReportFilters) (*models.MyReportDTO, int, int, error) {
    // 1. Query database for raw data
    var rawData []MyRawData
    // ... database query logic ...
    
    // 2. Convert to DTO structure
    var dtoData []models.MyReportRow
    for _, item := range rawData {
        dtoRow := models.MyReportRow{
            Date:       item.Date.Format("2006-01-02"),
            DeviceName: item.DeviceName,
            Value:      item.Value,
            Unit:       item.Unit,
        }
        dtoData = append(dtoData, dtoRow)
    }
    
    // 3. Create DTO with headers
    dto := &models.MyReportDTO{
        Headers: GetHeadersFromStruct([]models.MyReportRow{}),
        Data:    dtoData,
    }
    
    return dto, totalCount, filteredCount, nil
}
```

### 3. Update Report Service
```go
// Add case to GenerateReport switch
case "my_report":
    data, summary, totalRecords, filteredRecords, err = s.generateMyReportDTO(filters)
```

## Best Practices

1. **Consistent Naming**: Use descriptive names for DTOs and rows
2. **CSV Tags**: Always include `csv` tags for proper header extraction
3. **Data Types**: Use appropriate data types (string for formatted values)
4. **Summary Stats**: Include summary statistics for aggregated reports
5. **Error Handling**: Proper error handling in DTO generation
6. **Performance**: Consider pagination for large datasets
7. **Documentation**: Document any complex calculations or business logic

## Migration from Old Reports

To migrate existing reports to use DTOs:

1. **Identify Report**: Choose which report to migrate
2. **Create DTO Structure**: Define DTO row and summary structures
3. **Update Query Logic**: Modify existing query to populate DTO structure
4. **Test Data**: Verify data accuracy and formatting
5. **Update Frontend**: Ensure frontend can handle new structure
6. **Add CSV Export**: Implement CSV export using utility functions

## Benefits Summary

- ✅ **Reduced API Payload**: Clean data without model relationships
- ✅ **Better Frontend Experience**: Simplified data structure
- ✅ **CSV Export Ready**: Built-in CSV conversion
- ✅ **Type Safety**: Strongly typed structures
- ✅ **Consistent Format**: Standardized across all reports
- ✅ **Maintainable**: Easy to modify and extend
- ✅ **Performance**: Optimized data transfer

This DTO system provides a robust foundation for clean, maintainable report generation with excellent frontend integration and export capabilities.
