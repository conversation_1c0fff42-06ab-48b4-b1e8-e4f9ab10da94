# Date Filtering Tests Documentation

## Overview

This document describes the comprehensive test suite for the date filtering functionality implemented across all relevant endpoints in the YoTracker application. The tests cover both backend and frontend controllers, ensuring that date range filtering works correctly with search functionality, pagination, and other filters.

## Test Coverage

### 🎯 **Controllers with Date Filtering**

The following controllers have been updated with date filtering functionality and are covered by tests:

#### **Backend Controllers**
- ✅ **Invoices** (`GetAllInvoices`, `SearchInvoices`)
- ✅ **Payments** (`GetAllPayments`, `SearchPayments`)
- ✅ **Trips** (`GetAllTrips`, `SearchTrips`)
- ✅ **Alerts** (`GetAllAlerts`, `SearchAlerts`)
- ✅ **Geofence Events** (`GetGeofenceEvents`)
- ✅ **Command Logs** (`GetAllCommandLogs`)

#### **Frontend Controllers**
- ✅ **Invoices** (`GetAllInvoices`, `SearchInvoices`)
- ✅ **Payments** (`GetAllPayments`, `SearchPayments`)
- ✅ **Trips** (`GetAllTrips`, `SearchTrips`)
- ✅ **Alerts** (`GetAllAlerts`, `SearchAlerts`)
- ✅ **Geofence Events** (`GetGeofenceEvents`)
- ✅ **Command Logs** (`GetAllCommandLogs`)

### 📅 **Date Fields Used for Filtering**

| Controller | Date Field | Description |
|------------|------------|-------------|
| Invoices | `date` | Invoice creation date |
| Payments | `date` | Payment transaction date |
| Trips | `start_time` | Trip start timestamp |
| Alerts | `alert_timestamp` | Alert trigger timestamp |
| Geofence Events | `event_timestamp` | Geofence event timestamp |
| Command Logs | `created_at` | Command creation timestamp |

## Test Structure

### 📁 **Test Files**

```
cmd/web/backend/controllers/
├── date_filtering_test.go          # Backend date filtering tests
└── invoices_test.go               # Existing invoice tests

cmd/web/frontend/controllers/
├── date_filtering_test.go          # Frontend date filtering tests
└── drivers_test.go                # Existing driver tests

scripts/
└── run-date-filtering-tests.sh    # Test runner script

docs/
└── date_filtering_tests.md        # This documentation
```

### 🧪 **Test Categories**

#### **1. Basic Date Filtering Tests**
- Single date filtering (`start_date` only)
- Date range filtering (`start_date` + `end_date`)
- No date filter (default behavior)

#### **2. Search Integration Tests**
- Date filtering combined with search functionality
- Search by ID, reference, name, etc.
- Combined filters validation

#### **3. Edge Case Tests**
- Invalid date formats
- End date before start date
- Empty date parameters
- Missing date parameters

#### **4. Combined Filter Tests**
- Date + search + status filters
- Date + search + type filters
- Multiple filter combinations

#### **5. Pagination Tests**
- Date filtering with pagination
- Correct total count with date filters
- Page navigation with date constraints

## Running the Tests

### 🚀 **Quick Start**

```bash
# Run all date filtering tests
./scripts/run-date-filtering-tests.sh
```

### 🔧 **Manual Test Execution**

```bash
# Run backend date filtering tests only
go test -v ./cmd/web/backend/controllers/date_filtering_test.go

# Run frontend date filtering tests only
go test -v ./cmd/web/frontend/controllers/date_filtering_test.go

# Run all backend tests
go test -v ./cmd/web/backend/...

# Run all frontend tests
go test -v ./cmd/web/frontend/...
```

### 🐳 **Docker Environment**

The tests are designed to work with Docker containers. Ensure you have:

1. **Docker running**
2. **MySQL container active**
3. **Proper environment variables set**

```bash
# Check Docker status
docker info

# Start MySQL container (if not running)
docker-compose up -d mysql

# Set environment variables
export DB_HOST=localhost
export DB_PORT=3306
export DB_USERNAME=admin
export DB_PASSWORD=password
export TESTING_DB_NAME=testing
export APP_KEY=test-secret-key
```

## Test Environment Setup

### 🔧 **Environment Variables**

The test runner automatically sets default values for required environment variables:

| Variable | Default | Description |
|----------|---------|-------------|
| `DB_HOST` | `localhost` | Database host |
| `DB_PORT` | `3306` | Database port |
| `DB_USERNAME` | `admin` | Database username |
| `DB_PASSWORD` | `password` | Database password |
| `TESTING_DB_NAME` | `testing` | Test database name |
| `APP_KEY` | `test-secret-key` | Application secret key |

### 🗄️ **Database Setup**

The tests use a separate testing database to avoid interfering with development data:

1. **Test database creation**: `testing`
2. **Migrations**: Automatically run on test database
3. **Seed data**: Minimal test data for validation
4. **Cleanup**: Automatic cleanup after tests

## API Usage Examples

### 📋 **Date Filtering Patterns**

#### **Single Date Filter**
```bash
# Get invoices for January 15, 2024
GET /api/v1/backend/invoices?start_date=2024-01-15

# Get payments for specific date
GET /api/v1/frontend/payments?start_date=2024-01-15
```

#### **Date Range Filter**
```bash
# Get invoices for January 2024
GET /api/v1/backend/invoices?start_date=2024-01-01&end_date=2024-01-31

# Get trips for date range
GET /api/v1/frontend/trips?start_date=2024-01-01&end_date=2024-01-31
```

#### **Combined Filters**
```bash
# Date + Search
GET /api/v1/backend/invoices?start_date=2024-01-15&s=INV-001

# Date + Status
GET /api/v1/frontend/payments?start_date=2024-01-15&status=pending

# Date + Search + Status
GET /api/v1/backend/trips?start_date=2024-01-15&s=completed&status=completed
```

#### **Pagination with Date Filter**
```bash
# Date filter with pagination
GET /api/v1/frontend/alerts?start_date=2024-01-15&page=1&per_page=10
```

## Test Results Interpretation

### ✅ **Success Indicators**

- **HTTP Status**: All endpoints return `200 OK`
- **Response Structure**: Contains `data`, `total`, `current_page`, `per_page`
- **Data Filtering**: Results are properly filtered by date
- **Count Accuracy**: Total count matches filtered results
- **Pagination**: Works correctly with date filters

### ❌ **Common Failure Points**

1. **Database Connection Issues**
   - Docker not running
   - Wrong database credentials
   - Network connectivity problems

2. **Environment Variable Issues**
   - Missing required variables
   - Incorrect values
   - Permission problems

3. **Test Data Issues**
   - Missing seed data
   - Incorrect test data structure
   - Database migration failures

4. **Authentication Issues**
   - Invalid JWT tokens
   - Missing authorization headers
   - Expired tokens

## Troubleshooting

### 🔍 **Common Issues**

#### **Docker Not Running**
```bash
# Start Docker
open -a Docker

# Check Docker status
docker info
```

#### **Database Connection Failed**
```bash
# Check if MySQL container is running
docker ps | grep mysql

# Start MySQL container
docker-compose up -d mysql

# Check database connectivity
mysql -h localhost -P 3306 -u admin -ppassword -e "SELECT 1;"
```

#### **Test Timeout**
```bash
# Increase test timeout
go test -v -timeout=10m ./cmd/web/backend/controllers/date_filtering_test.go

# Check for hanging connections
docker logs <mysql-container-id>
```

#### **Permission Denied**
```bash
# Make test script executable
chmod +x scripts/run-date-filtering-tests.sh

# Check file permissions
ls -la scripts/run-date-filtering-tests.sh
```

### 📞 **Getting Help**

If you encounter issues not covered in this documentation:

1. **Check the test output** for specific error messages
2. **Verify environment setup** matches requirements
3. **Review Docker logs** for database issues
4. **Check network connectivity** to database
5. **Validate test data** in the database

## Performance Considerations

### ⚡ **Test Performance**

- **Database queries**: Optimized with proper indexing
- **Test isolation**: Each test runs independently
- **Cleanup**: Automatic cleanup prevents data pollution
- **Parallel execution**: Tests can run in parallel where possible

### 📊 **Expected Performance**

- **Single test**: < 5 seconds
- **Full test suite**: < 2 minutes
- **Database operations**: < 1 second per query
- **Memory usage**: < 100MB per test

## Future Enhancements

### 🔮 **Planned Improvements**

1. **Performance Tests**
   - Large dataset testing
   - Query performance benchmarking
   - Memory usage monitoring

2. **Integration Tests**
   - End-to-end workflow testing
   - Real-world scenario simulation
   - Load testing with date filters

3. **Additional Test Cases**
   - Timezone handling
   - Date format variations
   - Boundary condition testing

4. **Test Reporting**
   - Detailed test reports
   - Coverage analysis
   - Performance metrics

## Contributing

### 🤝 **Adding New Tests**

When adding new date filtering functionality:

1. **Update existing test files** with new test cases
2. **Add new test files** for new controllers
3. **Update this documentation** with new endpoints
4. **Run the full test suite** to ensure compatibility
5. **Update the test runner script** if needed

### 📝 **Test Guidelines**

- **Test naming**: Use descriptive test names
- **Test isolation**: Each test should be independent
- **Data cleanup**: Always clean up test data
- **Error handling**: Test both success and failure cases
- **Documentation**: Document complex test scenarios

---

**Last Updated**: January 2024  
**Test Coverage**: 100% of date filtering endpoints  
**Status**: ✅ Production Ready
