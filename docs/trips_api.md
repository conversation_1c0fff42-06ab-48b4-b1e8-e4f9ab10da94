# Trips API Documentation

This document describes the trips API endpoints that have been added to both frontend and backend services.

## Overview

The trips API provides paginated access to trip data with preloaded client device information. Trips represent vehicle journeys with start/end locations, timing, and metrics.

## Frontend Endpoints

### GET /api/v1/frontend/trips
Get paginated trips for the authenticated client.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 20)
- `sort_by` (optional): Sort field (default: start_time)
- `sort_order` (optional): Sort direction (asc/desc, default: desc)
- `device_id` (optional): Filter by device ID
- `driver_id` (optional): Filter by driver ID
- `status` (optional): Filter by trip status
- `trip_type` (optional): Filter by trip type
- `start_date` (optional): Filter by start date (YYYY-MM-DD)
- `end_date` (optional): Filter by end date (YYYY-MM-DD)
- `min_distance` (optional): Minimum distance filter
- `max_distance` (optional): Maximum distance filter
- `min_duration` (optional): Minimum duration filter (seconds)
- `max_duration` (optional): Maximum duration filter (seconds)

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "client_device_id": 123,
      "client_device": {
        "id": 123,
        "name": "Vehicle 1",
        "plate_number": "ABC123"
      },
      "driver_id": 456,
      "driver": {
        "id": 456,
        "name": "John Doe"
      },
      "start_time": "2024-01-15T08:00:00Z",
      "end_time": "2024-01-15T10:30:00Z",
      "duration": 9000,
      "start_latitude": -17.8252,
      "start_longitude": 31.0335,
      "start_location": "Harare, Zimbabwe",
      "end_latitude": -17.8500,
      "end_longitude": 31.0500,
      "end_location": "Bulawayo, Zimbabwe",
      "distance": 45.5,
      "max_speed": 80.0,
      "avg_speed": 60.0,
      "idle_time": 300,
      "status": "completed",
      "trip_type": "business",
      "notes": "Delivery trip",
      "created_at": "2024-01-15T08:00:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 150,
  "current_page": 1,
  "per_page": 20,
  "total_pages": 8
}
```

### GET /api/v1/frontend/trips/search
Search trips with various filters.

**Query Parameters:**
- `s` (optional): Search term
- `limit` (optional): Maximum results (default: 50)
- All filters from GET /trips endpoint

**Response:**
```json
{
  "data": [
    // Array of trip objects
  ]
}
```

### GET /api/v1/frontend/trips/:id
Get a specific trip by ID.

**Response:**
```json
{
  "data": {
    // Single trip object with preloaded relationships
  }
}
```

## Backend Endpoints

### GET /api/v1/backend/trips
Get paginated trips (admin access).

**Query Parameters:**
- All parameters from frontend endpoint
- `client_id` (optional): Filter by client ID

### GET /api/v1/backend/trips/search
Search trips (admin access).

### GET /api/v1/backend/trips/:id
Get a specific trip by ID (admin access).

### POST /api/v1/backend/trips
Create a new trip.

**Request Body:**
```json
{
  "client_device_id": 123,
  "driver_id": 456,
  "start_time": "2024-01-15T08:00:00Z",
  "start_latitude": -17.8252,
  "start_longitude": 31.0335,
  "start_location": "Harare, Zimbabwe",
  "trip_type": "business",
  "notes": "Delivery trip"
}
```

**Response:**
```json
{
  "message": "Trip created successfully",
  "data": {
    // Created trip object
  }
}
```

### PUT /api/v1/backend/trips/:id
Update an existing trip.

**Request Body:**
```json
{
  "driver_id": 456,
  "end_time": "2024-01-15T10:30:00Z",
  "end_latitude": -17.8500,
  "end_longitude": 31.0500,
  "end_location": "Bulawayo, Zimbabwe",
  "distance": 45.5,
  "max_speed": 80.0,
  "avg_speed": 60.0,
  "idle_time": 300,
  "duration": 9000,
  "status": "completed",
  "trip_type": "business",
  "notes": "Updated notes"
}
```

### DELETE /api/v1/backend/trips/:id
Delete a trip.

**Response:**
```json
{
  "message": "Trip deleted successfully"
}
```

## Trip Model

```go
type Trip struct {
    Id             uint         `json:"id"`
    ClientDeviceId uint         `json:"client_device_id"`
    ClientDevice   ClientDevice `json:"client_device"`
    DriverId       *uint        `json:"driver_id"`
    Driver         *Driver      `json:"driver,omitempty"`
    
    // Trip timing
    StartTime time.Time  `json:"start_time"`
    EndTime   *time.Time `json:"end_time"`
    Duration  *int       `json:"duration"` // Duration in seconds
    
    // Trip locations
    StartLatitude  float64  `json:"start_latitude"`
    StartLongitude float64  `json:"start_longitude"`
    StartLocation  *string  `json:"start_location"`
    EndLatitude    *float64 `json:"end_latitude"`
    EndLongitude   *float64 `json:"end_longitude"`
    EndLocation    *string  `json:"end_location"`
    
    // Trip metrics
    Distance float64  `json:"distance"`  // Distance in kilometers
    MaxSpeed *float64 `json:"max_speed"` // Maximum speed during trip
    AvgSpeed *float64 `json:"avg_speed"` // Average speed during trip
    IdleTime *int     `json:"idle_time"` // Total idle time in seconds
    
    // Trip status and metadata
    Status   string  `json:"status"` // active, completed, cancelled
    TripType *string `json:"trip_type"` // business, personal, maintenance, etc.
    Notes    *string `json:"notes"`
    
    CreatedAt time.Time `json:"created_at"`
    UpdatedAt time.Time `json:"updated_at"`
}
```

## Features

- **Pagination**: All list endpoints support pagination with configurable page size
- **Filtering**: Multiple filter options for device, driver, status, date range, etc.
- **Sorting**: Configurable sorting by any field
- **Search**: Full-text search across trip and device fields
- **Preloaded Data**: Client device and driver information is preloaded for efficiency
- **Client Isolation**: Frontend endpoints automatically filter by authenticated client
- **Admin Access**: Backend endpoints provide full CRUD operations for administrators

## Authentication

- **Frontend endpoints**: Require valid JWT token with client context
- **Backend endpoints**: Require valid JWT token with admin/backend user permissions

## Error Handling

All endpoints return appropriate HTTP status codes:
- `200 OK`: Successful operation
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server error

