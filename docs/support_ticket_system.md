# Support Ticket System Documentation

## Overview

The Support Ticket System provides a comprehensive customer support solution for YoTracker, allowing clients to create and manage support tickets while enabling staff to efficiently handle and resolve customer issues.

## Features

- **Multi-Department Support**: Billing, Technical, General, Feature Requests, and Bug Reports
- **Priority Management**: Low, Medium, High, and Urgent priority levels
- **Status Tracking**: Open, Assigned, In Progress, Waiting for Customer, Resolved, Closed
- **Device Integration**: Link tickets to specific client devices for technical issues
- **Reply System**: Internal and external replies with proper notification handling
- **Email & Slack Notifications**: Automatic notifications for ticket events
- **Statistics & Reporting**: Comprehensive ticket analytics and reporting
- **Role-Based Access**: Different permissions for clients and staff

## Models

### Request Models

#### CreateSupportTicketRequest (Backend/Admin)
Used by backend API when creating tickets on behalf of clients:
```go
type CreateSupportTicketRequest struct {
    ClientId       uint   `json:"client_id" binding:"required"`
    ClientDeviceId *uint  `json:"client_device_id"`
    Department     string `json:"department" binding:"required"`
    Priority       string `json:"priority"`
    Subject        string `json:"subject" binding:"required"`
    Description    string `json:"description" binding:"required"`
}
```

#### CreateSupportTicketClientRequest (Frontend/Client)
Used by frontend API when clients create their own tickets:
```go
type CreateSupportTicketClientRequest struct {
    ClientDeviceId *uint  `json:"client_device_id"`
    Department     string `json:"department" binding:"required"`
    Priority       string `json:"priority"`
    Subject        string `json:"subject" binding:"required"`
    Description    string `json:"description" binding:"required"`
}
```

**Key Difference**: The client request model doesn't include `client_id` as it's automatically extracted from the authentication context.

### SupportTicket

```go
type SupportTicket struct {
    Id             uint       `json:"id" gorm:"primaryKey"`
    ClientId       uint       `json:"client_id" gorm:"index"`
    Client         Client     `json:"client"`
    CreatedById    uint       `json:"created_by_id" gorm:"index"`
    CreatedBy      User       `json:"created_by"`
    AssignedToId   *uint      `json:"assigned_to_id" gorm:"index"`
    AssignedTo     *User      `json:"assigned_to,omitempty"`
    ClientDeviceId *uint      `json:"client_device_id" gorm:"index"`
    ClientDevice   *ClientDevice `json:"client_device,omitempty"`
    Department     string     `json:"department" gorm:"type:enum('billing','technical','general','feature_request','bug_report');default:'general'"`
    Priority       string     `json:"priority" gorm:"type:enum('low','medium','high','urgent');default:'medium'"`
    Status         string     `json:"status" gorm:"type:enum('open','assigned','in_progress','waiting_for_customer','resolved','closed');default:'open'"`
    Subject        string     `json:"subject" gorm:"type:varchar(255);not null"`
    Description    string     `json:"description" gorm:"type:text;not null"`
    Resolution     *string    `json:"resolution" gorm:"type:text"`
    CreatedAt      time.Time  `json:"created_at"`
    UpdatedAt      time.Time  `json:"updated_at"`
    ResolvedAt     *time.Time `json:"resolved_at"`
    ClosedAt       *time.Time `json:"closed_at"`
}
```

### SupportTicketReply

```go
type SupportTicketReply struct {
    Id              uint      `json:"id" gorm:"primaryKey"`
    TicketId        uint      `json:"ticket_id" gorm:"index"`
    Ticket          SupportTicket `json:"ticket"`
    CreatedById     uint      `json:"created_by_id" gorm:"index"`
    CreatedBy       User      `json:"created_by"`
    IsInternal      bool      `json:"is_internal" gorm:"default:false"`
    Message         string    `json:"message" gorm:"type:text;not null"`
    CreatedAt       time.Time `json:"created_at"`
    UpdatedAt       time.Time `json:"updated_at"`
}
```

## API Endpoints

### Backend API (Admin/Staff Access)

**Note**: Backend API requires `client_id` in request body for ticket creation.

#### Get All Support Tickets
```
GET /api/v1/backend/support_tickets
```

**Query Parameters:**
- `client_id` (uint): Filter by client ID
- `department` (string): Filter by department
- `priority` (string): Filter by priority
- `status` (string): Filter by status
- `assigned_to_id` (uint): Filter by assigned staff member
- `client_device_id` (uint): Filter by device
- `created_by_id` (uint): Filter by ticket creator
- `page` (int): Page number (default: 1)
- `per_page` (int): Items per page (default: 20)

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "client_id": 1,
      "client": { ... },
      "created_by_id": 1,
      "created_by": { ... },
      "assigned_to_id": 2,
      "assigned_to": { ... },
      "client_device_id": 1,
      "client_device": { ... },
      "department": "technical",
      "priority": "high",
      "status": "open",
      "subject": "Device not responding",
      "description": "Device TEST001 is not responding to commands",
      "resolution": null,
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "resolved_at": null,
      "closed_at": null
    }
  ],
  "total": 1,
  "current_page": 1,
  "per_page": 20,
  "last_page": 1
}
```

#### Get Support Ticket by ID
```
GET /api/v1/backend/support_tickets/:id
```

#### Create Support Ticket
```
POST /api/v1/backend/support_tickets
```

**Request Body:**
```json
{
  "client_id": 1,
  "client_device_id": 1,
  "department": "technical",
  "priority": "high",
  "subject": "Device not responding",
  "description": "Device TEST001 is not responding to commands"
}
```

#### Update Support Ticket
```
PUT /api/v1/backend/support_tickets/:id
```

**Request Body:**
```json
{
  "assigned_to_id": 2,
  "priority": "urgent",
  "status": "in_progress",
  "subject": "Updated subject",
  "description": "Updated description",
  "resolution": "Issue resolved by restarting device"
}
```

#### Delete Support Ticket
```
DELETE /api/v1/backend/support_tickets/:id
```

#### Get Support Ticket Replies
```
GET /api/v1/backend/support_tickets/:id/replies
```

#### Create Support Ticket Reply
```
POST /api/v1/backend/support_tickets/:id/replies
```

**Request Body:**
```json
{
  "ticket_id": 1,
  "is_internal": false,
  "message": "We are investigating the issue and will update you shortly."
}
```

#### Get Support Ticket Statistics
```
GET /api/v1/backend/support_tickets/stats?client_id=1
```

**Response:**
```json
{
  "data": {
    "total_tickets": 10,
    "open_tickets": 3,
    "assigned_tickets": 2,
    "in_progress_tickets": 1,
    "resolved_tickets": 3,
    "closed_tickets": 1,
    "urgent_tickets": 1,
    "high_priority_tickets": 4
  }
}
```

#### Search Support Tickets
```
GET /api/v1/backend/support_tickets/search?q=device
```

### Frontend API (Client Access)

**Note**: Frontend API automatically extracts `client_id` from authentication context - no need to include in request body.

#### Get All Support Tickets (Client)
```
GET /api/v1/frontend/support_tickets
```

#### Get Support Ticket by ID (Client)
```
GET /api/v1/frontend/support_tickets/:id
```

#### Create Support Ticket (Client)
```
POST /api/v1/frontend/support_tickets
```

#### Update Support Ticket (Client - Limited Fields)
```
PUT /api/v1/frontend/support_tickets/:id
```

**Note:** Clients can only update `subject` and `description` fields.

#### Get Support Ticket Replies (Client)
```
GET /api/v1/frontend/support_tickets/:id/replies
```

#### Create Support Ticket Reply (Client)
```
POST /api/v1/frontend/support_tickets/:id/replies
```

**Note:** Clients cannot create internal replies.

#### Get Support Ticket Statistics (Client)
```
GET /api/v1/frontend/support_tickets/stats
```

#### Search Support Tickets (Client)
```
GET /api/v1/frontend/support_tickets/search?q=device
```

## Notification System

### Email Notifications

The system sends email notifications for the following events:

1. **Ticket Created**: Sent to admin email addresses
2. **Ticket Assigned**: Sent to assigned staff member
3. **Ticket Status Changed**: Sent to admin email addresses
4. **Reply Added**: 
   - Sent to client (if reply is not internal)
   - Sent to admin email addresses

### Slack Notifications

All ticket events are sent to the `#yopractice-support` Slack channel with detailed information including:
- Ticket ID and subject
- Department and priority
- Status changes
- Client information
- Staff assignments

### Configuration

Support ticket notifications can be configured via settings:

- `support_ticket_admin_email`: Admin email for notifications
- `support_ticket_slack_channel`: Slack channel for notifications
- `support_ticket_email_notifications`: Enable/disable email notifications
- `support_ticket_slack_notifications`: Enable/disable Slack notifications

## Usage Examples

### Creating a Technical Support Ticket

```javascript
// Frontend - Client creates a technical ticket
const ticketData = {
  client_device_id: 123,
  department: "technical",
  priority: "high",
  subject: "GPS signal issues",
  description: "Device is not receiving GPS signals properly"
};

const response = await fetch('/api/v1/frontend/support_tickets', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(ticketData)
});
```

### Staff Assigning and Updating a Ticket

```javascript
// Backend - Staff assigns ticket to themselves
const updateData = {
  assigned_to_id: staffUserId,
  status: "assigned",
  priority: "urgent"
};

const response = await fetch(`/api/v1/backend/support_tickets/${ticketId}`, {
  method: 'PUT',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(updateData)
});
```

### Adding a Reply to a Ticket

```javascript
// Add a reply (can be done by client or staff)
const replyData = {
  ticket_id: ticketId,
  is_internal: false, // true for staff-only internal notes
  message: "We have identified the issue and are working on a solution."
};

const response = await fetch(`/api/v1/frontend/support_tickets/${ticketId}/replies`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify(replyData)
});
```

### Getting Ticket Statistics

```javascript
// Get statistics for dashboard
const response = await fetch('/api/v1/frontend/support_tickets/stats', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const stats = await response.json();
console.log(`Total tickets: ${stats.data.total_tickets}`);
console.log(`Open tickets: ${stats.data.open_tickets}`);
console.log(`Urgent tickets: ${stats.data.urgent_tickets}`);
```

## Database Schema

### support_tickets Table

```sql
CREATE TABLE `support_tickets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_id` bigint unsigned NOT NULL,
  `created_by_id` bigint unsigned NOT NULL,
  `assigned_to_id` bigint unsigned DEFAULT NULL,
  `client_device_id` bigint unsigned DEFAULT NULL,
  `department` enum('billing','technical','general','feature_request','bug_report') NOT NULL DEFAULT 'general',
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `status` enum('open','assigned','in_progress','waiting_for_customer','resolved','closed') NOT NULL DEFAULT 'open',
  `subject` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `resolution` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `closed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_support_tickets_client_id` (`client_id`),
  KEY `idx_support_tickets_created_by_id` (`created_by_id`),
  KEY `idx_support_tickets_assigned_to_id` (`assigned_to_id`),
  KEY `idx_support_tickets_client_device_id` (`client_device_id`),
  KEY `idx_support_tickets_department` (`department`),
  KEY `idx_support_tickets_priority` (`priority`),
  KEY `idx_support_tickets_status` (`status`),
  KEY `idx_support_tickets_created_at` (`created_at`),
  CONSTRAINT `fk_support_tickets_client_id` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_support_tickets_created_by_id` FOREIGN KEY (`created_by_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_support_tickets_assigned_to_id` FOREIGN KEY (`assigned_to_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_support_tickets_client_device_id` FOREIGN KEY (`client_device_id`) REFERENCES `client_devices` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### support_ticket_replies Table

```sql
CREATE TABLE `support_ticket_replies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ticket_id` bigint unsigned NOT NULL,
  `created_by_id` bigint unsigned NOT NULL,
  `is_internal` tinyint(1) NOT NULL DEFAULT '0',
  `message` text NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_support_ticket_replies_ticket_id` (`ticket_id`),
  KEY `idx_support_ticket_replies_created_by_id` (`created_by_id`),
  KEY `idx_support_ticket_replies_created_at` (`created_at`),
  CONSTRAINT `fk_support_ticket_replies_ticket_id` FOREIGN KEY (`ticket_id`) REFERENCES `support_tickets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_support_ticket_replies_created_by_id` FOREIGN KEY (`created_by_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## Testing

The support ticket system includes comprehensive tests for all layers. To run all support ticket tests:

```bash
# Run all support ticket tests
./scripts/run-support-ticket-tests.sh

# Or run individual test suites
go test ./internal/services -run TestSupportTicketService -v
go test ./cmd/web/backend/controllers -run TestBackendSupportTicketControllers -v
go test ./cmd/web/frontend/controllers -run TestFrontendSupportTicketControllers -v
```

### Test Coverage

#### Service Layer Tests (`internal/services/support_ticket_service_test.go`)
- CRUD operations for tickets and replies
- Filtering and pagination functionality
- Statistics generation
- Notification system
- Error handling and edge cases

#### Backend Controller Tests (`cmd/web/backend/controllers/support_tickets_test.go`)
- Admin/staff access to all tickets
- Full CRUD operations with admin permissions
- Search and filtering capabilities
- Statistics and reporting
- Error handling for invalid inputs
- Authentication and authorization

#### Frontend Controller Tests (`cmd/web/frontend/controllers/support_tickets_test.go`)
- Client access restricted to their own tickets
- Limited update permissions (subject and description only)
- Cross-client data isolation
- Reply creation with internal flag restrictions
- Search within client's own tickets
- Error handling for unauthorized access

## Migration

To set up the support ticket system, run the migration:

```bash
# Apply the migration
mysql -u username -p database_name < migrations/support_tickets_migration.sql
```

Or use the Go migration:

```bash
go run cmd/web/backend/backend.go
```

The migration will automatically create the necessary tables and insert default settings.
