# Alert Preferences API Documentation

## Overview

The Alert Preferences API provides endpoints to manage notification preferences for alerts at three hierarchical levels:
- **Client Level**: Default preferences for all devices under a client
- **Fleet Level**: Preferences that override client defaults for specific fleets
- **Device Level**: Preferences that override fleet defaults for specific devices

## API Endpoints

### Client-Level Alert Preferences (Backend)

#### Get All Alert Preferences
```http
GET /api/v1/backend/alert_preferences
```

**Response:**
```json
{
  "message": "Alert preferences retrieved successfully",
  "data": [
    {
      "id": 1,
      "client_id": 1,
      "client": {
        "id": 1,
        "name": "Test Client"
      },
      "alert_type": "speed_alert",
      "enabled": true,
      "channels": ["email", "sms"],
      "priority": "high",
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z"
    }
  ]
}
```

#### Get Alert Preference by ID
```http
GET /api/v1/backend/alert_preferences/:id
```

#### Create Alert Preference
```http
POST /api/v1/backend/alert_preferences
Content-Type: application/json

{
  "alert_type": "speed_alert",
  "enabled": true,
  "channels": ["email", "sms", "whatsapp"],
  "priority": "high"
}
```

#### Update Alert Preference
```http
PUT /api/v1/backend/alert_preferences/:id
Content-Type: application/json

{
  "alert_type": "speed_alert",
  "enabled": false,
  "channels": ["email"],
  "priority": "normal"
}
```

#### Delete Alert Preference
```http
DELETE /api/v1/backend/alert_preferences/:id
```

#### Get Alert Preferences by Client
```http
GET /api/v1/backend/alert_preferences/client/:clientId
```

### Device-Level Alert Preferences (Backend)

#### Get Device Alert Preferences
```http
GET /api/v1/backend/client_devices/device/:deviceId/alert_preferences
```

#### Get Device Alert Preference by ID
```http
GET /api/v1/backend/client_devices/device/:deviceId/alert_preferences/:id
```

#### Create Device Alert Preference
```http
POST /api/v1/backend/client_devices/device/:deviceId/alert_preferences
Content-Type: application/json

{
  "alert_type": "towing_event",
  "enabled": true,
  "channels": ["whatsapp", "slack"],
  "priority": "critical"
}
```

#### Update Device Alert Preference
```http
PUT /api/v1/backend/client_devices/device/:deviceId/alert_preferences/:id
Content-Type: application/json

{
  "alert_type": "towing_event",
  "enabled": true,
  "channels": ["sms"],
  "priority": "high"
}
```

#### Delete Device Alert Preference
```http
DELETE /api/v1/backend/client_devices/device/:deviceId/alert_preferences/:id
```

### Fleet-Level Alert Preferences (Backend)

#### Get Fleet Alert Preferences
```http
GET /api/v1/backend/clients/fleet/:fleetId/alert_preferences
```

#### Get Fleet Alert Preference by ID
```http
GET /api/v1/backend/clients/fleet/:fleetId/alert_preferences/:id
```

#### Create Fleet Alert Preference
```http
POST /api/v1/backend/clients/fleet/:fleetId/alert_preferences
Content-Type: application/json

{
  "alert_type": "impact_detection",
  "enabled": true,
  "channels": ["email", "slack"],
  "priority": "normal"
}
```

#### Update Fleet Alert Preference
```http
PUT /api/v1/backend/clients/fleet/:fleetId/alert_preferences/:id
Content-Type: application/json

{
  "alert_type": "impact_detection",
  "enabled": true,
  "channels": ["email", "sms", "whatsapp"],
  "priority": "high"
}
```

#### Delete Fleet Alert Preference
```http
DELETE /api/v1/backend/clients/fleet/:fleetId/alert_preferences/:id
```

### Client Alert Preferences (Frontend)

#### Get Client's Alert Preferences
```http
GET /api/v1/frontend/alert_preferences
Authorization: Bearer <token>
```

#### Get Client's Alert Preference by ID
```http
GET /api/v1/frontend/alert_preferences/:id
Authorization: Bearer <token>
```

#### Create Client Alert Preference
```http
POST /api/v1/frontend/alert_preferences
Authorization: Bearer <token>
Content-Type: application/json

{
  "alert_type": "speed_alert",
  "enabled": true,
  "channels": ["email", "sms"],
  "priority": "high"
}
```

#### Update Client Alert Preference
```http
PUT /api/v1/frontend/alert_preferences/:id
Authorization: Bearer <token>
Content-Type: application/json

{
  "alert_type": "speed_alert",
  "enabled": false,
  "channels": ["email"],
  "priority": "normal"
}
```

#### Delete Client Alert Preference
```http
DELETE /api/v1/frontend/alert_preferences/:id
Authorization: Bearer <token>
```

## Data Models

### AlertPreferenceRequest
```json
{
  "alert_type": "speed_alert",
  "enabled": true,
  "channels": ["email", "sms", "whatsapp", "slack"],
  "priority": "low|normal|high|critical"
}
```

### Alert Types
- `speed_alert` - Speed limit violations
- `shutdown_time_alert` - Device shutdown during restricted hours
- `towing_event` - Vehicle towing detection
- `impact_detection` - Potential impact/collision detection
- `geofence_event` - Geofence entry/exit events
- `maintenance_alert` - Maintenance reminders
- `battery_alert` - Low battery warnings
- `sos_alert` - Emergency SOS alerts

### Notification Channels
- `email` - Email notifications
- `sms` - SMS text messages
- `whatsapp` - WhatsApp messages
- `slack` - Slack notifications

### Priority Levels
- `low` - Low priority alerts
- `normal` - Standard priority (default)
- `high` - High priority alerts
- `critical` - Critical alerts requiring immediate attention

## Preference Hierarchy

The system uses a hierarchical preference system where more specific settings override general ones:

```
Device Level (Highest Priority)
    ↓
Fleet Level
    ↓
Client Level
    ↓
Default Settings (Lowest Priority)
```

## Example Usage

### Setting up Client Defaults
```bash
# Create default speed alert preference for client
curl -X POST http://localhost:8080/api/v1/backend/alert_preferences \
  -H "Content-Type: application/json" \
  -d '{
    "alert_type": "speed_alert",
    "enabled": true,
    "channels": ["email", "sms"],
    "priority": "high"
  }'
```

### Overriding for Specific Fleet
```bash
# Create fleet-specific preference
curl -X POST http://localhost:8080/api/v1/backend/clients/fleet/1/alert_preferences \
  -H "Content-Type: application/json" \
  -d '{
    "alert_type": "speed_alert",
    "enabled": true,
    "channels": ["whatsapp", "slack"],
    "priority": "critical"
  }'
```

### Device-Specific Override
```bash
# Create device-specific preference
curl -X POST http://localhost:8080/api/v1/backend/client_devices/device/1/alert_preferences \
  -H "Content-Type: application/json" \
  -d '{
    "alert_type": "speed_alert",
    "enabled": true,
    "channels": ["sms"],
    "priority": "normal"
  }'
```

## Error Responses

### 400 Bad Request
```json
{
  "message": "Invalid request data",
  "error": "validation error details"
}
```

### 401 Unauthorized
```json
{
  "message": "Client not authenticated"
}
```

### 404 Not Found
```json
{
  "message": "Alert preference not found"
}
```

### 500 Internal Server Error
```json
{
  "message": "Failed to create alert preference",
  "error": "database error details"
}
```

## Testing

Run the test suite to verify API functionality:

```bash
# Build and run the test
go build -o test-alert-preferences-api cmd/test-alert-preferences-api/main.go
./test-alert-preferences-api
```

## Integration with Notification System

These preferences are automatically used by the `FlexibleNotificationService` when sending alerts. The service:

1. Checks device-level preferences first
2. Falls back to fleet-level preferences
3. Falls back to client-level preferences
4. Uses default settings if no preferences are found

The resolved preferences determine which notification channels to use and the priority level for the alert.
