# YoTracker API Quick Reference

## Base URLs
- **Backend**: `/api/v1/backend`
- **Frontend**: `/api/v1/frontend`

## Authentication
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

## Most Common Routes

### 🔐 Authentication
- `POST /login` - User login
- `POST /register` - User registration
- `GET /users/profile` - Get profile
- `PUT /users/profile` - Update profile

### 👥 User Management
- `GET /users` - List users
- `GET /users/:id` - Get user
- `POST /users` - Create user
- `PUT /users/:id` - Update user
- `DELETE /users/:id` - Delete user

### 🏢 Client Management
- `GET /clients` - List clients
- `GET /clients/:id` - Get client
- `POST /clients` - Create client
- `PUT /clients/:id` - Update client
- `DELETE /clients/:id` - Delete client

### 📱 Device Management
- `GET /client_devices` - List devices
- `GET /client_devices/:id` - Get device
- `POST /client_devices` - Create device
- `PUT /client_devices/:id` - Update device
- `DELETE /client_devices/:id` - Delete device

### 🚨 Alerts & Monitoring
- `GET /alerts` - List alerts
- `GET /alerts/:id` - Get alert
- `POST /alerts` - Create alert
- `PATCH /alerts/:id/mark-read` - Mark as read

### 🗺️ Geofences
- `GET /geofences` - List geofences
- `GET /geofences/:id` - Get geofence
- `POST /geofences` - Create geofence
- `PUT /geofences/:id` - Update geofence
- `DELETE /geofences/:id` - Delete geofence

### 🚗 Driver Management
- `GET /drivers` - List drivers
- `GET /drivers/:id` - Get driver
- `POST /drivers` - Create driver
- `PUT /drivers/:id` - Update driver
- `DELETE /drivers/:id` - Delete driver

### 📍 GPS Data
- `GET /gps_data/search` - Search GPS data
- `GET /gps_data/last_location` - Get last location
- `POST /gps_data` - Create GPS data

### 💰 Financial
- `GET /invoices` - List invoices
- `GET /payments` - List payments
- `GET /payment_types` - List payment types

### 🎫 Support Tickets
- `GET /support_tickets` - List tickets
- `GET /support_tickets/:id` - Get ticket
- `POST /support_tickets` - Create ticket
- `PUT /support_tickets/:id` - Update ticket
- `POST /support_tickets/:id/replies` - Add reply

### 📊 Reports
- `GET /reports` - List reports
- `POST /reports/:id/generate` - Generate report
- `GET /reports/scheduled` - List scheduled reports

### 📈 Dashboard
- `GET /dashboard/dashboard_stats` - Get dashboard stats
- `GET /dashboard/server_stats` - Get server stats

## Response Format
```json
{
  "success": true,
  "message": "Operation completed",
  "data": { /* response data */ }
}
```

## Common Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `404` - Not Found
- `500` - Server Error

## Search & Filtering
Most list endpoints support:
- `?page=1&per_page=10` - Pagination
- `?search=term` - Search
- `?start_date=2024-01-01&end_date=2024-12-31` - Date range
- `?status=active` - Status filter

## WebSocket
```javascript
const ws = new WebSocket('ws://localhost:8080/api/v1/backend/ws/alerts');
```

## Development Tips
1. Use underscores in route names (`/support_tickets`, not `/support-tickets`)
2. Always include Authorization header
3. Handle pagination for large datasets
4. Check response status before processing data
5. Use WebSocket for real-time updates

---
*Quick Reference v1.0 - December 2024*
