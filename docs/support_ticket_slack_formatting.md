# Support Ticket Slack Formatting Improvements

## Overview

The support ticket Slack notifications have been completely redesigned to match the professional formatting used for device alerts. The new format provides rich, structured information with clear visual hierarchy and interactive elements.

## Before vs After

### Before (Plain Text)
```
*Support Ticket Reply Added*
• Ticket ID: #3
• Subject: A ford
• Department: technical
• Priority: low
• Status: open
• Client: Tererai Mugova
• Created By: 
```

### After (Rich Blocks)
```
📝 New Support Ticket Created

Ticket ID: #3          Department: technical
Priority: high         Status: open
Client: Tererai Mugova Created By: Admin User

Subject:
Test Ticket Subject

Description:
This is a test ticket description for testing Slack formatting...

⏰ 2025-08-31 08:14:39 MST

[View Ticket] (button)
```

## New Features

### 1. Rich Block Structure
- **Header**: Emoji + title for immediate visual recognition
- **Fields**: Organized information in columns
- **Sections**: Dedicated areas for subject and description
- **Timestamp**: Clear time indication
- **Action Button**: Direct link to view the ticket

### 2. Contextual Emojis
- 📝 **New Ticket**: When a ticket is created
- 💬 **Staff Reply**: When staff responds
- 👤 **Client Reply**: When client responds
- 🔄 **Status Change**: When status is updated
- 👤 **Assignment**: When ticket is assigned

### 3. Smart Reply Detection
The system now distinguishes between different types of replies:

#### Staff Reply Format
```
💬 Staff Reply Added

Ticket ID: #3          Department: technical
Priority: high         Status: open
Client: Tererai Mugova Replied By: Admin User

Subject:
Test Ticket Subject

Reply:
This is a test reply from staff

⏰ 2025-08-31 08:14:39 MST

[View Ticket] (button)
```

#### Client Reply Format
```
👤 Client Reply Added

Ticket ID: #3          Department: technical
Priority: high         Status: open
Client: Tererai Mugova Replied By: Test User

Subject:
Test Ticket Subject

Reply:
This is a test reply from client

⏰ 2025-08-31 08:14:39 MST

[View Ticket] (button)
```

#### Internal Note Format
```
💬 Staff Reply Added

Ticket ID: #3          Department: technical
Priority: high         Status: open
Client: Tererai Mugova Replied By: Admin User

Subject:
Test Ticket Subject

Reply:
This is an internal staff note

🔒 Internal Note (not visible to client)

⏰ 2025-08-31 08:14:39 MST

[View Ticket] (button)
```

## Technical Implementation

### New Functions

1. **`sendSlackTicketNotification`** - Enhanced for general ticket events
2. **`sendSlackReplyNotification`** - Specialized for reply notifications

### Key Improvements

#### 1. Structured Data Display
```go
blocks := []SlackBlock{
    {
        Type: "header",
        Text: &SlackText{
            Type: "plain_text",
            Text: fmt.Sprintf("%s %s", emoji, title),
        },
    },
    {
        Type: "section",
        Fields: []SlackText{
            {
                Type: "mrkdwn",
                Text: fmt.Sprintf("*Ticket ID:*\n#%d", ticket.Id),
            },
            {
                Type: "mrkdwn",
                Text: fmt.Sprintf("*Department:*\n%s", ticket.Department),
            },
        },
    },
    // ... more structured blocks
}
```

#### 2. Smart Emoji Selection
```go
var emoji string
switch {
case strings.Contains(strings.ToLower(title), "created"):
    emoji = "📝" // New ticket
case strings.Contains(strings.ToLower(title), "reply"):
    emoji = "💬" // Reply
case strings.Contains(strings.ToLower(title), "status"):
    emoji = "🔄" // Status change
case strings.Contains(strings.ToLower(title), "assigned"):
    emoji = "👤" // Assignment
default:
    emoji = "🎫" // Default ticket
}
```

#### 3. Reply Type Detection
```go
// Determine if reply is from staff or client
isStaffReply := reply.CreatedBy.ClientId == nil
var emoji, title string

if isStaffReply {
    emoji = "💬"
    title = "Staff Reply Added"
} else {
    emoji = "👤"
    title = "Client Reply Added"
}
```

#### 4. Internal Note Indicator
```go
// Add internal note indicator if applicable
if reply.IsInternal {
    blocks = append(blocks, SlackBlock{
        Type: "section",
        Text: &SlackText{
            Type: "mrkdwn",
            Text: "🔒 *Internal Note* (not visible to client)",
        },
    })
}
```

## Benefits

### 1. Better Visual Hierarchy
- Clear separation of information
- Consistent formatting across all notifications
- Easy to scan and understand

### 2. Improved User Experience
- One-click access to tickets via action buttons
- Clear indication of who replied
- Visual distinction between staff and client replies

### 3. Professional Appearance
- Matches the quality of device alert notifications
- Consistent branding and styling
- Modern Slack block format

### 4. Enhanced Information
- More detailed ticket information
- Better organized data presentation
- Clear timestamps and context

## Configuration

The Slack formatting uses the same configuration as device alerts:

- **Channel**: `#yotracker-support` (configurable)
- **App URL**: From `APP_URL` environment variable
- **Fallback URL**: `https://yourdomain.com` if not configured

## Future Enhancements

1. **Priority Color Coding**: Different colors for urgent/high priority tickets
2. **Status Indicators**: Visual status badges
3. **Rich Text**: Support for markdown in descriptions
4. **Quick Actions**: Reply directly from Slack
5. **Mention Integration**: @mention relevant staff members

## Migration Notes

- **Backward Compatible**: Old notifications still work
- **No Configuration Changes**: Uses existing Slack settings
- **Automatic Upgrade**: All new notifications use the new format
- **Testing**: Comprehensive testing ensures reliability
