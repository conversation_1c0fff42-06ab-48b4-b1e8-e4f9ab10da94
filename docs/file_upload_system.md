# File Upload System Documentation

## Overview

The File Upload System provides a secure and scalable solution for handling file uploads in YoTracker. It supports various file types, automatic file type detection, and role-based access control for both admin and client users.

## Features

- **Multi-File Type Support**: Images, Documents, Videos, Audio, and other file types
- **Automatic File Type Detection**: Based on MIME type
- **Unique File Naming**: Prevents filename conflicts with timestamp and random hash
- **File Validation**: Size limits and allowed file types
- **Role-Based Access**: Different permissions for admin and client users
- **Client Isolation**: Clients can only access their own files
- **File Serving**: Direct access to uploaded files via URL
- **Comprehensive CRUD Operations**: Upload, list, retrieve, and delete files

## Models

### FileUpload

```go
type FileUpload struct {
    Id          uint      `json:"id" gorm:"primaryKey"`
    ClientId    *uint     `json:"client_id" gorm:"index"` // Optional, for client-specific files
    CreatedById uint      `json:"created_by_id" gorm:"index"`
    CreatedBy   User      `json:"created_by"`
    FileName    string    `json:"file_name" gorm:"type:varchar(255);not null"`
    OriginalName string   `json:"original_name" gorm:"type:varchar(255);not null"`
    FileSize    int64     `json:"file_size" gorm:"not null"`
    MimeType    string    `json:"mime_type" gorm:"type:varchar(100);not null"`
    FileUrl     string    `json:"file_url" gorm:"type:varchar(500);not null"`
    FilePath    string    `json:"file_path" gorm:"type:varchar(500);not null"`
    FileType    string    `json:"file_type" gorm:"type:enum('image','document','video','audio','other');default:'other'"`
    Description *string   `json:"description" gorm:"type:text"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`
}
```

### FileUploadResponse

```go
type FileUploadResponse struct {
    Id          uint   `json:"id"`
    FileName    string `json:"file_name"`
    OriginalName string `json:"original_name"`
    FileSize    int64  `json:"file_size"`
    MimeType    string `json:"mime_type"`
    FileUrl     string `json:"file_url"`
    FileType    string `json:"file_type"`
    Description *string `json:"description"`
}
```

## API Endpoints

### Backend API (Admin/Staff Access)

#### Upload File
```
POST /api/v1/backend/files/upload
```

**Form Data:**
- `file` (required): The file to upload
- `client_id` (optional): Client ID to associate the file with
- `description` (optional): File description

**Response:**
```json
{
  "message": "File uploaded successfully",
  "data": {
    "id": 1,
    "file_name": "20250101_123456_abc123.txt",
    "original_name": "test.txt",
    "file_size": 1024,
    "mime_type": "text/plain",
    "file_url": "/media/20250101_123456_abc123.txt",
    "file_type": "document",
    "description": "Test file description",
    "client_id": 1
  }
}
```

#### Get All Files
```
GET /api/v1/backend/files
```

**Query Parameters:**
- `client_id` (uint): Filter by client ID
- `file_type` (string): Filter by file type (image, document, video, audio, other)
- `created_by_id` (uint): Filter by uploader
- `page` (int): Page number (default: 1)
- `per_page` (int): Items per page (default: 20)

#### Get File by ID
```
GET /api/v1/backend/files/:id
```

#### Delete File
```
DELETE /api/v1/backend/files/:id
```

### Frontend API (Client Access)

#### Upload File
```
POST /api/v1/frontend/files/upload
```

**Form Data:**
- `file` (required): The file to upload
- `description` (optional): File description

**Note**: Client ID is automatically extracted from authentication context.

#### Get Client Files
```
GET /api/v1/frontend/files
```

**Query Parameters:**
- `file_type` (string): Filter by file type
- `page` (int): Page number (default: 1)
- `per_page` (int): Items per page (default: 20)

**Note**: Only returns files belonging to the authenticated client.

#### Get File by ID
```
GET /api/v1/frontend/files/:id
```

**Note**: Only returns files belonging to the authenticated client.

#### Delete File
```
DELETE /api/v1/frontend/files/:id
```

**Note**: Only allows deletion of files belonging to the authenticated client.

### File Serving

#### Access Uploaded Files
```
GET /api/v1/backend/media/:filename
```

**Note**: This endpoint serves the actual file content and is publicly accessible.

## File Validation

### Supported File Types

**Images:**
- JPEG, PNG, GIF, WebP

**Documents:**
- PDF, Word documents, Excel files, Text files, CSV

**Videos:**
- MP4, AVI, MOV

**Audio:**
- MP3, WAV, MPEG

### File Size Limits

- **Maximum file size**: 50MB
- **Form data limit**: 50MB

## File Storage

### Directory Structure

```
/var/www/yotrackermedia/
├── 20250101_123456_abc123.txt
├── 20250101_123457_def456.jpg
└── 20250101_123458_ghi789.pdf
```

### File Naming Convention

Files are automatically renamed using the following format:
```
{timestamp}_{random_hex}.{extension}
```

Example: `20250101_123456_a1b2c3d4e5f6.txt`

## Security Features

### File Access Control

- **Admin users**: Can access all files and upload files for any client
- **Client users**: Can only access files belonging to their client
- **File serving**: Includes path traversal protection

### File Validation

- **MIME type validation**: Only allowed file types are accepted
- **File size limits**: Prevents large file uploads
- **Filename sanitization**: Prevents directory traversal attacks

## Usage Examples

### Upload a File (Backend)

```bash
curl -X POST http://localhost:8080/api/v1/backend/files/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@/path/to/file.txt" \
  -F "client_id=1" \
  -F "description=Important document"
```

### Upload a File (Frontend)

```bash
curl -X POST http://localhost:8080/api/v1/frontend/files/upload \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@/path/to/image.jpg" \
  -F "description=Company logo"
```

### Get Files with Filtering

```bash
curl -X GET "http://localhost:8080/api/v1/backend/files?client_id=1&file_type=image&page=1&per_page=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Access Uploaded File

```bash
curl -X GET http://localhost:8080/api/v1/backend/media/20250101_123456_abc123.txt
```

## Error Handling

### Common Error Responses

**File too large:**
```json
{
  "error": "file size exceeds maximum limit of 50MB"
}
```

**Invalid file type:**
```json
{
  "error": "file type not allowed"
}
```

**File not found:**
```json
{
  "error": "File not found"
}
```

**Unauthorized access:**
```json
{
  "error": "User not found in context"
}
```

**Client access required:**
```json
{
  "error": "Client access required"
}
```

## Database Schema

```sql
CREATE TABLE `file_uploads` (
    `id` bigint unsigned AUTO_INCREMENT PRIMARY KEY,
    `client_id` bigint unsigned NULL,
    `created_by_id` bigint unsigned NOT NULL,
    `file_name` varchar(255) NOT NULL,
    `original_name` varchar(255) NOT NULL,
    `file_size` bigint NOT NULL,
    `mime_type` varchar(100) NOT NULL,
    `file_url` varchar(500) NOT NULL,
    `file_path` varchar(500) NOT NULL,
    `file_type` enum('image','document','video','audio','other') DEFAULT 'other',
    `description` text NULL,
    `created_at` datetime(3) NULL,
    `updated_at` datetime(3) NULL,
    INDEX `idx_file_uploads_client_id` (`client_id`),
    INDEX `idx_file_uploads_created_by_id` (`created_by_id`),
    INDEX `idx_file_uploads_file_type` (`file_type`),
    CONSTRAINT `fk_file_uploads_client` FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_file_uploads_created_by` FOREIGN KEY (`created_by_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
);
```

## Testing

### Running Tests

```bash
# Test file upload service
go test ./internal/services -run TestFileUploadService -v

# Test backend controllers
go test ./cmd/web/backend/controllers -run TestBackendFileUploadControllers -v

# Test frontend controllers
go test ./cmd/web/frontend/controllers -run TestFrontendFileUploadControllers -v
```

### Test Coverage

The file upload system includes comprehensive tests covering:

- File upload functionality
- File validation (size, type)
- File retrieval and filtering
- File deletion (database and physical)
- Access control and permissions
- Error handling
- File serving

## Configuration

### Environment Variables

No additional environment variables are required for the file upload system. It uses the existing database configuration.

### Directory Permissions

Ensure the `/var/www/yotrackermedia` directory has proper write permissions:

```bash
sudo mkdir -p /var/www/yotrackermedia
sudo chown www-data:www-data /var/www/yotrackermedia
sudo chmod 755 /var/www/yotrackermedia
```

## Best Practices

1. **File Organization**: Files are automatically organized with unique names
2. **Access Control**: Always verify user permissions before file operations
3. **File Cleanup**: Deleted files are automatically removed from both database and disk
4. **Error Handling**: Implement proper error handling for file operations
5. **Monitoring**: Monitor disk space usage in the media directory
6. **Backup**: Include the media directory in your backup strategy

## Troubleshooting

### Common Issues

1. **Permission Denied**: Check directory permissions for `/var/www/yotrackermedia`
2. **File Not Found**: Verify the file exists and the URL is correct
3. **Upload Fails**: Check file size and type restrictions
4. **Database Errors**: Ensure the `file_uploads` table exists and is properly migrated

### Debugging

Enable debug logging to troubleshoot file upload issues:

```go
// Add logging to file upload service
log.Printf("Uploading file: %s, size: %d", file.Filename, file.Size)
```


