# Support Ticket Status Change API

## Overview

Clients can now change the status of their support tickets using a dedicated endpoint instead of the general update method. This provides better control and security for status changes.

## Endpoints

### Frontend (Client) API
- **URL**: `PATCH /api/v1/support_tickets/:id/status`
- **Authentication**: Required (Client user)
- **Permissions**: Client can only change status of their own tickets

### Backend (Admin) API
- **URL**: `PATCH /api/v1/support_tickets/:id/status`
- **Authentication**: Required (Admin user)
- **Permissions**: Admin can change status of any ticket

## Request Format

```json
{
  "status": "waiting_for_customer"
}
```

## Allowed Status Values

### For Clients
Clients can only change status to:
- `waiting_for_customer` - When waiting for staff response
- `resolved` - When the issue is resolved
- `closed` - When the ticket is closed

### For Admins
Admins can change status to any valid status:
- `open` - Initial status
- `assigned` - Assigned to staff member
- `in_progress` - Staff is working on it
- `waiting_for_customer` - Waiting for customer response
- `resolved` - Issue resolved
- `closed` - Ticket closed

## Response Format

### Success Response (200 OK)
```json
{
  "message": "Support ticket status changed successfully",
  "data": {
    "id": 1,
    "client_id": 123,
    "subject": "Test Ticket",
    "description": "Test Description",
    "status": "resolved",
    "priority": "medium",
    "department": "general",
    "created_at": "2025-08-31T07:31:48Z",
    "updated_at": "2025-08-31T07:31:48Z",
    "resolved_at": "2025-08-31T07:31:48Z"
  }
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "error": "Invalid ticket ID"
}
```

#### 401 Unauthorized
```json
{
  "error": "User not found in context"
}
```

#### 403 Forbidden
```json
{
  "error": "Clients can only change status to: waiting_for_customer, resolved, or closed"
}
```

#### 404 Not Found
```json
{
  "error": "Ticket not found"
}
```

## Usage Examples

### Client Changing Status to Resolved
```bash
curl -X PATCH \
  http://localhost:8000/api/v1/support_tickets/123/status \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "resolved"
  }'
```

### Admin Changing Status to In Progress
```bash
curl -X PATCH \
  http://localhost:9000/api/v1/support_tickets/123/status \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "in_progress"
  }'
```

## Features

### Automatic Timestamps
- When status is changed to `resolved`, `resolved_at` timestamp is automatically set
- When status is changed to `closed`, `closed_at` timestamp is automatically set

### Notifications
- Status change notifications are sent to relevant parties
- Slack notifications for staff
- Email notifications for clients

### Security
- Clients can only modify their own tickets
- Status validation prevents invalid status changes
- Proper authentication and authorization checks

## Migration from Update Method

Previously, clients had to use the general `PUT /support_tickets/:id` endpoint to change status. The new dedicated endpoint provides:

1. **Better Security**: Only status changes are allowed, no other fields
2. **Clearer Intent**: Dedicated endpoint makes the purpose obvious
3. **Validation**: Specific validation for status changes
4. **Documentation**: Clear API documentation for status changes

## Testing

Comprehensive tests are included to verify:
- Valid status changes for clients
- Invalid status changes are rejected
- Proper authentication and authorization
- Error handling for invalid requests
- Database state changes
- Timestamp updates
