# Firebase Push Notifications API

This document describes the Firebase push notification system implemented in YoTracker.

## Overview

The system allows sending push notifications to mobile devices using Firebase Cloud Messaging (FCM). It includes:

- Single device notifications
- Bulk notifications to multiple devices
- Notification logging and tracking
- Support for various notification types

## API Endpoints

### 1. Send Single Push Notification

**Endpoint:** `POST /api/v1/backend/notifications/push`

**Headers:**
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "to": "fcm_token_here",
  "title": "Engine ON",
  "body": "Vehicle ABC123 just turned on.",
  "type": "ignition_alert",
  "client_device_id": 1234,
  "timestamp": "2025-09-04T22:11:00Z",
  "data": {
    "additional_field": "value"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Notification sent successfully",
  "data": {
    "success": 1,
    "failure": 0,
    "results": [
      {
        "message_id": "firebase_message_id_here"
      }
    ]
  }
}
```

### 2. Send Bulk Push Notifications

**Endpoint:** `POST /api/v1/backend/notifications/push/bulk`

**Headers:**
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "tokens": [
    "fcm_token_1",
    "fcm_token_2",
    "fcm_token_3"
  ],
  "title": "System Maintenance",
  "body": "Scheduled maintenance will begin in 30 minutes.",
  "type": "system_notice",
  "data": {
    "maintenance_id": "12345"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Bulk notifications sent successfully",
  "data": {
    "success": 3,
    "failure": 0,
    "results": [
      {
        "message_id": "firebase_message_id_1"
      },
      {
        "message_id": "firebase_message_id_2"
      },
      {
        "message_id": "firebase_message_id_3"
      }
    ]
  }
}
```

### 3. Get Notification Logs

**Endpoint:** `GET /api/v1/backend/notifications/logs`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 10, max: 100)
- `type` (optional): Filter by notification type
- `status` (optional): Filter by status (sent, failed, pending)

**Response:**
```json
{
  "data": [
    {
      "id": 1,
      "client_id": 1,
      "user_id": 1,
      "client_device_id": 1234,
      "firebase_token": "fcm_token_here",
      "title": "Engine ON",
      "body": "Vehicle ABC123 just turned on.",
      "type": "ignition_alert",
      "data": "{\"additional_field\":\"value\"}",
      "status": "sent",
      "firebase_message_id": "firebase_message_id_here",
      "error_message": null,
      "created_at": "2025-09-04T22:11:00Z",
      "updated_at": "2025-09-04T22:11:00Z",
      "user": {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      "client_device": {
        "id": 1234,
        "name": "Vehicle ABC123",
        "device_id": "ABC123",
        "phone_number": "+1234567890",
        "status": "active"
      }
    }
  ],
  "total": 1,
  "current_page": 1,
  "per_page": 10,
  "total_pages": 1
}
```

### 4. Get Notification Types

**Endpoint:** `GET /api/v1/backend/notifications/types`

**Response:**
```json
{
  "data": [
    {
      "value": "ignition_alert",
      "label": "Ignition Alert",
      "description": "Vehicle ignition status change"
    },
    {
      "value": "admin_message",
      "label": "Admin Message",
      "description": "Administrative message"
    },
    {
      "value": "geofence_alert",
      "label": "Geofence Alert",
      "description": "Geofence entry/exit alert"
    }
  ]
}
```

## Supported Notification Types

| Type | Description |
|------|-------------|
| `ignition_alert` | Vehicle ignition status change |
| `admin_message` | Administrative message |
| `geofence_alert` | Geofence entry/exit alert |
| `trip_summary` | Trip summary report |
| `system_notice` | System notification |
| `new_chat_message` | New chat message received |
| `maintenance_alert` | Vehicle maintenance reminder |
| `speed_alert` | Speed limit violation |
| `fuel_alert` | Fuel level alert |
| `battery_alert` | Battery level alert |
| `panic_alert` | Panic button activated |
| `driver_alert` | Driver-related alert |
| `device_offline` | Device went offline |
| `device_online` | Device came online |

## Environment Variables

Set the following environment variable for Firebase configuration:

```bash
FIREBASE_SERVER_KEY=your_firebase_server_key_here
```

## Frontend Integration

### Update Firebase Token

**Endpoint:** `PUT /api/v1/frontend/users/firebase_token`

**Headers:**
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "firebase_push_token": "fcm_token_here"
}
```

**Response:**
```json
{
  "message": "Firebase push token updated successfully"
}
```

## Example Usage

### JavaScript/Vue.js Example

```javascript
// Update Firebase token when user logs in
async function updateFirebaseToken(token) {
  try {
    const response = await fetch('/api/v1/frontend/users/firebase_token', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${userToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        firebase_push_token: token
      })
    });
    
    const result = await response.json();
    console.log('Token updated:', result.message);
  } catch (error) {
    console.error('Error updating token:', error);
  }
}

// Send push notification from admin panel
async function sendPushNotification(deviceToken, title, body, type, deviceId) {
  try {
    const response = await fetch('/api/v1/backend/notifications/push', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        to: deviceToken,
        title: title,
        body: body,
        type: type,
        client_device_id: deviceId,
        data: {
          timestamp: new Date().toISOString()
        }
      })
    });
    
    const result = await response.json();
    console.log('Notification sent:', result);
  } catch (error) {
    console.error('Error sending notification:', error);
  }
}
```

## Error Handling

The API returns appropriate HTTP status codes:

- `200 OK`: Notification sent successfully
- `400 Bad Request`: Invalid request data or notification type
- `401 Unauthorized`: Missing or invalid authentication token
- `500 Internal Server Error`: Firebase service error or database error

Error responses include a `message` field with details about the error.
