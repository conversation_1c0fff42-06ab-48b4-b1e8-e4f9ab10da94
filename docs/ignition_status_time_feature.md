# Ignition Status Time Feature

## 📌 Overview

The Ignition Status Time feature adds comprehensive tracking of ignition status changes, allowing you to monitor how long a vehicle's ignition has been in its current state (ON or OFF). This is essential for trip detection, idle time calculations, and vehicle monitoring.

## 🎯 Key Features

- **Real-time ignition status tracking** with precise timestamps
- **Duration calculations** in seconds, minutes, and hours
- **Device filtering** by ignition status and duration
- **API endpoints** for programmatic access
- **Integration** with GPS data processing and trip detection
- **Utility functions** for duration calculations

## 🏗️ Implementation Details

### Database Schema

Added `ignition_status_time` field to `client_devices` table:

```sql
ALTER TABLE client_devices 
ADD COLUMN ignition_status_time TIMESTAMP NULL 
COMMENT 'Timestamp when ignition status last changed';
```

### Model Updates

Updated `ClientDevice` model to include:

```go
type ClientDevice struct {
    // ... existing fields ...
    IgnitionStatus     *bool      `json:"ignition_status"`
    IgnitionStatusTime *time.Time `json:"ignition_status_time"`
    // ... other fields ...
}
```

### Service Integration

#### Location Data Service
- **Automatic tracking**: Updates `ignition_status_time` when ignition status changes
- **Real-time updates**: Integrated with GPS data processing pipeline

#### Real-Time Trip Detection Service
- **Enhanced trip detection**: Uses ignition status duration for better trip logic
- **Key event detection**: Tracks ignition status changes with timestamps

## 🔧 API Endpoints

### Get Ignition Status for Device
```http
GET /api/v1/backend/ignition_status/device/{deviceId}
```

**Response:**
```json
{
  "success": true,
  "message": "Ignition status retrieved successfully",
  "data": {
    "device_id": 1,
    "ignition_status": true,
    "status_time": "2025-01-15T10:00:00Z",
    "last_update": "2025-01-15T10:05:00Z",
    "duration_minutes": 5.0,
    "duration_hours": 0.083,
    "duration_formatted": "5m 0s",
    "status_description": "Ignition ON"
  }
}
```

### Get Devices by Ignition Status
```http
GET /api/v1/backend/ignition_status/devices?status=on
GET /api/v1/backend/ignition_status/devices?status=off
```

### Get Devices with Ignition ON for Duration
```http
GET /api/v1/backend/ignition_status/devices/on_for_duration?duration=2h
GET /api/v1/backend/ignition_status/devices/on_for_duration?duration=30m
GET /api/v1/backend/ignition_status/devices/on_for_duration?duration=1800s
```

### Get Devices with Ignition OFF for Duration
```http
GET /api/v1/backend/ignition_status/devices/off_for_duration?duration=1h
```

### Update Ignition Status
```http
PUT /api/v1/backend/ignition_status/device/{deviceId}
Content-Type: application/json

{
  "ignition_status": true,
  "timestamp": "2025-01-15T10:00:00Z"
}
```

## 🛠️ Utility Functions

### Duration Calculations
```go
// Get duration since last ignition status change
duration, err := utils.GetIgnitionStatusDuration(device)

// Get duration in minutes
minutes, err := utils.GetIgnitionStatusDurationMinutes(device)

// Get duration in hours
hours, err := utils.GetIgnitionStatusDurationHours(device)

// Get formatted duration string
formatted, err := utils.FormatIgnitionDuration(device)
```

### Status Checks
```go
// Check if ignition has been ON for specific duration
hasBeenOn, err := utils.IsIgnitionOnForDuration(device, 2*time.Hour)

// Check if ignition has been OFF for specific duration
hasBeenOff, err := utils.IsIgnitionOffForDuration(device, 30*time.Minute)
```

## 📊 Use Cases

### 1. Trip Detection Enhancement
```go
// Use ignition duration for better trip end detection
if ignitionOff && ignitionOffDuration > 5*time.Minute {
    // End trip after 5 minutes of ignition OFF
    endTrip()
}
```

### 2. Idle Time Monitoring
```go
// Monitor vehicles with ignition ON but no movement
devices, err := ignitionService.GetDevicesWithIgnitionOnForDuration(30*time.Minute)
for _, device := range devices {
    // Check if device has been stationary
    if isStationary(device) {
        sendIdleAlert(device)
    }
}
```

### 3. Fleet Management
```go
// Find vehicles that have been running for too long
longRunning, err := ignitionService.GetDevicesWithIgnitionOnForDuration(8*time.Hour)
for _, device := range longRunning {
    sendLongRunningAlert(device)
}
```

### 4. Maintenance Scheduling
```go
// Find vehicles that haven't been used recently
unused, err := ignitionService.GetDevicesWithIgnitionOffForDuration(7*24*time.Hour)
for _, device := range unused {
    scheduleMaintenanceCheck(device)
}
```

## 🧪 Testing

### Manual Testing
```bash
# Run ignition status test
cd cmd/test-ignition-status
go run main.go
```

### API Testing
```bash
# Test ignition status API
curl -X GET "http://localhost:5022/api/v1/backend/ignition_status/device/1" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Test devices with ignition ON for 1 hour
curl -X GET "http://localhost:5022/api/v1/backend/ignition_status/devices/on_for_duration?duration=1h" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📈 Performance Considerations

### Database Indexing
- **Index on `ignition_status_time`**: Efficient queries for duration-based filtering
- **Composite indexes**: For complex queries combining status and time

### Caching Strategy
- **In-memory caching**: For frequently accessed ignition status data
- **Redis integration**: For distributed caching across multiple instances

### Query Optimization
- **Batch updates**: Update multiple devices in single transaction
- **Async processing**: Non-blocking ignition status updates

## 🔍 Monitoring and Alerts

### Key Metrics
- **Ignition status change frequency**: Monitor for unusual patterns
- **Average ignition duration**: Track vehicle usage patterns
- **Long-running vehicles**: Identify potential issues

### Alert Scenarios
- **Long idle time**: Vehicle with ignition ON but no movement
- **Extended operation**: Vehicle running for unusually long periods
- **Unused vehicles**: Vehicles not started for extended periods

## 🚀 Future Enhancements

### Planned Features
- **Historical tracking**: Store ignition status change history
- **Predictive analytics**: Predict maintenance needs based on usage patterns
- **Integration with fuel monitoring**: Correlate ignition status with fuel consumption
- **Mobile app integration**: Real-time ignition status in mobile dashboard

### Advanced Analytics
- **Usage patterns**: Analyze vehicle usage by time of day, day of week
- **Driver behavior**: Track ignition patterns by driver
- **Fleet optimization**: Optimize vehicle allocation based on usage data

## 📚 Related Documentation

- [Real-Time Trip Detection](./hybrid_realtime_trip_detection.md)
- [API Documentation](./api_routes.md)
- [Testing Guide](./realtime_trip_detection_testing.md)

## 🎯 Benefits

### For Fleet Managers
- **Real-time visibility**: Know which vehicles are running and for how long
- **Efficient monitoring**: Quickly identify vehicles that need attention
- **Usage analytics**: Understand vehicle utilization patterns

### For Drivers
- **Trip accuracy**: More precise trip detection and timing
- **Idle monitoring**: Awareness of idle time and fuel consumption
- **Maintenance alerts**: Proactive maintenance scheduling

### For System Performance
- **Enhanced trip detection**: Better accuracy in trip start/end detection
- **Reduced false positives**: Fewer incorrect trip detections
- **Improved analytics**: More detailed vehicle usage data
