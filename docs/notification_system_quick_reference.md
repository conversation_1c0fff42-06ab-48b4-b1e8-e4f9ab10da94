# Notification System Quick Reference

## 🚀 Quick Start

### 1. Create Alert Preferences

```go
// Client-level preference
pref := &models.AlertPreference{
    ClientId:  clientId,
    AlertType: models.AlertTypeSpeed,
    Enabled:   true,
    Channels:  []byte(`["email", "whatsapp"]`),
    Priority:  "high",
}
config.DB.Create(pref)
```

### 2. Send Notifications

```go
service, _ := NewFlexibleNotificationService()
req := &NotificationRequest{
    Alert:        alert,
    Client:       client,
    ClientDevice: device,
    Fleet:        fleet,
    AlertType:    models.AlertTypeSpeed,
}
service.SendNotification(req)
```

## 📋 Alert Types

| Type | Constant | Description |
|------|----------|-------------|
| Speed Alert | `AlertTypeSpeed` | Vehicle exceeds speed limit |
| Shutdown Time | `AlertTypeShutdown` | Vehicle active during shutdown hours |
| Towing Event | `AlertTypeTowing` | Vehicle being towed |
| Impact Detection | `AlertTypeImpact` | Sudden deceleration/impact |
| Geofence Event | `AlertTypeGeofence` | Enter/exit geofence |
| Maintenance | `AlertTypeMaintenance` | Maintenance due |
| Battery Alert | `AlertTypeBattery` | Low battery |
| SOS Alert | `AlertTypeSOS` | Emergency signal |

## 📱 Notification Channels

| Channel | Constant | Service |
|---------|----------|---------|
| Email | `NotificationChannelEmail` | Email Alert Service |
| SMS | `NotificationChannelSMS` | Twilio SMS Service |
| WhatsApp | `NotificationChannelWhatsApp` | WhatsApp Service |
| Slack | `NotificationChannelSlack` | Slack Service |

## 🏗️ Hierarchy Priority

```
Device Level (Highest) → Fleet Level → Client Level → Default (Lowest)
```

## 🔧 Common Patterns

### Check if Alert Type is Enabled

```go
func isAlertEnabled(clientId, fleetId, deviceId uint, alertType models.AlertType) bool {
    // Check device level first
    var devicePref models.DeviceAlertPreference
    if err := config.DB.Where("client_device_id = ? AND alert_type = ? AND enabled = ?", 
        deviceId, alertType, true).First(&devicePref).Error; err == nil {
        return true
    }
    
    // Check fleet level
    var fleetPref models.FleetAlertPreference
    if err := config.DB.Where("fleet_id = ? AND alert_type = ? AND enabled = ?", 
        fleetId, alertType, true).First(&fleetPref).Error; err == nil {
        return true
    }
    
    // Check client level
    var clientPref models.AlertPreference
    if err := config.DB.Where("client_id = ? AND alert_type = ? AND enabled = ?", 
        clientId, alertType, true).First(&clientPref).Error; err == nil {
        return true
    }
    
    return false
}
```

### Get Notification Channels

```go
func getNotificationChannels(clientId, fleetId, deviceId uint, alertType models.AlertType) []models.NotificationChannel {
    // Device level
    var devicePref models.DeviceAlertPreference
    if err := config.DB.Where("client_device_id = ? AND alert_type = ? AND enabled = ?", 
        deviceId, alertType, true).First(&devicePref).Error; err == nil {
        return devicePref.GetChannelsAsSlice()
    }
    
    // Fleet level
    var fleetPref models.FleetAlertPreference
    if err := config.DB.Where("fleet_id = ? AND alert_type = ? AND enabled = ?", 
        fleetId, alertType, true).First(&fleetPref).Error; err == nil {
        return fleetPref.GetChannelsAsSlice()
    }
    
    // Client level
    var clientPref models.AlertPreference
    if err := config.DB.Where("client_id = ? AND alert_type = ? AND enabled = ?", 
        clientId, alertType, true).First(&clientPref).Error; err == nil {
        return clientPref.GetChannelsAsSlice()
    }
    
    return []models.NotificationChannel{}
}
```

### Create JSON Channel Array

```go
channels := []models.NotificationChannel{
    models.NotificationChannelEmail,
    models.NotificationChannelWhatsApp,
}
channelsJSON, _ := json.Marshal(channels)
```

## 🧪 Testing

### Run Tests

```bash
# Run integration tests
docker exec yotracker-web-backend-dev-1 go test ./internal/services/flexible_notification_service_integration_test.go -v

# Run all service tests
docker exec yotracker-web-backend-dev-1 go test ./internal/services/ -v
```

### Test Data Setup

```go
// Create test preference
pref := suite.createClientAlertPreference(
    clientId,
    models.AlertTypeSpeed,
    true,
    []models.NotificationChannel{models.NotificationChannelEmail},
)

// Verify preference
suite.NotZero(pref.Id)
channels := pref.GetChannelsAsSlice()
suite.Contains(channels, models.NotificationChannelEmail)
```

## 🐛 Debugging

### Check Database Tables

```bash
# List preference tables
docker exec yotracker-web-backend-dev-1 mysql -u admin -ppassword testing -e "SHOW TABLES LIKE '%alert_preference%';"

# Check preference data
docker exec yotracker-web-backend-dev-1 mysql -u admin -ppassword testing -e "SELECT * FROM alert_preferences WHERE client_id = 1;"
```

### Common Issues

1. **Tables don't exist**: Run `migrations.Migrate()`
2. **JSON parsing errors**: Use `json.Marshal()` for channels
3. **Preferences not found**: Check hierarchy order
4. **Notifications not sent**: Verify channel configuration

## 📊 Database Queries

### Find All Preferences for Client

```sql
SELECT * FROM alert_preferences 
WHERE client_id = ? AND enabled = true;
```

### Find Device Preferences

```sql
SELECT * FROM device_alert_preferences 
WHERE client_device_id = ? AND alert_type = ? AND enabled = true;
```

### Count Preferences by Type

```sql
SELECT alert_type, COUNT(*) as count 
FROM alert_preferences 
WHERE enabled = true 
GROUP BY alert_type;
```

## 🔄 Integration Points

### GPS Data Processing

```go
// In location_data_service.go
go func() {
    alertService := NewAlertDetectionService()
    if err := alertService.ProcessGPSData(gps); err != nil {
        log.Printf("Alert detection failed: %v", err)
    }
}()
```

### Alert Creation

```go
// In alert_detection_service.go
func (s *AlertDetectionService) createAlert(alert *models.Alert) error {
    if err := config.DB.Create(alert).Error; err != nil {
        return err
    }
    
    // Send notifications
    return s.sendNotifications(alert)
}
```

## 📝 Best Practices

1. **Always check hierarchy**: Device → Fleet → Client → Default
2. **Use constants**: `models.AlertTypeSpeed` not `"speed_alert"`
3. **Handle errors gracefully**: Don't fail alert creation if notifications fail
4. **Validate JSON**: Ensure channel arrays are properly formatted
5. **Test thoroughly**: Use integration tests for preference logic
6. **Log failures**: Always log notification failures for debugging

## 🚨 Error Handling

```go
// Graceful notification failure
if err := notificationService.SendNotification(req); err != nil {
    log.Printf("Notification failed for alert %d: %v", alert.Id, err)
    // Continue with alert creation
}

// Partial failure handling
var errors []string
for _, channel := range channels {
    if err := sendToChannel(channel, req); err != nil {
        errors = append(errors, fmt.Sprintf("%s: %v", channel, err))
    }
}
if len(errors) > 0 {
    log.Printf("Partial notification failures: %s", strings.Join(errors, "; "))
}
```

## 📚 Related Files

- `internal/models/alert_preferences.go` - Alert preference models
- `internal/services/flexible_notification_service.go` - Main service
- `internal/services/alert_detection_service.go` - Alert detection
- `internal/services/flexible_notification_service_integration_test.go` - Tests
- `migrations/migrate.go` - Database migrations
