# Real-Time Trip Detection Testing Guide

## 🧪 Testing Overview

This guide provides comprehensive testing approaches for the new hybrid real-time trip detection system, including sample GT06 GPS points with ignition data.

## 🚀 Quick Start Testing

### 1. Automated Test Suite
```bash
# Run the comprehensive test suite
./scripts/test-realtime-trip-detection.sh
```

### 2. Manual Interactive Testing
```bash
# Build and run manual test
cd cmd/manual-test-realtime
go run main.go
```

### 3. GT06 Protocol Testing
```bash
# Test with actual GT06 protocol simulation
cd cmd/test-gt06-realtime
go run main.go
```

## 📊 Test Scenarios

### 🔑 Key Events Testing
**Purpose**: Test ignition status change detection

**Sample GT06 Data**:
```
Device: GT06_DEVICE_1
Time: 2025-01-15 10:00:00
Location: -17.8252, 31.0335 (Harare, Zimbabwe)
Speed: 0 km/h
ACC Bit: 1 (Ignition ON)
```

**Expected Results**:
- ✅ Key On alert created
- ✅ Real-time processing within 100ms
- ✅ Alert stored in database

### 🚗 Trip Detection Testing
**Purpose**: Test trip start/end detection

**Sample Journey**:
```
1. Key ON + Stationary (Trip Start)
2. Movement (25 km/h) + Ignition ON
3. Continue driving (30 km/h)
4. Stop + Key OFF (Trip End)
```

**Expected Results**:
- ✅ Trip created with start/end times
- ✅ Distance calculated
- ✅ Speed metrics recorded
- ✅ Trip validation passed

### ⚡ Speed Alert Testing
**Purpose**: Test speed limit violation detection

**Sample Data**:
```
Device: GT06_DEVICE_3
Speed Limit: 60 km/h (device setting)
GPS Speed: 85 km/h
ACC Bit: 1 (Ignition ON)
```

**Expected Results**:
- ✅ Speed alert created immediately
- ✅ Alert level: "warning"
- ✅ Speed details included

### 🚛 Towing Event Testing
**Purpose**: Test towing detection

**Sample Scenario**:
```
1. Vehicle stationary, ignition OFF
2. Significant movement (>100m) without ignition
3. Continued movement pattern
```

**Expected Results**:
- ✅ Towing alert created
- ✅ Alert level: "critical"
- ✅ Movement pattern analyzed

## 🔧 GT06 Protocol Details

### Ignition Status Detection
The GT06 protocol uses the **ACC bit** in the course status byte to determine ignition status:

```go
// GT06 Course Status Byte (bit 7 = ACC)
var courseStatus byte = 0x00
if ignitionOn {
    courseStatus |= 0x80 // Set ACC bit (bit 7)
}
```

### Sample GT06 Packet Structure
```
[Start][Length][Protocol][Date][Satellites][Lat][Lon][Speed][Course][MCC][MNC][LAC][CellID][ACC][...]
 0x78   0x78    0x22     6b    1b         4b   4b   1b    2b      2b   1b   2b   3b      1b
```

### Key Fields for Testing
- **ACC Bit (bit 7)**: Ignition status (1=ON, 0=OFF)
- **GPS Fix (bit 4)**: GPS positioning status
- **Latitude North (bit 2)**: Latitude direction
- **Longitude East (bit 3)**: Longitude direction

## 📱 Sample GT06 GPS Points

### Key On Event
```json
{
  "device_id": "GT06_DEVICE_1",
  "timestamp": "2025-01-15T10:00:00Z",
  "latitude": -17.8252,
  "longitude": 31.0335,
  "speed": 0.0,
  "ignition_status": true,
  "acc_bit": 1,
  "course_status": "0x90"
}
```

### Key Off Event
```json
{
  "device_id": "GT06_DEVICE_1", 
  "timestamp": "2025-01-15T10:00:30Z",
  "latitude": -17.8252,
  "longitude": 31.0335,
  "speed": 0.0,
  "ignition_status": false,
  "acc_bit": 0,
  "course_status": "0x10"
}
```

### Trip Start (Movement + Ignition)
```json
{
  "device_id": "GT06_DEVICE_2",
  "timestamp": "2025-01-15T10:01:00Z",
  "latitude": -17.8253,
  "longitude": 31.0336,
  "speed": 25.0,
  "ignition_status": true,
  "acc_bit": 1,
  "course_status": "0x90"
}
```

### Speed Violation
```json
{
  "device_id": "GT06_DEVICE_3",
  "timestamp": "2025-01-15T10:02:00Z",
  "latitude": -17.8254,
  "longitude": 31.0337,
  "speed": 85.0,
  "ignition_status": true,
  "acc_bit": 1,
  "course_status": "0x90"
}
```

### Towing Event (Movement without Ignition)
```json
{
  "device_id": "GT06_DEVICE_4",
  "timestamp": "2025-01-15T10:03:00Z",
  "latitude": -17.8258,
  "longitude": 31.0345,
  "speed": 0.0,
  "ignition_status": false,
  "acc_bit": 0,
  "course_status": "0x10"
}
```

## 🎯 Testing Checklist

### ✅ Real-Time Processing
- [ ] GPS data processed within 100ms
- [ ] No blocking on database operations
- [ ] Worker pool handling concurrent devices
- [ ] Queue not overflowing under load

### ✅ Event Detection
- [ ] Key On events detected immediately
- [ ] Key Off events detected immediately
- [ ] Trip start/end boundaries accurate
- [ ] Speed alerts triggered at threshold
- [ ] Towing events detected with movement

### ✅ Data Integrity
- [ ] All events stored in database
- [ ] GPS data properly linked to trips
- [ ] Alert details complete and accurate
- [ ] No duplicate events created

### ✅ Performance
- [ ] System handles 1000+ concurrent devices
- [ ] Memory usage stable over time
- [ ] Database load within acceptable limits
- [ ] Real-time latency < 1 second

## 🔍 Verification Queries

### Check Recent Trips
```sql
SELECT 
    id, client_device_id, start_time, end_time, 
    distance, max_speed, avg_speed
FROM trips 
WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY start_time DESC 
LIMIT 10;
```

### Check Recent Alerts
```sql
SELECT 
    id, client_device_id, alert_type, alert_name, 
    alert_level, message, alert_timestamp
FROM alerts 
WHERE alert_timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY alert_timestamp DESC 
LIMIT 10;
```

### Check Key Events
```sql
SELECT 
    alert_type, COUNT(*) as event_count,
    MIN(alert_timestamp) as first_event,
    MAX(alert_timestamp) as last_event
FROM alerts 
WHERE alert_type IN ('key_on', 'key_off')
AND alert_timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR)
GROUP BY alert_type;
```

### Check Speed Alerts
```sql
SELECT 
    client_device_id, alert_timestamp, message
FROM alerts 
WHERE alert_type = 'speed_alert'
AND alert_timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY alert_timestamp DESC;
```

### Check Towing Events
```sql
SELECT 
    client_device_id, alert_timestamp, message
FROM alerts 
WHERE alert_type = 'towing_event'
AND alert_timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY alert_timestamp DESC;
```

## 🚨 Troubleshooting

### Common Issues

**1. No Events Detected**
- Check if real-time service is initialized
- Verify device exists in database
- Check GPS data is being saved

**2. Duplicate Events**
- Check device state management
- Verify debouncing logic
- Review ignition status changes

**3. Performance Issues**
- Monitor queue length
- Check worker pool utilization
- Review database connection pool

**4. Missing Trips**
- Verify trip validation criteria
- Check minimum distance/duration settings
- Review ignition vs speed-based detection

### Debug Commands
```bash
# Check real-time service status
docker-compose logs server | grep -i "realtime"

# Monitor database connections
docker-compose exec mysql mysql -u root -ppassword -e "SHOW PROCESSLIST;"

# Check queue metrics
docker-compose exec mysql mysql -u root -ppassword yotracker -e "SELECT * FROM settings WHERE setting_key LIKE 'realtime%';"
```

## 📈 Performance Benchmarks

### Expected Performance
- **Event Detection Latency**: < 100ms
- **Database Write Latency**: < 50ms
- **Concurrent Devices**: 1000+
- **GPS Points/Second**: 100+
- **Memory Usage**: < 100MB per 100 devices

### Load Testing
```bash
# Simulate high load
for i in {1..100}; do
    go run cmd/manual-test-realtime/main.go &
done
wait
```

## 🎉 Success Criteria

The real-time trip detection system is working correctly when:

1. ✅ **All event types detected** within 100ms
2. ✅ **No duplicate events** created
3. ✅ **Database integrity** maintained
4. ✅ **Performance targets** met
5. ✅ **GT06 protocol** properly handled
6. ✅ **Real-time processing** confirmed

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Review system logs
3. Verify database state
4. Test with manual scenarios
5. Contact development team
