stages:
  - test
  - build
  - deploy

variables:
  SSH_PRIVATE_KEY: $SSH_PRIVATE_KEY
  VPS_USER: admin
  VPS_HOST: *************
  VPS_PATH: /opt/yotracker

test:
  stage: test
  image: ubuntu:22.04
  services:
    - name: mysql:8.0
      command: ["--default-authentication-plugin=mysql_native_password", "--bind-address=0.0.0.0"]
  variables:
    MYSQL_DATABASE: yotracker
    MYSQL_USER: admin
    MYSQL_PASSWORD: password
    MYSQL_ROOT_PASSWORD: password
    MYSQL_ROOT_HOST: "%"
    MYSQL_ALLOW_EMPTY_PASSWORD: "yes"
    MYSQL_MAX_CONNECTIONS: "50"
    DB_HOST: mysql
    DB_PORT: "3306"
    DB_USERNAME: admin
    DB_PASSWORD: password
    DB_NAME: yotracker
    TESTING_DB_NAME: testing
  before_script:
    - apt-get update && apt-get install -y wget git tar
    - wget https://go.dev/dl/go1.24rc1.linux-amd64.tar.gz
    - tar -C /usr/local -xzf go1.24rc1.linux-amd64.tar.gz
    - export PATH="/usr/local/go/bin:$PATH"
    - go version
    - apt-get install -y -qq git default-mysql-client
    # Wait for MySQL to be ready
    - |
      for i in {30..0}; do
        if mysql --host=mysql --user=root --password=password -e "SELECT 1" &> /dev/null; then
          echo "MySQL is ready!"
          break
        fi
        echo "Waiting for MySQL... ($i seconds remaining)"
        sleep 1
      done
      if [ "$i" = 0 ]; then
        echo "MySQL failed to start"
        exit 1
      fi
    - mysql --host=mysql --user=root --password=password -e "CREATE DATABASE IF NOT EXISTS testing;"
    - mysql --host=mysql --user=root --password=password -e "GRANT ALL PRIVILEGES ON testing.* TO 'admin'@'%';"
    - mysql --host=mysql --user=root --password=password -e "FLUSH PRIVILEGES;"
    - export DB_HOST=mysql
    - export DB_PORT=3306
    - export DB_USERNAME=admin
    - export DB_PASSWORD=password
    - export DB_NAME=yotracker
    - export TESTING_DB_NAME=testing
    - export GT06_PORT=5022
    - export H02_PORT=5010
    - export BACKEND_HOST=0.0.0.0
    - export BACKEND_PORT=9000
    - export FRONTEND_PORT=9001
    - export APP_KEY=secret
    # Wait a bit more for database to be fully ready
    - sleep 5
  script:
    - echo "Running tests..."
    - echo "Database connection info:"
    - echo "DB_HOST=$DB_HOST"
    - echo "DB_PORT=$DB_PORT"
    - echo "DB_USERNAME=$DB_USERNAME"
    - echo "TESTING_DB_NAME=$TESTING_DB_NAME"
    - echo "Testing database connection..."
    - mysql --host=$DB_HOST --user=$DB_USERNAME --password=$DB_PASSWORD -e "SELECT 1;" || echo "Database connection failed"
    - echo "Initializing test database..."
    - go run scripts/init_test_db.go
    - echo "Running tests..."
    - go test ./cmd/web/backend/controllers -timeout=2m -v
    - go test ./cmd/web/frontend/controllers -timeout=2m -v
    - go test ./internal/services -timeout=2m -v
    - go test ./config -timeout=1m -v
    - go test ./internal/models -timeout=1m -v
    - go test ./internal/utils -timeout=1m -v
    - go test ./internal/mail -timeout=1m -v
    - go test ./internal/templates -timeout=1m -v

build:

  stage: build
  image: golang:1.24
  script:
    - echo "Building Go applications..."
    - cd cmd/server && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o tcp_server && cd ../..
    - cd cmd/web/backend &&CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o backend && cd ../../..
    - cd cmd/web/frontend &&CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o frontend && cd ../../..
    - cd cmd/cron &&CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o cron && cd ../../..
  artifacts:
    paths:
      - cmd/server/tcp_server
      - cmd/web/backend/backend
      - cmd/web/frontend/frontend
      - cmd/cron/cron

deploy:
  stage: deploy
  image: ubuntu:22.04
  before_script:
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add - > /dev/null # Adding private key for SSH access
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
  script:
    - echo "Deploying application..."
    # Copy built binaries to the VPS
    - echo "Copying binaries to server..."
    - ssh -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST 'sudo supervisorctl stop yotracker-tcp_server && sudo supervisorctl stop yotracker-backend && sudo supervisorctl stop yotracker-frontend && sudo supervisorctl stop yotracker-cron'
    - scp -o StrictHostKeyChecking=no cmd/server/tcp_server $VPS_USER@$VPS_HOST:$VPS_PATH/tcp_server
    - scp -o StrictHostKeyChecking=no cmd/web/backend/backend $VPS_USER@$VPS_HOST:$VPS_PATH/backend
    - scp -o StrictHostKeyChecking=no cmd/web/frontend/frontend $VPS_USER@$VPS_HOST:$VPS_PATH/frontend
    - scp -o StrictHostKeyChecking=no cmd/cron/cron $VPS_USER@$VPS_HOST:$VPS_PATH/cron
    # Make the binaries executable
    - echo "Making binaries executable..."
    - ssh -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST "chmod +x $VPS_PATH/tcp_server $VPS_PATH/backend $VPS_PATH/frontend $VPS_PATH/cron"
    # Restart the applications using supervisor or systemd
    - echo "Restarting the applications..."
    #- ssh -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST 'sudo systemctl restart supervisor'
    - ssh -o StrictHostKeyChecking=no $VPS_USER@$VPS_HOST 'sudo supervisorctl start yotracker-tcp_server && sudo supervisorctl start yotracker-backend && sudo supervisorctl start yotracker-frontend && sudo supervisorctl start yotracker-cron'

    - echo "Deployment completed successfully!"
  only:
    - main # Run this job only on the main branch