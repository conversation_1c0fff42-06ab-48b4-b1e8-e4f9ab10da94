#!/bin/bash

echo "🧪 Testing the new test setup..."

# Test the services package first
echo "Testing services package..."
cd internal/services
go test -v -timeout=5m ./...

if [ $? -eq 0 ]; then
    echo "✅ Services package tests passed!"
else
    echo "❌ Services package tests failed!"
    exit 1
fi

# Test the frontend controllers package
echo "Testing frontend controllers package..."
cd ../../cmd/web/frontend/controllers
go test -v -timeout=5m ./...

if [ $? -eq 0 ]; then
    echo "✅ Frontend controllers package tests passed!"
else
    echo "❌ Frontend controllers package tests failed!"
    exit 1
fi

# Test the backend controllers package
echo "Testing backend controllers package..."
cd ../../backend/controllers
go test -v -timeout=5m ./...

if [ $? -eq 0 ]; then
    echo "✅ Backend controllers package tests passed!"
else
    echo "❌ Backend controllers package tests failed!"
    exit 1
fi

echo "🎉 All test packages are working with the new setup!"
