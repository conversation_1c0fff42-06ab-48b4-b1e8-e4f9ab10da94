package main

import (
	"log"
	"sync"
	"yotracker/config"
	"yotracker/internal/services"

	"github.com/robfig/cron/v3"
)

func main() {
	config.InitDB()
	c := cron.New()
	var err error
	// Process recurring invoices daily at 9:00 AM
	_, err = c.AddFunc("0 9 * * *", processRecurringInvoices)
	if err != nil {
		log.Printf("Failed to add recurring invoice job: %v\n", err)
	}
	// Process subscription billing daily at 9:15 AM
	_, err = c.AddFunc("15 9 * * *", processSubscriptionBilling)
	if err != nil {
		log.Printf("Failed to add subscription billing job: %v\n", err)
	}

	// First overdue reminders: 9:30 AM
	_, err = c.AddFunc("30 9 * * *", processFirstOverdueReminders)
	if err != nil {
		log.Printf("Failed to add first overdue reminder job: %v\n", err)

	}
	// Second overdue reminders: 9:35 AM
	_, err = c.AddFunc("35 9 * * *", processSecondOverdueReminders)
	if err != nil {
		log.Printf("Failed to add second overdue reminder job: %v\n", err)
	}
	// Third overdue reminders: 9:40 AM
	_, err = c.AddFunc("40 9 * * *", processThirdOverdueReminders)
	if err != nil {
		log.Printf("Failed to add third overdue reminder job: %v\n", err)
	}

	// Process client suspensions daily at 10:00 AM
	_, err = c.AddFunc("0 10 * * *", processClientSuspensions)
	if err != nil {
		log.Printf("Failed to add client suspension job: %v\n", err)
	}

	// Update GPS location names every minute
	_, err = c.AddFunc("* * * * *", processGPSLocationNames)
	if err != nil {
		log.Printf("Failed to add GPS location names job: %v\n", err)
	}

	// Process trip detection every 5 minutes
	//_, err = c.AddFunc("*/5 * * * *", processTripDetection)
	//_, err = c.AddFunc("* * * * *", processTripDetection)
	//if err != nil {
	//	log.Printf("Failed to add trip detection job: %v\n", err)
	//}

	// Start the cron scheduler
	c.Start()

	log.Println("Cron scheduler started:")
	log.Println("- Recurring invoices: 9:00 AM")
	log.Println("- Subscription billing: 9:15 AM")
	log.Println("- First overdue reminders: 9:30 AM")
	log.Println("- Second overdue reminders: 9:35 AM")
	log.Println("- Third overdue reminders: 9:40 AM")
	log.Println("- Client suspensions: 10:00 AM")
	log.Println("- GPS location names: Every minute")
	log.Println("- Trip detection: Every 5 minutes")

	// Keep the program running
	select {}
}

func processRecurringInvoices() {
	log.Println("Starting recurring invoice processing...")

	recurringService := services.NewRecurringInvoiceService()
	err := recurringService.ProcessDailyRecurringInvoices()
	if err != nil {
		log.Printf("Error processing recurring invoices: %v", err)
	} else {
		log.Println("Recurring invoice processing completed successfully")
	}
}

func processSubscriptionBilling() {
	log.Println("Starting subscription billing processing...")

	subscriptionService := services.NewSubscriptionBillingService()
	err := subscriptionService.ProcessSubscriptionBilling()
	if err != nil {
		log.Printf("Error processing subscription billing: %v", err)
	} else {
		log.Println("Subscription billing processing completed successfully")
	}
}

func processFirstOverdueReminders() {
	log.Println("Starting first overdue reminder processing...")

	reminderService := services.NewInvoiceReminderService()
	err := reminderService.ProcessFirstOverdueReminders()
	if err != nil {
		log.Printf("Error processing first overdue reminders: %v", err)
	} else {
		log.Println("First overdue reminder processing completed successfully")
	}
}

func processSecondOverdueReminders() {
	log.Println("Starting second overdue reminder processing...")

	reminderService := services.NewInvoiceReminderService()
	err := reminderService.ProcessSecondOverdueReminders()
	if err != nil {
		log.Printf("Error processing second overdue reminders: %v", err)
	} else {
		log.Println("Second overdue reminder processing completed successfully")
	}
}

func processThirdOverdueReminders() {
	log.Println("Starting third overdue reminder processing...")

	reminderService := services.NewInvoiceReminderService()
	err := reminderService.ProcessThirdOverdueReminders()
	if err != nil {
		log.Printf("Error processing third overdue reminders: %v", err)
	} else {
		log.Println("Third overdue reminder processing completed successfully")
	}
}

func processClientSuspensions() {
	log.Println("Starting client suspension processing...")

	suspensionService := services.NewClientSuspensionService()
	err := suspensionService.ProcessOverdueSubscriptions()
	if err != nil {
		log.Printf("Error processing client suspensions: %v", err)
	} else {
		log.Println("Client suspension processing completed successfully")
	}
}

var gpsProcessingMutex sync.Mutex
var isGPSProcessing bool

var tripDetectionMutex sync.Mutex
var isTripDetectionProcessing bool

func processGPSLocationNames() {
	// Prevent overlapping runs
	gpsProcessingMutex.Lock()
	if isGPSProcessing {
		log.Println("GPS location names processing already running, skipping...")
		gpsProcessingMutex.Unlock()
		return
	}
	isGPSProcessing = true
	gpsProcessingMutex.Unlock()

	defer func() {
		gpsProcessingMutex.Lock()
		isGPSProcessing = false
		gpsProcessingMutex.Unlock()
	}()

	log.Println("Starting GPS location names processing...")

	geocodingService := services.NewReverseGeocodingService()
	err := geocodingService.UpdateGPSDataLocationNames()
	if err != nil {
		log.Printf("Error processing GPS location names: %v", err)
	} else {
		log.Println("GPS location names processing completed successfully")
	}
}

func processTripDetection() {
	// Prevent overlapping runs
	tripDetectionMutex.Lock()
	if isTripDetectionProcessing {
		log.Println("Trip detection processing already running, skipping...")
		tripDetectionMutex.Unlock()
		return
	}
	isTripDetectionProcessing = true
	tripDetectionMutex.Unlock()

	defer func() {
		tripDetectionMutex.Lock()
		isTripDetectionProcessing = false
		tripDetectionMutex.Unlock()
	}()

	log.Println("Starting trip detection processing...")

	tripDetectionService := services.NewTripDetectionService()
	err := tripDetectionService.ProcessUnassignedGPSData()
	if err != nil {
		log.Printf("Error processing trip detection: %v", err)
	} else {
		log.Println("Trip detection processing completed successfully")
	}
}
