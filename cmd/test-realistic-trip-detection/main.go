package main

import (
	"fmt"
	"log"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
)

func main() {
	// Initialize database
	config.InitDB()

	// Initialize real-time trip detection service
	manager := services.GetRealtimeServiceManager()
	if err := manager.StartService(); err != nil {
		log.Fatalf("Failed to start real-time trip detection service: %v", err)
	}
	realtimeService := services.GetRealtimeTripDetectionService()

	if realtimeService == nil {
		log.Fatal("Failed to initialize real-time trip detection service")
	}

	fmt.Println("🚀 Starting Realistic Trip Detection Test")
	fmt.Println("==========================================")

	// Test with a real device from the database
	testWithRealDevice(realtimeService)

	fmt.Println("\n✅ Test completed!")
	fmt.Println("Check the database for created trips and GPS data with trip_id.")
}

// testWithRealDevice tests trip detection with a real device
func testWithRealDevice(realtimeService *services.RealtimeTripDetectionService) {
	// Find a device that supports ignition status
	var device models.ClientDevice
	err := config.DB.Preload("DeviceType").Where("device_type_id IN (SELECT id FROM device_types WHERE ignition_status = true)").
		First(&device).Error
	if err != nil {
		log.Printf("No device with ignition status found, using first available device: %v", err)
		err = config.DB.Preload("DeviceType").First(&device).Error
		if err != nil {
			log.Fatalf("No devices found in database: %v", err)
		}
	}

	fmt.Printf("📱 Testing with device: %s (ID: %d, IMEI: %s)\n",
		*device.Name, device.Id, device.DeviceId)

	// Create realistic GPS data points for a trip
	now := time.Now()
	baseLat := -17.8252 // Harare, Zimbabwe
	baseLon := 31.0335

	// Trip scenario: Key on -> Drive -> Stop -> Key off
	tripPoints := []struct {
		lat         float64
		lon         float64
		speed       float64
		ignitionOn  bool
		description string
		timeOffset  time.Duration
	}{
		{baseLat, baseLon, 0, false, "At home - ignition off", 0},
		{baseLat, baseLon, 0, true, "Key on - starting engine", 10 * time.Second},
		{baseLat + 0.001, baseLon + 0.001, 15, true, "Moving slowly", 20 * time.Second},
		{baseLat + 0.005, baseLon + 0.005, 45, true, "Normal driving", 30 * time.Second},
		{baseLat + 0.010, baseLon + 0.010, 50, true, "Continuing drive", 45 * time.Second},
		{baseLat + 0.015, baseLon + 0.015, 0, true, "Stopped at destination", 60 * time.Second},
		{baseLat + 0.015, baseLon + 0.015, 0, false, "Key off - trip ended", 70 * time.Second},
	}

	fmt.Println("\n📍 Simulating realistic trip points:")
	for i, point := range tripPoints {
		timestamp := now.Add(point.timeOffset)

		// Create GPS data
		gpsData := &models.GPSData{
			ClientDeviceId: &device.Id,
			GPSTimestamp:   &timestamp,
			Latitude:       point.lat,
			Longitude:      point.lon,
			Speed:          &point.speed,
			IgnitionStatus: &point.ignitionOn,
			DeviceId:       device.DeviceId,
		}

		// Save GPS data to database first
		if err := config.DB.Create(gpsData).Error; err != nil {
			log.Printf("Failed to save GPS data: %v", err)
			continue
		}

		// Process with real-time trip detection
		if err := realtimeService.ProcessGPSData(gpsData); err != nil {
			log.Printf("Failed to process GPS data: %v", err)
		}

		fmt.Printf("  %d. %s - Lat: %.6f, Lon: %.6f, Speed: %.1f, Ignition: %v\n",
			i+1, point.description, point.lat, point.lon, point.speed, point.ignitionOn)

		// Small delay to simulate real-time processing
		time.Sleep(100 * time.Millisecond)
	}

	// Wait for processing to complete
	fmt.Println("\n⏳ Waiting for trip detection to complete...")
	time.Sleep(5 * time.Second)

	// Check results
	checkTripResults(device.Id)
}

// checkTripResults checks if trips were created and GPS data was updated
func checkTripResults(deviceId uint) {
	fmt.Println("\n🔍 Checking trip detection results:")

	// Check for trips
	var trips []models.Trip
	err := config.DB.Where("client_device_id = ?", deviceId).
		Order("created_at DESC").Find(&trips).Error
	if err != nil {
		log.Printf("Error querying trips: %v", err)
		return
	}

	fmt.Printf("📊 Found %d trips for device %d\n", len(trips), deviceId)
	for i, trip := range trips {
		fmt.Printf("  Trip %d: ID=%d, Start=%v, End=%v, Distance=%.2fkm, Status=%s\n",
			i+1, trip.Id, trip.StartTime, trip.EndTime, trip.Distance, trip.Status)
	}

	// Check GPS data with trip_id
	var gpsWithTrip []models.GPSData
	err = config.DB.Where("client_device_id = ? AND trip_id IS NOT NULL", deviceId).
		Order("gps_timestamp ASC").Find(&gpsWithTrip).Error
	if err != nil {
		log.Printf("Error querying GPS data with trip_id: %v", err)
		return
	}

	fmt.Printf("📍 Found %d GPS points with trip_id\n", len(gpsWithTrip))
	for i, gps := range gpsWithTrip {
		if i < 5 || i >= len(gpsWithTrip)-5 { // Show first 5 and last 5
			fmt.Printf("  GPS %d: ID=%d, TripID=%d, Time=%v, Lat=%.6f, Lon=%.6f, Speed=%.1f, Ignition=%v\n",
				i+1, gps.Id, *gps.TripId, gps.GPSTimestamp, gps.Latitude, gps.Longitude,
				*gps.Speed, *gps.IgnitionStatus)
		} else if i == 5 {
			fmt.Printf("  ... (showing first 5 and last 5 GPS points)\n")
		}
	}

	// Check GPS data without trip_id (should be minimal for recent data)
	var gpsWithoutTrip []models.GPSData
	err = config.DB.Where("client_device_id = ? AND trip_id IS NULL AND gps_timestamp > ?",
		deviceId, time.Now().Add(-1*time.Hour)).Find(&gpsWithoutTrip).Error
	if err != nil {
		log.Printf("Error querying GPS data without trip_id: %v", err)
		return
	}

	fmt.Printf("⚠️  Found %d recent GPS points WITHOUT trip_id (this might indicate an issue)\n", len(gpsWithoutTrip))
	if len(gpsWithoutTrip) > 0 {
		fmt.Println("  Recent GPS points without trip_id:")
		for i, gps := range gpsWithoutTrip {
			if i < 3 { // Show first 3
				fmt.Printf("    GPS ID=%d, Time=%v, Lat=%.6f, Lon=%.6f, Speed=%.1f, Ignition=%v\n",
					gps.Id, gps.GPSTimestamp, gps.Latitude, gps.Longitude,
					*gps.Speed, *gps.IgnitionStatus)
			}
		}
	}

	// Check alerts created
	var alerts []models.Alert
	err = config.DB.Where("client_device_id = ? AND created_at > ?",
		deviceId, time.Now().Add(-1*time.Hour)).Find(&alerts).Error
	if err != nil {
		log.Printf("Error querying alerts: %v", err)
		return
	}

	fmt.Printf("🚨 Found %d recent alerts for device %d\n", len(alerts), deviceId)
	for i, alert := range alerts {
		fmt.Printf("  Alert %d: Type=%s, Level=%s, Time=%v, Message=%s\n",
			i+1, alert.AlertType, *alert.AlertLevel, alert.AlertTimestamp, *alert.Message)
	}
}
