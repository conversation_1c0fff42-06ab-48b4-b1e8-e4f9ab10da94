package main

import (
	"encoding/binary"
	"fmt"
	"log"
	"math"
	"net"
	"time"
	"yotracker/internal/protocol/gt06"
)

// RealisticTripSimulator simulates a realistic vehicle trip with proper ignition status changes
type RealisticTripSimulator struct {
	conn       net.Conn
	imei       string
	deviceId   string
	currentLat float64
	currentLon float64
	ignitionOn bool
	tripActive bool
	speed      float64
	direction  float64
	lastUpdate time.Time
}

// TripPoint represents a point in a realistic trip
type TripPoint struct {
	Latitude    float64
	Longitude   float64
	Speed       float64
	IgnitionOn  bool
	Duration    time.Duration
	Description string
}

// NewRealisticTripSimulator creates a new realistic trip simulator
func NewRealisticTripSimulator(imei, deviceId string) *RealisticTripSimulator {
	return &RealisticTripSimulator{
		imei:       imei,
		deviceId:   deviceId,
		currentLat: -17.8252, // Harare, Zimbabwe coordinates
		currentLon: 31.0335,
		ignitionOn: false,
		tripActive: false,
		speed:      0.0,
		direction:  0.0,
		lastUpdate: time.Now(),
	}
}

// Connect establishes connection to the server
func (s *RealisticTripSimulator) Connect(serverAddr string) error {
	conn, err := net.Dial("tcp", serverAddr)
	if err != nil {
		return fmt.Errorf("failed to connect: %v", err)
	}
	s.conn = conn
	log.Printf("Connected to server %s", serverAddr)
	return nil
}

// Login sends login packet to the server
func (s *RealisticTripSimulator) Login() error {
	// Convert IMEI to BCD format
	imeiBCD := s.imeiToBCD(s.imei)

	loginPacket := []byte{
		0x78, 0x78, 0x0D, 0x01, 0x03, 0x52, 0x67, 0x21, 0x08, 0x14, 0x55, 0x74, 0x04, 0xE4, 0xDC, 0x07, 0x0D, 0x0A,
	}

	// Replace IMEI in packet (bytes 4-11)
	copy(loginPacket[4:12], imeiBCD)

	// Recalculate CRC
	crc := gt06.CRC16_ITU(loginPacket[2 : len(loginPacket)-4])
	loginPacket[len(loginPacket)-4] = byte(crc >> 8)
	loginPacket[len(loginPacket)-3] = byte(crc & 0xFF)

	log.Printf("Sending login packet for IMEI: %s", s.imei)
	_, err := s.conn.Write(loginPacket)
	return err
}

// SimulateRealisticTrip simulates a complete realistic trip
func (s *RealisticTripSimulator) SimulateRealisticTrip() error {
	log.Println("🚗 Starting realistic trip simulation...")

	// Define realistic trip points
	tripPoints := []TripPoint{
		// Start at home - ignition off
		{Latitude: -17.8252, Longitude: 31.0335, Speed: 0, IgnitionOn: false, Duration: 30 * time.Second, Description: "At home - ignition off"},

		// Key on - start trip
		{Latitude: -17.8252, Longitude: 31.0335, Speed: 0, IgnitionOn: true, Duration: 10 * time.Second, Description: "Key on - starting engine"},

		// Start moving slowly
		{Latitude: -17.8250, Longitude: 31.0337, Speed: 15, IgnitionOn: true, Duration: 20 * time.Second, Description: "Moving slowly from home"},

		// Accelerate to normal speed
		{Latitude: -17.8245, Longitude: 31.0340, Speed: 45, IgnitionOn: true, Duration: 30 * time.Second, Description: "Normal driving speed"},

		// Continue driving
		{Latitude: -17.8235, Longitude: 31.0345, Speed: 50, IgnitionOn: true, Duration: 45 * time.Second, Description: "Continuing on route"},

		// Stop at traffic light
		{Latitude: -17.8225, Longitude: 31.0350, Speed: 0, IgnitionOn: true, Duration: 20 * time.Second, Description: "Stopped at traffic light"},

		// Resume driving
		{Latitude: -17.8215, Longitude: 31.0355, Speed: 40, IgnitionOn: true, Duration: 35 * time.Second, Description: "Resumed driving"},

		// Speed up on highway
		{Latitude: -17.8200, Longitude: 31.0365, Speed: 80, IgnitionOn: true, Duration: 60 * time.Second, Description: "Highway driving"},

		// Slow down for exit
		{Latitude: -17.8185, Longitude: 31.0375, Speed: 25, IgnitionOn: true, Duration: 30 * time.Second, Description: "Slowing for exit"},

		// Arrive at destination
		{Latitude: -17.8170, Longitude: 31.0385, Speed: 0, IgnitionOn: true, Duration: 15 * time.Second, Description: "Arrived at destination"},

		// Key off - end trip
		{Latitude: -17.8170, Longitude: 31.0385, Speed: 0, IgnitionOn: false, Duration: 30 * time.Second, Description: "Key off - trip ended"},
	}

	// Execute trip points
	for i, point := range tripPoints {
		log.Printf("📍 Point %d/%d: %s", i+1, len(tripPoints), point.Description)
		log.Printf("   Lat: %.6f, Lon: %.6f, Speed: %.1f km/h, Ignition: %v",
			point.Latitude, point.Longitude, point.Speed, point.IgnitionOn)

		// Update simulator state
		s.currentLat = point.Latitude
		s.currentLon = point.Longitude
		s.speed = point.Speed
		s.ignitionOn = point.IgnitionOn

		// Send GPS data
		if err := s.sendGPSData(); err != nil {
			log.Printf("Error sending GPS data: %v", err)
		}

		// Wait for the specified duration
		time.Sleep(point.Duration)
	}

	log.Println("✅ Trip simulation completed!")
	return nil
}

// sendGPSData sends GPS data packet to the server
func (s *RealisticTripSimulator) sendGPSData() error {
	now := time.Now()

	// Convert coordinates to GT06 format
	lat := s.currentLat
	lon := s.currentLon

	// Convert to GT06 format (multiply by 30000 * 60)
	latGT06 := uint32(math.Abs(lat) * 30000 * 60)
	lonGT06 := uint32(math.Abs(lon) * 30000 * 60)

	// Determine direction bits
	var directionByte byte
	if lat >= 0 {
		directionByte |= 0x04 // North
	}
	if lon >= 0 {
		directionByte |= 0x08 // East
	}

	// Create GPS data packet
	packet := make([]byte, 39)

	// Header
	packet[0] = 0x78
	packet[1] = 0x78
	packet[2] = 0x22 // Packet length
	packet[3] = 0x22 // Protocol number (positioning data)

	// Date and time (6 bytes)
	packet[4] = byte(now.Year() - 2000)
	packet[5] = byte(now.Month())
	packet[6] = byte(now.Day())
	packet[7] = byte(now.Hour())
	packet[8] = byte(now.Minute())
	packet[9] = byte(now.Second())

	// Satellites
	packet[10] = 8 // Good satellite count

	// Latitude (4 bytes)
	binary.BigEndian.PutUint32(packet[11:15], latGT06)

	// Longitude (4 bytes)
	binary.BigEndian.PutUint32(packet[15:19], lonGT06)

	// Speed (1 byte)
	packet[19] = byte(s.speed)

	// Course and status (2 bytes)
	courseByte := byte(int(s.direction) & 0xFF)
	statusByte := directionByte
	if s.ignitionOn {
		statusByte |= 0x80 // ACC bit (ignition on)
	}
	packet[20] = statusByte
	packet[21] = courseByte

	// Cell tower info (8 bytes) - dummy values
	binary.BigEndian.PutUint16(packet[22:24], 716)    // MCC
	packet[24] = 1                                    // MNC
	binary.BigEndian.PutUint16(packet[25:27], 1234)   // LAC
	binary.BigEndian.PutUint32(packet[27:30], 567890) // Cell ID (3 bytes)

	// Additional data
	packet[30] = 0x00 // ACC
	packet[31] = 0x00 // Data reporting mode
	packet[32] = 0x00 // GPS retransmission

	// Serial number
	packet[33] = 0x00
	packet[34] = 0x01

	// Calculate CRC
	crc := gt06.CRC16_ITU(packet[2:35])
	packet[35] = byte(crc >> 8)
	packet[36] = byte(crc & 0xFF)

	// End marker
	packet[37] = 0x0D
	packet[38] = 0x0A

	// Send packet
	_, err := s.conn.Write(packet)
	if err != nil {
		return err
	}

	log.Printf("📡 Sent GPS: Lat=%.6f, Lon=%.6f, Speed=%.1f, Ignition=%v",
		lat, lon, s.speed, s.ignitionOn)

	return nil
}

// imeiToBCD converts IMEI string to BCD format
func (s *RealisticTripSimulator) imeiToBCD(imei string) []byte {
	// Pad IMEI to 8 bytes if needed
	for len(imei) < 16 {
		imei = "0" + imei
	}

	bcd := make([]byte, 8)
	for i := 0; i < 8; i++ {
		// Convert two hex digits to one byte
		high := imei[i*2] - '0'
		low := imei[i*2+1] - '0'
		bcd[i] = (high << 4) | low
	}

	return bcd
}

// Close closes the connection
func (s *RealisticTripSimulator) Close() error {
	if s.conn != nil {
		return s.conn.Close()
	}
	return nil
}

func mainRealistic() {
	// Configuration
	serverAddr := "localhost:5022"
	imei := "123456789012345" // Test IMEI
	deviceId := "TEST001"

	log.Println("🚀 Starting Realistic GT06 Trip Simulator")
	log.Printf("Server: %s", serverAddr)
	log.Printf("IMEI: %s", imei)
	log.Printf("Device ID: %s", deviceId)

	// Create simulator
	simulator := NewRealisticTripSimulator(imei, deviceId)

	// Connect to server
	if err := simulator.Connect(serverAddr); err != nil {
		log.Fatalf("Failed to connect: %v", err)
	}
	defer simulator.Close()

	// Login
	if err := simulator.Login(); err != nil {
		log.Fatalf("Failed to login: %v", err)
	}

	// Wait a bit for login to be processed
	time.Sleep(2 * time.Second)

	// Simulate realistic trip
	if err := simulator.SimulateRealisticTrip(); err != nil {
		log.Fatalf("Trip simulation failed: %v", err)
	}

	log.Println("🎉 Simulation completed successfully!")
}
