package main

import (
	"fmt"
	"log"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
)

func main() {
	// Initialize database
	config.InitDB()

	fmt.Println("--- Starting Real-Time Impact Detection Test ---")

	// Initialize real-time trip detection service
	manager := services.GetRealtimeServiceManager()
	if err := manager.StartService(); err != nil {
		log.Fatalf("Failed to start real-time trip detection service: %v", err)
	}
	defer manager.StopService()

	// Create test device
	fmt.Println("\n1. Creating test device...")
	deviceName := "Impact Test Device"
	device := models.ClientDevice{
		DeviceId:     "TEST_IMPACT_DEVICE_1",
		Name:         &deviceName,
		ClientId:     1,
		DeviceTypeId: 1, // Assuming device type 1 exists
	}
	if err := config.DB.Create(&device).Error; err != nil {
		fmt.Printf("Failed to create device: %v\n", err)
		return
	}
	fmt.Printf("Created device %d\n", device.Id)

	// Test 1: Normal driving (no impact)
	fmt.Println("\n2. Testing normal driving (no impact)...")
	baseTime := time.Now()

	// First GPS point - normal speed
	direction1 := "N"
	cellId1 := "12345"
	speed1 := 50.0
	ignition1 := true
	gps1 := models.GPSData{
		ClientDeviceId: &device.Id,
		GPSTimestamp:   &baseTime,
		Latitude:       40.7128,
		Longitude:      -74.0060,
		Speed:          &speed1,
		IgnitionStatus: &ignition1,
		Direction:      &direction1,
		CellId:         &cellId1,
	}
	config.DB.Create(&gps1)

	// Process first point
	realtimeService := manager.GetService()
	if err := realtimeService.ProcessGPSData(&gps1); err != nil {
		fmt.Printf("Failed to process GPS data: %v\n", err)
	}

	// Second GPS point - normal speed (no impact)
	timestamp2 := baseTime.Add(30 * time.Second)
	direction2 := "N"
	cellId2 := "12346"
	speed2 := 45.0
	ignition2 := true
	gps2 := models.GPSData{
		ClientDeviceId: &device.Id,
		GPSTimestamp:   &timestamp2,
		Latitude:       40.7130,
		Longitude:      -74.0062,
		Speed:          &speed2,
		IgnitionStatus: &ignition2,
		Direction:      &direction2,
		CellId:         &cellId2,
	}
	config.DB.Create(&gps2)

	// Process second point
	if err := realtimeService.ProcessGPSData(&gps2); err != nil {
		fmt.Printf("Failed to process GPS data: %v\n", err)
	}

	fmt.Println("Normal driving test completed - no impact should be detected")

	// Test 2: Sudden deceleration (impact scenario)
	fmt.Println("\n3. Testing sudden deceleration (impact scenario)...")

	// Third GPS point - high speed
	timestamp3 := baseTime.Add(60 * time.Second)
	direction3 := "N"
	cellId3 := "12347"
	speed3 := 80.0
	ignition3 := true
	gps3 := models.GPSData{
		ClientDeviceId: &device.Id,
		GPSTimestamp:   &timestamp3,
		Latitude:       40.7132,
		Longitude:      -74.0064,
		Speed:          &speed3,
		IgnitionStatus: &ignition3,
		Direction:      &direction3,
		CellId:         &cellId3,
	}
	config.DB.Create(&gps3)

	// Process third point
	if err := realtimeService.ProcessGPSData(&gps3); err != nil {
		fmt.Printf("Failed to process GPS data: %v\n", err)
	}

	// Fourth GPS point - sudden stop (impact)
	timestamp4 := baseTime.Add(90 * time.Second)
	direction4 := "N"
	cellId4 := "12348"
	speed4 := 5.0 // Sudden stop from 80 km/h
	ignition4 := false
	gps4 := models.GPSData{
		ClientDeviceId: &device.Id,
		GPSTimestamp:   &timestamp4,
		Latitude:       40.7134,
		Longitude:      -74.0066,
		Speed:          &speed4,
		IgnitionStatus: &ignition4,
		Direction:      &direction4,
		CellId:         &cellId4,
	}
	config.DB.Create(&gps4)

	// Process fourth point (should trigger impact detection)
	if err := realtimeService.ProcessGPSData(&gps4); err != nil {
		fmt.Printf("Failed to process GPS data: %v\n", err)
	}

	fmt.Println("Sudden deceleration test completed - impact should be detected")

	// Test 3: High deceleration rate (impact scenario)
	fmt.Println("\n4. Testing high deceleration rate (impact scenario)...")

	// Fifth GPS point - high speed
	timestamp5 := baseTime.Add(120 * time.Second)
	direction5 := "N"
	cellId5 := "12349"
	speed5 := 60.0
	ignition5 := true
	gps5 := models.GPSData{
		ClientDeviceId: &device.Id,
		GPSTimestamp:   &timestamp5,
		Latitude:       40.7136,
		Longitude:      -74.0068,
		Speed:          &speed5,
		IgnitionStatus: &ignition5,
		Direction:      &direction5,
		CellId:         &cellId5,
	}
	config.DB.Create(&gps5)

	// Process fifth point
	if err := realtimeService.ProcessGPSData(&gps5); err != nil {
		fmt.Printf("Failed to process GPS data: %v\n", err)
	}

	// Sixth GPS point - very high deceleration
	timestamp6 := baseTime.Add(125 * time.Second) // 5 seconds later
	direction6 := "N"
	cellId6 := "12350"
	speed6 := 0.0 // Stop from 60 km/h in 5 seconds = 12 km/h/s
	ignition6 := false
	gps6 := models.GPSData{
		ClientDeviceId: &device.Id,
		GPSTimestamp:   &timestamp6,
		Latitude:       40.7138,
		Longitude:      -74.0070,
		Speed:          &speed6,
		IgnitionStatus: &ignition6,
		Direction:      &direction6,
		CellId:         &cellId6,
	}
	config.DB.Create(&gps6)

	// Process sixth point (should trigger impact detection due to high deceleration rate)
	if err := realtimeService.ProcessGPSData(&gps6); err != nil {
		fmt.Printf("Failed to process GPS data: %v\n", err)
	}

	fmt.Println("High deceleration rate test completed - impact should be detected")

	// Wait a moment for processing
	time.Sleep(2 * time.Second)

	// Check for impact alerts
	fmt.Println("\n5. Checking for impact alerts...")
	var alerts []models.Alert
	if err := config.DB.Where("client_device_id = ? AND alert_type = ?", device.Id, "impact_detection").Find(&alerts).Error; err != nil {
		fmt.Printf("Failed to query alerts: %v\n", err)
	} else {
		fmt.Printf("Found %d impact alerts:\n", len(alerts))
		for i, alert := range alerts {
			fmt.Printf("  Alert %d: %s at %v - %s\n", i+1, *alert.AlertName, alert.AlertTimestamp, *alert.Message)
		}
	}

	// Clean up
	fmt.Println("\n6. Cleaning up test data...")
	config.DB.Where("client_device_id = ?", device.Id).Delete(&models.GPSData{})
	config.DB.Where("client_device_id = ?", device.Id).Delete(&models.Alert{})
	config.DB.Delete(&device)
	fmt.Println("Test data cleaned up")

	fmt.Println("\n--- Real-Time Impact Detection Test Completed ---")
}
