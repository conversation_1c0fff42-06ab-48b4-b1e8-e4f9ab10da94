package main

import (
	"encoding/binary"
	"fmt"
	"log"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
)

func main() {
	// Initialize database
	config.InitDB()

	// Initialize real-time trip detection service
	manager := services.GetRealtimeServiceManager()
	if err := manager.StartService(); err != nil {
		log.Fatalf("Failed to start real-time trip detection service: %v", err)
	}
	realtimeService := services.GetRealtimeTripDetectionService()

	if realtimeService == nil {
		log.Fatal("Failed to initialize real-time trip detection service")
	}

	fmt.Println("🚀 Starting GT06 Real-Time Trip Detection Test")
	fmt.Println("==============================================")

	// Test with actual GT06 protocol data
	testGT06KeyEvents(realtimeService)
	testGT06TripDetection(realtimeService)
	testGT06SpeedAlerts(realtimeService)
	testGT06TowingEvents(realtimeService)

	fmt.Println("\n✅ All GT06 tests completed!")
	fmt.Println("Check the database for created trips and alerts.")
}

// testGT06KeyEvents tests Key On/Off with actual GT06 protocol data
func testGT06KeyEvents(realtimeService *services.RealtimeTripDetectionService) {
	fmt.Println("\n🔑 Testing GT06 Key Events Detection")
	fmt.Println("------------------------------------")

	deviceId := uint(1)
	baseTime := time.Now()

	// GT06 GPS Data with ACC bit set (ignition ON)
	fmt.Println("📱 Sending GT06 GPS data with ACC=1 (Key ON)...")
	gt06Data1 := createGT06GPSData(deviceId, baseTime, -17.8252, 31.0335, 0.0, true)
	processGT06Data(gt06Data1, realtimeService)
	time.Sleep(100 * time.Millisecond)

	// GT06 GPS Data with ACC bit clear (ignition OFF)
	fmt.Println("📱 Sending GT06 GPS data with ACC=0 (Key OFF)...")
	gt06Data2 := createGT06GPSData(deviceId, baseTime.Add(30*time.Second), -17.8252, 31.0335, 0.0, false)
	processGT06Data(gt06Data2, realtimeService)
	time.Sleep(100 * time.Millisecond)

	// GT06 GPS Data with ACC bit set again (Key ON)
	fmt.Println("📱 Sending GT06 GPS data with ACC=1 (Key ON again)...")
	gt06Data3 := createGT06GPSData(deviceId, baseTime.Add(60*time.Second), -17.8252, 31.0335, 0.0, true)
	processGT06Data(gt06Data3, realtimeService)
	time.Sleep(100 * time.Millisecond)
}

// testGT06TripDetection tests trip detection with GT06 data
func testGT06TripDetection(realtimeService *services.RealtimeTripDetectionService) {
	fmt.Println("\n🚗 Testing GT06 Trip Detection")
	fmt.Println("------------------------------")

	deviceId := uint(2)
	baseTime := time.Now().Add(5 * time.Minute)

	// Trip Start: ACC=1 + movement
	fmt.Println("🚀 Starting GT06 trip with ACC=1 and movement...")

	gt06Data1 := createGT06GPSData(deviceId, baseTime, -17.8252, 31.0335, 0.0, true)
	processGT06Data(gt06Data1, realtimeService)
	time.Sleep(100 * time.Millisecond)

	gt06Data2 := createGT06GPSData(deviceId, baseTime.Add(30*time.Second), -17.8253, 31.0336, 25.0, true)
	processGT06Data(gt06Data2, realtimeService)
	time.Sleep(100 * time.Millisecond)

	gt06Data3 := createGT06GPSData(deviceId, baseTime.Add(60*time.Second), -17.8254, 31.0337, 30.0, true)
	processGT06Data(gt06Data3, realtimeService)
	time.Sleep(100 * time.Millisecond)

	// Trip End: ACC=0
	fmt.Println("🛑 Ending GT06 trip with ACC=0...")
	gt06Data4 := createGT06GPSData(deviceId, baseTime.Add(90*time.Second), -17.8255, 31.0338, 0.0, false)
	processGT06Data(gt06Data4, realtimeService)
	time.Sleep(100 * time.Millisecond)
}

// testGT06SpeedAlerts tests speed alerts with GT06 data
func testGT06SpeedAlerts(realtimeService *services.RealtimeTripDetectionService) {
	fmt.Println("\n⚡ Testing GT06 Speed Alerts")
	fmt.Println("----------------------------")

	deviceId := uint(3)
	baseTime := time.Now().Add(10 * time.Minute)

	fmt.Println("🏃 Sending GT06 GPS data exceeding speed limit...")

	// Normal speed
	gt06Data1 := createGT06GPSData(deviceId, baseTime, -17.8252, 31.0335, 50.0, true)
	processGT06Data(gt06Data1, realtimeService)
	time.Sleep(100 * time.Millisecond)

	// Speed violation
	gt06Data2 := createGT06GPSData(deviceId, baseTime.Add(30*time.Second), -17.8253, 31.0336, 85.0, true)
	processGT06Data(gt06Data2, realtimeService)
	time.Sleep(100 * time.Millisecond)

	// Another violation
	gt06Data3 := createGT06GPSData(deviceId, baseTime.Add(60*time.Second), -17.8254, 31.0337, 95.0, true)
	processGT06Data(gt06Data3, realtimeService)
	time.Sleep(100 * time.Millisecond)
}

// testGT06TowingEvents tests towing events with GT06 data
func testGT06TowingEvents(realtimeService *services.RealtimeTripDetectionService) {
	fmt.Println("\n🚛 Testing GT06 Towing Events")
	fmt.Println("-----------------------------")

	deviceId := uint(4)
	baseTime := time.Now().Add(15 * time.Minute)

	fmt.Println("🚛 Simulating GT06 towing event (movement with ACC=0)...")

	// Initial position (ACC=0, stationary)
	gt06Data1 := createGT06GPSData(deviceId, baseTime, -17.8252, 31.0335, 0.0, false)
	processGT06Data(gt06Data1, realtimeService)
	time.Sleep(100 * time.Millisecond)

	// Moved significantly without ACC (towing)
	gt06Data2 := createGT06GPSData(deviceId, baseTime.Add(2*time.Minute), -17.8255, 31.0340, 0.0, false)
	processGT06Data(gt06Data2, realtimeService)
	time.Sleep(100 * time.Millisecond)

	// Continue moving (more towing)
	gt06Data3 := createGT06GPSData(deviceId, baseTime.Add(4*time.Minute), -17.8258, 31.0345, 0.0, false)
	processGT06Data(gt06Data3, realtimeService)
	time.Sleep(100 * time.Millisecond)
}

// createGT06GPSData creates GPS data that simulates GT06 protocol processing
func createGT06GPSData(deviceId uint, timestamp time.Time, lat, lng, speed float64, accHigh bool) *models.GPSData {
	// Simulate GT06 course status byte with ACC bit
	var courseStatus byte = 0x00
	if accHigh {
		courseStatus |= 0x80 // Set ACC bit (bit 7)
	}
	courseStatus |= 0x10 // Set GPS fix bit (bit 4)
	courseStatus |= 0x04 // Set latitude north bit (bit 2)
	// Longitude east bit (bit 3) is 0 by default

	direction := "N"
	mcc := "716" // Zimbabwe MCC
	mnc := "01"  // Zimbabwe MNC
	lac := "1234"
	cellId := "5678"

	// Create GPS data as it would be processed by GT06 protocol
	return &models.GPSData{
		ClientDeviceId: &deviceId,
		DeviceId:       fmt.Sprintf("GT06_DEVICE_%d", deviceId),
		GPSTimestamp:   &timestamp,
		Latitude:       lat,
		Longitude:      lng,
		Speed:          &speed,
		IgnitionStatus: &accHigh, // This comes from ACC bit in GT06
		Direction:      &direction,
		Mcc:            &mcc,
		Mnc:            &mnc,
		Lac:            &lac,
		CellId:         &cellId,
	}
}

// processGT06Data simulates the GT06 protocol processing and sends to real-time service
func processGT06Data(gpsData *models.GPSData, realtimeService *services.RealtimeTripDetectionService) {
	// Simulate GT06 protocol processing
	fmt.Printf("  📡 GT06 Data: Lat=%.6f, Lon=%.6f, Speed=%.1f km/h, ACC=%v\n",
		gpsData.Latitude, gpsData.Longitude, *gpsData.Speed, *gpsData.IgnitionStatus)

	// Save GPS data to database (simulating location_data_service)
	if err := config.DB.Create(gpsData).Error; err != nil {
		fmt.Printf("❌ Error saving GPS data: %v\n", err)
		return
	}

	// Send to real-time trip detection service
	if err := realtimeService.ProcessGPSData(gpsData); err != nil {
		fmt.Printf("❌ Error processing GPS data in real-time service: %v\n", err)
	}
}

// createRawGT06Packet creates a raw GT06 packet for testing (optional)
func createRawGT06Packet(deviceId string, timestamp time.Time, lat, lng, speed float64, accHigh bool) []byte {
	// This would create actual GT06 protocol bytes
	// For testing purposes, we'll use the processed GPS data instead

	// GT06 packet structure (simplified):
	// [Start][Length][Protocol][Date][Satellites][Lat][Lon][Speed][Course][MCC][MNC][LAC][CellID][ACC][...]

	packet := make([]byte, 39) // Minimum GT06 positioning packet size

	// Start bytes
	packet[0] = 0x78
	packet[1] = 0x78

	// Packet length
	packet[2] = 0x22 // 34 bytes of data

	// Protocol number
	packet[3] = 0x22 // GPS positioning data

	// Date and time (6 bytes)
	year := timestamp.Year() - 2000
	packet[4] = byte(year)
	packet[5] = byte(timestamp.Month())
	packet[6] = byte(timestamp.Day())
	packet[7] = byte(timestamp.Hour())
	packet[8] = byte(timestamp.Minute())
	packet[9] = byte(timestamp.Second())

	// Satellites
	packet[10] = 0x08 // 8 satellites

	// Latitude (4 bytes)
	latInt := uint32(lat * 30000.0 * 60.0)
	binary.BigEndian.PutUint32(packet[11:15], latInt)

	// Longitude (4 bytes)
	lngInt := uint32(lng * 30000.0 * 60.0)
	binary.BigEndian.PutUint32(packet[15:19], lngInt)

	// Speed
	packet[19] = byte(speed)

	// Course (2 bytes) - includes ACC bit
	var courseStatus byte = 0x00
	if accHigh {
		courseStatus |= 0x80 // Set ACC bit
	}
	courseStatus |= 0x10 // GPS fix
	courseStatus |= 0x04 // Latitude north
	packet[20] = courseStatus
	packet[21] = 0x00 // Course value

	// MCC (2 bytes)
	packet[22] = 0x02
	packet[23] = 0xCC // 716 in hex

	// MNC
	packet[24] = 0x01

	// LAC (2 bytes)
	packet[25] = 0x04
	packet[26] = 0xD2 // 1234 in hex

	// Cell ID (3 bytes)
	packet[27] = 0x00
	packet[28] = 0x16
	packet[29] = 0x2E // 5678 in hex

	// ACC status
	if accHigh {
		packet[30] = 0x01
	} else {
		packet[30] = 0x00
	}

	// Data reporting mode
	packet[31] = 0x00

	// GPS retransmission
	packet[32] = 0x00

	// Serial number (2 bytes)
	packet[33] = 0x00
	packet[34] = 0x01

	// Error check (2 bytes) - simplified
	packet[35] = 0x00
	packet[36] = 0x00

	// Stop bytes
	packet[37] = 0x0D
	packet[38] = 0x0A

	return packet
}
