package main

import (
	"fmt"
	"log"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
)

func main() {
	// Initialize database
	config.InitDB()

	// Initialize real-time trip detection service
	manager := services.GetRealtimeServiceManager()
	if err := manager.StartService(); err != nil {
		log.Fatalf("Failed to start real-time trip detection service: %v", err)
	}
	realtimeService := services.GetRealtimeTripDetectionService()

	if realtimeService == nil {
		log.Fatal("Failed to initialize real-time trip detection service")
	}

	fmt.Println("🚀 Starting Real-Time Trip Detection Test")
	fmt.Println("==========================================")

	// Test scenarios
	testKeyEvents(realtimeService)
	testTripDetection(realtimeService)
	testSpeedAlerts(realtimeService)
	testTowingEvents(realtimeService)

	fmt.Println("\n✅ All tests completed!")
	fmt.Println("Check the database for created trips and alerts.")
}

// testKeyEvents tests Key On/Off event detection
func testKeyEvents(realtimeService *services.RealtimeTripDetectionService) {
	fmt.Println("\n🔑 Testing Key Events Detection")
	fmt.Println("-------------------------------")

	deviceId := uint(1) // Use existing device ID
	baseTime := time.Now()

	// Scenario 1: Key On event
	fmt.Println("📱 Sending GPS data with Key ON...")
	gpsData1 := createTestGPSData(deviceId, baseTime, -17.8252, 31.0335, 0.0, true)
	if err := realtimeService.ProcessGPSData(gpsData1); err != nil {
		fmt.Printf("❌ Error processing GPS data: %v\n", err)
	}

	time.Sleep(100 * time.Millisecond)

	// Scenario 2: Key Off event
	fmt.Println("📱 Sending GPS data with Key OFF...")
	gpsData2 := createTestGPSData(deviceId, baseTime.Add(30*time.Second), -17.8252, 31.0335, 0.0, false)
	if err := realtimeService.ProcessGPSData(gpsData2); err != nil {
		fmt.Printf("❌ Error processing GPS data: %v\n", err)
	}

	time.Sleep(100 * time.Millisecond)

	// Scenario 3: Key On again (should trigger another event)
	fmt.Println("📱 Sending GPS data with Key ON again...")
	gpsData3 := createTestGPSData(deviceId, baseTime.Add(60*time.Second), -17.8252, 31.0335, 0.0, true)
	if err := realtimeService.ProcessGPSData(gpsData3); err != nil {
		fmt.Printf("❌ Error processing GPS data: %v\n", err)
	}
}

// testTripDetection tests trip start/end detection
func testTripDetection(realtimeService *services.RealtimeTripDetectionService) {
	fmt.Println("\n🚗 Testing Trip Detection")
	fmt.Println("-------------------------")

	deviceId := uint(2) // Use different device
	baseTime := time.Now().Add(5 * time.Minute)

	// Scenario 1: Trip Start (ignition ON + movement)
	fmt.Println("🚀 Starting trip with ignition ON and movement...")

	// Start position
	gpsData1 := createTestGPSData(deviceId, baseTime, -17.8252, 31.0335, 0.0, true)
	realtimeService.ProcessGPSData(gpsData1)
	time.Sleep(100 * time.Millisecond)

	// Moving
	gpsData2 := createTestGPSData(deviceId, baseTime.Add(30*time.Second), -17.8253, 31.0336, 25.0, true)
	realtimeService.ProcessGPSData(gpsData2)
	time.Sleep(100 * time.Millisecond)

	// Continue moving
	gpsData3 := createTestGPSData(deviceId, baseTime.Add(60*time.Second), -17.8254, 31.0337, 30.0, true)
	realtimeService.ProcessGPSData(gpsData3)
	time.Sleep(100 * time.Millisecond)

	// Trip End (ignition OFF)
	fmt.Println("🛑 Ending trip with ignition OFF...")
	gpsData4 := createTestGPSData(deviceId, baseTime.Add(90*time.Second), -17.8255, 31.0338, 0.0, false)
	realtimeService.ProcessGPSData(gpsData4)
	time.Sleep(100 * time.Millisecond)
}

// testSpeedAlerts tests speed limit violation detection
func testSpeedAlerts(realtimeService *services.RealtimeTripDetectionService) {
	fmt.Println("\n⚡ Testing Speed Alerts")
	fmt.Println("----------------------")

	deviceId := uint(3) // Use different device
	baseTime := time.Now().Add(10 * time.Minute)

	// Set up device with speed limit (assuming device 3 has MaxSpeed set)
	fmt.Println("🏃 Sending GPS data exceeding speed limit...")

	// Normal speed
	gpsData1 := createTestGPSData(deviceId, baseTime, -17.8252, 31.0335, 50.0, true)
	realtimeService.ProcessGPSData(gpsData1)
	time.Sleep(100 * time.Millisecond)

	// Speed violation (assuming limit is 60 km/h)
	gpsData2 := createTestGPSData(deviceId, baseTime.Add(30*time.Second), -17.8253, 31.0336, 85.0, true)
	realtimeService.ProcessGPSData(gpsData2)
	time.Sleep(100 * time.Millisecond)

	// Another violation
	gpsData3 := createTestGPSData(deviceId, baseTime.Add(60*time.Second), -17.8254, 31.0337, 95.0, true)
	realtimeService.ProcessGPSData(gpsData3)
	time.Sleep(100 * time.Millisecond)
}

// testTowingEvents tests towing event detection
func testTowingEvents(realtimeService *services.RealtimeTripDetectionService) {
	fmt.Println("\n🚛 Testing Towing Events")
	fmt.Println("------------------------")

	deviceId := uint(4) // Use different device
	baseTime := time.Now().Add(15 * time.Minute)

	// Scenario: Vehicle moving without ignition (towing)
	fmt.Println("🚛 Simulating towing event (movement without ignition)...")

	// Initial position (ignition OFF, stationary)
	gpsData1 := createTestGPSData(deviceId, baseTime, -17.8252, 31.0335, 0.0, false)
	realtimeService.ProcessGPSData(gpsData1)
	time.Sleep(100 * time.Millisecond)

	// Moved significantly without ignition (towing)
	gpsData2 := createTestGPSData(deviceId, baseTime.Add(2*time.Minute), -17.8255, 31.0340, 0.0, false)
	realtimeService.ProcessGPSData(gpsData2)
	time.Sleep(100 * time.Millisecond)

	// Continue moving (more towing)
	gpsData3 := createTestGPSData(deviceId, baseTime.Add(4*time.Minute), -17.8258, 31.0345, 0.0, false)
	realtimeService.ProcessGPSData(gpsData3)
	time.Sleep(100 * time.Millisecond)
}

// createTestGPSData creates a test GPS data point
func createTestGPSData(deviceId uint, timestamp time.Time, lat, lng, speed float64, ignitionOn bool) *models.GPSData {
	direction := "N"
	mcc := "716"
	mnc := "01"
	lac := "1234"
	cellId := "5678"

	return &models.GPSData{
		ClientDeviceId: &deviceId,
		DeviceId:       fmt.Sprintf("TEST_DEVICE_%d", deviceId),
		GPSTimestamp:   &timestamp,
		Latitude:       lat,
		Longitude:      lng,
		Speed:          &speed,
		IgnitionStatus: &ignitionOn,
		Direction:      &direction,
		Mcc:            &mcc,
		Mnc:            &mnc,
		Lac:            &lac,
		CellId:         &cellId,
	}
}
