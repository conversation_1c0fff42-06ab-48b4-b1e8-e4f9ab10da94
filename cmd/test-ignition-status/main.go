package main

import (
	"fmt"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"
)

func main() {
	// Initialize database
	config.InitDB()

	fmt.Println("🔑 Testing Ignition Status Time Functionality")
	fmt.Println("=============================================")

	// Test device ID
	deviceId := uint(1)

	// Test 1: Get current ignition status info
	fmt.Println("\n1️⃣ Getting current ignition status info...")
	ignitionService := services.NewIgnitionStatusService()
	info, err := ignitionService.GetIgnitionStatusInfo(deviceId)
	if err != nil {
		fmt.Printf("❌ Error getting ignition status info: %v\n", err)
	} else {
		fmt.Printf("✅ Device %d ignition status:\n", deviceId)
		fmt.Printf("   Status: %s\n", info.StatusDescription)
		if info.Duration != nil {
			fmt.Printf("   Duration: %s\n", info.DurationFormatted)
			fmt.Printf("   Duration (minutes): %.2f\n", info.DurationMinutes)
			fmt.Printf("   Duration (hours): %.2f\n", info.DurationHours)
		}
		if info.StatusTime != nil {
			fmt.Printf("   Last change: %v\n", info.StatusTime.Format("2006-01-02 15:04:05"))
		}
	}

	// Test 2: Update ignition status
	fmt.Println("\n2️⃣ Updating ignition status...")
	currentTime := time.Now()
	err = ignitionService.UpdateIgnitionStatus(deviceId, true, &currentTime)
	if err != nil {
		fmt.Printf("❌ Error updating ignition status: %v\n", err)
	} else {
		fmt.Printf("✅ Updated ignition status to ON at %v\n", currentTime.Format("2006-01-02 15:04:05"))
	}

	// Wait a moment
	time.Sleep(2 * time.Second)

	// Test 3: Get updated ignition status info
	fmt.Println("\n3️⃣ Getting updated ignition status info...")
	info, err = ignitionService.GetIgnitionStatusInfo(deviceId)
	if err != nil {
		fmt.Printf("❌ Error getting updated ignition status info: %v\n", err)
	} else {
		fmt.Printf("✅ Updated device %d ignition status:\n", deviceId)
		fmt.Printf("   Status: %s\n", info.StatusDescription)
		if info.Duration != nil {
			fmt.Printf("   Duration: %s\n", info.DurationFormatted)
			fmt.Printf("   Duration (minutes): %.2f\n", info.DurationMinutes)
		}
	}

	// Test 4: Test utility functions
	fmt.Println("\n4️⃣ Testing utility functions...")
	var device models.ClientDevice
	err = config.DB.First(&device, deviceId).Error
	if err != nil {
		fmt.Printf("❌ Error loading device: %v\n", err)
	} else {
		// Test duration calculation
		duration, err := utils.GetIgnitionStatusDuration(&device)
		if err != nil {
			fmt.Printf("❌ Error calculating duration: %v\n", err)
		} else {
			fmt.Printf("✅ Duration since last ignition change: %v\n", duration)
		}

		// Test duration in minutes
		minutes, err := utils.GetIgnitionStatusDurationMinutes(&device)
		if err != nil {
			fmt.Printf("❌ Error calculating duration in minutes: %v\n", err)
		} else {
			fmt.Printf("✅ Duration in minutes: %.2f\n", minutes)
		}

		// Test duration in hours
		hours, err := utils.GetIgnitionStatusDurationHours(&device)
		if err != nil {
			fmt.Printf("❌ Error calculating duration in hours: %v\n", err)
		} else {
			fmt.Printf("✅ Duration in hours: %.2f\n", hours)
		}

		// Test formatted duration
		formatted, err := utils.FormatIgnitionDuration(&device)
		if err != nil {
			fmt.Printf("❌ Error formatting duration: %v\n", err)
		} else {
			fmt.Printf("✅ Formatted duration: %s\n", formatted)
		}

		// Test if ignition has been on for specific duration
		hasBeenOn, err := utils.IsIgnitionOnForDuration(&device, 1*time.Second)
		if err != nil {
			fmt.Printf("❌ Error checking ignition duration: %v\n", err)
		} else {
			fmt.Printf("✅ Has ignition been ON for 1 second: %v\n", hasBeenOn)
		}
	}

	// Test 5: Get devices with ignition on
	fmt.Println("\n5️⃣ Getting devices with ignition ON...")
	devicesOn, err := ignitionService.GetDevicesByIgnitionStatus(true)
	if err != nil {
		fmt.Printf("❌ Error getting devices with ignition ON: %v\n", err)
	} else {
		fmt.Printf("✅ Found %d devices with ignition ON\n", len(devicesOn))
		for _, device := range devicesOn {
			fmt.Printf("   Device %d: %s for %s\n", device.DeviceId, device.StatusDescription, device.DurationFormatted)
		}
	}

	// Test 6: Get devices with ignition off
	fmt.Println("\n6️⃣ Getting devices with ignition OFF...")
	devicesOff, err := ignitionService.GetDevicesByIgnitionStatus(false)
	if err != nil {
		fmt.Printf("❌ Error getting devices with ignition OFF: %v\n", err)
	} else {
		fmt.Printf("✅ Found %d devices with ignition OFF\n", len(devicesOff))
		for _, device := range devicesOff {
			fmt.Printf("   Device %d: %s for %s\n", device.DeviceId, device.StatusDescription, device.DurationFormatted)
		}
	}

	// Test 7: Get devices with ignition on for specific duration
	fmt.Println("\n7️⃣ Getting devices with ignition ON for at least 1 second...")
	devicesOnDuration, err := ignitionService.GetDevicesWithIgnitionOnForDuration(1 * time.Second)
	if err != nil {
		fmt.Printf("❌ Error getting devices with ignition ON for duration: %v\n", err)
	} else {
		fmt.Printf("✅ Found %d devices with ignition ON for at least 1 second\n", len(devicesOnDuration))
		for _, device := range devicesOnDuration {
			fmt.Printf("   Device %d: %s for %s\n", device.DeviceId, device.StatusDescription, device.DurationFormatted)
		}
	}

	// Test 8: Simulate GPS data with ignition status change
	fmt.Println("\n8️⃣ Simulating GPS data with ignition status change...")

	// Create GPS data with ignition OFF
	gpsData := &models.GPSData{
		ClientDeviceId: &deviceId,
		DeviceId:       fmt.Sprintf("TEST_DEVICE_%d", deviceId),
		GPSTimestamp:   &currentTime,
		Latitude:       -17.8252,
		Longitude:      31.0335,
		Speed:          func() *float64 { v := 0.0; return &v }(),
		IgnitionStatus: func() *bool { v := false; return &v }(),
		Direction:      func() *string { v := "N"; return &v }(),
		Mcc:            func() *string { v := "716"; return &v }(),
		Mnc:            func() *string { v := "01"; return &v }(),
		Lac:            func() *string { v := "1234"; return &v }(),
		CellId:         func() *string { v := "5678"; return &v }(),
	}

	// Save GPS data (this should trigger ignition status time update)
	err = config.DB.Create(gpsData).Error
	if err != nil {
		fmt.Printf("❌ Error saving GPS data: %v\n", err)
	} else {
		fmt.Printf("✅ Saved GPS data with ignition OFF\n")
	}

	// Wait a moment
	time.Sleep(1 * time.Second)

	// Check ignition status after GPS data
	fmt.Println("\n9️⃣ Checking ignition status after GPS data...")
	info, err = ignitionService.GetIgnitionStatusInfo(deviceId)
	if err != nil {
		fmt.Printf("❌ Error getting ignition status after GPS data: %v\n", err)
	} else {
		fmt.Printf("✅ Device %d ignition status after GPS data:\n", deviceId)
		fmt.Printf("   Status: %s\n", info.StatusDescription)
		if info.Duration != nil {
			fmt.Printf("   Duration: %s\n", info.DurationFormatted)
		}
	}

	fmt.Println("\n✅ Ignition Status Time Testing Completed!")
	fmt.Println("\n📊 Summary:")
	fmt.Println("   • Added ignition_status_time field to track ignition status changes")
	fmt.Println("   • Created utility functions for duration calculations")
	fmt.Println("   • Created service for ignition status management")
	fmt.Println("   • Added API endpoints for ignition status queries")
	fmt.Println("   • Integrated with GPS data processing")

	fmt.Println("\n🔍 To test API endpoints:")
	fmt.Println("   GET /api/v1/backend/ignition_status/device/1")
	fmt.Println("   GET /api/v1/backend/ignition_status/devices?status=on")
	fmt.Println("   GET /api/v1/backend/ignition_status/devices/on_for_duration?duration=1m")
	fmt.Println("   PUT /api/v1/backend/ignition_status/device/1")
}
