package main

import (
	"fmt"
	"log"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
)

func main() {
	// Initialize database
	config.InitDB()

	fmt.Println("--- Starting Alert-GPS Data Linking Test ---")

	// Initialize real-time trip detection service
	manager := services.GetRealtimeServiceManager()
	if err := manager.StartService(); err != nil {
		log.Fatalf("Failed to start real-time trip detection service: %v", err)
	}
	defer manager.StopService()

	// Create test device
	fmt.Println("\n1. Creating test device...")
	deviceName := "Alert Location Test Device"
	device := models.ClientDevice{
		DeviceId:     "TEST_ALERT_LOCATION_DEVICE_1",
		Name:         &deviceName,
		ClientId:     1,
		DeviceTypeId: 1,
		MaxSpeed:     func() *float64 { v := 50.0; return &v }(), // Set speed limit to 50 km/h
	}
	if err := config.DB.Create(&device).Error; err != nil {
		fmt.Printf("Failed to create device: %v\n", err)
		return
	}
	fmt.Printf("Created device %d with speed limit %.1f km/h\n", device.Id, *device.MaxSpeed)

	// Test 1: Speed Alert with GPS Data Linking
	fmt.Println("\n2. Testing speed alert with GPS data linking...")
	baseTime := time.Now()

	// Create GPS data that exceeds speed limit
	timestamp1 := baseTime
	direction1 := "N"
	cellId1 := "12345"
	speed1 := 60.0 // Exceeds 50 km/h limit
	ignition1 := true
	gps1 := models.GPSData{
		ClientDeviceId: &device.Id,
		GPSTimestamp:   &timestamp1,
		Latitude:       40.7128,
		Longitude:      -74.0060,
		Speed:          &speed1,
		IgnitionStatus: &ignition1,
		Direction:      &direction1,
		CellId:         &cellId1,
	}
	config.DB.Create(&gps1)

	// Process GPS data (should trigger speed alert)
	realtimeService := manager.GetService()
	if err := realtimeService.ProcessGPSData(&gps1); err != nil {
		fmt.Printf("Failed to process GPS data: %v\n", err)
	}

	// Test 2: Impact Detection with GPS Data Linking
	fmt.Println("\n3. Testing impact detection with GPS data linking...")

	// Create second GPS point with normal speed
	timestamp2 := baseTime.Add(30 * time.Second)
	direction2 := "N"
	cellId2 := "12346"
	speed2 := 45.0
	ignition2 := true
	gps2 := models.GPSData{
		ClientDeviceId: &device.Id,
		GPSTimestamp:   &timestamp2,
		Latitude:       40.7130,
		Longitude:      -74.0062,
		Speed:          &speed2,
		IgnitionStatus: &ignition2,
		Direction:      &direction2,
		CellId:         &cellId2,
	}
	config.DB.Create(&gps2)

	// Process second point
	if err := realtimeService.ProcessGPSData(&gps2); err != nil {
		fmt.Printf("Failed to process GPS data: %v\n", err)
	}

	// Create third GPS point with high speed
	timestamp3 := baseTime.Add(60 * time.Second)
	direction3 := "N"
	cellId3 := "12347"
	speed3 := 80.0
	ignition3 := true
	gps3 := models.GPSData{
		ClientDeviceId: &device.Id,
		GPSTimestamp:   &timestamp3,
		Latitude:       40.7132,
		Longitude:      -74.0064,
		Speed:          &speed3,
		IgnitionStatus: &ignition3,
		Direction:      &direction3,
		CellId:         &cellId3,
	}
	config.DB.Create(&gps3)

	// Process third point
	if err := realtimeService.ProcessGPSData(&gps3); err != nil {
		fmt.Printf("Failed to process GPS data: %v\n", err)
	}

	// Create fourth GPS point with sudden stop (impact)
	timestamp4 := baseTime.Add(90 * time.Second)
	direction4 := "N"
	cellId4 := "12348"
	speed4 := 5.0 // Sudden stop from 80 km/h
	ignition4 := false
	gps4 := models.GPSData{
		ClientDeviceId: &device.Id,
		GPSTimestamp:   &timestamp4,
		Latitude:       40.7134,
		Longitude:      -74.0066,
		Speed:          &speed4,
		IgnitionStatus: &ignition4,
		Direction:      &direction4,
		CellId:         &cellId4,
	}
	config.DB.Create(&gps4)

	// Process fourth point (should trigger impact detection)
	if err := realtimeService.ProcessGPSData(&gps4); err != nil {
		fmt.Printf("Failed to process GPS data: %v\n", err)
	}

	// Wait for processing
	time.Sleep(2 * time.Second)

	// Test 3: Verify Alert-GPS Data Linking
	fmt.Println("\n4. Verifying alert-GPS data linking...")

	// Get alerts with location data
	alertService := services.NewAlertWithLocationService()
	alerts, err := alertService.GetAlertsWithLocation(&device.Id, nil, nil, nil, 10)
	if err != nil {
		fmt.Printf("Failed to get alerts with location: %v\n", err)
		return
	}

	fmt.Printf("Found %d alerts with location data:\n", len(alerts))
	for i, alert := range alerts {
		fmt.Printf("  Alert %d: %s at %v\n", i+1, *alert.AlertName, alert.AlertTimestamp)
		if alert.GPSData != nil {
			fmt.Printf("    GPS Data ID: %d, Location: %.6f, %.6f, Speed: %.1f km/h\n",
				alert.GPSData.Id, alert.GPSData.Latitude, alert.GPSData.Longitude,
				func() float64 {
					if alert.GPSData.Speed != nil {
						return *alert.GPSData.Speed
					} else {
						return 0
					}
				}())
		} else {
			fmt.Printf("    No GPS data linked\n")
		}
	}

	// Test 4: Get alerts for map view
	fmt.Println("\n5. Testing map view alerts...")
	mapAlerts, err := alertService.GetAlertsForMapView(&device.Id, nil, nil, nil)
	if err != nil {
		fmt.Printf("Failed to get alerts for map view: %v\n", err)
		return
	}

	fmt.Printf("Found %d alerts for map view:\n", len(mapAlerts))
	for i, alert := range mapAlerts {
		fmt.Printf("  Map Alert %d: %s at (%.6f, %.6f)\n", i+1, *alert.AlertName, alert.Latitude, alert.Longitude)
	}

	// Test 5: Get alert statistics
	fmt.Println("\n6. Testing alert statistics...")
	stats, err := alertService.GetAlertStatisticsWithLocation(&device.Id, nil, nil)
	if err != nil {
		fmt.Printf("Failed to get alert statistics: %v\n", err)
		return
	}

	fmt.Printf("Alert Statistics:\n")
	fmt.Printf("  Total Alerts: %d\n", stats.TotalAlerts)
	fmt.Printf("  Alerts with Location: %d\n", stats.AlertsWithLocation)
	fmt.Printf("  Location Coverage: %.1f%%\n", stats.LocationCoveragePercentage)
	fmt.Printf("  Alerts by Type:\n")
	for alertType, count := range stats.AlertsByType {
		fmt.Printf("    %s: %d\n", alertType, count)
	}

	// Clean up
	fmt.Println("\n7. Cleaning up test data...")
	config.DB.Where("client_device_id = ?", device.Id).Delete(&models.GPSData{})
	config.DB.Where("client_device_id = ?", device.Id).Delete(&models.Alert{})
	config.DB.Delete(&device)
	fmt.Println("Test data cleaned up")

	fmt.Println("\n--- Alert-GPS Data Linking Test Completed ---")
}
