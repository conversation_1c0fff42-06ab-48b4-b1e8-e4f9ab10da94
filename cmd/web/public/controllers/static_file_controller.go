package controllers

import (
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/gin-gonic/gin"
)

// ServeMediaFile serves uploaded files from the media directory
func ServeMediaFile(c *gin.Context) {
	// Get the filename from the URL path
	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "No filename provided",
		})
		return
	}

	// Sanitize filename to prevent directory traversal
	if strings.Contains(filename, "..") || strings.Contains(filename, "/") || strings.Contains(filename, "\\") {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid filename",
		})
		return
	}

	// Construct the full file path
	filePath := filepath.Join("/var/www/yotrackermedia", filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.<PERSON>(http.StatusNotFound, gin.H{
			"error": "File not found",
		})
		return
	}

	// Serve the file
	c.File(filePath)
}
