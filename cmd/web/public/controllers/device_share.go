package controllers

import (
	"fmt"
	"net/http"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

func GetSharedDeviceLocation(c *gin.Context) {

	claimsInterface, _ := c.Get("device_share_claims")
	claims := claimsInterface.(*services.DeviceShareClaims)

	// Get the device information from the token
	var clientDevice models.ClientDevice
	if err := config.DB.Where("id = ?", claims.ClientDeviceId).First(&clientDevice).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Device not found",
		})
		return
	}

	// Get the latest GPS data for the device
	var gpsData models.GPSData
	if err := config.DB.Where("client_device_id = ?", claims.ClientDeviceId).
		Preload("ClientDevice").
		Order("id DESC").
		First(&gpsData).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "No GPS data found for this device",
		})
		return
	}

	// Check if location name is empty and fetch it using Google Geocoding API
	if gpsData.LocationName == nil || *gpsData.LocationName == "" {
		geocodingService := services.NewGoogleGeocodingService()
		err := geocodingService.UpdateGPSDataLocationName(&gpsData)
		if err != nil {
			// Log the error but don't fail the request
			fmt.Printf("Failed to fetch location name for GPS data %d: %v\n", gpsData.Id, err)
		}
	}

	// Prepare response with device info and latest location
	response := gin.H{
		"gps_data": gpsData,
		"token_info": gin.H{
			"expires_at": time.Unix(claims.ExpiresAt, 0),
		},
	}

	c.JSON(http.StatusOK, response)
}
