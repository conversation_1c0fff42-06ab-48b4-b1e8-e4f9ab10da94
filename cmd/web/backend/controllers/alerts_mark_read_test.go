package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupMarkReadTest() {
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.<PERSON>env("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}
	if os.Getenv("APP_KEY") == "" {
		os.Setenv("APP_KEY", "test-secret-key")
	}

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	gin.SetMode(gin.TestMode)
}

func createTestAlert() models.Alert {
	// Get the first available device type (should exist after seeding)
	var deviceType models.DeviceType
	err := config.DB.First(&deviceType).Error
	if err != nil {
		// If no device type exists, create one
		protocol := models.Protocol{
			Name:   "Test Protocol",
			Active: true,
		}
		config.DB.Create(&protocol)

		deviceType = models.DeviceType{
			ProtocolId: protocol.Id,
			Name:       "Test Device Type",
			Active:     true,
		}
		config.DB.Create(&deviceType)
	}

	// Create a test client with unique email
	email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())
	client := models.Client{
		Name:   "Test Client",
		Email:  email,
		Status: "active",
	}
	result := config.DB.Create(&client)
	if result.Error != nil {
		panic("Failed to create test client: " + result.Error.Error())
	}

	// Create a test client device
	deviceName := "Test Device"
	deviceId := fmt.Sprintf("TEST%d", time.Now().UnixNano())
	clientDevice := models.ClientDevice{
		DeviceTypeId: deviceType.Id,
		ClientId:     client.Id,
		DeviceId:     deviceId,
		Name:         &deviceName,
	}
	result = config.DB.Create(&clientDevice)
	if result.Error != nil {
		panic("Failed to create test client device: " + result.Error.Error())
	}

	// Create a test alert
	alert := models.Alert{
		ClientDeviceId: clientDevice.Id,
		AlertType:      "test_alert",
		AlertTimestamp: time.Now(),
		Read:           nil, // Unread by default
		ReadAt:         nil,
	}
	result = config.DB.Create(&alert)
	if result.Error != nil {
		panic("Failed to create test alert: " + result.Error.Error())
	}

	return alert
}

func TestMarkAlertAsRead(t *testing.T) {
	setupMarkReadTest()
	alert := createTestAlert()

	// Get the client device to access client
	var clientDevice models.ClientDevice
	config.DB.First(&clientDevice, alert.ClientDeviceId)

	defer func() {
		config.DB.Where("id = ?", alert.Id).Delete(&models.Alert{})
		config.DB.Where("id = ?", alert.ClientDeviceId).Delete(&models.ClientDevice{})
		config.DB.Where("id = ?", clientDevice.ClientId).Delete(&models.Client{})
	}()

	router := gin.New()
	router.PUT("/alerts/:id/mark_read", MarkAlertAsRead)

	req, _ := http.NewRequest("PUT", "/alerts/"+fmt.Sprintf("%d", alert.Id)+"/mark_read", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, "Alert marked as read successfully", response["message"])

	// Verify alert was marked as read
	var updatedAlert models.Alert
	config.DB.First(&updatedAlert, alert.Id)
	assert.NotNil(t, updatedAlert.Read)
	assert.True(t, *updatedAlert.Read)
	assert.NotNil(t, updatedAlert.ReadAt)
}

func TestMarkAlertAsUnread(t *testing.T) {
	setupMarkReadTest()
	alert := createTestAlert()

	// Get the client device to access client
	var clientDevice models.ClientDevice
	config.DB.First(&clientDevice, alert.ClientDeviceId)

	// First mark as read
	read := true
	now := time.Now()
	alert.Read = &read
	alert.ReadAt = &now
	config.DB.Save(&alert)

	defer func() {
		config.DB.Where("id = ?", alert.Id).Delete(&models.Alert{})
		config.DB.Where("id = ?", alert.ClientDeviceId).Delete(&models.ClientDevice{})
		config.DB.Where("id = ?", clientDevice.ClientId).Delete(&models.Client{})
	}()

	router := gin.New()
	router.PUT("/alerts/:id/mark_unread", MarkAlertAsUnread)

	req, _ := http.NewRequest("PUT", "/alerts/"+fmt.Sprintf("%d", alert.Id)+"/mark_unread", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, "Alert marked as unread successfully", response["message"])

	// Verify alert was marked as unread
	var updatedAlert models.Alert
	config.DB.First(&updatedAlert, alert.Id)
	assert.NotNil(t, updatedAlert.Read)
	assert.False(t, *updatedAlert.Read)
	assert.Nil(t, updatedAlert.ReadAt)
}

func TestBulkMarkAlertsAsRead(t *testing.T) {
	setupMarkReadTest()

	// Create multiple test alerts
	alert1 := createTestAlert()
	alert2 := createTestAlert()

	defer config.DB.Where("id IN ?", []uint{alert1.Id, alert2.Id}).Delete(&models.Alert{})

	router := gin.New()
	router.PUT("/alerts/bulk/mark_read", BulkMarkAlertsAsRead)

	reqBody := map[string]interface{}{
		"alert_ids": []uint{alert1.Id, alert2.Id},
	}
	jsonBody, _ := json.Marshal(reqBody)

	req, _ := http.NewRequest("PUT", "/alerts/bulk/mark_read", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, "Alerts marked as read successfully", response["message"])
	assert.Equal(t, float64(2), response["updated_count"])

	// Verify both alerts were marked as read
	var updatedAlert1, updatedAlert2 models.Alert
	config.DB.First(&updatedAlert1, alert1.Id)
	config.DB.First(&updatedAlert2, alert2.Id)

	assert.NotNil(t, updatedAlert1.Read)
	assert.True(t, *updatedAlert1.Read)
	assert.NotNil(t, updatedAlert2.Read)
	assert.True(t, *updatedAlert2.Read)
}

func TestMarkAllAlertsAsRead(t *testing.T) {
	setupMarkReadTest()

	// Create multiple test alerts
	alert1 := createTestAlert()
	alert2 := createTestAlert()

	defer config.DB.Where("id IN ?", []uint{alert1.Id, alert2.Id}).Delete(&models.Alert{})

	router := gin.New()
	router.PUT("/alerts/mark_all_read", MarkAllAlertsAsRead)

	req, _ := http.NewRequest("PUT", "/alerts/mark_all_read", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	json.Unmarshal(w.Body.Bytes(), &response)
	assert.Equal(t, "All alerts marked as read successfully", response["message"])

	// Verify both alerts were marked as read
	var updatedAlert1, updatedAlert2 models.Alert
	config.DB.First(&updatedAlert1, alert1.Id)
	config.DB.First(&updatedAlert2, alert2.Id)

	assert.NotNil(t, updatedAlert1.Read)
	assert.True(t, *updatedAlert1.Read)
	assert.NotNil(t, updatedAlert2.Read)
	assert.True(t, *updatedAlert2.Read)
}
