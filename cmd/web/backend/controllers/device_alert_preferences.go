package controllers

import (
	"net/http"
	"strconv"

	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
)

// GetDeviceAlertPreferences retrieves all alert preferences for a specific device
func GetDeviceAlertPreferences(c *gin.Context) {
	deviceId, err := strconv.ParseUint(c.Param("deviceId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid device ID",
		})
		return
	}

	var preferences []models.DeviceAlertPreference
	if err := config.DB.Where("client_device_id = ?", deviceId).Preload("ClientDevice").Find(&preferences).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to retrieve device alert preferences",
			"error":   err.Error(),
		})
		return
	}

	c.<PERSON>SO<PERSON>(http.StatusOK, gin.H{
		"message": "Device alert preferences retrieved successfully",
		"data":    preferences,
	})
}

// GetDeviceAlertPreferenceById retrieves a specific device alert preference by ID
func GetDeviceAlertPreferenceById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid device alert preference ID",
		})
		return
	}

	var preference models.DeviceAlertPreference
	if err := config.DB.Preload("ClientDevice").First(&preference, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Device alert preference not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Device alert preference retrieved successfully",
		"data":    preference,
	})
}

// CreateDeviceAlertPreference creates a new device alert preference
func CreateDeviceAlertPreference(c *gin.Context) {
	deviceId, err := strconv.ParseUint(c.Param("deviceId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid device ID",
		})
		return
	}

	var req models.AlertPreferenceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// Verify device exists
	var device models.ClientDevice
	if err := config.DB.First(&device, deviceId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Device not found",
		})
		return
	}

	preference := models.DeviceAlertPreference{
		ClientDeviceId: uint(deviceId),
		AlertType:      req.AlertType,
		Enabled:        req.Enabled,
		Channels:       req.Channels,
		Priority:       req.Priority,
	}

	if err := config.DB.Create(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to create device alert preference",
			"error":   err.Error(),
		})
		return
	}

	// Reload with device data
	config.DB.Preload("ClientDevice").First(&preference, preference.Id)

	c.JSON(http.StatusCreated, gin.H{
		"message": "Device alert preference created successfully",
		"data":    preference,
	})
}

// UpdateDeviceAlertPreference updates an existing device alert preference
func UpdateDeviceAlertPreference(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid device alert preference ID",
		})
		return
	}

	var req models.AlertPreferenceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	var preference models.DeviceAlertPreference
	if err := config.DB.First(&preference, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Device alert preference not found",
		})
		return
	}

	// Update fields
	preference.AlertType = req.AlertType
	preference.Enabled = req.Enabled
	preference.Channels = req.Channels
	preference.Priority = req.Priority

	if err := config.DB.Save(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to update device alert preference",
			"error":   err.Error(),
		})
		return
	}

	// Reload with device data
	config.DB.Preload("ClientDevice").First(&preference, preference.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Device alert preference updated successfully",
		"data":    preference,
	})
}

// DeleteDeviceAlertPreference deletes a device alert preference
func DeleteDeviceAlertPreference(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid device alert preference ID",
		})
		return
	}

	var preference models.DeviceAlertPreference
	if err := config.DB.First(&preference, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Device alert preference not found",
		})
		return
	}

	if err := config.DB.Delete(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to delete device alert preference",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Device alert preference deleted successfully",
	})
}
