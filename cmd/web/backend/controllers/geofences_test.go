package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupBackendGeofencesTest(t *testing.T) (*gin.Engine, string) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupBackendGeofencesTestEnvVars()

	config.InitTestDB()

	// Create test user
	user := createBackendGeofencesTestUser(t)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())
	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/backend")
	v1.Use(middleware.AuthMiddleware())

	// Add geofences routes
	geofences := v1.Group("/geofences")
	geofences.GET("", GetAllGeofences)
	geofences.GET("/:id", GetGeofenceById)
	geofences.POST("", CreateGeofence)
	geofences.PUT("/:id", UpdateGeofence)
	geofences.DELETE("/:id", DeleteGeofence)
	geofences.GET("/search", SearchGeofences)
	geofences.GET("/events", GetGeofenceEvents)
	geofences.GET("/device/:device_id", GetGeofencesByDevice)

	return r, token
}

func setupBackendGeofencesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createBackendGeofencesTestUser(t *testing.T) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Backend Geofences Admin",
		UserType: "backend",
		Status:   &status,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func createBackendGeofencesTestClient(t *testing.T) models.Client {
	// Clean up any existing test data
	config.DB.Where("email LIKE ?", "<EMAIL>").Delete(&models.Client{})

	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Backend Geofences Test Client",
		Email:      "<EMAIL>",
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createBackendGeofencesTestDevice(t *testing.T, clientId uint) models.ClientDevice {
	status := "active"

	device := models.ClientDevice{
		ClientId:     clientId,
		Status:       status,
		DeviceTypeId: 1, // Use a valid device type ID
	}

	result := config.DB.Create(&device)
	assert.NoError(t, result.Error)

	return device
}

func createBackendGeofencesTestGeofence(t *testing.T, clientId uint) models.Geofence {
	name := "Test Geofence"
	geofenceType := "circle"
	latitude := 40.7128
	longitude := -74.0060
	radius := 100.0

	geofence := models.Geofence{
		ClientId:     clientId,
		Name:         name,
		GeofenceType: geofenceType,
		Latitude:     &latitude,
		Longitude:    &longitude,
		Radius:       &radius,
	}

	result := config.DB.Create(&geofence)
	assert.NoError(t, result.Error)

	return geofence
}

func createBackendGeofencesTestEvent(t *testing.T, deviceId uint, geofenceId uint, timestamp time.Time) models.GeofenceEvent {
	eventType := "enter"

	event := models.GeofenceEvent{
		ClientDeviceId: deviceId,
		GeofenceId:     geofenceId,
		EventTimestamp: timestamp,
		EventType:      eventType,
	}

	result := config.DB.Create(&event)
	assert.NoError(t, result.Error)

	return event
}

// Test GetAllGeofences functionality
func TestBackendGetAllGeofences(t *testing.T) {
	r, token := setupBackendGeofencesTest(t)

	// Create test client and geofences
	client := createBackendGeofencesTestClient(t)
	_ = createBackendGeofencesTestGeofence(t, client.Id)
	_ = createBackendGeofencesTestGeofence(t, client.Id)

	t.Run("Get all geofences successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data - check that we have at least 2 geofences with the expected name
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2)

		// Check that we have the expected geofences
		foundCount := 0
		for _, geofence := range data {
			if geofenceMap, ok := geofence.(map[string]interface{}); ok {
				if name, exists := geofenceMap["name"]; exists && name == "Test Geofence" {
					foundCount++
				}
			}
		}
		assert.GreaterOrEqual(t, foundCount, 2, "Expected to find at least 2 geofences with name 'Test Geofence'")

		// Check total count
		assert.GreaterOrEqual(t, response["total"], float64(2))
	})

	t.Run("Get all geofences without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetGeofenceById functionality
func TestBackendGetGeofenceById(t *testing.T) {
	r, token := setupBackendGeofencesTest(t)

	// Create test client and geofence
	client := createBackendGeofencesTestClient(t)
	geofence := createBackendGeofencesTestGeofence(t, client.Id)

	t.Run("Get geofence by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/"+strconv.FormatUint(uint64(geofence.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify geofence data
		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(geofence.Id), data["id"])
		assert.Equal(t, float64(client.Id), data["client_id"])
	})

	t.Run("Get geofence by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Get geofence by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/"+strconv.FormatUint(uint64(geofence.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test CreateGeofence functionality
func TestBackendCreateGeofence(t *testing.T) {
	r, token := setupBackendGeofencesTest(t)

	// Create test client
	client := createBackendGeofencesTestClient(t)

	t.Run("Create geofence successfully", func(t *testing.T) {
		geofenceData := map[string]interface{}{
			"name":          "New Test Geofence",
			"geofence_type": "circle",
			"applies_to":    "client",
			"latitude":      40.7128,
			"longitude":     -74.0060,
			"radius":        100.0,
		}

		jsonData, _ := json.Marshal(geofenceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/geofences?client_id="+strconv.FormatUint(uint64(client.Id), 10), bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "Geofence created successfully", response["message"])
	})

	t.Run("Create geofence with missing required fields", func(t *testing.T) {
		geofenceData := map[string]interface{}{
			"client_id": client.Id,
			"name":      "New Test Geofence",
			// Missing latitude, longitude, radius
		}

		jsonData, _ := json.Marshal(geofenceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/geofences", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create geofence without authorization", func(t *testing.T) {
		geofenceData := map[string]interface{}{
			"name":          "New Test Geofence",
			"geofence_type": "circle",
			"applies_to":    "client",
			"latitude":      40.7128,
			"longitude":     -74.0060,
			"radius":        100.0,
		}

		jsonData, _ := json.Marshal(geofenceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/geofences?client_id="+strconv.FormatUint(uint64(client.Id), 10), bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetGeofenceEvents functionality
func TestBackendGetGeofenceEvents(t *testing.T) {
	r, token := setupBackendGeofencesTest(t)

	// Create test client, device, geofence, and events
	client := createBackendGeofencesTestClient(t)
	device := createBackendGeofencesTestDevice(t, client.Id)
	geofence := createBackendGeofencesTestGeofence(t, client.Id)
	timestamp1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	timestamp2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)

	_ = createBackendGeofencesTestEvent(t, device.Id, geofence.Id, timestamp1)
	_ = createBackendGeofencesTestEvent(t, device.Id, geofence.Id, timestamp2)

	t.Run("Get geofence events successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/events?geofence_id="+strconv.FormatUint(uint64(geofence.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")

		// Verify data - check that we have at least 2 events for this geofence
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2)

		// Check that we have the expected events for this geofence
		foundCount := 0
		for _, event := range data {
			if eventMap, ok := event.(map[string]interface{}); ok {
				if geofenceId, exists := eventMap["geofence_id"]; exists && geofenceId == float64(geofence.Id) {
					foundCount++
				}
			}
		}
		assert.GreaterOrEqual(t, foundCount, 2, "Expected to find at least 2 events for this geofence")

		// Check total count
		assert.GreaterOrEqual(t, response["total"], float64(2))
	})

	t.Run("Get geofence events with date filtering", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/events?geofence_id="+strconv.FormatUint(uint64(geofence.Id), 10)+"&start_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data - check that we have at least 1 event for this geofence on the specified date
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 1)

		// Check that we have the expected events for this geofence
		foundCount := 0
		for _, event := range data {
			if eventMap, ok := event.(map[string]interface{}); ok {
				if geofenceId, exists := eventMap["geofence_id"]; exists && geofenceId == float64(geofence.Id) {
					foundCount++
				}
			}
		}
		assert.GreaterOrEqual(t, foundCount, 1, "Expected to find at least 1 event for this geofence on 2024-01-15")
	})

	t.Run("Get geofence events with date range", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/events?geofence_id="+strconv.FormatUint(uint64(geofence.Id), 10)+"&start_date=2024-01-15&end_date=2024-01-25", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data - check that we have at least 2 events for this geofence in the date range
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2)

		// Check that we have the expected events for this geofence
		foundCount := 0
		for _, event := range data {
			if eventMap, ok := event.(map[string]interface{}); ok {
				if geofenceId, exists := eventMap["geofence_id"]; exists && geofenceId == float64(geofence.Id) {
					foundCount++
				}
			}
		}
		assert.GreaterOrEqual(t, foundCount, 2, "Expected to find at least 2 events for this geofence in the date range")
	})

	t.Run("Get geofence events without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/events?geofence_id="+strconv.FormatUint(uint64(geofence.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test SearchGeofences functionality
func TestBackendSearchGeofences(t *testing.T) {
	r, token := setupBackendGeofencesTest(t)

	// Create test client and geofences
	client := createBackendGeofencesTestClient(t)
	_ = createBackendGeofencesTestGeofence(t, client.Id)
	_ = createBackendGeofencesTestGeofence(t, client.Id)

	t.Run("Search geofences by name", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/search?s=Test Geofence", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data - check that we have at least 2 geofences with "Test Geofence" in the name
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2)

		// Check that we have the expected geofences
		foundCount := 0
		for _, geofence := range data {
			if geofenceMap, ok := geofence.(map[string]interface{}); ok {
				if name, exists := geofenceMap["name"]; exists && name == "Test Geofence" {
					foundCount++
				}
			}
		}
		assert.GreaterOrEqual(t, foundCount, 2, "Expected to find at least 2 geofences with name 'Test Geofence'")
	})

	t.Run("Search geofences with geofence type filter", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/search?geofence_type=circle", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data - check that we have at least 2 geofences with type=circle
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2)

		// Check that we have the expected geofences
		foundCount := 0
		for _, geofence := range data {
			if geofenceMap, ok := geofence.(map[string]interface{}); ok {
				if geofenceType, exists := geofenceMap["geofence_type"]; exists && geofenceType == "circle" {
					foundCount++
				}
			}
		}
		assert.GreaterOrEqual(t, foundCount, 2, "Expected to find at least 2 geofences with type 'circle'")
	})

	t.Run("Search geofences with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/search?s=NONEXISTENT", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data - check that we don't find any geofences with "NONEXISTENT" in the name
		data := response["data"].([]interface{})

		// Check that we don't find any geofences with "NONEXISTENT" in the name
		found := false
		for _, geofence := range data {
			if geofenceMap, ok := geofence.(map[string]interface{}); ok {
				if name, exists := geofenceMap["name"]; exists {
					nameStr := name.(string)
					if nameStr == "NONEXISTENT" {
						found = true
						break
					}
				}
			}
		}
		assert.False(t, found, "Should not find any geofences with 'NONEXISTENT' in the name")
	})
}

// Test pagination functionality
func TestBackendGeofencesPagination(t *testing.T) {
	r, token := setupBackendGeofencesTest(t)

	// Create test client and multiple geofences for pagination testing
	client := createBackendGeofencesTestClient(t)
	for i := 1; i <= 15; i++ {
		createBackendGeofencesTestGeofence(t, client.Id)
	}

	t.Run("First page with default pagination", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify pagination values
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.GreaterOrEqual(t, response["total"], float64(15)) // At least 15 items (including previous test data)

		// Verify total is reasonable
		total := response["total"].(float64)
		assert.Greater(t, total, float64(0))

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 10)
	})

	t.Run("Second page", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count - should have remaining items on second page
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 1) // At least 1 item on second page
		assert.LessOrEqual(t, len(data), 10)   // No more than 10 items per page
	})
}

// Test edge cases and error handling
func TestBackendGeofencesEdgeCases(t *testing.T) {
	r, token := setupBackendGeofencesTest(t)

	t.Run("Invalid date format in query", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/events?start_date=invalid-date", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("End date before start date", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/events?start_date=2024-01-20&end_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid geofence ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/geofences/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}
