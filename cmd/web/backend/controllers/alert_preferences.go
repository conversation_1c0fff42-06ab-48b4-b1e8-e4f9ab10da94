package controllers

import (
	"net/http"
	"strconv"

	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
)

// GetAlertPreferences retrieves all alert preferences for a client
func GetAlertPreferences(c *gin.Context) {
	var preferences []models.AlertPreference

	if err := config.DB.Preload("Client").Find(&preferences).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to retrieve alert preferences",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert preferences retrieved successfully",
		"data":    preferences,
	})
}

// GetAlertPreferenceById retrieves a specific alert preference by ID
func GetAlertPreferenceById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"message": "Invalid alert preference ID",
		})
		return
	}

	var preference models.AlertPreference
	if err := config.DB.Preload("Client").First(&preference, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert preference not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert preference retrieved successfully",
		"data":    preference,
	})
}

// CreateAlertPreference creates a new alert preference
func CreateAlertPreference(c *gin.Context) {
	var req models.AlertPreferenceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// Get client ID from context (assuming it's set by middleware)
	clientId, exists := c.Get("client_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Client ID not found in context",
		})
		return
	}

	preference := models.AlertPreference{
		ClientId:  clientId.(uint),
		AlertType: req.AlertType,
		Enabled:   req.Enabled,
		Channels:  req.Channels,
		Priority:  req.Priority,
	}

	if err := config.DB.Create(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to create alert preference",
			"error":   err.Error(),
		})
		return
	}

	// Reload with client data
	config.DB.Preload("Client").First(&preference, preference.Id)

	c.JSON(http.StatusCreated, gin.H{
		"message": "Alert preference created successfully",
		"data":    preference,
	})
}

// UpdateAlertPreference updates an existing alert preference
func UpdateAlertPreference(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid alert preference ID",
		})
		return
	}

	var req models.AlertPreferenceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	var preference models.AlertPreference
	if err := config.DB.First(&preference, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert preference not found",
		})
		return
	}

	// Update fields
	preference.AlertType = req.AlertType
	preference.Enabled = req.Enabled
	preference.Channels = req.Channels
	preference.Priority = req.Priority

	if err := config.DB.Save(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to update alert preference",
			"error":   err.Error(),
		})
		return
	}

	// Reload with client data
	config.DB.Preload("Client").First(&preference, preference.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert preference updated successfully",
		"data":    preference,
	})
}

// DeleteAlertPreference deletes an alert preference
func DeleteAlertPreference(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid alert preference ID",
		})
		return
	}

	var preference models.AlertPreference
	if err := config.DB.First(&preference, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert preference not found",
		})
		return
	}

	if err := config.DB.Delete(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to delete alert preference",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert preference deleted successfully",
	})
}

// GetAlertPreferencesByClient retrieves alert preferences for a specific client
func GetAlertPreferencesByClient(c *gin.Context) {
	clientId, err := strconv.ParseUint(c.Param("clientId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid client ID",
		})
		return
	}

	var preferences []models.AlertPreference
	if err := config.DB.Where("client_id = ?", clientId).Preload("Client").Find(&preferences).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to retrieve alert preferences",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert preferences retrieved successfully",
		"data":    preferences,
	})
}
