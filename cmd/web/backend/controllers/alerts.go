package controllers

import (
	"log"
	"math"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
)

func GetAllAlerts(c *gin.Context) {
	var alerts []models.Alert
	var total int64
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		filter["alert_type"] = alertType
	}

	query := config.DB.Scopes(utils.Paginate(c)).Preload("ClientDevice").Preload("ClientDevice.Client").Where(filter)

	// Add date range filtering
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			query.Where("DATE(alert_timestamp) >= ? AND DATE(alert_timestamp) <= ?", startDate, endDate)
		} else {
			query.Where("DATE(alert_timestamp) = ?", startDate)
		}
	}

	query.Order("id desc").Find(&alerts)

	// Count with same filters
	countQuery := config.DB.Model(&models.Alert{}).Where(filter)
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			countQuery.Where("DATE(alert_timestamp) >= ? AND DATE(alert_timestamp) <= ?", startDate, endDate)
		} else {
			countQuery.Where("DATE(alert_timestamp) = ?", startDate)
		}
	}
	countQuery.Count(&total)
	// Extract current_page and per_page from query params (same logic as utils.Paginate)
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}
	perPage, _ := strconv.Atoi(c.Query("per_page"))
	switch {
	case perPage > 100:
		perPage = 100
	case perPage <= 0:
		perPage = 10
	}

	// Calculate total pages
	totalPages := int(math.Ceil(float64(total) / float64(perPage)))

	c.JSON(http.StatusOK, gin.H{
		"data":         alerts,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
		"total_pages":  totalPages,
	})
}

func GetAlertById(c *gin.Context) {
	// Validate ID format
	alertId := c.Param("id")
	if _, err := strconv.ParseUint(alertId, 10, 32); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid alert ID format",
		})
		return
	}

	var alert models.Alert
	if err := config.DB.Preload("GPSData").Preload("ClientDevice").Preload("ClientDevice.Client").First(&alert, alertId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Auto-mark as read if not already read
	if alert.Read == nil || !*alert.Read {
		read := true
		now := time.Now()
		alert.Read = &read
		alert.ReadAt = &now

		// Save the updated alert
		if err := config.DB.Save(&alert).Error; err != nil {
			log.Printf("Failed to mark alert as read: %v", err)
			// Continue with the response even if marking as read fails
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": alert,
	})
}

func CreateAlert(c *gin.Context) {

	var req models.CreateAlertRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	alert := models.Alert{
		ClientDeviceId: req.ClientDeviceId,
		DeviceId:       req.DeviceId,
		AlertType:      req.AlertType,
		AlertName:      req.AlertName,
		Message:        req.Message,
		RawData:        req.RawData,
		AdditionalData: req.AdditionalData,
		Speed:          req.Speed,
		Direction:      req.Direction,
		AlertTimestamp: req.AlertTimestamp,
	}
	result := config.DB.Create(&alert)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	// Send Slack notification asynchronously
	go func() {
		// Get client device for additional context
		var clientDevice models.ClientDevice
		config.DB.First(&clientDevice, req.ClientDeviceId)

		// Send Slack notification
		slackService, err := services.NewSlackService()
		if err != nil {
			log.Printf("Failed to initialize Slack service: %v", err)
			return
		}

		if err := slackService.SendAlert(&alert, &clientDevice); err != nil {
			log.Printf("Failed to send Slack alert: %v", err)
		}
	}()

	c.JSON(http.StatusCreated, gin.H{
		"message": "Alert created successfully",
	})
}

func UpdateAlert(c *gin.Context) {
	var req models.UpdateAlertRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var alert models.Alert
	if err := config.DB.Preload("GPSData").First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Update read status and timestamp
	if req.Read != nil {
		alert.Read = req.Read
		if *req.Read {
			now := time.Now()
			alert.ReadAt = &now
		} else {
			alert.ReadAt = nil
		}
	}

	result := config.DB.Save(&alert)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"message": "Alert updated successfully",
		"data":    alert,
	})
}

func DeleteAlert(c *gin.Context) {
	var alert models.Alert
	if err := config.DB.First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	result := config.DB.Delete(&alert)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusNoContent, gin.H{
		"message": "Alert deleted successfully",
	})
}

func SearchAlerts(c *gin.Context) {
	var alerts []models.Alert
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		filter["alert_type"] = alertType
	}

	query := config.DB.Where(filter).Preload("ClientDevice").Preload("ClientDevice.Client")

	// Add date range filtering
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			query.Where("DATE(alert_timestamp) >= ? AND DATE(alert_timestamp) <= ?", startDate, endDate)
		} else {
			query.Where("DATE(alert_timestamp) = ?", startDate)
		}
	}

	query.Order("id desc").Find(&alerts)
	c.JSON(http.StatusOK, gin.H{
		"data": alerts,
	})
}

// MarkAlertAsRead marks a single alert as read
func MarkAlertAsRead(c *gin.Context) {
	var alert models.Alert
	if err := config.DB.Preload("GPSData").First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Mark as read
	read := true
	now := time.Now()
	alert.Read = &read
	alert.ReadAt = &now

	result := config.DB.Save(&alert)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert marked as read successfully",
		"data":    alert,
	})
}

// MarkAlertAsUnread marks a single alert as unread
func MarkAlertAsUnread(c *gin.Context) {
	var alert models.Alert
	if err := config.DB.Preload("GPSData").First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Mark as unread
	read := false
	alert.Read = &read
	alert.ReadAt = nil

	result := config.DB.Save(&alert)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert marked as unread successfully",
		"data":    alert,
	})
}

// BulkMarkAlertsAsRead marks multiple alerts as read
func BulkMarkAlertsAsRead(c *gin.Context) {
	type BulkMarkRequest struct {
		AlertIds []uint `json:"alert_ids" binding:"required"`
	}

	var req BulkMarkRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	if len(req.AlertIds) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "No alert IDs provided",
		})
		return
	}

	// Update multiple alerts
	read := true
	now := time.Now()
	result := config.DB.Model(&models.Alert{}).
		Where("id IN ?", req.AlertIds).
		Updates(map[string]interface{}{
			"read":    &read,
			"read_at": &now,
		})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "Alerts marked as read successfully",
		"updated_count": result.RowsAffected,
	})
}

// MarkAllAlertsAsRead marks all alerts as read (with optional filters)
func MarkAllAlertsAsRead(c *gin.Context) {
	query := config.DB.Model(&models.Alert{})

	// Apply filters if provided
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		if id, err := strconv.Atoi(clientDeviceId); err == nil {
			query = query.Where("client_device_id = ?", id)
		}
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		query = query.Where("device_id = ?", deviceId)
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		query = query.Where("alert_type = ?", alertType)
	}

	// Only update unread alerts (escape 'read' as it's a reserved word in MySQL)
	query = query.Where("`read` IS NULL OR `read` = ?", false)

	read := true
	now := time.Now()
	result := query.Updates(map[string]interface{}{
		"read":    &read,
		"read_at": &now,
	})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "All alerts marked as read successfully",
		"updated_count": result.RowsAffected,
	})
}
