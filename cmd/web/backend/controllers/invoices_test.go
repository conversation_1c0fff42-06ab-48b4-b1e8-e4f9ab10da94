package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"strings"
	"testing"
	"time"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupBackendInvoicesTest(t *testing.T) (*gin.Engine, string) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupBackendTestEnvVars()

	config.InitTestDB()

	// Create test user
	user := createBackendTestUser(t)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())
	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/backend")
	v1.Use(middleware.AuthMiddleware())

	// Add invoices routes
	invoices := v1.Group("/invoices")
	invoices.GET("", GetAllInvoices)
	invoices.GET("/:id", GetInvoiceById)
	invoices.POST("", CreateInvoice)
	invoices.PUT("/:id", UpdateInvoice)
	invoices.DELETE("/:id", DeleteInvoice)
	invoices.GET("/search", SearchInvoices)

	return r, token
}

func setupBackendTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createBackendTestUser(t *testing.T) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Backend Admin",
		UserType: "backend",
		Status:   &status,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func createBackendTestClient(t *testing.T) models.Client {
	// Clean up existing test client
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.Client{})

	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Backend Test Client",
		Email:      "<EMAIL>",
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createBackendTestInvoice(t *testing.T, clientId uint, date time.Time) models.Invoice {
	amount := 100.0
	reference := "INV-BACKEND-001"
	status := "pending"

	invoice := models.Invoice{
		ClientId:  clientId,
		Date:      &date,
		Amount:    &amount,
		Status:    status,
		Reference: &reference,
	}

	result := config.DB.Create(&invoice)
	assert.NoError(t, result.Error)

	return invoice
}

// Test GetAllInvoices functionality
func TestBackendGetAllInvoices(t *testing.T) {
	r, token := setupBackendInvoicesTest(t)

	// Create test client and invoices
	client := createBackendTestClient(t)
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)

	_ = createBackendTestInvoice(t, client.Id, date1)
	_ = createBackendTestInvoice(t, client.Id, date2)

	t.Run("Get all invoices successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2) // Should have at least 2 invoices
		assert.GreaterOrEqual(t, response["total"], float64(2))
	})

	t.Run("Get all invoices without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetAllInvoices with date filtering
func TestBackendGetAllInvoicesWithDateFiltering(t *testing.T) {
	r, token := setupBackendInvoicesTest(t)

	// Create test client and invoices with different dates
	client := createBackendTestClient(t)
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)
	date3 := time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC)

	_ = createBackendTestInvoice(t, client.Id, date1)
	_ = createBackendTestInvoice(t, client.Id, date2)
	_ = createBackendTestInvoice(t, client.Id, date3)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedCount  int
	}{
		{
			name:           "Get invoices for specific date",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get invoices for date range",
			queryParams:    "?start_date=2024-01-15&end_date=2024-01-25",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:           "Get invoices with search and date filter",
			queryParams:    "?s=INV-BACKEND-001&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get invoices with status and date filter",
			queryParams:    "?status=pending&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.Contains(t, response, "data")
				assert.Contains(t, response, "total")

				// Verify data count
				data := response["data"].([]interface{})
				assert.GreaterOrEqual(t, len(data), tt.expectedCount)
			}
		})
	}
}

// Test GetInvoiceById functionality
func TestBackendGetInvoiceById(t *testing.T) {
	r, token := setupBackendInvoicesTest(t)

	// Create test client and invoice
	client := createBackendTestClient(t)
	date := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	invoice := createBackendTestInvoice(t, client.Id, date)

	t.Run("Get invoice by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices/"+strconv.FormatUint(uint64(invoice.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify invoice data
		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(invoice.Id), data["id"])
		assert.Equal(t, float64(client.Id), data["client_id"])
	})

	t.Run("Get invoice by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Get invoice by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices/"+strconv.FormatUint(uint64(invoice.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test CreateInvoice functionality
func TestBackendCreateInvoice(t *testing.T) {
	r, token := setupBackendInvoicesTest(t)

	// Create test client
	client := createBackendTestClient(t)

	t.Run("Create invoice successfully", func(t *testing.T) {
		invoiceData := map[string]interface{}{
			"client_id":   client.Id,
			"currency_id": 1, // Add required currency_id
			"date":        "2024-01-15T00:00:00Z",
			"amount":      150.0,
			"reference":   "INV-BACKEND-TEST-001",
			"status":      "draft",
		}

		jsonData, _ := json.Marshal(invoiceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/invoices", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "Invoice created successfully", response["message"])
	})

	t.Run("Create invoice with missing required fields", func(t *testing.T) {
		invoiceData := map[string]interface{}{
			"client_id": client.Id,
			// Missing date and amount
		}

		jsonData, _ := json.Marshal(invoiceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/invoices", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create invoice without authorization", func(t *testing.T) {
		invoiceData := map[string]interface{}{
			"client_id": client.Id,
			"date":      "2024-01-15T00:00:00Z",
			"amount":    150.0,
		}

		jsonData, _ := json.Marshal(invoiceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/invoices", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test SearchInvoices functionality
func TestBackendSearchInvoices(t *testing.T) {
	r, token := setupBackendInvoicesTest(t)

	// Create test client and invoices
	client := createBackendTestClient(t)
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)

	_ = createBackendTestInvoice(t, client.Id, date1)
	_ = createBackendTestInvoice(t, client.Id, date2)

	t.Run("Search invoices by reference", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices/search?s=INV-BACKEND-001", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2) // Should find at least 2 invoices with INV-BACKEND-001 in reference
	})

	t.Run("Search invoices with date filter", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices/search?start_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 1) // Should find at least one invoice on 2024-01-15
	})

	t.Run("Search invoices with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices/search?s=NONEXISTENT", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		// Check that we don't find any invoices with "NONEXISTENT" in the reference
		found := false
		for _, item := range data {
			invoice := item.(map[string]interface{})
			if reference, ok := invoice["reference"].(string); ok {
				if strings.Contains(reference, "NONEXISTENT") {
					found = true
					break
				}
			}
		}
		assert.False(t, found, "Should not find any invoices with 'NONEXISTENT' in reference")
	})
}

// Test pagination functionality
func TestBackendInvoicesPagination(t *testing.T) {
	r, token := setupBackendInvoicesTest(t)

	// Create test client and multiple invoices for pagination testing
	client := createBackendTestClient(t)
	for i := 1; i <= 15; i++ {
		date := time.Date(2024, 1, i, 0, 0, 0, 0, time.UTC)
		createBackendTestInvoice(t, client.Id, date)
	}

	t.Run("First page with default pagination", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")
		// Note: total_pages is not returned by the controller

		// Verify pagination values
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.GreaterOrEqual(t, response["total"], float64(15))

		// Verify data count
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 10)
	})

	t.Run("Second page", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 5) // Should have at least 5 items on second page
	})
}

// Test edge cases and error handling
func TestBackendInvoicesEdgeCases(t *testing.T) {
	r, token := setupBackendInvoicesTest(t)

	t.Run("Invalid date format in query", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices?start_date=invalid-date", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("End date before start date", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices?start_date=2024-01-20&end_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid invoice ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/invoices/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}
