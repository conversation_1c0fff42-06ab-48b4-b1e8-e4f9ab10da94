package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupBackendCurrenciesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}
	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func cleanupBackendCurrenciesTestData() {
	config.DB.Where("code IN (?)", []string{"TST1", "TST2", "TST", "NTC", "ORG", "UPD", "TCD", "SCO", "AC"}).Delete(&models.Currency{})
}

func setupBackendCurrenciesTest(t *testing.T) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendCurrenciesTestEnvVars()
	config.InitTestDB()
	cleanupBackendCurrenciesTestData()
}

func TestGetAllCurrencies(t *testing.T) {
	setupBackendCurrenciesTest(t)

	// Create test currencies
	currency1 := models.Currency{
		Name:   "Test Currency 1",
		Code:   "TST1",
		Active: true,
	}
	currency2 := models.Currency{
		Name:   "Test Currency 2",
		Code:   "TST2",
		Active: true,
	}
	config.DB.Create(&currency1)
	config.DB.Create(&currency2)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	currencies := v1.Group("/currencies")
	currencies.GET("", GetAllCurrencies)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/backend/currencies", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 5) // 3 seeded currencies (USD, ZAR, ZWG) + 2 test currencies
}

func TestGetCurrencyById(t *testing.T) {
	setupBackendCurrenciesTest(t)

	// Create test currency
	currency := models.Currency{
		Name:   "Test Currency",
		Code:   "TST",
		Active: true,
	}
	config.DB.Create(&currency)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	currencies := v1.Group("/currencies")
	currencies.GET("/:id", GetCurrencyById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/backend/currencies/%d", currency.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Currency", data["name"])
	assert.Equal(t, "TST", data["code"])
	assert.Equal(t, true, data["active"])
}

func TestGetCurrencyByIdNotFound(t *testing.T) {
	setupBackendCurrenciesTest(t)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	currencies := v1.Group("/currencies")
	currencies.GET("/:id", GetCurrencyById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/backend/currencies/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Currency not found", response["message"])
}

func TestCreateCurrency(t *testing.T) {
	setupBackendCurrenciesTest(t)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	currencies := v1.Group("/currencies")
	currencies.POST("", CreateCurrency)

	// Test data
	currencyData := models.CurrencyRequest{
		Name:   "New Test Currency",
		Code:   "NTC",
		Symbol: "$",
		Active: true,
	}

	jsonData, _ := json.Marshal(currencyData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/currencies", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Currency created successfully", response["message"])

	// Verify currency was created in database
	var currency models.Currency
	err = config.DB.Where("code = ?", "NTC").First(&currency).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Currency", currency.Name)
}

func TestCreateCurrencyInvalidRequest(t *testing.T) {
	setupBackendCurrenciesTest(t)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	currencies := v1.Group("/currencies")
	currencies.POST("", CreateCurrency)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Currency", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/currencies", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateCurrency(t *testing.T) {
	setupBackendCurrenciesTest(t)

	// Create test currency
	currency := models.Currency{
		Name:   "Original Currency",
		Code:   "ORG",
		Active: true,
	}
	config.DB.Create(&currency)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	currencies := v1.Group("/currencies")
	currencies.PUT("/:id", UpdateCurrency)

	// Test data
	updateData := models.CurrencyRequest{
		Name:   "Updated Currency",
		Code:   "UPD",
		Symbol: "€",
		Active: false,
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/backend/currencies/%d", currency.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Currency updated successfully", response["message"])

	// Verify currency was updated in database
	var updatedCurrency models.Currency
	err = config.DB.First(&updatedCurrency, currency.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "Updated Currency", updatedCurrency.Name)
}

func TestUpdateCurrencyNotFound(t *testing.T) {
	setupBackendCurrenciesTest(t)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	currencies := v1.Group("/currencies")
	currencies.PUT("/:id", UpdateCurrency)

	// Test data
	updateData := models.CurrencyRequest{
		Name:   "Updated Currency",
		Code:   "UPD",
		Symbol: "€",
		Active: true,
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/backend/currencies/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Currency not found", response["message"])
}

func TestDeleteCurrency(t *testing.T) {
	setupBackendCurrenciesTest(t)

	// Create test currency
	currency := models.Currency{
		Name:   "Test Currency to Delete",
		Code:   "TCD",
		Active: true,
	}
	config.DB.Create(&currency)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	currencies := v1.Group("/currencies")
	currencies.DELETE("/:id", DeleteCurrency)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/backend/currencies/%d", currency.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)

	// Verify currency was deleted from database
	var deletedCurrency models.Currency
	err := config.DB.First(&deletedCurrency, currency.Id).Error
	assert.Error(t, err) // Should not find the currency
}

func TestDeleteCurrencyNotFound(t *testing.T) {
	setupBackendCurrenciesTest(t)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	currencies := v1.Group("/currencies")
	currencies.DELETE("/:id", DeleteCurrency)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/backend/currencies/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Currency not found", response["message"])
}

func TestSearchCurrencies(t *testing.T) {
	setupBackendCurrenciesTest(t)

	// Create test currencies
	currency1 := models.Currency{
		Name:   "Searchable Currency One",
		Code:   "SCO",
		Active: true,
	}
	currency2 := models.Currency{
		Name:   "Another Currency",
		Code:   "AC",
		Active: true,
	}
	config.DB.Create(&currency1)
	config.DB.Create(&currency2)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	currencies := v1.Group("/currencies")
	currencies.GET("/search", SearchCurrencies)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/backend/currencies/search?s=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	// Should find "Searchable Currency One" plus any seeded currencies that match "Searchable"
	assert.GreaterOrEqual(t, len(data), 1)

	// Verify that our test currency is in the results
	found := false
	for _, item := range data {
		currency := item.(map[string]interface{})
		if currency["name"] == "Searchable Currency One" {
			found = true
			break
		}
	}
	assert.True(t, found, "Searchable Currency One should be in the results")
}

func TestSearchCurrenciesNoResults(t *testing.T) {
	setupBackendCurrenciesTest(t)

	// Create test currency
	currency := models.Currency{
		Name:   "Test Currency",
		Code:   "TST",
		Active: true,
	}
	config.DB.Create(&currency)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	currencies := v1.Group("/currencies")
	currencies.GET("/search", SearchCurrencies)

	// Test search with query parameter that won't match
	req, _ := http.NewRequest("GET", "/api/v1/backend/currencies/search?s=XYZ123", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	// Should find no results for "NonExistent"
	assert.Len(t, data, 0)
}
