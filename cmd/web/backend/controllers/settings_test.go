package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupBackendSettingsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func TestGetAllSettings(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendSettingsTestEnvVars()
	config.InitTestDB()

	// Clean up existing test settings to prevent duplicate key errors
	config.DB.Where("setting_key LIKE ?", "test_setting_%").Delete(&models.Setting{})

	// Create test settings
	setting1 := models.Setting{
		Name:         "Test Setting 1",
		SettingKey:   "test_setting_1",
		SettingValue: "test_value_1",
	}
	setting2 := models.Setting{
		Name:         "Test Setting 2",
		SettingKey:   "test_setting_2",
		SettingValue: "test_value_2",
	}
	config.DB.Create(&setting1)
	config.DB.Create(&setting2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	settings := v1.Group("/settings")
	settings.GET("", GetAllSettings)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/backend/settings", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(t, len(data), 2) // Should have at least 2 settings
}

func TestGetSettingByKey(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendSettingsTestEnvVars()
	config.InitTestDB()

	// Clean up existing test setting to prevent duplicate key errors
	config.DB.Where("setting_key = ?", "test_setting").Delete(&models.Setting{})

	// Create test setting
	setting := models.Setting{
		Name:         "Test Setting",
		SettingKey:   "test_setting",
		SettingValue: "test_value",
	}
	config.DB.Create(&setting)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	settings := v1.Group("/settings")
	settings.GET("/:key", GetSettingByKey)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/backend/settings/test_setting", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Setting", data["name"])
}

func TestGetSettingByKeyNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendSettingsTestEnvVars()
	config.InitTestDB()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	settings := v1.Group("/settings")
	settings.GET("/:key", GetSettingByKey)

	// Create request with non-existent key
	req, _ := http.NewRequest("GET", "/api/v1/backend/settings/nonexistent_setting", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Setting not found", response["message"])
}

func TestCreateSetting(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendSettingsTestEnvVars()
	config.InitTestDB()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	settings := v1.Group("/settings")
	settings.POST("", CreateSetting)

	// Clean up existing test setting to prevent duplicate key errors
	config.DB.Where("setting_key = ?", "new_test_setting").Delete(&models.Setting{})

	// Test data
	settingData := map[string]interface{}{
		"name":          "New Test Setting",
		"setting_key":   "new_test_setting",
		"setting_value": "new_test_value",
	}

	jsonData, _ := json.Marshal(settingData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/settings", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Setting created successfully", response["message"])

	// Verify setting was created in database
	var setting models.Setting
	err = config.DB.Where("setting_key = ?", "new_test_setting").First(&setting).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Setting", setting.Name)
}

func TestCreateSettingInvalidRequest(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendSettingsTestEnvVars()
	config.InitTestDB()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	settings := v1.Group("/settings")
	settings.POST("", CreateSetting)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Setting", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/settings", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateSetting(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendSettingsTestEnvVars()
	config.InitTestDB()

	// Clean up existing test setting to prevent duplicate key errors
	config.DB.Where("setting_key = ?", "original_setting").Delete(&models.Setting{})

	// Create test setting
	setting := models.Setting{
		Name:         "Original Setting",
		SettingKey:   "original_setting",
		SettingValue: "original_value",
	}
	config.DB.Create(&setting)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	settings := v1.Group("/settings")
	settings.PUT("/:key", UpdateSetting)

	// Test data
	updateData := map[string]interface{}{
		"setting_value": "updated_value",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/backend/settings/%s", setting.SettingKey), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Setting updated successfully", response["message"])

	// Verify setting was updated in database
	var updatedSetting models.Setting
	err = config.DB.Where("setting_key = ?", setting.SettingKey).First(&updatedSetting).Error
	assert.NoError(t, err)
	assert.Equal(t, "updated_value", updatedSetting.SettingValue) // Only setting_value is updated
}

func TestUpdateSettingNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendSettingsTestEnvVars()
	config.InitTestDB()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	settings := v1.Group("/settings")
	settings.PUT("/:key", UpdateSetting)

	// Test data
	updateData := map[string]interface{}{
		"setting_value": "updated_value",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent key
	req, _ := http.NewRequest("PUT", "/api/v1/backend/settings/nonexistent_key", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Setting not found", response["message"])
}

func TestDeleteSetting(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendSettingsTestEnvVars()
	config.InitTestDB()

	// Create test setting
	setting := models.Setting{
		Name:         "Test Setting to Delete",
		SettingKey:   "test_setting_delete",
		SettingValue: "test_value_delete",
	}
	config.DB.Create(&setting)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	settings := v1.Group("/settings")
	settings.DELETE("/:key", DeleteSetting)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/backend/settings/%s", setting.SettingKey), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)

	// 204 No Content should not have a response body
	// No need to unmarshal JSON or check message

	// Verify setting was deleted from database
	var deletedSetting models.Setting
	err := config.DB.First(&deletedSetting, setting.Id).Error
	assert.Error(t, err) // Should not find the setting
}

func TestDeleteSettingNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendSettingsTestEnvVars()
	config.InitTestDB()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	settings := v1.Group("/settings")
	settings.DELETE("/:key", DeleteSetting)

	// Create request with non-existent key
	req, _ := http.NewRequest("DELETE", "/api/v1/backend/settings/nonexistent_key", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Setting not found", response["message"])
}
