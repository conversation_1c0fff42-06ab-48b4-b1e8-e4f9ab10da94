package controllers

import (
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
)

func GetAllProtocols(c *gin.Context) {
	var protocols []models.Protocol
	var total int64
	config.DB.Scopes(utils.Paginate(c)).Find(&protocols)
	config.DB.Model(&models.Protocol{}).Count(&total)
	c.J<PERSON>(http.StatusOK, gin.H{
		"data":         protocols,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}
func GetProtocolById(c *gin.Context) {
	var protocol models.Protocol
	if err := config.DB.First(&protocol, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Protocol not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": protocol,
	})
}
func CreateProtocol(c *gin.Context) {
	var req models.ProtocolRequest
	if err := c.Bind<PERSON>(&req); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	protocol := models.Protocol{
		Name:   req.Name,
		Active: req.Active,
	}
	result := config.DB.Create(&protocol)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Protocol created successfully",
	})
}
func UpdateProtocol(c *gin.Context) {
	var req models.ProtocolRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var protocol models.Protocol
	if err := config.DB.First(&protocol, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Protocol not found",
		})
		return
	}
	protocol.Name = req.Name

	protocol.Active = req.Active
	result := config.DB.Save(&protocol)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Protocol updated successfully"})
}
func DeleteProtocol(c *gin.Context) {
	var protocol models.Protocol
	if err := config.DB.First(&protocol, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Protocol not found",
		})
		return
	}
	result := config.DB.Delete(&protocol)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusNoContent, gin.H{"message": "Protocol deleted successfully"})
}
func SearchProtocols(c *gin.Context) {
	var protocols []models.Protocol
	query := config.DB.Where("active=1")
	if search := c.Query("s"); search != "" {
		query.Where("name like ?", "%"+search+"%")
	}
	query.Find(&protocols)
	c.JSON(http.StatusOK, gin.H{
		"data": protocols,
	})
}
