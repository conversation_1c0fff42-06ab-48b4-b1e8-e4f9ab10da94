package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupBackendPaymentTypesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func TestGetAllPaymentTypes(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendPaymentTypesTestEnvVars()
	config.InitTestDB()

	// Create test payment types
	paymentType1 := models.PaymentType{
		Name:   "Test Payment Type 1",
		Active: true,
	}
	paymentType2 := models.PaymentType{
		Name:   "Test Payment Type 2",
		Active: true,
	}
	config.DB.Create(&paymentType1)
	config.DB.Create(&paymentType2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("", GetAllPaymentTypes)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/backend/payment_types", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(t, len(data), 2) // Should have at least 2 payment types
}

func TestGetPaymentTypeById(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendPaymentTypesTestEnvVars()
	config.InitTestDB()

	// Create test payment type
	paymentType := models.PaymentType{
		Name:   "Test Payment Type",
		Active: true,
	}
	config.DB.Create(&paymentType)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("/:id", GetPaymentTypeById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/backend/payment_types/%d", paymentType.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Payment Type", data["name"])
}

func TestGetPaymentTypeByIdNotFound(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendPaymentTypesTestEnvVars()
	config.InitTestDB()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("/:id", GetPaymentTypeById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/backend/payment_types/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Payment type not found", response["message"])
}

func TestCreatePaymentType(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendPaymentTypesTestEnvVars()
	config.InitTestDB()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.POST("", CreatePaymentType)

	// Test data
	paymentTypeData := models.PaymentTypeRequest{
		Name:   "New Test Payment Type",
		Active: true,
	}

	jsonData, _ := json.Marshal(paymentTypeData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/payment_types", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Payment type created successfully", response["message"])

	// Verify payment type was created in database
	var paymentType models.PaymentType
	err = config.DB.Where("name = ?", "New Test Payment Type").First(&paymentType).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Payment Type", paymentType.Name)
}

func TestCreatePaymentTypeInvalidRequest(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendPaymentTypesTestEnvVars()
	config.InitTestDB()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.POST("", CreatePaymentType)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Payment Type", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/payment_types", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdatePaymentType(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendPaymentTypesTestEnvVars()
	config.InitTestDB()

	// Create test payment type
	paymentType := models.PaymentType{
		Name:   "Original Payment Type",
		Active: true,
	}
	config.DB.Create(&paymentType)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.PUT("/:id", UpdatePaymentType)

	// Test data
	updateData := models.PaymentTypeRequest{
		Name:   "Updated Payment Type",
		Active: false,
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/backend/payment_types/%d", paymentType.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Payment type updated successfully", response["message"])

	// Verify payment type was updated in database
	var updatedPaymentType models.PaymentType
	err = config.DB.First(&updatedPaymentType, paymentType.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "Updated Payment Type", updatedPaymentType.Name)
}

func TestUpdatePaymentTypeNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendPaymentTypesTestEnvVars()
	config.InitTestDB()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.PUT("/:id", UpdatePaymentType)

	// Test data
	updateData := models.PaymentTypeRequest{
		Name:   "Updated Payment Type",
		Active: true,
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/backend/payment_types/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Payment type not found", response["message"])
}

func TestDeletePaymentType(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendPaymentTypesTestEnvVars()
	config.InitTestDB()

	// Create test payment type
	paymentType := models.PaymentType{
		Name:   "Test Payment Type to Delete",
		Active: true,
	}
	config.DB.Create(&paymentType)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.DELETE("/:id", DeletePaymentType)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/backend/payment_types/%d", paymentType.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)

	// 204 No Content should not have a response body
	// No need to unmarshal JSON or check message

	// Verify payment type was deleted from database
	var deletedPaymentType models.PaymentType
	err := config.DB.First(&deletedPaymentType, paymentType.Id).Error
	assert.Error(t, err) // Should not find the payment type
}

func TestDeletePaymentTypeNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendPaymentTypesTestEnvVars()
	config.InitTestDB()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.DELETE("/:id", DeletePaymentType)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/backend/payment_types/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Payment type not found", response["message"])
}

func TestSearchPaymentTypes(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendPaymentTypesTestEnvVars()
	config.InitTestDB()

	// Create test payment types
	paymentType1 := models.PaymentType{
		Name:   "Searchable Payment Type One",
		Active: true,
	}
	paymentType2 := models.PaymentType{
		Name:   "Another Payment Type",
		Active: true,
	}
	config.DB.Create(&paymentType1)
	config.DB.Create(&paymentType2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("/search", SearchPaymentTypes)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/backend/payment_types/search?s=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.GreaterOrEqual(t, len(data), 1) // Should find at least one payment type with "Searchable" in the name
}

func TestSearchPaymentTypesNoResults(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendPaymentTypesTestEnvVars()
	config.InitTestDB()

	// Create test payment type
	paymentType := models.PaymentType{
		Name:   "Test Payment Type",
		Active: true,
	}
	config.DB.Create(&paymentType)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("/search", SearchPaymentTypes)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/backend/payment_types/search?s=Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	// Check that we don't find any payment types with "NONEXISTENT" in the name
	found := false
	for _, item := range data {
		paymentType := item.(map[string]interface{})
		if name, ok := paymentType["name"].(string); ok {
			if strings.Contains(name, "NONEXISTENT") {
				found = true
				break
			}
		}
	}
	assert.False(t, found, "Should not find any payment types with 'NONEXISTENT' in name")
}
