package controllers

import (
	"net/http"
	"strconv"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

// GetAllSupportTickets returns all support tickets with filtering and pagination
func GetAllSupportTickets(c *gin.Context) {
	var filters models.SupportTicketFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Set default pagination if not provided
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.PerPage == 0 {
		filters.PerPage = 20
	}

	// Admin can filter by any client_id, no auto-assignment

	supportTicketService := services.NewSupportTicketService()
	tickets, total, err := supportTicketService.GetSupportTickets(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.<PERSON><PERSON><PERSON>(),
		})
		return
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"data":         tickets,
		"total":        total,
		"current_page": filters.Page,
		"per_page":     filters.PerPage,
		"last_page":    (total + int64(filters.PerPage) - 1) / int64(filters.PerPage),
	})
}

// GetSupportTicketById returns a specific support ticket
func GetSupportTicketById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid ticket ID",
		})
		return
	}

	// Admin can access any ticket without client restrictions

	// Admin access - get ticket without client restriction
	var tickets []models.SupportTicket
	err = config.DB.Preload("Client").Preload("CreatedBy").Preload("AssignedTo").Preload("ClientDevice").
		Where("id = ?", id).Find(&tickets).Error
	if err != nil || len(tickets) == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Ticket not found",
		})
		return
	}
	ticket := &tickets[0]

	c.JSON(http.StatusOK, gin.H{
		"data": ticket,
	})
}

// GetSupportTicketReplies returns all replies for a ticket
func GetSupportTicketReplies(c *gin.Context) {
	ticketId, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid ticket ID",
		})
		return
	}

	// Admin can access any ticket replies without client restrictions

	// Admin access - get replies including internal ones
	supportTicketService := services.NewSupportTicketService()
	replies, err := supportTicketService.GetSupportTicketRepliesAdmin(uint(ticketId))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": replies,
	})
}

// CreateSupportTicket creates a new support ticket
func CreateSupportTicket(c *gin.Context) {
	var req models.CreateSupportTicketRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Create ticket
	ticket := models.SupportTicket{
		ClientId:       req.ClientId,
		CreatedById:    req.CreatedById,
		ClientDeviceId: req.ClientDeviceId,
		Department:     req.Department,
		Priority:       req.Priority,
		Subject:        req.Subject,
		Description:    req.Description,
		Status:         "open",
	}

	supportTicketService := services.NewSupportTicketService()
	createdTicket, err := supportTicketService.CreateSupportTicket(ticket)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Support ticket created successfully",
		"data":    createdTicket,
	})
}

// UpdateSupportTicket updates an existing support ticket
func UpdateSupportTicket(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid ticket ID",
		})
		return
	}

	var req models.UpdateSupportTicketRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get existing ticket
	var existingTicket models.SupportTicket
	err = config.DB.Where("id = ?", id).First(&existingTicket).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Ticket not found",
		})
		return
	}

	// Admin can update any ticket - no client restrictions

	// Update ticket
	updateTicket := models.SupportTicket{
		Id:           uint(id),
		ClientId:     existingTicket.ClientId,
		AssignedToId: req.AssignedToId,
		Resolution:   req.Resolution,
	}

	// Only update fields that are provided
	if req.Priority != nil {
		updateTicket.Priority = *req.Priority
	}
	if req.Status != nil {
		updateTicket.Status = *req.Status
	}
	if req.Subject != nil {
		updateTicket.Subject = *req.Subject
	}
	if req.Description != nil {
		updateTicket.Description = *req.Description
	}

	supportTicketService := services.NewSupportTicketService()
	updatedTicket, err := supportTicketService.UpdateSupportTicket(updateTicket)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Support ticket updated successfully",
		"data":    updatedTicket,
	})
}

// ChangeSupportTicketStatus changes the status of a support ticket (admin version)
func ChangeSupportTicketStatus(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid ticket ID",
		})
		return
	}

	var req models.ChangeSupportTicketStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get existing ticket
	var existingTicket models.SupportTicket
	err = config.DB.Where("id = ?", id).First(&existingTicket).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Ticket not found",
		})
		return
	}

	// Admin can change status of any ticket - no client restrictions

	supportTicketService := services.NewSupportTicketService()
	updatedTicket, err := supportTicketService.ChangeSupportTicketStatus(uint(id), existingTicket.ClientId, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Support ticket status changed successfully",
		"data":    updatedTicket,
	})
}

// AssignSupportTicket assigns a support ticket to a user (admin version)
func AssignSupportTicket(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid ticket ID",
		})
		return
	}

	var req models.AssignSupportTicketRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Admin can assign any ticket - no client restrictions

	supportTicketService := services.NewSupportTicketService()
	updatedTicket, err := supportTicketService.AssignSupportTicket(uint(id), req.AssignedToId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Support ticket assigned successfully",
		"data":    updatedTicket,
	})
}

// CreateSupportTicketReply creates a new reply to a support ticket
func CreateSupportTicketReply(c *gin.Context) {
	var req models.CreateSupportTicketReplyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}
	ticketId := c.Param("id")
	ticketIdUint, err := strconv.ParseUint(ticketId, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid ticket ID",
		})
		return
	}
	// Get current user for admin context
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)

	// Create reply
	reply := models.SupportTicketReply{
		TicketId:    uint(ticketIdUint),
		CreatedById: currentUser.Id,
		IsInternal:  req.IsInternal,
		Message:     req.Message,
	}

	supportTicketService := services.NewSupportTicketService()
	createdReply, err := supportTicketService.CreateSupportTicketReply(reply)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Reply added successfully",
		"data":    createdReply,
	})
}

// GetSupportTicketStats returns ticket statistics
func GetSupportTicketStats(c *gin.Context) {
	// Admin can get stats for all clients or specific client, and filter by assigned_to

	// Parse optional filters
	var clientId uint
	if clientIdParam := c.Query("client_id"); clientIdParam != "" {
		if parsed, err := strconv.ParseUint(clientIdParam, 10, 32); err == nil {
			clientId = uint(parsed)
		}
	}

	var assignedToId uint
	if assignedToParam := c.Query("assigned_to_id"); assignedToParam != "" {
		if parsed, err := strconv.ParseUint(assignedToParam, 10, 32); err == nil {
			assignedToId = uint(parsed)
		}
	}

	supportTicketService := services.NewSupportTicketService()
	stats, err := supportTicketService.GetSupportTicketStatsWithFilters(clientId, assignedToId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}

// DeleteSupportTicket deletes a support ticket
func DeleteSupportTicket(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid ticket ID",
		})
		return
	}

	// Get existing ticket
	var existingTicket models.SupportTicket
	err = config.DB.Where("id = ?", id).First(&existingTicket).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Ticket not found",
		})
		return
	}

	// Admin can delete any ticket - no client restrictions

	supportTicketService := services.NewSupportTicketService()
	err = supportTicketService.DeleteSupportTicket(uint(id), existingTicket.ClientId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Support ticket deleted successfully",
	})
}

// SearchSupportTickets searches support tickets
func SearchSupportTickets(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Search query is required",
		})
		return
	}

	// Admin can search all tickets without client restrictions

	var tickets []models.SupportTicket
	dbQuery := config.DB.Preload("Client").Preload("CreatedBy").Preload("AssignedTo").Preload("ClientDevice").
		Where("subject LIKE ? OR description LIKE ?", "%"+query+"%", "%"+query+"%")

	// Admin can search all tickets - no client filter applied

	err := dbQuery.Order("created_at DESC").Find(&tickets).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": tickets,
	})
}
