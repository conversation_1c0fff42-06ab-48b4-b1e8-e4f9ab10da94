package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupBackendFleetsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}
	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createBackendFleetsTestUser(t *testing.T) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	// Create a test client first
	client := models.Client{
		Name:       "Backend Fleets Test Client",
		Email:      "<EMAIL>",
		Status:     "active",
		ClientType: "individual",
	}
	config.DB.Create(&client)

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Backend Fleets Admin",
		UserType: "backend",
		Status:   &status,
		ClientId: &client.Id,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func setupBackendFleetsTestWithAuth(t *testing.T) (*gin.Engine, *gin.RouterGroup, string) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendFleetsTestEnvVars()
	config.InitTestDB()

	// Create test user and generate token
	user := createBackendFleetsTestUser(t)
	token, err := services.GenerateToken(&user, "access")
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// Setup router with authentication
	router := gin.Default()
	router.Use(middleware.CorsMiddleware())
	v1 := router.Group("/api/v1/backend")
	v1.Use(middleware.AuthMiddleware())

	return router, v1, token
}

func TestGetAllFleets(t *testing.T) {
	// Setup test environment with fresh database
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendFleetsTestEnvVars()
	services.FastCleanupTestData()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleets
	fleet1 := models.Fleet{
		ClientId: client.Id,
		Name:     "Test Fleet 1",
	}
	fleet2 := models.Fleet{
		ClientId: client.Id,
		Name:     "Test Fleet 2",
	}
	config.DB.Create(&fleet1)
	config.DB.Create(&fleet2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	fleets := v1.Group("/clients/:id/fleets")
	fleets.GET("", GetAllFleets)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/backend/clients/%d/fleets", client.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 2)
}

func TestGetFleetById(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleet
	fleet := models.Fleet{
		ClientId: client.Id,
		Name:     "Test Fleet",
	}
	config.DB.Create(&fleet)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	fleets := v1.Group("/fleets")
	fleets.GET("/:id", GetFleetById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/backend/fleets/%d", fleet.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Fleet", data["name"])
}

func TestGetFleetByIdNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	fleets := v1.Group("/fleets")
	fleets.GET("/:id", GetFleetById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/backend/fleets/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Fleet not found", response["message"])
}

func TestCreateFleet(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendFleetsTestEnvVars()
	config.InitTestDB()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client Create Fleet",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	fleets := v1.Group("/clients/:id/fleets")
	fleets.POST("", CreateFleet)

	// Test data
	fleetData := models.FleetRequest{
		Name: "New Test Fleet",
	}

	jsonData, _ := json.Marshal(fleetData)

	// Create request
	req, _ := http.NewRequest("POST", fmt.Sprintf("/api/v1/backend/clients/%d/fleets", client.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Fleet created successfully", response["message"])

	// Verify fleet was created in database
	var fleet models.Fleet
	err = config.DB.Where("name = ?", "New Test Fleet").First(&fleet).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Fleet", fleet.Name)
	// Note: fleet.ClientId should match the client ID from the URL parameter, not necessarily the created client
}

func TestCreateFleetInvalidRequest(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	fleets := v1.Group("/clients/:id/fleets")
	fleets.POST("", CreateFleet)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Fleet", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", fmt.Sprintf("/api/v1/backend/clients/%d/fleets", client.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateFleet(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleet
	fleet := models.Fleet{
		ClientId: client.Id,
		Name:     "Original Fleet",
	}
	config.DB.Create(&fleet)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	fleets := v1.Group("/fleets")
	fleets.PUT("/:id", UpdateFleet)

	// Test data
	updateData := models.FleetRequest{
		Name: "Updated Fleet",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/backend/fleets/%d", fleet.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Fleet updated successfully", response["message"])

	// Verify fleet was updated in database
	var updatedFleet models.Fleet
	err = config.DB.First(&updatedFleet, fleet.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "Updated Fleet", updatedFleet.Name)
}

func TestUpdateFleetNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	fleets := v1.Group("/fleets")
	fleets.PUT("/:id", UpdateFleet)

	// Test data
	updateData := models.FleetRequest{
		Name: "Updated Fleet",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/backend/fleets/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Fleet not found", response["message"])
}

func TestDeleteFleet(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleet
	fleet := models.Fleet{
		ClientId: client.Id,
		Name:     "Test Fleet to Delete",
	}
	config.DB.Create(&fleet)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	fleets := v1.Group("/fleets")
	fleets.DELETE("/:id", DeleteFleet)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/backend/fleets/%d", fleet.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)

	// 204 No Content should not have a response body
	// No need to unmarshal JSON or check message

	// Verify fleet was deleted from database
	var deletedFleet models.Fleet
	err := config.DB.First(&deletedFleet, fleet.Id).Error
	assert.Error(t, err) // Should not find the fleet
}

func TestDeleteFleetNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	fleets := v1.Group("/fleets")
	fleets.DELETE("/:id", DeleteFleet)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/backend/fleets/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Fleet not found", response["message"])
}

func TestSearchFleets(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendFleetsTestEnvVars()
	config.InitTestDB()

	// Clean up any existing test data
	config.DB.Where("email LIKE ?", "<EMAIL>").Delete(&models.Client{})

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client Search Fleets",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleets
	fleet1 := models.Fleet{
		ClientId: client.Id,
		Name:     "Searchable Fleet One",
	}
	fleet2 := models.Fleet{
		ClientId: client.Id,
		Name:     "Another Fleet",
	}
	config.DB.Create(&fleet1)
	config.DB.Create(&fleet2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	fleets := v1.Group("/fleets")
	fleets.GET("/search", SearchFleets)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/backend/fleets/search?s=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})

	// Check if we found the expected fleet
	found := false
	for _, fleet := range data {
		if fleetMap, ok := fleet.(map[string]interface{}); ok {
			if name, exists := fleetMap["name"]; exists && name == "Searchable Fleet One" {
				found = true
				break
			}
		}
	}
	assert.True(t, found, "Expected to find 'Searchable Fleet One' in search results")
}

func TestSearchFleetsNoResults(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendFleetsTestEnvVars()
	config.InitTestDB()

	// Clean up any existing test data
	config.DB.Where("email LIKE ?", "<EMAIL>").Delete(&models.Client{})

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client Search No Results",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleet
	fleet := models.Fleet{
		ClientId: client.Id,
		Name:     "Test Fleet",
	}
	config.DB.Create(&fleet)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	fleets := v1.Group("/fleets")
	fleets.GET("/search", SearchFleets)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/backend/fleets/search?s=Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})

	// Check if we found any fleets with "Nonexistent" in the name
	found := false
	for _, fleet := range data {
		if fleetMap, ok := fleet.(map[string]interface{}); ok {
			if name, exists := fleetMap["name"]; exists {
				nameStr := name.(string)
				if nameStr == "Nonexistent" {
					found = true
					break
				}
			}
		}
	}
	assert.False(t, found, "Should not find any fleets with 'Nonexistent' in the name")
}
