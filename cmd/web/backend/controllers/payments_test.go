package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupBackendPaymentsTest(t *testing.T) (*gin.Engine, string) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupBackendPaymentsTestEnvVars()

	config.InitTestDB()

	// Create test user
	user := createBackendPaymentsTestUser(t)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())
	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/backend")
	v1.Use(middleware.AuthMiddleware())

	// Add payment routes
	payments := v1.Group("/payments")
	payments.GET("", GetAllPayments)
	payments.GET("/:id", GetPaymentById)
	payments.POST("", CreatePayment)
	payments.PUT("/:id", UpdatePayment)
	payments.DELETE("/:id", DeletePayment)
	payments.GET("/search", SearchPayments)

	// Add some basic routes for testing
	v1.GET("/test", func(c *gin.Context) { c.JSON(200, gin.H{"message": "test"}) })

	return r, token
}

func setupBackendPaymentsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createBackendPaymentsTestUser(t *testing.T) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Backend Payments Admin",
		UserType: "backend",
		Status:   &status,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func createBackendPaymentsTestClient(t *testing.T) models.Client {
	// Clean up existing test client to prevent duplicate email errors
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.Client{})

	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Backend Payments Test Client",
		Email:      "<EMAIL>",
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createBackendPaymentsTestInvoice(t *testing.T, clientId uint, date time.Time) models.Invoice {
	amount := 100.0
	baseCurrencyAmount := 100.0
	reference := "INV-BACKEND-PAY-001"
	status := "pending"

	invoice := models.Invoice{
		ClientId:           clientId,
		Date:               &date,
		Amount:             &amount,
		BaseCurrencyAmount: &baseCurrencyAmount,
		Status:             status,
		Reference:          &reference,
	}

	result := config.DB.Create(&invoice)
	assert.NoError(t, result.Error)

	return invoice
}

func createBackendPaymentsTestPayment(t *testing.T, invoiceId uint, date time.Time) models.InvoicePayment {
	amount := 100.0
	transId := "TXN-BACKEND-PAY-001"

	payment := models.InvoicePayment{
		InvoiceId: invoiceId,
		Date:      &date,
		Amount:    amount,
		TransId:   &transId,
	}

	result := config.DB.Create(&payment)
	assert.NoError(t, result.Error)

	return payment
}

// Test GetAllPayments functionality
func TestBackendGetAllPayments(t *testing.T) {
	r, token := setupBackendPaymentsTest(t)

	// Create test client, invoice and payments
	client := createBackendPaymentsTestClient(t)
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)

	invoice1 := createBackendPaymentsTestInvoice(t, client.Id, date1)
	invoice2 := createBackendPaymentsTestInvoice(t, client.Id, date2)

	_ = createBackendPaymentsTestPayment(t, invoice1.Id, date1)
	_ = createBackendPaymentsTestPayment(t, invoice2.Id, date2)

	t.Run("Get all payments successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2)                  // Should have at least 2 payments
		assert.GreaterOrEqual(t, response["total"], float64(2)) // Total should be at least 2
	})

	t.Run("Get all payments without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetAllPayments with date filtering
func TestBackendGetAllPaymentsWithDateFiltering(t *testing.T) {
	r, token := setupBackendPaymentsTest(t)

	// Create test client, invoices and payments with different dates
	client := createBackendPaymentsTestClient(t)
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)
	date3 := time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC)

	invoice1 := createBackendPaymentsTestInvoice(t, client.Id, date1)
	invoice2 := createBackendPaymentsTestInvoice(t, client.Id, date2)
	invoice3 := createBackendPaymentsTestInvoice(t, client.Id, date3)

	_ = createBackendPaymentsTestPayment(t, invoice1.Id, date1)
	_ = createBackendPaymentsTestPayment(t, invoice2.Id, date2)
	_ = createBackendPaymentsTestPayment(t, invoice3.Id, date3)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedCount  int
	}{
		{
			name:           "Get payments for specific date",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get payments for date range",
			queryParams:    "?start_date=2024-01-15&end_date=2024-01-25",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:           "Get payments with search and date filter",
			queryParams:    "?s=TXN-BACKEND-PAY-001&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get payments with invoice filter and date",
			queryParams:    "?invoice_id=" + strconv.FormatUint(uint64(invoice1.Id), 10) + "&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.Contains(t, response, "data")
				assert.Contains(t, response, "total")

				// Verify data count
				data := response["data"].([]interface{})
				assert.GreaterOrEqual(t, len(data), tt.expectedCount) // Should have at least the expected count
			}
		})
	}
}

// Test GetPaymentById functionality
func TestBackendGetPaymentById(t *testing.T) {
	r, token := setupBackendPaymentsTest(t)

	// Create test client, invoice and payment
	client := createBackendPaymentsTestClient(t)
	date := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	invoice := createBackendPaymentsTestInvoice(t, client.Id, date)
	payment := createBackendPaymentsTestPayment(t, invoice.Id, date)

	t.Run("Get payment by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments/"+strconv.FormatUint(uint64(payment.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify payment data
		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(payment.Id), data["id"])
		assert.Equal(t, float64(invoice.Id), data["invoice_id"])
	})

	t.Run("Get payment by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Get payment by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments/"+strconv.FormatUint(uint64(payment.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test CreatePayment functionality
func TestBackendCreatePayment(t *testing.T) {
	r, token := setupBackendPaymentsTest(t)

	// Create test client and invoice
	client := createBackendPaymentsTestClient(t)
	date := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	invoice := createBackendPaymentsTestInvoice(t, client.Id, date)

	t.Run("Create payment successfully", func(t *testing.T) {
		paymentData := map[string]interface{}{
			"invoice_id":      invoice.Id,
			"payment_type_id": 1,
			"currency_id":     1,
			"date":            "2024-01-15T00:00:00Z",
			"amount":          150.0,
			"trans_id":        "TXN-BACKEND-CREATE-001",
		}

		jsonData, _ := json.Marshal(paymentData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/payments", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "Payment created successfully", response["message"])
	})

	t.Run("Create payment with missing required fields", func(t *testing.T) {
		paymentData := map[string]interface{}{
			"invoice_id": invoice.Id,
			// Missing date and amount
		}

		jsonData, _ := json.Marshal(paymentData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/payments", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create payment with non-existent invoice", func(t *testing.T) {
		paymentData := map[string]interface{}{
			"invoice_id":      99999,
			"payment_type_id": 1,
			"currency_id":     1,
			"date":            "2024-01-15T00:00:00Z",
			"amount":          150.0,
		}

		jsonData, _ := json.Marshal(paymentData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/payments", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code) // Controller creates payment even for non-existent invoice
	})

	t.Run("Create payment without authorization", func(t *testing.T) {
		paymentData := map[string]interface{}{
			"invoice_id":      invoice.Id,
			"payment_type_id": 1,
			"currency_id":     1,
			"date":            "2024-01-15T00:00:00Z",
			"amount":          150.0,
		}

		jsonData, _ := json.Marshal(paymentData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/payments", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test SearchPayments functionality
func TestBackendSearchPayments(t *testing.T) {
	r, token := setupBackendPaymentsTest(t)

	// Create test client, invoices and payments
	client := createBackendPaymentsTestClient(t)
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)

	invoice1 := createBackendPaymentsTestInvoice(t, client.Id, date1)
	invoice2 := createBackendPaymentsTestInvoice(t, client.Id, date2)

	_ = createBackendPaymentsTestPayment(t, invoice1.Id, date1)
	_ = createBackendPaymentsTestPayment(t, invoice2.Id, date2)

	t.Run("Search payments by transaction ID", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments/search?s=TXN-BACKEND-PAY-001", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2) // Should find at least 2 payments with TXN-BACKEND-PAY-001 in trans_id
	})

	t.Run("Search payments with date filter", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments/search?start_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 1) // Should find at least one payment on 2024-01-15
	})

	t.Run("Search payments with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments/search?s=NONEXISTENT", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // No results
	})
}

// Test pagination functionality
func TestBackendPaymentsPagination(t *testing.T) {
	r, token := setupBackendPaymentsTest(t)

	// Create test client, invoices and multiple payments for pagination testing
	client := createBackendPaymentsTestClient(t)
	for i := 1; i <= 15; i++ {
		date := time.Date(2024, 1, i, 0, 0, 0, 0, time.UTC)
		invoice := createBackendPaymentsTestInvoice(t, client.Id, date)
		createBackendPaymentsTestPayment(t, invoice.Id, date)
	}

	t.Run("First page with default pagination", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify pagination values
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.GreaterOrEqual(t, response["total"], float64(15)) // Total should be at least 15

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 10)
	})

	t.Run("Second page", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count
		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 5) // Should have at least 5 items on second page
	})
}

// Test edge cases and error handling
func TestBackendPaymentsEdgeCases(t *testing.T) {
	r, token := setupBackendPaymentsTest(t)

	t.Run("Invalid date format in query", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments?start_date=invalid-date", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("End date before start date", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments?start_date=2024-01-20&end_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid payment ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/payments/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code) // Controller treats invalid ID as non-existent
	})
}
