package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGetAllClientDevices(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupClientDevicesTestEnvVars()

	config.InitTestDB()

	// Clean up existing test data
	cleanupClientDevicesTestData()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test client devices
	device1 := models.ClientDevice{
		ClientId:  client.Id,
		DeviceId:  "123456789012345",
		AssetType: "vehicle",
		Status:    "active",
	}
	device2 := models.ClientDevice{
		ClientId:  client.Id,
		DeviceId:  "123456789012346",
		AssetType: "vehicle",
		Status:    "active",
	}
	config.DB.Create(&device1)
	config.DB.Create(&device2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	devices := v1.Group("/clients/:id/devices")
	devices.GET("", GetAllClientDevices)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/backend/clients/%d/devices", client.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 2)
}

func TestGetClientDeviceById(t *testing.T) {
	// Setup test environment
	setupClientDevicesTest(t)

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test client device
	deviceName := "Test Device"
	device := models.ClientDevice{
		ClientId:  client.Id,
		DeviceId:  "123456789012345",
		AssetType: "vehicle",
		Status:    "active",
		Name:      &deviceName,
	}
	config.DB.Create(&device)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	devices := v1.Group("/client-devices")
	devices.GET("/:id", GetClientDeviceById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/backend/client-devices/%d", device.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Device", data["name"])
}

func TestGetClientDeviceByIdNotFound(t *testing.T) {
	// Setup test environment
	setupClientDevicesTest(t)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	devices := v1.Group("/client-devices")
	devices.GET("/:id", GetClientDeviceById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/backend/client-devices/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Client device not found", response["message"])
}

func TestCreateClientDevice(t *testing.T) {
	// Setup test environment
	setupClientDevicesTest(t)

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create a test protocol first
	protocol := models.Protocol{
		Name:   "Test Protocol",
		Active: true,
	}
	config.DB.Create(&protocol)

	// Create a test device type
	deviceType := models.DeviceType{
		ProtocolId: protocol.Id,
		Name:       "Test Device Type",
		Active:     true,
	}
	config.DB.Create(&deviceType)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	devices := v1.Group("/clients/:id/devices")
	devices.POST("", CreateClientDevice)

	// Test data
	deviceName := "New Test Device"
	deviceData := models.ClientDeviceRequest{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "123456789012347",
		AssetType:    "vehicle",
		Name:         &deviceName,
	}

	jsonData, _ := json.Marshal(deviceData)

	// Create a test user and generate token
	user := models.User{
		Email:    "<EMAIL>",
		Password: "password",
		UserType: "backend",
	}
	config.DB.Create(&user)
	token, _ := services.GenerateToken(&user, "access")

	// Create request
	req, _ := http.NewRequest("POST", fmt.Sprintf("/api/v1/backend/clients/%d/devices", client.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Client device created successfully", response["message"])

	// Verify device was created in database
	var device models.ClientDevice
	err = config.DB.Where("name = ?", "New Test Device").First(&device).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Device", *device.Name)
	assert.Equal(t, client.Id, device.ClientId)
}

func TestCreateClientDeviceInvalidRequest(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	devices := v1.Group("/clients/:id/devices")
	devices.POST("", CreateClientDevice)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Device", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", fmt.Sprintf("/api/v1/backend/clients/%d/devices", client.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateClientDevice(t *testing.T) {
	// Setup test environment
	setupClientDevicesTest(t)

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create a test protocol first
	protocol := models.Protocol{
		Name:   "Test Protocol",
		Active: true,
	}
	config.DB.Create(&protocol)

	// Create a test device type
	deviceType := models.DeviceType{
		ProtocolId: protocol.Id,
		Name:       "Test Device Type",
		Active:     true,
	}
	config.DB.Create(&deviceType)

	// Create test client device
	device := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "123456789012345",
		AssetType:    "vehicle",
		Status:       "active",
	}
	config.DB.Create(&device)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	devices := v1.Group("/client-devices")
	devices.PUT("/:id", UpdateClientDevice)

	// Test data
	updatedName := "Updated Device"
	updateData := models.ClientDeviceRequest{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		AssetType:    "vehicle",
		Name:         &updatedName,
		DeviceId:     "123456789012348",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/backend/client-devices/%d", device.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Client device updated successfully", response["message"])

	// Verify device was updated in database
	var updatedDevice models.ClientDevice
	err = config.DB.First(&updatedDevice, device.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "Updated Device", *updatedDevice.Name)
}

func TestUpdateClientDeviceNotFound(t *testing.T) {
	// Setup test environment
	setupClientDevicesTest(t)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	devices := v1.Group("/client-devices")
	devices.PUT("/:id", UpdateClientDevice)

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create a test protocol first
	protocol := models.Protocol{
		Name:   "Test Protocol",
		Active: true,
	}
	config.DB.Create(&protocol)

	// Create a test device type
	deviceType := models.DeviceType{
		ProtocolId: protocol.Id,
		Name:       "Test Device Type",
		Active:     true,
	}
	config.DB.Create(&deviceType)

	// Test data
	updatedName := "Updated Device"
	updateData := models.ClientDeviceRequest{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		AssetType:    "vehicle",
		Name:         &updatedName,
		DeviceId:     "123456789012348",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/backend/client-devices/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Client device not found", response["message"])
}

func TestDeleteClientDevice(t *testing.T) {
	// Setup test environment
	setupClientDevicesTest(t)

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test client device
	deviceName := "Test Device to Delete"
	device := models.ClientDevice{
		ClientId: client.Id,
		Name:     &deviceName,
		DeviceId: "123456789012349",
	}
	config.DB.Create(&device)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	devices := v1.Group("/client-devices")
	devices.DELETE("/:id", DeleteClientDevice)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/backend/client-devices/%d", device.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)

	// Verify device was deleted from database
	var deletedDevice models.ClientDevice
	err := config.DB.First(&deletedDevice, device.Id).Error
	assert.Error(t, err) // Should not find the device
}

func TestDeleteClientDeviceNotFound(t *testing.T) {
	// Setup test environment
	setupClientDevicesTest(t)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	devices := v1.Group("/client-devices")
	devices.DELETE("/:id", DeleteClientDevice)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/backend/client-devices/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Client device not found", response["message"])
}

func TestSearchClientDevices(t *testing.T) {
	// Setup test environment
	setupClientDevicesTest(t)

	// Create test client devices
	device1Name := "Searchable Device One"
	device2Name := "Another Device"
	device1 := models.ClientDevice{
		Name:     &device1Name,
		DeviceId: "123456789012350",
	}
	device2 := models.ClientDevice{
		Name:     &device2Name,
		DeviceId: "123456789012351",
	}
	config.DB.Create(&device1)
	config.DB.Create(&device2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	devices := v1.Group("/client-devices")
	devices.GET("/search", SearchClientDevices)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/backend/client-devices/search?s=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 1) // Should only find "Searchable Device One"
}

func TestSearchClientDevicesNoResults(t *testing.T) {
	// Setup test environment
	setupClientDevicesTest(t)

	// Create test client device
	deviceName := "Test Device"
	device := models.ClientDevice{
		Name:     &deviceName,
		DeviceId: "123456789012352",
	}
	config.DB.Create(&device)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	devices := v1.Group("/client-devices")
	devices.GET("/search", SearchClientDevices)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/backend/client-devices/search?s=Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 0) // Should find no results
}

func setupClientDevicesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func cleanupClientDevicesTestData() {
	// Clean up in order to respect foreign key constraints
	config.DB.Where("1=1").Delete(&models.Alert{})
	config.DB.Where("1=1").Delete(&models.ClientDevice{})
	config.DB.Where("email LIKE ?", "%test%").Delete(&models.Client{})
}

func setupClientDevicesTest(t *testing.T) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupClientDevicesTestEnvVars()

	config.InitTestDB()

	// Clean up existing test data
	cleanupClientDevicesTestData()
}
