package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupBackendDriversTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}
	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createBackendDriversTestUser(t *testing.T) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	// Create a test client first
	client := models.Client{
		Name:       "Backend Drivers Test Client",
		Email:      "<EMAIL>",
		Status:     "active",
		ClientType: "individual",
	}
	config.DB.Create(&client)

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Backend Drivers Admin",
		UserType: "backend",
		Status:   &status,
		ClientId: &client.Id,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func setupBackendDriversTestWithAuth(t *testing.T) (*gin.Engine, *gin.RouterGroup, string) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendDriversTestEnvVars()
	config.InitTestDB()

	// Create test user and generate token
	user := createBackendDriversTestUser(t)
	token, err := services.GenerateToken(&user, "access")
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}

	// Setup router with authentication
	router := gin.Default()
	router.Use(middleware.CorsMiddleware())
	v1 := router.Group("/api/v1/backend")
	v1.Use(middleware.AuthMiddleware())

	return router, v1, token
}

func TestGetAllDrivers(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test drivers
	driver1 := models.Driver{
		ClientId: client.Id,
		Name:     "Test Driver 1",
		Status:   "active",
	}
	driver2 := models.Driver{
		ClientId: client.Id,
		Name:     "Test Driver 2",
		Status:   "active",
	}
	config.DB.Create(&driver1)
	config.DB.Create(&driver2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	drivers := v1.Group("/drivers")
	drivers.GET("", GetAllDrivers)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/backend/drivers", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 2)
}

func TestGetDriverById(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test driver
	driver := models.Driver{
		ClientId: client.Id,
		Name:     "Test Driver",
		Status:   "active",
	}
	config.DB.Create(&driver)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	drivers := v1.Group("/drivers")
	drivers.GET("/:id", GetDriverById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/backend/drivers/%d", driver.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Driver", data["name"])
}

func TestGetDriverByIdNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	drivers := v1.Group("/drivers")
	drivers.GET("/:id", GetDriverById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/backend/drivers/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Driver not found", response["message"])
}

func TestCreateDriver(t *testing.T) {
	router, v1, token := setupBackendDriversTestWithAuth(t)

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Add routes
	drivers := v1.Group("/drivers")
	drivers.POST("", CreateDriver)

	// Test data
	driverData := models.CreateDriverRequest{
		ClientId: client.Id,
		Name:     "New Test Driver",
		Status:   "active",
	}

	jsonData, _ := json.Marshal(driverData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/drivers", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Driver created successfully", response["message"])

	// Verify driver was created in database
	var driver models.Driver
	err = config.DB.Where("name = ?", "New Test Driver").First(&driver).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Driver", driver.Name)
	assert.Equal(t, client.Id, driver.ClientId)
}

func TestCreateDriverInvalidRequest(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	drivers := v1.Group("/drivers")
	drivers.POST("", CreateDriver)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Driver", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/drivers", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateDriver(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test driver
	phoneNumber := "1234567890"
	driver := models.Driver{
		ClientId:    client.Id,
		Name:        "Original Driver",
		PhoneNumber: &phoneNumber,
		Status:      "active",
	}
	config.DB.Create(&driver)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	drivers := v1.Group("/drivers")
	drivers.PUT("/:id", UpdateDriver)

	// Test data
	phoneNumber2 := "1234567893"
	updateData := models.UpdateDriverRequest{
		Name:        "Updated Driver",
		PhoneNumber: &phoneNumber2,
		Status:      "inactive",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/backend/drivers/%d", driver.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Driver updated successfully", response["message"])

	// Verify driver was updated in database
	var updatedDriver models.Driver
	err = config.DB.First(&updatedDriver, driver.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "Updated Driver", updatedDriver.Name)
}

func TestUpdateDriverNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	drivers := v1.Group("/drivers")
	drivers.PUT("/:id", UpdateDriver)

	// Test data
	phoneNumber := "1234567893"
	updateData := models.UpdateDriverRequest{
		Name:        "Updated Driver",
		PhoneNumber: &phoneNumber,
		Status:      "active",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/backend/drivers/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Driver not found", response["message"])
}

func TestDeleteDriver(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test driver
	phoneNumber3 := "1234567894"
	driver := models.Driver{
		ClientId:    client.Id,
		Name:        "Test Driver to Delete",
		PhoneNumber: &phoneNumber3,
		Status:      "active",
	}
	config.DB.Create(&driver)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	drivers := v1.Group("/drivers")
	drivers.DELETE("/:id", DeleteDriver)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/backend/drivers/%d", driver.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)

	// 204 No Content should not have a response body
	// No need to unmarshal JSON or check message

	// Verify driver was deleted from database
	var deletedDriver models.Driver
	err := config.DB.First(&deletedDriver, driver.Id).Error
	assert.Error(t, err) // Should not find the driver
}

func TestDeleteDriverNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	drivers := v1.Group("/drivers")
	drivers.DELETE("/:id", DeleteDriver)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/backend/drivers/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Driver not found", response["message"])
}

func TestSearchDrivers(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendDriversTestEnvVars()
	config.InitTestDB()

	// Clean up any existing test data
	config.DB.Where("email LIKE ?", "<EMAIL>").Delete(&models.Client{})

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client Search Drivers",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test drivers
	phoneNumber4 := "1234567895"
	phoneNumber5 := "1234567896"
	driver1 := models.Driver{
		ClientId:    client.Id,
		Name:        "Searchable Driver One",
		PhoneNumber: &phoneNumber4,
		Status:      "active",
	}
	driver2 := models.Driver{
		ClientId:    client.Id,
		Name:        "Another Driver",
		PhoneNumber: &phoneNumber5,
		Status:      "active",
	}
	config.DB.Create(&driver1)
	config.DB.Create(&driver2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	drivers := v1.Group("/drivers")
	drivers.GET("/search", SearchDrivers)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/backend/drivers/search?s=Searchable Driver One", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})

	// Check if we found the expected driver
	found := false
	for _, driver := range data {
		if driverMap, ok := driver.(map[string]interface{}); ok {
			if name, exists := driverMap["name"]; exists && name == "Searchable Driver One" {
				found = true
				break
			}
		}
	}
	assert.True(t, found, "Expected to find 'Searchable Driver One' in search results")
}

func TestSearchDriversNoResults(t *testing.T) {
	// Setup test environment using the same pattern as other tests
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendDriversTestEnvVars()
	config.InitTestDB()

	// Clean up any existing test data
	config.DB.Where("email LIKE ?", "<EMAIL>").Delete(&models.Client{})

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client Search No Results",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test driver
	phoneNumber6 := "1234567897"
	driver := models.Driver{
		ClientId:    client.Id,
		Name:        "Test Driver",
		PhoneNumber: &phoneNumber6,
		Status:      "active",
	}
	config.DB.Create(&driver)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	drivers := v1.Group("/drivers")
	drivers.GET("/search", SearchDrivers)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/backend/drivers/search?s=XYZ123Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})

	// Check that we don't find any drivers with "XYZ123Nonexistent" in the name
	found := false
	for _, driver := range data {
		if driverMap, ok := driver.(map[string]interface{}); ok {
			if name, exists := driverMap["name"]; exists {
				nameStr := name.(string)
				if nameStr == "XYZ123Nonexistent" {
					found = true
					break
				}
			}
		}
	}
	assert.False(t, found, "Should not find any drivers with 'XYZ123Nonexistent' in the name")
}
