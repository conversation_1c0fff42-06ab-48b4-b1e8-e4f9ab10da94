package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestPasswordResetStructValidation(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Set required environment variables
	os.Setenv("APP_KEY", "test-secret-key")

	t.Run("PasswordResetRequest validation", func(t *testing.T) {
		router := gin.New()
		router.POST("/test", func(c *gin.Context) {
			var req PasswordResetRequest
			if err := c.ShouldBindJSON(&req); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"email": req.Email})
		})

		// Test valid email
		reqBody := PasswordResetRequest{Email: "<EMAIL>"}
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		// Test invalid email
		reqBody = PasswordResetRequest{Email: "invalid-email"}
		jsonBody, _ = json.Marshal(reqBody)
		req, _ = http.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Test empty email
		reqBody = PasswordResetRequest{Email: ""}
		jsonBody, _ = json.Marshal(reqBody)
		req, _ = http.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("PasswordResetVerify validation", func(t *testing.T) {
		router := gin.New()
		router.POST("/test", func(c *gin.Context) {
			var req PasswordResetVerify
			if err := c.ShouldBindJSON(&req); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"token": req.Token})
		})

		// Test valid token
		reqBody := PasswordResetVerify{Token: "valid.jwt.token"}
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		// Test empty token
		reqBody = PasswordResetVerify{Token: ""}
		jsonBody, _ = json.Marshal(reqBody)
		req, _ = http.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("PasswordResetConfirm validation", func(t *testing.T) {
		router := gin.New()
		router.POST("/test", func(c *gin.Context) {
			var req PasswordResetConfirm
			if err := c.ShouldBindJSON(&req); err != nil {
				c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
				return
			}
			c.JSON(http.StatusOK, gin.H{"token": req.Token, "password": req.Password})
		})

		// Test valid data
		reqBody := PasswordResetConfirm{Token: "valid.jwt.token", Password: "password123"}
		jsonBody, _ := json.Marshal(reqBody)
		req, _ := http.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)

		// Test short password
		reqBody = PasswordResetConfirm{Token: "valid.jwt.token", Password: "123"}
		jsonBody, _ = json.Marshal(reqBody)
		req, _ = http.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Test empty token
		reqBody = PasswordResetConfirm{Token: "", Password: "password123"}
		jsonBody, _ = json.Marshal(reqBody)
		req, _ = http.NewRequest("POST", "/test", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		w = httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestPasswordResetTokenGeneration(t *testing.T) {
	os.Setenv("APP_KEY", "test-secret-key")

	user := &models.User{
		Id:    1,
		Email: "<EMAIL>",
		Name:  "Test User",
	}

	t.Run("Generate password reset token", func(t *testing.T) {
		token, err := services.GenerateToken(user, "password_reset")
		assert.NoError(t, err)
		assert.NotEmpty(t, token)

		// Verify the token
		claims, err := services.VerifyToken(token, "password_reset")
		assert.NoError(t, err)
		assert.Equal(t, user.Id, claims.UserId)
		assert.Equal(t, user.Email, claims.Email)
		assert.Equal(t, "password_reset", claims.Subject)
	})

	t.Run("Access token cannot be used as password reset token", func(t *testing.T) {
		accessToken, err := services.GenerateToken(user, "access")
		assert.NoError(t, err)

		// Try to verify access token as password reset token
		_, err = services.VerifyToken(accessToken, "password_reset")
		assert.Error(t, err)
	})

	t.Run("Password reset token cannot be used as access token", func(t *testing.T) {
		resetToken, err := services.GenerateToken(user, "password_reset")
		assert.NoError(t, err)

		// Try to verify reset token as access token
		_, err = services.VerifyToken(resetToken, "access")
		assert.Error(t, err)
	})
}

func TestPasswordHashing(t *testing.T) {
	t.Run("Hash and verify password", func(t *testing.T) {
		password := "testpassword123"
		hashedPassword := services.HashPassword(password)

		assert.NotEmpty(t, hashedPassword)
		assert.NotEqual(t, password, hashedPassword)
		assert.True(t, services.CheckPassword(hashedPassword, password))
		assert.False(t, services.CheckPassword(hashedPassword, "wrongpassword"))
	})
}
