package controllers

import (
	"net/http"
	"strings"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
)

func GetAllClientDevices(c *gin.Context) {
	var total int64
	filter := map[string]interface{}{}

	if fleetId := c.Query("fleet_id"); fleetId != "" {
		filter["fleet_id"] = fleetId
	}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}
	if deviceTypeId := c.Query("device_type_id"); deviceTypeId != "" {
		filter["device_type_id"] = deviceTypeId
	}

	// Use the efficient query helper
	preloads := []string{"DeviceType", "Client", "Fleet"}
	pagination := true
	searchTerm := c.Query("s") // Get search term from query parameter 's'

	clientDevices, err := utils.GetClientDevicesWithLastGPS(filter, preloads, pagination, c, searchTerm)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"message": "Failed to retrieve client devices",
			"error":   err.Error(),
		})
		return
	}

	// Get total count
	config.DB.Model(&models.ClientDevice{}).Where(filter).Count(&total)

	c.JSON(http.StatusOK, gin.H{
		"data":         clientDevices,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}
func GetClientDeviceById(c *gin.Context) {
	// Use the efficient query helper
	preloads := []string{"DeviceType", "DeviceType.Protocol", "Client", "Fleet"}

	clientDevice, err := utils.GetClientDeviceByIdWithLastGPS(c.Param("id"), preloads)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client device not found",
		})
		return
	}

	// Check if device was actually found (ID should not be 0)
	if clientDevice.Id == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client device not found",
		})
		return
	}

	// Also get the complete last GPS data record for more detailed information
	var lastGPSData models.GPSData
	config.DB.Where("client_device_id = ?", clientDevice.Id).Order("gps_timestamp DESC").First(&lastGPSData)

	c.JSON(http.StatusOK, gin.H{
		"data":          clientDevice,
		"last_gps_data": lastGPSData,
	})
}
func CreateClientDevice(c *gin.Context) {
	var req models.ClientDeviceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	token := strings.ReplaceAll(c.GetHeader("Authorization"), "Bearer ", "")
	claims, _ := services.VerifyToken(token, "access")
	clientDevice := models.ClientDevice{
		Name:                  req.Name,
		CreatedById:           claims.UserId,
		DeviceTypeId:          req.DeviceTypeId,
		ClientId:              req.ClientId,
		FleetId:               req.FleetId,
		AssetType:             req.AssetType,
		DriverId:              req.DriverId,
		PhoneNumber:           req.PhoneNumber,
		DeviceId:              req.DeviceId,
		Password:              req.Password,
		AlertPhoneNumber:      req.AlertPhoneNumber,
		PlateNumber:           req.PlateNumber,
		MaxSpeed:              req.MaxSpeed,
		SubscriptionStartDate: req.SubscriptionStartDate,
		SubscriptionEndDate:   req.SubscriptionEndDate,
		Icon:                  req.Icon,
		Model:                 req.Model,
		Make:                  req.Make,
		ManufactureYear:       req.ManufactureYear,
		Color:                 req.Color,
		EngineNumber:          req.EngineNumber,
		ChassisNumber:         req.ChassisNumber,
		Vin:                   req.Vin,
		ObdProtocol:           req.ObdProtocol,
		FuelType:              req.FuelType,
		FuelRate:              req.FuelRate,
		FuelRateUnit:          req.FuelRateUnit,
		Manufacturer:          req.Manufacturer,
		Status:                req.Status,
		InsuranceExpiryDate:   req.InsuranceExpiryDate,
		InitialMileage:        req.InitialMileage,
		CurrentMileage:        req.CurrentMileage,
		ServiceMileage:        req.ServiceMileage,
		ServiceDays:           req.ServiceDays,
		LastServiceDate:       req.LastServiceDate,
		NextServiceDate:       req.NextServiceDate,
		LastServiceMileage:    req.LastServiceMileage,
		NextServiceMileage:    req.NextServiceMileage,
		Emails:                req.Emails,
		Description:           req.Description,
		Amount:                req.Amount,
		BillingCycle:          req.BillingCycle,
		NextBillingDate:       req.NextBillingDate,
		IsLifetime:            req.IsLifetime,
		Mileage:               req.Mileage,
	}
	result := config.DB.Create(&clientDevice)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Client device created successfully",
	})
}
func UpdateClientDevice(c *gin.Context) {
	var req models.ClientDeviceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var clientDevice models.ClientDevice
	if err := config.DB.First(&clientDevice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client device not found",
		})
		return
	}
	clientDevice.Name = req.Name
	clientDevice.ClientId = req.ClientId
	clientDevice.DeviceTypeId = req.DeviceTypeId
	clientDevice.AssetType = req.AssetType
	clientDevice.DriverId = req.DriverId
	clientDevice.FleetId = req.FleetId
	clientDevice.PhoneNumber = req.PhoneNumber
	clientDevice.DeviceId = req.DeviceId
	clientDevice.Password = req.Password
	clientDevice.AlertPhoneNumber = req.AlertPhoneNumber
	clientDevice.PlateNumber = req.PlateNumber
	clientDevice.MaxSpeed = req.MaxSpeed
	clientDevice.SubscriptionStartDate = req.SubscriptionStartDate
	clientDevice.SubscriptionEndDate = req.SubscriptionEndDate
	clientDevice.Icon = req.Icon
	clientDevice.Model = req.Model
	clientDevice.Make = req.Make
	clientDevice.ManufactureYear = req.ManufactureYear
	clientDevice.Color = req.Color
	clientDevice.EngineNumber = req.EngineNumber
	clientDevice.ChassisNumber = req.ChassisNumber
	clientDevice.Vin = req.Vin
	clientDevice.ObdProtocol = req.ObdProtocol
	clientDevice.FuelType = req.FuelType
	clientDevice.FuelRate = req.FuelRate
	clientDevice.FuelRateUnit = req.FuelRateUnit
	clientDevice.Manufacturer = req.Manufacturer
	clientDevice.Status = req.Status
	clientDevice.InsuranceExpiryDate = req.InsuranceExpiryDate
	clientDevice.InitialMileage = req.InitialMileage
	clientDevice.Mileage = req.Mileage
	clientDevice.CurrentMileage = req.CurrentMileage
	clientDevice.ServiceMileage = req.ServiceMileage
	clientDevice.ServiceDays = req.ServiceDays
	clientDevice.LastServiceDate = req.LastServiceDate
	clientDevice.NextServiceDate = req.NextServiceDate
	clientDevice.LastServiceMileage = req.LastServiceMileage
	clientDevice.NextServiceMileage = req.NextServiceMileage
	clientDevice.Emails = req.Emails
	clientDevice.PhoneNumbers = req.PhoneNumbers
	clientDevice.Description = req.Description
	clientDevice.Amount = req.Amount
	clientDevice.BillingCycle = req.BillingCycle
	clientDevice.NextBillingDate = req.NextBillingDate
	clientDevice.IsLifetime = req.IsLifetime

	result := config.DB.Save(&clientDevice)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Client device updated successfully"})
}
func DeleteClientDevice(c *gin.Context) {
	var clientDevice models.ClientDevice
	if err := config.DB.First(&clientDevice, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client device not found",
		})
		return
	}
	result := config.DB.Delete(&clientDevice)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.Status(http.StatusNoContent)
}
func SearchClientDevices(c *gin.Context) {
	var clientDevices []models.ClientDevice
	filter := map[string]interface{}{}
	if fleetId := c.Query("fleet_id"); fleetId != "" {
		filter["fleet_id"] = fleetId
	}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}
	if deviceTypeId := c.Query("device_type_id"); deviceTypeId != "" {
		filter["device_type_id"] = deviceTypeId
	}
	query := config.DB.Preload("DeviceType").Preload("Client").Preload("Fleet").Where(filter)
	if search := c.Query("s"); search != "" {
		query.Where("name like ? or id like ? or device_id like ? or phone_number like ? or engine_number like ? or chassis_number like ? or vin like ? or plate_number like ?", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}
	query.Find(&clientDevices)
	c.JSON(http.StatusOK, gin.H{
		"data": clientDevices,
	})
}
