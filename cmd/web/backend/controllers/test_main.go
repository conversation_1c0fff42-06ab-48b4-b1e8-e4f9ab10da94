package controllers

import (
	"log"
	"os"
	"testing"
	"yotracker/config"
	"yotracker/internal/seed"
	"yotracker/migrations"
)

func TestMain(m *testing.M) {
	log.Println("Setting up test database for backend controllers package...")

	// Set test environment variables
	os.Setenv("GO_ENV", "test")
	os.Setenv("TESTING_DB_NAME", "testing")
	os.Setenv("TEST_ENV", "true")

	// Initialize test database
	config.InitTestDB()

	// Run migrations once
	log.Println("Running database migrations...")
	migrations.Migrate()

	// Seed data once
	log.Println("Seeding test data...")
	seed.Seed()

	log.Println("Test database setup completed. Running tests...")

	// Run all tests
	code := m.Run()

	// Cleanup
	log.Println("Cleaning up test database...")
	if config.DB != nil {
		config.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
		if sqlDB, err := config.DB.DB(); err == nil {
			sqlDB.Exec("DROP DATABASE IF EXISTS " + os.Getenv("TESTING_DB_NAME"))
		}
		config.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")
	}

	os.Exit(code)
}
