package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"
	"yotracker/migrations"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupPasswordResetTest() {
	utils.ForceProjectRoot()

	// Set required environment variables for testing
	os.Setenv("APP_KEY", "test-secret-key")
	os.Setenv("APP_URL", "http://localhost:3000")
	os.Setenv("SENDGRID_API_KEY", "test-key")
	os.Setenv("FROM_EMAIL", "<EMAIL>")
	os.Setenv("FROM_NAME", "Test")
	os.Setenv("TESTING_DB_NAME", "testing")

	// Only set database connection variables if they're not already set (for CI compatibility)
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}

	config.InitTestDB()
	migrations.Migrate()
	gin.SetMode(gin.TestMode)
}

func createTestBackendUser() models.User {
	// Clean up any existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	hashedPassword := services.HashPassword("password123")
	user := models.User{
		Name:     "Test User",
		Email:    "<EMAIL>",
		Password: hashedPassword,
		UserType: "backend",
	}
	result := config.DB.Create(&user)
	if result.Error != nil {
		panic(fmt.Sprintf("Failed to create test user: %v", result.Error))
	}
	return user
}

func TestRequestPasswordReset(t *testing.T) {
	setupPasswordResetTest()
	user := createTestBackendUser()
	defer config.DB.Where("email = ?", user.Email).Delete(&models.User{})

	router := gin.New()
	router.POST("/password_reset/request", RequestPasswordReset)

	t.Run("Valid email", func(t *testing.T) {
		reqBody := PasswordResetRequest{
			Email: user.Email,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password_reset/request", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Contains(t, response["message"], "password reset link has been sent")
	})

	t.Run("Invalid email", func(t *testing.T) {
		reqBody := PasswordResetRequest{
			Email: "<EMAIL>",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password_reset/request", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Should still return 200 for security (don't reveal if email exists)
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Invalid request body", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/password_reset/request", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestVerifyPasswordResetToken(t *testing.T) {
	setupPasswordResetTest()
	user := createTestBackendUser()
	defer config.DB.Where("email = ?", user.Email).Delete(&models.User{})

	router := gin.New()
	router.POST("/password_reset/verify", VerifyPasswordResetToken)

	t.Run("Valid token", func(t *testing.T) {
		// Generate a valid reset token
		resetToken, err := services.GenerateToken(&user, "password_reset")
		assert.NoError(t, err)

		reqBody := PasswordResetVerify{
			Token: resetToken,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password_reset/verify", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Token is valid", response["message"])
		assert.Equal(t, user.Email, response["email"])
	})

	t.Run("Invalid token", func(t *testing.T) {
		reqBody := PasswordResetVerify{
			Token: "invalid.token.here",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password_reset/verify", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Access token used as reset token", func(t *testing.T) {
		// Generate an access token instead of reset token
		accessToken, err := services.GenerateToken(&user, "access")
		assert.NoError(t, err)

		reqBody := PasswordResetVerify{
			Token: accessToken,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password_reset/verify", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestResetPassword(t *testing.T) {
	setupPasswordResetTest()

	router := gin.New()
	router.POST("/password_reset/confirm", ResetPassword)

	t.Run("Valid password reset", func(t *testing.T) {
		user := createTestBackendUser()
		defer config.DB.Where("email = ?", user.Email).Delete(&models.User{})

		// Generate a valid reset token
		resetToken, err := services.GenerateToken(&user, "password_reset")
		assert.NoError(t, err)

		newPassword := "newpassword123"
		reqBody := PasswordResetConfirm{
			Token:    resetToken,
			Password: newPassword,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password_reset/confirm", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Password has been reset successfully", response["message"])

		// Verify password was actually changed
		var updatedUser models.User
		config.DB.First(&updatedUser, user.Id)
		assert.True(t, services.CheckPassword(updatedUser.Password, newPassword))
	})

	t.Run("Invalid token", func(t *testing.T) {
		reqBody := PasswordResetConfirm{
			Token:    "invalid.token.here",
			Password: "newpassword123",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password_reset/confirm", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Short password", func(t *testing.T) {
		user := createTestBackendUser()
		defer config.DB.Where("email = ?", user.Email).Delete(&models.User{})

		resetToken, err := services.GenerateToken(&user, "password_reset")
		assert.NoError(t, err)

		reqBody := PasswordResetConfirm{
			Token:    resetToken,
			Password: "123", // Too short
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("POST", "/password_reset/confirm", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
