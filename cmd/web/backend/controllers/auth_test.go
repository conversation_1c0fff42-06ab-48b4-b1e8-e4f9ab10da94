package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupAuthTest(t *testing.T) (*gin.Engine, models.User) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupAuthTestEnvVars()

	config.InitTestDB()

	// Create test user
	user := createAuthTestUser(t)

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())
	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/backend")

	// Public auth routes (no authentication required)
	v1.POST("/login", Login)
	v1.POST("/register", Register)
	v1.POST("/password_reset/request", RequestPasswordReset)
	v1.POST("/password_reset/verify", VerifyPasswordResetToken)
	v1.POST("/password_reset/confirm", ResetPassword)
	v1.POST("/refresh", RefreshToken) // Refresh handles its own token validation

	// Protected routes (authentication required)
	protected := v1.Group("")
	protected.Use(middleware.AuthMiddleware())
	protected.POST("/logout", Logout)

	return r, user
}

func setupAuthTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createAuthTestUser(t *testing.T) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Auth Test User",
		UserType: "backend",
		Status:   &status,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

// Test login functionality
func TestLogin(t *testing.T) {
	r, _ := setupAuthTest(t)

	t.Run("Login with valid credentials", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "password",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "token")
		assert.NotEmpty(t, response["token"])
	})

	t.Run("Login with invalid password", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "wrongpassword",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnprocessableEntity, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify error message
		assert.Contains(t, response, "errors")
		errors := response["errors"].(map[string]interface{})
		assert.Contains(t, errors, "password")
	})

	t.Run("Login with non-existent email", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "password",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify error message
		assert.Contains(t, response, "errors")
		errors := response["errors"].(map[string]interface{})
		assert.Contains(t, errors, "email")
	})

	t.Run("Login with missing email", func(t *testing.T) {
		loginData := map[string]interface{}{
			"password": "password",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Login with missing password", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email": "<EMAIL>",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Login with invalid JSON", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/login", bytes.NewBufferString("invalid json"))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

// Test logout functionality
func TestLogout(t *testing.T) {
	r, user := setupAuthTest(t)

	// Get valid token
	token, _ := services.GenerateToken(&user, "access")

	t.Run("Logout with valid token", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/logout", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Contains(t, response["message"], "Logout successful")
	})

	t.Run("Logout without token", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/logout", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Logout with invalid token", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/logout", nil)
		req.Header.Set("Authorization", "Bearer invalid-token")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test refresh token functionality
func TestRefreshToken(t *testing.T) {
	r, user := setupAuthTest(t)

	// Get valid refresh token
	refreshToken, _ := services.GenerateToken(&user, "refresh")

	t.Run("Refresh token with valid refresh token", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/refresh", nil)
		req.Header.Set("Authorization", "Bearer "+refreshToken)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "message")
		assert.Contains(t, response, "data")
		assert.Contains(t, response["message"], "Token refreshed successfully")

		// Verify data contains new token
		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "token")
		assert.NotEmpty(t, data["token"])
	})

	t.Run("Refresh token without token", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/refresh", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Refresh token with invalid token", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/refresh", nil)
		req.Header.Set("Authorization", "Bearer invalid-token")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test password reset functionality
func TestPasswordReset(t *testing.T) {
	r, _ := setupAuthTest(t)

	t.Run("Request password reset with valid email", func(t *testing.T) {
		resetData := map[string]interface{}{
			"email": "<EMAIL>",
		}

		jsonData, _ := json.Marshal(resetData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/password_reset/request", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Contains(t, response["message"], "If the email exists, a password reset link has been sent")
	})

	t.Run("Request password reset with non-existent email", func(t *testing.T) {
		resetData := map[string]interface{}{
			"email": "<EMAIL>",
		}

		jsonData, _ := json.Marshal(resetData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/password_reset/request", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Should still return success for security reasons
		assert.Contains(t, response, "message")
		assert.Contains(t, response["message"], "If the email exists, a password reset link has been sent")
	})

	t.Run("Request password reset with invalid email", func(t *testing.T) {
		resetData := map[string]interface{}{
			"email": "invalid-email",
		}

		jsonData, _ := json.Marshal(resetData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/password_reset/request", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Request password reset with missing email", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/password_reset/request", bytes.NewBufferString("{}"))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

// Test edge cases and error handling
func TestAuthEdgeCases(t *testing.T) {
	r, _ := setupAuthTest(t)

	t.Run("Login with empty request body", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/login", bytes.NewBufferString(""))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Login with malformed JSON", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/login", bytes.NewBufferString(`{"email": "<EMAIL>", "password": "password"`))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Login with extra fields", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "password",
			"extra":    "field",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Should still work as extra fields are ignored
		assert.Equal(t, http.StatusOK, w.Code)
	})
}
