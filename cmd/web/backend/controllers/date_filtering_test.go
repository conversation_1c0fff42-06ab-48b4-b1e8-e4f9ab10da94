package controllers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupTestEnvironment(t *testing.T) (*gin.Engine, string) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupTestEnvVars()

	config.InitTestDB()

	// Create test user and get token
	user := createTestUser(t)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())
	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/backend")
	v1.Use(middleware.AuthMiddleware())
	// Add the actual routes that the tests are trying to access
	v1.GET("/invoices", GetAllInvoices)
	v1.GET("/payments", GetAllPayments)
	v1.GET("/trips", GetAllTrips)
	v1.GET("/alerts", GetAllAlerts)
	v1.GET("/command_logs", GetAllCommandLogs)
	// Add search routes
	v1.GET("/invoices/search", SearchInvoices)
	v1.GET("/payments/search", SearchPayments)
	v1.GET("/alerts/search", SearchAlerts)
	v1.GET("/trips/search", SearchTrips)
	// Add some basic routes for testing
	v1.GET("/test", func(c *gin.Context) { c.JSON(200, gin.H{"message": "test"}) })

	return r, token
}

func setupTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createTestUser(t *testing.T) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Admin",
		UserType: "backend",
		Status:   &status,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

// Test date filtering functionality
func TestDateFilteringFunctionality(t *testing.T) {
	r, token := setupTestEnvironment(t)

	// Test that endpoints with date filtering respond correctly
	tests := []struct {
		name           string
		endpoint       string
		queryParams    string
		expectedStatus int
	}{
		{
			name:           "Invoices with date filter",
			endpoint:       "/api/v1/backend/invoices",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Invoices with date range",
			endpoint:       "/api/v1/backend/invoices",
			queryParams:    "?start_date=2024-01-15&end_date=2024-01-31",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Payments with date filter",
			endpoint:       "/api/v1/backend/payments",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Trips with date filter",
			endpoint:       "/api/v1/backend/trips",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Alerts with date filter",
			endpoint:       "/api/v1/backend/alerts",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
		},

		{
			name:           "Command logs with date filter",
			endpoint:       "/api/v1/backend/command_logs",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, tt.endpoint+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.Contains(t, response, "data")
				assert.Contains(t, response, "total")
			}
		})
	}
}

// Test search endpoints with date filtering
func TestSearchEndpointsWithDateFiltering(t *testing.T) {
	r, token := setupTestEnvironment(t)

	tests := []struct {
		name           string
		endpoint       string
		queryParams    string
		expectedStatus int
	}{
		{
			name:           "Search invoices with date filter",
			endpoint:       "/api/v1/backend/invoices/search",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Search payments with date filter",
			endpoint:       "/api/v1/backend/payments/search",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Search alerts with date filter",
			endpoint:       "/api/v1/backend/alerts/search",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Search trips with date filter",
			endpoint:       "/api/v1/backend/trips/search",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, tt.endpoint+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.Contains(t, response, "data")
			}
		})
	}
}

// Test edge cases and error scenarios
func TestDateFilteringEdgeCases(t *testing.T) {
	r, token := setupTestEnvironment(t)

	tests := []struct {
		name           string
		endpoint       string
		queryParams    string
		expectedStatus int
	}{
		{
			name:           "Invalid date format",
			endpoint:       "/api/v1/backend/invoices",
			queryParams:    "?start_date=invalid-date",
			expectedStatus: http.StatusOK, // Should handle gracefully
		},
		{
			name:           "End date before start date",
			endpoint:       "/api/v1/backend/invoices",
			queryParams:    "?start_date=2024-01-20&end_date=2024-01-15",
			expectedStatus: http.StatusOK, // Should handle gracefully
		},
		{
			name:           "Empty date parameters",
			endpoint:       "/api/v1/backend/invoices",
			queryParams:    "?start_date=&end_date=",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Only start date",
			endpoint:       "/api/v1/backend/invoices",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Only end date",
			endpoint:       "/api/v1/backend/invoices",
			queryParams:    "?end_date=2024-01-15",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, tt.endpoint+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

// Test combined filters (date + search + other filters)
func TestCombinedFilters(t *testing.T) {
	r, token := setupTestEnvironment(t)

	tests := []struct {
		name           string
		endpoint       string
		queryParams    string
		expectedStatus int
	}{
		{
			name:           "Invoices with date and search",
			endpoint:       "/api/v1/backend/invoices",
			queryParams:    "?start_date=2024-01-15&s=test",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Invoices with date and status",
			endpoint:       "/api/v1/backend/invoices",
			queryParams:    "?start_date=2024-01-15status=pending",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Payments with date and search",
			endpoint:       "/api/v1/backend/payments",
			queryParams:    "?start_date=2024-01-15&s=test",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Trips with date and status",
			endpoint:       "/api/v1/backend/trips",
			queryParams:    "?start_date=2024-01-15status=completed",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Alerts with date and type",
			endpoint:       "/api/v1/backend/alerts",
			queryParams:    "?start_date=2024-01-15&alert_type=speed",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, tt.endpoint+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.Contains(t, response, "data")
			}
		})
	}
}
