package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupBackendDeviceTypesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}
	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func cleanupBackendDeviceTypesTestData() {
	config.DB.Where("name IN (?)", []string{"Test Device Type 1", "Test Device Type 2", "Test Device Type", "New Test Device Type", "Original Device Type", "Updated Device Type", "Test Device Type to Delete", "Searchable Device Type One", "Another Device Type"}).Delete(&models.DeviceType{})
	config.DB.Where("name IN (?)", []string{"Test Protocol 1", "Test Protocol 2", "Test Protocol"}).Delete(&models.Protocol{})
}

func setupBackendDeviceTypesTest(t *testing.T) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendDeviceTypesTestEnvVars()
	config.InitTestDB()
	cleanupBackendDeviceTypesTestData()
}

func TestGetAllDeviceTypes(t *testing.T) {
	setupBackendDeviceTypesTest(t)

	// Create test protocols first
	protocol1 := models.Protocol{Name: "Test Protocol 1", Active: true}
	protocol2 := models.Protocol{Name: "Test Protocol 2", Active: true}
	config.DB.Create(&protocol1)
	config.DB.Create(&protocol2)

	// Create test device types
	deviceType1 := models.DeviceType{
		Name:       "Test Device Type 1",
		ProtocolId: protocol1.Id,
		Active:     true,
	}
	deviceType2 := models.DeviceType{
		Name:       "Test Device Type 2",
		ProtocolId: protocol2.Id,
		Active:     true,
	}
	config.DB.Create(&deviceType1)
	config.DB.Create(&deviceType2)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("", GetAllDeviceTypes)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/backend/device_types", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 5) // 3 seeded device types (st901, chinese, TK003) + 2 test device types
}

func TestGetDeviceTypeById(t *testing.T) {
	setupBackendDeviceTypesTest(t)

	// Create test protocol first
	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)

	// Create test device type
	deviceType := models.DeviceType{
		Name:       "Test Device Type",
		ProtocolId: protocol.Id,
		Active:     true,
	}
	config.DB.Create(&deviceType)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("/:id", GetDeviceTypeById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/backend/device_types/%d", deviceType.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Device Type", data["name"])
}

func TestGetDeviceTypeByIdNotFound(t *testing.T) {
	setupBackendDeviceTypesTest(t)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("/:id", GetDeviceTypeById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/backend/device_types/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Device type not found", response["message"])
}

func TestCreateDeviceType(t *testing.T) {
	setupBackendDeviceTypesTest(t)

	// Create test protocol first
	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.POST("", CreateDeviceType)

	// Test data
	deviceTypeData := models.DeviceTypeRequest{
		Name:       "New Test Device Type",
		ProtocolId: protocol.Id,
		Active:     true,
	}

	jsonData, _ := json.Marshal(deviceTypeData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/device_types", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Device type created successfully", response["message"])

	// Verify device type was created in database
	var deviceType models.DeviceType
	err = config.DB.Where("name = ?", "New Test Device Type").First(&deviceType).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Device Type", deviceType.Name)
}

func TestCreateDeviceTypeInvalidRequest(t *testing.T) {
	setupBackendDeviceTypesTest(t)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.POST("", CreateDeviceType)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Device Type", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/device_types", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateDeviceType(t *testing.T) {
	setupBackendDeviceTypesTest(t)

	// Create test protocol first
	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)

	// Create test device type
	deviceType := models.DeviceType{
		Name:       "Original Device Type",
		ProtocolId: protocol.Id,
		Active:     true,
	}
	config.DB.Create(&deviceType)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.PUT("/:id", UpdateDeviceType)

	// Test data
	updateData := models.DeviceTypeRequest{
		Name:       "Updated Device Type",
		ProtocolId: protocol.Id,
		Active:     false,
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/backend/device_types/%d", deviceType.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Device type updated successfully", response["message"])

	// Verify device type was updated in database
	var updatedDeviceType models.DeviceType
	err = config.DB.First(&updatedDeviceType, deviceType.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "Updated Device Type", updatedDeviceType.Name)
}

func TestUpdateDeviceTypeNotFound(t *testing.T) {
	setupBackendDeviceTypesTest(t)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.PUT("/:id", UpdateDeviceType)

	// Test data
	updateData := models.DeviceTypeRequest{
		Name:       "Updated Device Type",
		ProtocolId: 1, // Use a default protocol ID
		Active:     true,
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/backend/device_types/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Device type not found", response["message"])
}

func TestDeleteDeviceType(t *testing.T) {
	setupBackendDeviceTypesTest(t)

	// Create test protocol first
	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)

	// Create test device type
	deviceType := models.DeviceType{
		Name:       "Test Device Type to Delete",
		ProtocolId: protocol.Id,
		Active:     true,
	}
	config.DB.Create(&deviceType)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.DELETE("/:id", DeleteDeviceType)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/backend/device_types/%d", deviceType.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)

	// Verify device type was deleted from database
	var deletedDeviceType models.DeviceType
	err := config.DB.First(&deletedDeviceType, deviceType.Id).Error
	assert.Error(t, err) // Should not find the device type
}

func TestDeleteDeviceTypeNotFound(t *testing.T) {
	setupBackendDeviceTypesTest(t)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.DELETE("/:id", DeleteDeviceType)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/backend/device_types/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Device type not found", response["message"])
}

func TestSearchDeviceTypes(t *testing.T) {
	setupBackendDeviceTypesTest(t)

	// Create test protocols first
	protocol1 := models.Protocol{Name: "Test Protocol 1", Active: true}
	protocol2 := models.Protocol{Name: "Test Protocol 2", Active: true}
	config.DB.Create(&protocol1)
	config.DB.Create(&protocol2)

	// Create test device types
	deviceType1 := models.DeviceType{
		Name:       "Searchable Device Type One",
		ProtocolId: protocol1.Id,
		Active:     true,
	}
	deviceType2 := models.DeviceType{
		Name:       "Another Device Type",
		ProtocolId: protocol2.Id,
		Active:     true,
	}
	config.DB.Create(&deviceType1)
	config.DB.Create(&deviceType2)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("/search", SearchDeviceTypes)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/backend/device_types/search?s=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	// Should find "Searchable Device Type One" only
	assert.Len(t, data, 1)

	// Verify that our test device type is in the results
	found := false
	for _, item := range data {
		deviceType := item.(map[string]interface{})
		if deviceType["name"] == "Searchable Device Type One" {
			found = true
			break
		}
	}
	assert.True(t, found, "Searchable Device Type One should be in the results")
}

func TestSearchDeviceTypesNoResults(t *testing.T) {
	setupBackendDeviceTypesTest(t)

	// Create test protocol first
	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)

	// Create test device type
	deviceType := models.DeviceType{
		Name:       "Test Device Type",
		ProtocolId: protocol.Id,
		Active:     true,
	}
	config.DB.Create(&deviceType)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/backend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("/search", SearchDeviceTypes)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/backend/device_types/search?s=Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 0) // Should find no results
}
