package controllers

import (
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
)

func GetAllDeviceTypes(c *gin.Context) {
	var deviceTypes []models.DeviceType
	var total int64
	query := config.DB.Preload("Protocol")
	if noPagination := c.Query("no_pagination"); noPagination == "" {
		query = query.Scopes(utils.Paginate(c))
	}
	query.Find(&deviceTypes)
	config.DB.Model(&models.DeviceType{}).Count(&total)
	c.JSON(http.StatusOK, gin.H{
		"data":         deviceTypes,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}
func GetDeviceTypeById(c *gin.Context) {
	var deviceType models.DeviceType
	if err := config.DB.Preload("Protocol").First(&deviceType, c.<PERSON>m("id")).Error; err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{
			"message": "Device type not found",
		})
		return
	}
	c.JSO<PERSON>(http.StatusOK, gin.H{
		"data": deviceType,
	})
}
func CreateDeviceType(c *gin.Context) {
	var req models.DeviceTypeRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	deviceType := models.DeviceType{
		ProtocolId:                req.ProtocolId,
		Name:                      req.Name,
		Active:                    req.Active,
		Amount:                    req.Amount,
		RealTimeTracking:          req.RealTimeTracking,
		PeriodicReporting:         req.PeriodicReporting,
		Geofencing:                req.Geofencing,
		SpeedAlerts:               req.SpeedAlerts,
		SOSAlerts:                 req.SOSAlerts,
		BatteryAlerts:             req.BatteryAlerts,
		RemoteRestart:             req.RemoteRestart,
		SleepMode:                 req.SleepMode,
		WakeUp:                    req.WakeUp,
		FirmwareUpdate:            req.FirmwareUpdate,
		HistoricalDataLogging:     req.HistoricalDataLogging,
		DeviceStatusReporting:     req.DeviceStatusReporting,
		DiagnosticFunctions:       req.DiagnosticFunctions,
		MultiNetworkSupport:       req.MultiNetworkSupport,
		WeatherProofing:           req.WeatherProofing,
		ExtendedBatteryLife:       req.ExtendedBatteryLife,
		MappingServiceIntegration: req.MappingServiceIntegration,
		OTAConfiguration:          req.OTAConfiguration,
		CustomCommands:            req.CustomCommands,
		LocationDataEncryption:    req.LocationDataEncryption,
		VoiceCommunication:        req.VoiceCommunication,
		MotionDetection:           req.MotionDetection,
		TemperatureMonitoring:     req.TemperatureMonitoring,
		TamperAlerts:              req.TamperAlerts,
		CameraSupport:             req.CameraSupport,
		AudioMonitoring:           req.AudioMonitoring,
		FuelMonitoring:            req.FuelMonitoring,
		OBDIISupport:              req.OBDIISupport,
		BluetoothConnectivity:     req.BluetoothConnectivity,
		WifiConnectivity:          req.WifiConnectivity,
		GNSSSupport:               req.GNSSSupport,
	}
	result := config.DB.Create(&deviceType)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Device type created successfully",
	})
}
func UpdateDeviceType(c *gin.Context) {
	var req models.DeviceTypeRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var deviceType models.DeviceType
	if err := config.DB.First(&deviceType, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Device type not found",
		})
		return
	}
	deviceType.ProtocolId = req.ProtocolId
	deviceType.Name = req.Name
	deviceType.Active = req.Active
	deviceType.Amount = req.Amount
	deviceType.RealTimeTracking = req.RealTimeTracking
	deviceType.PeriodicReporting = req.PeriodicReporting
	deviceType.Geofencing = req.Geofencing
	deviceType.SpeedAlerts = req.SpeedAlerts
	deviceType.SOSAlerts = req.SOSAlerts
	deviceType.BatteryAlerts = req.BatteryAlerts
	deviceType.RemoteRestart = req.RemoteRestart
	deviceType.SleepMode = req.SleepMode
	deviceType.WakeUp = req.WakeUp
	deviceType.FirmwareUpdate = req.FirmwareUpdate
	deviceType.HistoricalDataLogging = req.HistoricalDataLogging
	deviceType.DeviceStatusReporting = req.DeviceStatusReporting
	deviceType.DiagnosticFunctions = req.DiagnosticFunctions
	deviceType.MultiNetworkSupport = req.MultiNetworkSupport
	deviceType.WeatherProofing = req.WeatherProofing
	deviceType.ExtendedBatteryLife = req.ExtendedBatteryLife
	deviceType.MappingServiceIntegration = req.MappingServiceIntegration
	deviceType.OTAConfiguration = req.OTAConfiguration
	deviceType.CustomCommands = req.CustomCommands
	deviceType.LocationDataEncryption = req.LocationDataEncryption
	deviceType.VoiceCommunication = req.VoiceCommunication
	deviceType.MotionDetection = req.MotionDetection
	deviceType.TemperatureMonitoring = req.TemperatureMonitoring
	deviceType.TamperAlerts = req.TamperAlerts
	deviceType.CameraSupport = req.CameraSupport
	deviceType.AudioMonitoring = req.AudioMonitoring
	deviceType.FuelMonitoring = req.FuelMonitoring
	deviceType.OBDIISupport = req.OBDIISupport
	deviceType.BluetoothConnectivity = req.BluetoothConnectivity
	deviceType.WifiConnectivity = req.WifiConnectivity
	deviceType.GNSSSupport = req.GNSSSupport

	result := config.DB.Save(&deviceType)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Device type updated successfully"})
}
func DeleteDeviceType(c *gin.Context) {
	var deviceType models.DeviceType
	if err := config.DB.First(&deviceType, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Device type not found",
		})
		return
	}
	result := config.DB.Delete(&deviceType)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusNoContent, gin.H{"message": "Device type deleted successfully"})
}
func SearchDeviceTypes(c *gin.Context) {
	var deviceTypes []models.DeviceType
	query := config.DB.Preload("Protocol").Where("active=1")
	if search := c.Query("s"); search != "" {
		query.Where("name like ?", "%"+search+"%")
	}
	query.Find(&deviceTypes)
	c.JSON(http.StatusOK, gin.H{
		"data": deviceTypes,
	})
}
