package controllers

import (
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
)

func GetAllRoles(c *gin.Context) {
	var roles []models.Role
	var total int64
	filter := map[string]interface{}{}
	if roleType := c.Query("role_type"); roleType != "" {
		filter["role_type"] = roleType
	}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}

	config.DB.Where(filter).Find(&roles)
	config.DB.Model(&models.Role{}).Where(filter).Count(&total)
	c.<PERSON>(http.StatusOK, gin.H{
		"data":         roles,
		"total":        total,
		"current_page": 1,
		"per_page":     20,
	})
}
func GetRoleById(c *gin.Context) {
	var role models.Role
	if err := config.DB.First(&role, c.Param("id")).Error; err != nil {
		c.<PERSON>(http.StatusNotFound, gin.H{
			"message": "Role not found",
		})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"data": role,
	})
}
func <PERSON>reate<PERSON>ole(c *gin.Context) {
	var req models.RoleRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	role := models.Role{
		ClientId:    req.ClientId,
		Name:        req.Name,
		RoleType:    req.RoleType,
		Description: req.Description,
	}
	result := config.DB.Create(&role)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Role created successfully",
	})
}
func UpdateRole(c *gin.Context) {
	var req models.RoleRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var role models.Role
	if err := config.DB.First(&role, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Role not found",
		})
		return
	}
	if role.IsSystem != nil && *role.IsSystem {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "System role cannot be updated",
		})
		return
	}
	role.Name = req.Name
	role.Description = req.Description
	result := config.DB.Save(&role)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Role updated successfully"})
}
func DeleteRole(c *gin.Context) {
	var role models.Role
	if err := config.DB.First(&role, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Role not found",
		})
		return
	}
	if role.IsSystem != nil && *role.IsSystem {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "System role cannot be deleted",
		})
		return
	}
	result := config.DB.Delete(&role)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusNoContent, gin.H{"message": "Role deleted successfully"})
}
func SearchRoles(c *gin.Context) {
	var roles []models.Role
	query := config.DB
	if search := c.Query("s"); search != "" {
		query.Where("name like ?", "%"+search+"%")
	}
	query.Find(&roles)
	c.JSON(http.StatusOK, gin.H{
		"data": roles,
	})
}
