package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupBackendDriverDeviceAssignmentsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}
	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func cleanupBackendDriverDeviceAssignmentsTestData() {
	config.DB.Where("1=1").Delete(&models.DriverDeviceAssignment{})
	config.DB.Where("1=1").Delete(&models.ClientDevice{})
	config.DB.Where("email LIKE ?", "%test%").Delete(&models.Driver{})
	config.DB.Where("email LIKE ?", "%test%").Delete(&models.Client{})
}

func createBackendDriverDeviceAssignmentsTestUser(t *testing.T) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	// Create a test client first
	client := models.Client{
		Name:       "Backend Driver Device Assignments Test Client",
		Email:      "<EMAIL>",
		Status:     "active",
		ClientType: "individual",
	}
	config.DB.Create(&client)

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Backend Driver Device Assignments Admin",
		UserType: "backend",
		Status:   &status,
		ClientId: &client.Id,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func setupBackendDriverDeviceAssignmentsTest(t *testing.T) (*gin.Engine, *gin.RouterGroup, string) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupBackendDriverDeviceAssignmentsTestEnvVars()
	config.InitTestDB()
	cleanupBackendDriverDeviceAssignmentsTestData()

	// Create test user and generate token
	user := createBackendDriverDeviceAssignmentsTestUser(t)
	token, err := services.GenerateToken(&user, "access")
	if err != nil {
		t.Fatalf("Failed to generate token: %v", err)
	}
	t.Logf("Generated token: %s", token)
	t.Logf("User ID: %d", user.Id)

	// Setup router with authentication
	router := gin.Default()
	router.Use(middleware.CorsMiddleware())
	v1 := router.Group("/api/v1/backend")
	v1.Use(middleware.AuthMiddleware())

	return router, v1, token
}

func TestGetAllDriverDeviceAssignments(t *testing.T) {
	router, v1, token := setupBackendDriverDeviceAssignmentsTest(t)

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test driver
	driver := models.Driver{
		ClientId: client.Id,
		Name:     "Test Driver",
		Status:   "active",
	}
	config.DB.Create(&driver)

	// Create test protocol and device type first
	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)
	deviceType := models.DeviceType{ProtocolId: protocol.Id, Name: "Test Device Type", Active: true}
	config.DB.Create(&deviceType)

	// Create test client device
	device := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "123456789012345",
		AssetType:    "vehicle",
		Status:       "active",
	}
	config.DB.Create(&device)

	// Create test assignments
	assignment1 := models.DriverDeviceAssignment{
		DriverId:       driver.Id,
		ClientDeviceId: device.Id,
		AssignmentDate: time.Now(),
		Status:         "active",
	}
	assignment2 := models.DriverDeviceAssignment{
		DriverId:       driver.Id,
		ClientDeviceId: device.Id,
		AssignmentDate: time.Now(),
		Status:         "inactive",
	}
	config.DB.Create(&assignment1)
	config.DB.Create(&assignment2)

	// Add routes
	assignments := v1.Group("/driver-device-assignments")
	assignments.GET("", GetAllDriverDeviceAssignments)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/backend/driver-device-assignments", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 2)
}

func TestGetDriverDeviceAssignmentById(t *testing.T) {
	router, v1, token := setupBackendDriverDeviceAssignmentsTest(t)

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test driver
	driver := models.Driver{
		ClientId: client.Id,
		Name:     "Test Driver",
		Status:   "active",
	}
	config.DB.Create(&driver)

	// Create test protocol and device type first
	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)
	deviceType := models.DeviceType{ProtocolId: protocol.Id, Name: "Test Device Type", Active: true}
	config.DB.Create(&deviceType)

	// Create test client device
	device := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "123456789012345",
		AssetType:    "vehicle",
		Status:       "active",
	}
	config.DB.Create(&device)

	// Create test assignment
	assignment := models.DriverDeviceAssignment{
		DriverId:       driver.Id,
		ClientDeviceId: device.Id,
		AssignmentDate: time.Now(),
		Status:         "active",
	}
	config.DB.Create(&assignment)

	// Add routes
	assignments := v1.Group("/driver-device-assignments")
	assignments.GET("/:id", GetDriverDeviceAssignmentById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/backend/driver-device-assignments/%d", assignment.Id), nil)
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "active", data["status"])
}

func TestGetDriverDeviceAssignmentByIdNotFound(t *testing.T) {
	router, v1, token := setupBackendDriverDeviceAssignmentsTest(t)

	// Add routes
	assignments := v1.Group("/driver-device-assignments")
	assignments.GET("/:id", GetDriverDeviceAssignmentById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/backend/driver-device-assignments/999", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Driver device assignment not found", response["message"])
}

func TestCreateDriverDeviceAssignment(t *testing.T) {
	router, v1, token := setupBackendDriverDeviceAssignmentsTest(t)

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test driver
	driver := models.Driver{
		ClientId: client.Id,
		Name:     "Test Driver",
		Status:   "active",
	}
	config.DB.Create(&driver)

	// Create test protocol and device type first
	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)
	deviceType := models.DeviceType{ProtocolId: protocol.Id, Name: "Test Device Type", Active: true}
	config.DB.Create(&deviceType)

	// Create test client device
	device := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "123456789012345",
		AssetType:    "vehicle",
		Status:       "active",
	}
	config.DB.Create(&device)

	// Add routes
	assignments := v1.Group("/driver-device-assignments")
	assignments.POST("", CreateDriverDeviceAssignment)

	// Test data
	assignmentData := map[string]interface{}{
		"driver_id":        driver.Id,
		"client_device_id": device.Id,
		"assignment_date":  time.Now().Format("2006-01-02T15:04:05Z07:00"),
		"status":           "active",
	}

	jsonData, _ := json.Marshal(assignmentData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/driver-device-assignments", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Driver device assignment created successfully", response["message"])

	// Verify assignment was created in database
	var assignment models.DriverDeviceAssignment
	err = config.DB.Where("driver_id = ? AND client_device_id = ?", driver.Id, device.Id).First(&assignment).Error
	assert.NoError(t, err)
	assert.Equal(t, driver.Id, assignment.DriverId)
	assert.Equal(t, device.Id, assignment.ClientDeviceId)
}

func TestCreateDriverDeviceAssignmentInvalidRequest(t *testing.T) {
	router, v1, token := setupBackendDriverDeviceAssignmentsTest(t)

	// Add routes
	assignments := v1.Group("/driver-device-assignments")
	assignments.POST("", CreateDriverDeviceAssignment)

	// Test data with invalid JSON
	jsonData := []byte(`{"driver_id": 1, "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/backend/driver-device-assignments", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateDriverDeviceAssignment(t *testing.T) {
	router, v1, token := setupBackendDriverDeviceAssignmentsTest(t)

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test driver
	driver := models.Driver{
		ClientId: client.Id,
		Name:     "Test Driver",
		Status:   "active",
	}
	config.DB.Create(&driver)

	// Create test protocol and device type first
	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)
	deviceType := models.DeviceType{ProtocolId: protocol.Id, Name: "Test Device Type", Active: true}
	config.DB.Create(&deviceType)

	// Create test client device
	device := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "123456789012345",
		AssetType:    "vehicle",
		Status:       "active",
	}
	config.DB.Create(&device)

	// Create test assignment
	assignment := models.DriverDeviceAssignment{
		DriverId:       driver.Id,
		ClientDeviceId: device.Id,
		AssignmentDate: time.Now(),
		Status:         "active",
	}
	config.DB.Create(&assignment)

	// Add routes
	assignments := v1.Group("/driver-device-assignments")
	assignments.PUT("/:id", UpdateDriverDeviceAssignment)

	// Test data
	updateData := map[string]interface{}{
		"driver_id":        driver.Id,
		"client_device_id": device.Id,
		"assignment_date":  time.Now().Format("2006-01-02T15:04:05Z07:00"),
		"status":           "inactive",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/backend/driver-device-assignments/%d", assignment.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Driver device assignment updated successfully", response["message"])

	// Verify assignment was updated in database
	var updatedAssignment models.DriverDeviceAssignment
	err = config.DB.First(&updatedAssignment, assignment.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "inactive", updatedAssignment.Status)
}

func TestUpdateDriverDeviceAssignmentNotFound(t *testing.T) {
	router, v1, token := setupBackendDriverDeviceAssignmentsTest(t)

	// Add routes
	assignments := v1.Group("/driver-device-assignments")
	assignments.PUT("/:id", UpdateDriverDeviceAssignment)

	// Test data
	updateData := map[string]interface{}{
		"driver_id":        1,
		"client_device_id": 1,
		"assignment_date":  time.Now().Format("2006-01-02T15:04:05Z07:00"),
		"status":           "inactive",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/backend/driver-device-assignments/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Driver device assignment not found", response["message"])
}

func TestDeleteDriverDeviceAssignment(t *testing.T) {
	router, v1, token := setupBackendDriverDeviceAssignmentsTest(t)

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test driver
	driver := models.Driver{
		ClientId: client.Id,
		Name:     "Test Driver",
		Status:   "active",
	}
	config.DB.Create(&driver)

	// Create test protocol and device type first
	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)
	deviceType := models.DeviceType{ProtocolId: protocol.Id, Name: "Test Device Type", Active: true}
	config.DB.Create(&deviceType)

	// Create test client device
	device := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "123456789012345",
		AssetType:    "vehicle",
		Status:       "active",
	}
	config.DB.Create(&device)

	// Create test assignment
	assignment := models.DriverDeviceAssignment{
		DriverId:       driver.Id,
		ClientDeviceId: device.Id,
		AssignmentDate: time.Now(),
		Status:         "active",
	}
	config.DB.Create(&assignment)

	// Add routes
	assignments := v1.Group("/driver-device-assignments")
	assignments.DELETE("/:id", DeleteDriverDeviceAssignment)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/backend/driver-device-assignments/%d", assignment.Id), nil)
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)

	// Verify assignment was deleted from database
	var deletedAssignment models.DriverDeviceAssignment
	err := config.DB.First(&deletedAssignment, assignment.Id).Error
	assert.Error(t, err) // Should not find the assignment
}

func TestDeleteDriverDeviceAssignmentNotFound(t *testing.T) {
	router, v1, token := setupBackendDriverDeviceAssignmentsTest(t)

	// Add routes
	assignments := v1.Group("/driver-device-assignments")
	assignments.DELETE("/:id", DeleteDriverDeviceAssignment)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/backend/driver-device-assignments/999", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Driver device assignment not found", response["message"])
}

func TestSearchDriverDeviceAssignments(t *testing.T) {
	router, v1, token := setupBackendDriverDeviceAssignmentsTest(t)

	// Create test client, driver, and device first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	driver := models.Driver{
		ClientId: client.Id,
		Name:     "Test Driver",
		Status:   "active",
	}
	config.DB.Create(&driver)

	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)
	deviceType := models.DeviceType{ProtocolId: protocol.Id, Name: "Test Device Type", Active: true}
	config.DB.Create(&deviceType)

	device := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "123456789012345",
		AssetType:    "vehicle",
		Status:       "active",
	}
	config.DB.Create(&device)

	// Create test assignments
	assignment1 := models.DriverDeviceAssignment{
		DriverId:       driver.Id,
		ClientDeviceId: device.Id,
		AssignmentDate: time.Now(),
		Status:         "active",
	}
	assignment2 := models.DriverDeviceAssignment{
		DriverId:       driver.Id,
		ClientDeviceId: device.Id,
		AssignmentDate: time.Now(),
		Status:         "inactive",
	}
	config.DB.Create(&assignment1)
	config.DB.Create(&assignment2)

	// Add routes
	assignments := v1.Group("/driver-device-assignments")
	assignments.GET("/search", SearchDriverDeviceAssignments)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/backend/driver-device-assignments/search?status=active", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 1) // Should only find active assignments
}

func TestSearchDriverDeviceAssignmentsNoResults(t *testing.T) {
	router, v1, token := setupBackendDriverDeviceAssignmentsTest(t)

	// Create test client, driver, and device first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	driver := models.Driver{
		ClientId: client.Id,
		Name:     "Test Driver",
		Status:   "active",
	}
	config.DB.Create(&driver)

	protocol := models.Protocol{Name: "Test Protocol", Active: true}
	config.DB.Create(&protocol)
	deviceType := models.DeviceType{ProtocolId: protocol.Id, Name: "Test Device Type", Active: true}
	config.DB.Create(&deviceType)

	device := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "123456789012345",
		AssetType:    "vehicle",
		Status:       "active",
	}
	config.DB.Create(&device)

	// Create test assignment
	assignment := models.DriverDeviceAssignment{
		DriverId:       driver.Id,
		ClientDeviceId: device.Id,
		AssignmentDate: time.Now(),
		Status:         "active",
	}
	config.DB.Create(&assignment)

	// Add routes
	assignments := v1.Group("/driver-device-assignments")
	assignments.GET("/search", SearchDriverDeviceAssignments)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/backend/driver-device-assignments/search?search=Nonexistent", nil)
	req.Header.Set("Authorization", "Bearer "+token)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 0) // Should find no results
}
