package controllers

import (
	"log"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/mail"
	"yotracker/internal/models"
	"yotracker/internal/response"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
)

func GetAllClients(c *gin.Context) {
	var clients []models.Client
	var total int64
	filter := map[string]interface{}{}
	if clientType := c.Query("client_type"); clientType != "" {
		filter["client_type"] = clientType
	}
	if staffId := c.Query("staff_id"); staffId != "" {
		filter["staff_id"] = staffId
	}
	config.DB.Scopes(utils.Paginate(c)).Where(filter).Order("id desc").Find(&clients)
	config.DB.Model(&models.Client{}).Where(filter).Count(&total)

	var clientResponses []response.ClientResponse
	for _, client := range clients {
		clientResponse := response.ClientResponse{
			Id:                    client.Id,
			Name:                  client.Name,
			Email:                 client.Email,
			ClientType:            client.ClientType,
			Status:                client.Status,
			CreatedById:           client.CreatedById,
			PhoneNumber:           client.PhoneNumber,
			ReferredById:          client.ReferredById,
			BranchId:              client.BranchId,
			StaffId:               client.StaffId,
			CountryId:             client.CountryId,
			Gender:                client.Gender,
			Company:               client.Company,
			State:                 client.State,
			City:                  client.City,
			Town:                  client.Town,
			Address:               client.Address,
			Description:           client.Description,
			MapsProvider:          client.MapsProvider,
			DefaultLandingPage:    client.DefaultLandingPage,
			BillingCycle:          client.BillingCycle,
			BillingDay:            client.BillingDay,
			IsLifetime:            client.IsLifetime,
			NextBillingDate:       utils.FormatTimeOrEmptyString(client.NextBillingDate),
			LastBilledAt:          utils.FormatTimeOrEmptyString(client.LastBilledAt),
			SuspendedAt:           utils.FormatTimeOrEmptyString(client.SuspendedAt),
			WhatsappAlertsEnabled: client.WhatsappAlertsEnabled,
			SmsAlertsEnabled:      client.SmsAlertsEnabled,
			EmailAlertsEnabled:    client.EmailAlertsEnabled,
			WhatsappPhoneNumber:   client.WhatsappPhoneNumber,
			SmsPhoneNumber:        client.SmsPhoneNumber,
			AlertsEmail:           client.AlertsEmail,
			CreatedAt:             utils.FormatTimeOrEmptyString(&client.CreatedAt),
			UpdatedAt:             utils.FormatTimeOrEmptyString(&client.UpdatedAt),
		}
		clientResponses = append(clientResponses, clientResponse)
	}
	// Extract current_page and per_page from query params (same logic as utils.Paginate)
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}
	perPage, _ := strconv.Atoi(c.Query("per_page"))
	switch {
	case perPage > 100:
		perPage = 100
	case perPage <= 0:
		perPage = 10
	}
	c.JSON(http.StatusOK, gin.H{
		"data":         clientResponses,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
	})
}
func GetClientById(c *gin.Context) {
	var client models.Client
	if err := config.DB.Preload("Country").Preload("Currency").First(&client, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client not found",
		})
		return
	}
	//get client total invoices and total payments
	var totalInvoicesAmount float64
	config.DB.Model(&models.Invoice{}).Where("client_id = ?", client.Id).Select("COALESCE(SUM(amount), 0)").Scan(&totalInvoicesAmount)
	var totalPayments float64
	config.DB.Model(&models.InvoicePayment{}).Where("invoice_id IN (?)", config.DB.Model(&models.Invoice{}).Select("id").Where("client_id = ?", client.Id)).Select("COALESCE(SUM(amount), 0)").Scan(&totalPayments)
	balance := totalInvoicesAmount - totalPayments
	clientResponse := response.ClientResponse{
		Id:                    client.Id,
		Name:                  client.Name,
		Email:                 client.Email,
		ClientType:            client.ClientType,
		Status:                client.Status,
		CreatedById:           client.CreatedById,
		PhoneNumber:           client.PhoneNumber,
		ReferredById:          client.ReferredById,
		BranchId:              client.BranchId,
		StaffId:               client.StaffId,
		CountryId:             client.CountryId,
		Country:               client.Country,
		Gender:                client.Gender,
		Company:               client.Company,
		State:                 client.State,
		City:                  client.City,
		Town:                  client.Town,
		Address:               client.Address,
		Description:           client.Description,
		MapsProvider:          client.MapsProvider,
		DefaultLandingPage:    client.DefaultLandingPage,
		CurrencyId:            client.CurrencyId,
		Currency:              client.Currency,
		BillingCycle:          client.BillingCycle,
		BillingDay:            client.BillingDay,
		IsLifetime:            client.IsLifetime,
		NextBillingDate:       utils.FormatTimeOrEmptyString(client.NextBillingDate),
		LastBilledAt:          utils.FormatTimeOrEmptyString(client.LastBilledAt),
		SuspendedAt:           utils.FormatTimeOrEmptyString(client.SuspendedAt),
		WhatsappAlertsEnabled: client.WhatsappAlertsEnabled,
		SmsAlertsEnabled:      client.SmsAlertsEnabled,
		EmailAlertsEnabled:    client.EmailAlertsEnabled,
		WhatsappPhoneNumber:   client.WhatsappPhoneNumber,
		SmsPhoneNumber:        client.SmsPhoneNumber,
		AlertsEmail:           client.AlertsEmail,
		CreatedAt:             utils.FormatTimeOrEmptyString(&client.CreatedAt),
		UpdatedAt:             utils.FormatTimeOrEmptyString(&client.UpdatedAt),
		TotalInvoicesAmount:   &totalInvoicesAmount,
		TotalPayments:         &totalPayments,
		TotalBalance:          &balance,
	}
	c.JSON(http.StatusOK, gin.H{
		"data": clientResponse,
	})
}
func CreateClient(c *gin.Context) {
	var req models.ClientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	userIDValue, _ := c.Get("userID")
	userID := userIDValue.(uint)
	client := models.Client{
		Name:                  req.Name,
		Email:                 req.Email,
		ClientType:            req.ClientType,
		Status:                req.Status,
		CreatedById:           userID,
		PhoneNumber:           req.PhoneNumber,
		CountryId:             req.CountryId,
		CurrencyId:            req.CurrencyId,
		StaffId:               req.StaffId,
		ReferredById:          req.ReferredById,
		MapsProvider:          req.MapsProvider,
		DefaultLandingPage:    req.DefaultLandingPage,
		BillingCycle:          &req.BillingCycle,
		BillingDay:            &req.BillingDay,
		IsLifetime:            &req.IsLifetime,
		NextBillingDate:       req.NextBillingDate,
		Gender:                &req.Gender,
		Company:               &req.Company,
		State:                 &req.State,
		City:                  &req.City,
		Town:                  &req.Town,
		Address:               &req.Address,
		Description:           &req.Description,
		WhatsappAlertsEnabled: req.WhatsappAlertsEnabled,
		SmsAlertsEnabled:      req.SmsAlertsEnabled,
		EmailAlertsEnabled:    req.EmailAlertsEnabled,
		WhatsappPhoneNumber:   req.WhatsappPhoneNumber,
		SmsPhoneNumber:        req.SmsPhoneNumber,
		AlertsEmail:           req.AlertsEmail,
	}

	result := config.DB.Create(&client)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	// Send welcome email to new client (non-blocking)
	go func() {
		err := mail.SendWelcomeEmail(client.Email, client.Name)
		if err != nil {
			// Log error but don't fail the request
			log.Printf("Failed to send welcome email to client %s: %v", client.Email, err)
		}
	}()

	c.JSON(http.StatusCreated, gin.H{
		"message": "Client created successfully",
		"email":   "Welcome email will be sent shortly",
	})
}
func UpdateClient(c *gin.Context) {
	var req models.ClientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var client models.Client
	if err := config.DB.First(&client, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client not found",
		})
		return
	}
	client.Name = req.Name
	client.Email = req.Email
	client.ClientType = req.ClientType
	client.Status = req.Status
	client.PhoneNumber = req.PhoneNumber
	client.ReferredById = req.ReferredById
	client.StaffId = req.StaffId
	client.CurrencyId = req.CurrencyId
	client.CountryId = req.CountryId
	client.MapsProvider = req.MapsProvider
	client.DefaultLandingPage = req.DefaultLandingPage
	client.Gender = &req.Gender
	client.State = &req.State
	client.City = &req.City
	client.Town = &req.Town
	client.Address = &req.Address
	client.Description = &req.Description
	client.BillingCycle = &req.BillingCycle
	client.BillingDay = &req.BillingDay
	client.IsLifetime = &req.IsLifetime
	client.NextBillingDate = req.NextBillingDate
	client.WhatsappAlertsEnabled = req.WhatsappAlertsEnabled
	client.SmsAlertsEnabled = req.SmsAlertsEnabled
	client.EmailAlertsEnabled = req.EmailAlertsEnabled
	client.WhatsappPhoneNumber = req.WhatsappPhoneNumber
	client.SmsPhoneNumber = req.SmsPhoneNumber
	client.AlertsEmail = req.AlertsEmail
	result := config.DB.Save(&client)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Client updated successfully"})
}
func DeleteClient(c *gin.Context) {
	var client models.Client
	if err := config.DB.First(&client, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client not found",
		})
		return
	}
	result := config.DB.Delete(&client)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusNoContent, gin.H{"message": "Client deleted successfully"})

}
func SearchClients(c *gin.Context) {
	var clients []models.Client
	query := config.DB
	if search := c.Query("s"); search != "" {
		query.Where("(name like ? or id like ? or email like ? or phone_number like ?)", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}
	query.Find(&clients)
	var clientResponses []response.ClientResponse
	for _, client := range clients {
		clientResponse := response.ClientResponse{
			Id:                    client.Id,
			Name:                  client.Name,
			Email:                 client.Email,
			ClientType:            client.ClientType,
			Status:                client.Status,
			CreatedById:           client.CreatedById,
			PhoneNumber:           client.PhoneNumber,
			ReferredById:          client.ReferredById,
			BranchId:              client.BranchId,
			StaffId:               client.StaffId,
			CountryId:             client.CountryId,
			CurrencyId:            client.CurrencyId,
			Gender:                client.Gender,
			Company:               client.Company,
			MapsProvider:          client.MapsProvider,
			State:                 client.State,
			City:                  client.City,
			Town:                  client.Town,
			Address:               client.Address,
			Description:           client.Description,
			BillingCycle:          client.BillingCycle,
			BillingDay:            client.BillingDay,
			IsLifetime:            client.IsLifetime,
			WhatsappAlertsEnabled: client.WhatsappAlertsEnabled,
			SmsAlertsEnabled:      client.SmsAlertsEnabled,
			EmailAlertsEnabled:    client.EmailAlertsEnabled,
			WhatsappPhoneNumber:   client.WhatsappPhoneNumber,
			SmsPhoneNumber:        client.SmsPhoneNumber,
			AlertsEmail:           client.AlertsEmail,
			CreatedAt:             utils.FormatTimeOrEmptyString(&client.CreatedAt),
			UpdatedAt:             utils.FormatTimeOrEmptyString(&client.UpdatedAt),
		}
		clientResponses = append(clientResponses, clientResponse)
	}
	c.JSON(http.StatusOK, gin.H{
		"data": clientResponses,
	})
}
func CreateClientUser(c *gin.Context) {
	var req models.CreateClientUserRequest
	clientId, _ := strconv.Atoi(c.Param("id"))
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	if req.ExistingUser {
		var user models.User
		userId, _ := strconv.Atoi(c.Param("id"))
		if err := config.DB.First(&user, req.UserId).Error; err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"message": "User not found",
			})
			return
		}
		//check if we don't have a client user already
		var clientUser models.ClientUser
		if err := config.DB.Where("client_id = ? AND user_id = ?", clientId, userId).First(&clientUser).Error; err == nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "User already added",
			})
			return
		}

		clientUser = models.ClientUser{
			ClientId: uint(clientId),
			UserId:   uint(userId),
		}
		result := config.DB.Create(&clientUser)
		if result.Error != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": result.Error.Error(),
			})
			return
		}
		c.JSON(http.StatusOK, gin.H{
			"message": "User added successfully",
		})
		return
	}
	if req.Password == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Password is required",
		})
		return
	}
	if req.Email == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Email is required",
		})
		return
	}
	if req.Name == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Name is required",
		})
		return
	}

	hashedPassword := services.HashPassword(*req.Password)
	status := "active"
	userType := "client"
	emailVerifiedAt := time.Now().Format(time.RFC3339)
	user := models.User{
		Name:            *req.Name,
		Email:           *req.Email,
		Password:        hashedPassword,
		Status:          &status,
		Gender:          req.Gender,
		TelegramUserId:  req.TelegramUserId,
		SlackWebhookUrl: req.SlackWebhookUrl,
		Description:     req.Description,
		UserType:        userType,
		Username:        req.Username,
		EmailVerifiedAt: &emailVerifiedAt,
	}
	result := config.DB.Create(&user)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	clientUser := models.ClientUser{
		ClientId: uint(clientId),
		UserId:   user.Id,
	}
	result = config.DB.Create(&clientUser)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	// Send new account email with credentials
	go func() {
		err := mail.SendNewAccountEmail(user.Email, user.Name, *req.Password)
		if err != nil {
			// Log error but don't fail the request
			log.Printf("Failed to send new account email to %s: %v", user.Email, err)
		}
	}()

	c.JSON(http.StatusOK, gin.H{
		"message": "User created successfully",
		"email":   "Account credentials will be sent via email",
	})
}
func DeleteClientUser(c *gin.Context) {
	id := c.Param("id")
	var clientUser models.ClientUser
	if err := config.DB.First(&clientUser, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client user not found",
		})
		return
	}
	result := config.DB.Delete(&clientUser)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Client user deleted successfully"})
}
func UpdateClientUser(c *gin.Context) {

}
