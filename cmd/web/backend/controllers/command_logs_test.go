package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupBackendCommandLogsTest(t *testing.T) (*gin.Engine, string) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupBackendCommandLogsTestEnvVars()

	config.InitTestDB()

	// Clean up existing test data
	cleanupBackendCommandLogsTestData()

	// Create test user
	user := createBackendCommandLogsTestUser(t)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())
	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/backend")
	v1.Use(middleware.AuthMiddleware())
	// Add command logs routes
	v1.GET("/command_logs", GetAllCommandLogs)
	v1.GET("/command_logs/:id", GetCommandLogById)
	v1.POST("/command_logs", CreateCommandLog)

	return r, token
}

func setupBackendCommandLogsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func cleanupBackendCommandLogsTestData() {
	config.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	defer config.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")
	config.DB.Where("1=1").Delete(&models.CommandLog{})
	config.DB.Where("1=1").Delete(&models.ClientDevice{})
	config.DB.Where("email LIKE ?", "%backend-command_logs%").Delete(&models.Client{})
	config.DB.Where("email LIKE ?", "%backend-command_logs%").Delete(&models.User{})
}

func createBackendCommandLogsTestUser(t *testing.T) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Backend Command Logs Admin",
		UserType: "backend",
		Status:   &status,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func createBackendCommandLogsTestClient(t *testing.T) models.Client {
	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Backend Command Logs Test Client",
		Email:      "<EMAIL>",
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createBackendCommandLogsTestDevice(t *testing.T, clientId uint) models.ClientDevice {
	// Create a test protocol first
	protocol := models.Protocol{
		Name:   "Test Protocol",
		Active: true,
	}
	config.DB.Create(&protocol)

	// Create a test device type
	deviceType := models.DeviceType{
		ProtocolId: protocol.Id,
		Name:       "Test Device Type",
		Active:     true,
	}
	config.DB.Create(&deviceType)

	status := "active"

	device := models.ClientDevice{
		ClientId:     clientId,
		DeviceTypeId: deviceType.Id,
		AssetType:    "vehicle",
		Status:       status,
	}

	result := config.DB.Create(&device)
	assert.NoError(t, result.Error)

	return device
}

func createBackendCommandLogsTestCommandLog(t *testing.T, deviceId uint, createdAt time.Time) models.CommandLog {
	command := "TEST_COMMAND"
	response := "SUCCESS"

	commandLog := models.CommandLog{
		ClientDeviceId:  deviceId,
		CommandContent:  &command,
		CommandResponse: &response,
		CreatedAt:       createdAt,
	}

	result := config.DB.Create(&commandLog)
	assert.NoError(t, result.Error)

	return commandLog
}

// Test GetAllCommandLogs functionality
func TestBackendGetAllCommandLogs(t *testing.T) {
	r, token := setupBackendCommandLogsTest(t)

	// Create test client, device and command logs
	client := createBackendCommandLogsTestClient(t)
	device := createBackendCommandLogsTestDevice(t, client.Id)
	createdAt1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	createdAt2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)

	_ = createBackendCommandLogsTestCommandLog(t, device.Id, createdAt1)
	_ = createBackendCommandLogsTestCommandLog(t, device.Id, createdAt2)

	t.Run("Get all command logs successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2)
		assert.Equal(t, float64(2), response["total"])
	})

	t.Run("Get all command logs without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetAllCommandLogs with date filtering
func TestBackendGetAllCommandLogsWithDateFiltering(t *testing.T) {
	r, token := setupBackendCommandLogsTest(t)

	// Create test client, device and command logs with different dates
	client := createBackendCommandLogsTestClient(t)
	device := createBackendCommandLogsTestDevice(t, client.Id)
	createdAt1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	createdAt2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)
	createdAt3 := time.Date(2024, 2, 1, 9, 0, 0, 0, time.UTC)

	_ = createBackendCommandLogsTestCommandLog(t, device.Id, createdAt1)
	_ = createBackendCommandLogsTestCommandLog(t, device.Id, createdAt2)
	_ = createBackendCommandLogsTestCommandLog(t, device.Id, createdAt3)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedCount  int
	}{
		{
			name:           "Get command logs for specific date",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get command logs for date range",
			queryParams:    "?start_date=2024-01-15&end_date=2024-01-25",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:           "Get command logs with search and date filter",
			queryParams:    "?s=TEST_COMMAND&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get command logs with device filter and date",
			queryParams:    "?device_id=" + strconv.FormatUint(uint64(device.Id), 10) + "&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.Contains(t, response, "data")
				assert.Contains(t, response, "total")

				// Verify data count
				data := response["data"].([]interface{})
				assert.Len(t, data, tt.expectedCount)
			}
		})
	}
}

// Test GetCommandLogById functionality
func TestBackendGetCommandLogById(t *testing.T) {
	r, token := setupBackendCommandLogsTest(t)

	// Create test client, device and command log
	client := createBackendCommandLogsTestClient(t)
	device := createBackendCommandLogsTestDevice(t, client.Id)
	createdAt := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	commandLog := createBackendCommandLogsTestCommandLog(t, device.Id, createdAt)

	t.Run("Get command log by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs/"+strconv.FormatUint(uint64(commandLog.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify command log data
		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(commandLog.Id), data["id"])
		assert.Equal(t, float64(device.Id), data["client_device_id"])
	})

	t.Run("Get command log by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Get command log by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs/"+strconv.FormatUint(uint64(commandLog.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test CreateCommandLog functionality
func TestBackendCreateCommandLog(t *testing.T) {
	r, token := setupBackendCommandLogsTest(t)

	// Create test client and device
	client := createBackendCommandLogsTestClient(t)
	device := createBackendCommandLogsTestDevice(t, client.Id)

	t.Run("Create command log successfully", func(t *testing.T) {

		commandLogData := map[string]interface{}{
			"client_device_id": device.Id,
			"name":             "Test Command Log",
			"select_type":      "device",
			"command_type":     "tcp",
			"command_content":  "NEW_TEST_COMMAND",
			"command_response": "SUCCESS",
		}

		jsonData, _ := json.Marshal(commandLogData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/command_logs", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "Command created successfully", response["message"])
	})

	t.Run("Create command log with missing required fields", func(t *testing.T) {
		commandLogData := map[string]interface{}{
			"client_device_id": device.Id,
			// Missing command and response
		}

		jsonData, _ := json.Marshal(commandLogData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/command_logs", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create command log with non-existent device", func(t *testing.T) {

		commandLogData := map[string]interface{}{
			"client_device_id": 99999,
			"name":             "Test Command Log",
			"select_type":      "device",
			"command_type":     "tcp",
			"command_content":  "NEW_TEST_COMMAND",
			"command_response": "SUCCESS",
		}

		jsonData, _ := json.Marshal(commandLogData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/command_logs", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Should return 400 Bad Request due to foreign key constraint
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create command log without authorization", func(t *testing.T) {

		commandLogData := map[string]interface{}{
			"client_device_id": device.Id,
			"name":             "Test Command Log",
			"select_type":      "device",
			"command_type":     "tcp",
			"command_content":  "NEW_TEST_COMMAND",
			"command_response": "SUCCESS",
		}

		jsonData, _ := json.Marshal(commandLogData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/command_logs", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test SearchCommandLogs functionality
func TestBackendSearchCommandLogs(t *testing.T) {
	r, token := setupBackendCommandLogsTest(t)

	// Create test client, device and command logs
	client := createBackendCommandLogsTestClient(t)
	device := createBackendCommandLogsTestDevice(t, client.Id)
	createdAt1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	createdAt2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)

	_ = createBackendCommandLogsTestCommandLog(t, device.Id, createdAt1)
	_ = createBackendCommandLogsTestCommandLog(t, device.Id, createdAt2)

	t.Run("Search command logs by command type", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs?command_type=tcp", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2) // Both command logs have command_type=tcp
	})

	t.Run("Search command logs with date filter", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs?start_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Only one command log on 2024-01-15
	})

	t.Run("Search command logs with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs?command_type=invalid_type", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // No results
	})
}

// Test pagination functionality
func TestBackendCommandLogsPagination(t *testing.T) {
	r, token := setupBackendCommandLogsTest(t)

	// Create test client, device and multiple command logs for pagination testing
	client := createBackendCommandLogsTestClient(t)
	device := createBackendCommandLogsTestDevice(t, client.Id)
	for i := 1; i <= 15; i++ {
		createdAt := time.Date(2024, 1, i, 10, 0, 0, 0, time.UTC)
		createBackendCommandLogsTestCommandLog(t, device.Id, createdAt)
	}

	t.Run("First page with default pagination", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")
		assert.Contains(t, response, "total_pages")

		// Verify pagination values
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.Equal(t, float64(15), response["total"])
		assert.Equal(t, float64(2), response["total_pages"]) // 15 items, 10 per page = 2 pages

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 10)
	})

	t.Run("Second page", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 5) // Remaining 5 items on second page
	})
}

// Test edge cases and error handling
func TestBackendCommandLogsEdgeCases(t *testing.T) {
	r, token := setupBackendCommandLogsTest(t)

	t.Run("Invalid date format in query", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs?start_date=invalid-date", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("End date before start date", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs?start_date=2024-01-20&end_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid command log ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/command_logs/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
