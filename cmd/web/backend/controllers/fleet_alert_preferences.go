package controllers

import (
	"net/http"
	"strconv"

	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
)

// GetFleetAlertPreferences retrieves all alert preferences for a specific fleet
func GetFleetAlertPreferences(c *gin.Context) {
	fleetId, err := strconv.ParseUint(c.Param("fleetId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid fleet ID",
		})
		return
	}

	var preferences []models.FleetAlertPreference
	if err := config.DB.Where("fleet_id = ?", fleetId).Preload("Fleet").Find(&preferences).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to retrieve fleet alert preferences",
			"error":   err.Error(),
		})
		return
	}

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"message": "Fleet alert preferences retrieved successfully",
		"data":    preferences,
	})
}

// GetFleetAlertPreferenceById retrieves a specific fleet alert preference by ID
func GetFleetAlertPreferenceById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid fleet alert preference ID",
		})
		return
	}

	var preference models.FleetAlertPreference
	if err := config.DB.Preload("Fleet").First(&preference, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Fleet alert preference not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Fleet alert preference retrieved successfully",
		"data":    preference,
	})
}

// CreateFleetAlertPreference creates a new fleet alert preference
func CreateFleetAlertPreference(c *gin.Context) {
	fleetId, err := strconv.ParseUint(c.Param("fleetId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid fleet ID",
		})
		return
	}

	var req models.AlertPreferenceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	// Verify fleet exists
	var fleet models.Fleet
	if err := config.DB.First(&fleet, fleetId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Fleet not found",
		})
		return
	}

	preference := models.FleetAlertPreference{
		FleetId:   uint(fleetId),
		AlertType: req.AlertType,
		Enabled:   req.Enabled,
		Channels:  req.Channels,
		Priority:  req.Priority,
	}

	if err := config.DB.Create(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to create fleet alert preference",
			"error":   err.Error(),
		})
		return
	}

	// Reload with fleet data
	config.DB.Preload("Fleet").First(&preference, preference.Id)

	c.JSON(http.StatusCreated, gin.H{
		"message": "Fleet alert preference created successfully",
		"data":    preference,
	})
}

// UpdateFleetAlertPreference updates an existing fleet alert preference
func UpdateFleetAlertPreference(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid fleet alert preference ID",
		})
		return
	}

	var req models.AlertPreferenceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	var preference models.FleetAlertPreference
	if err := config.DB.First(&preference, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Fleet alert preference not found",
		})
		return
	}

	// Update fields
	preference.AlertType = req.AlertType
	preference.Enabled = req.Enabled
	preference.Channels = req.Channels
	preference.Priority = req.Priority

	if err := config.DB.Save(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to update fleet alert preference",
			"error":   err.Error(),
		})
		return
	}

	// Reload with fleet data
	config.DB.Preload("Fleet").First(&preference, preference.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Fleet alert preference updated successfully",
		"data":    preference,
	})
}

// DeleteFleetAlertPreference deletes a fleet alert preference
func DeleteFleetAlertPreference(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid fleet alert preference ID",
		})
		return
	}

	var preference models.FleetAlertPreference
	if err := config.DB.First(&preference, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Fleet alert preference not found",
		})
		return
	}

	if err := config.DB.Delete(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to delete fleet alert preference",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Fleet alert preference deleted successfully",
	})
}
