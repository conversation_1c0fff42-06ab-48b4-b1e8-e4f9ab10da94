package controllers

import (
	"fmt"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

func SearchGpsData(c *gin.Context) {
	var gpsData []models.GPSData
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}

	config.DB.Preload("ClientDevice").Where(filter).Where("gps_timestamp BETWEEN ? AND ?", c.<PERSON><PERSON>("start_date"), c.Query("end_date")).Order("id").Find(&gpsData)
	c.JSON(http.StatusOK, gin.H{
		"data": gpsData,
	})
}
func GetLastLocation(c *gin.Context) {
	var gpsData models.GPSData
	config.DB.Preload("ClientDevice").Where("client_device_id = ?", c.Query("client_device_id")).Last(&gpsData)

	// Check if location name is empty and fetch it using Google Geocoding API
	if gpsData.LocationName == nil || *gpsData.LocationName == "" {
		geocodingService := services.NewGoogleGeocodingService()
		err := geocodingService.UpdateGPSDataLocationName(&gpsData)
		if err != nil {
			// Log the error but don't fail the request
			fmt.Printf("Failed to fetch location name for GPS data %d: %v\n", gpsData.Id, err)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": gpsData,
	})
}
func GetGpsDataById(c *gin.Context) {
	var gpsData models.GPSData
	if err := config.DB.Preload("ClientDevice").First(&gpsData, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Gps data not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": gpsData,
	})
}
func CreateGpsData(c *gin.Context) {
	var req models.GPSDataRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	clientDevice := models.ClientDevice{DeviceId: req.DeviceId}
	config.DB.First(&clientDevice)
	gpsData := models.GPSData{
		ClientDeviceId: &clientDevice.Id,
		GPSTimestamp:   req.GPSTimestamp,
		Latitude:       req.Latitude,
		Longitude:      req.Longitude,
		Altitude:       req.Altitude,
		Speed:          req.Speed,
		Temperature:    req.Temperature,
		BatteryLevel:   req.BatteryLevel,
		Direction:      req.Direction,
		VehicleStatus:  req.VehicleStatus,
		IgnitionStatus: req.IgnitionStatus,
		CellId:         req.CellId,
		Mcc:            req.Mcc,
		Mnc:            req.Mnc,
		Lac:            req.Lac,
		Cid:            req.Cid,
		RawData:        req.RawData,
		AdditionalData: req.AdditionalData,
	}
	result := config.DB.Create(&gpsData)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}



	c.JSON(http.StatusCreated, gin.H{
		"message": "Gps data created successfully",
	})
}
func UpdateGpsData(c *gin.Context) {
	var req models.GPSDataRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var gpsData models.GPSData
	if err := config.DB.First(&gpsData, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Gps data not found",
		})
		return
	}
	gpsData.Latitude = req.Latitude
	gpsData.Longitude = req.Longitude
	gpsData.Altitude = req.Altitude
	gpsData.Speed = req.Speed
	gpsData.Temperature = req.Temperature
	gpsData.BatteryLevel = req.BatteryLevel
	gpsData.Direction = req.Direction
	gpsData.VehicleStatus = req.VehicleStatus
	gpsData.IgnitionStatus = req.IgnitionStatus
	gpsData.CellId = req.CellId
	gpsData.Mnc = req.Mnc
	gpsData.Cid = req.Cid
	gpsData.RawData = req.RawData
	gpsData.AdditionalData = req.AdditionalData
	result := config.DB.Save(&gpsData)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Gps data updated successfully"})
}
func DeleteGpsData(c *gin.Context) {
	var gpsData models.GPSData
	if err := config.DB.First(&gpsData, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Gps data not found",
		})
		return
	}
	result := config.DB.Delete(&gpsData)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	c.JSON(http.StatusNoContent, gin.H{"message": "Gps data deleted successfully"})
}
