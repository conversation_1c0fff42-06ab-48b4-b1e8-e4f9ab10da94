package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupBackendAlertsTest(t *testing.T) (*gin.Engine, string) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupBackendAlertsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test user
	user := createBackendAlertsTestUser(t)

	// Generate token after user is created and saved to database
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())

	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/backend")
	v1.Use(middleware.AuthMiddleware())

	// Alerts routes
	alerts := v1.Group("/alerts")
	alerts.GET("", GetAllAlerts)
	alerts.GET("/search", SearchAlerts)
	alerts.GET("/:id", GetAlertById)
	alerts.POST("", CreateAlert)
	alerts.PUT("/:id", UpdateAlert)
	alerts.DELETE("/:id", DeleteAlert)
	alerts.PUT("/:id/mark_read", MarkAlertAsRead)
	alerts.PUT("/:id/mark_unread", MarkAlertAsUnread)
	alerts.PUT("/bulk/mark_read", BulkMarkAlertsAsRead)
	alerts.PUT("/mark_all_read", MarkAllAlertsAsRead)

	return r, token
}

func setupBackendAlertsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createBackendAlertsTestUser(t *testing.T) models.User {
	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    fmt.Sprintf("<EMAIL>", time.Now().UnixNano()),
		Password: password,
		Name:     "Backend Alerts Admin",
		UserType: "backend",
		Status:   &status,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func createBackendAlertsTestClient(t *testing.T) models.Client {
	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Backend Alerts Test Client",
		Email:      fmt.Sprintf("<EMAIL>", time.Now().UnixNano()),
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createBackendAlertsTestDevice(t *testing.T, clientId uint) models.ClientDevice {
	status := "active"

	// Get the first available device type (should exist after seeding)
	var deviceType models.DeviceType
	result := config.DB.First(&deviceType)
	if result.Error != nil {
		// If no device type exists, create one with a protocol
		var protocol models.Protocol
		protocolResult := config.DB.First(&protocol)
		if protocolResult.Error != nil {
			// Create a protocol if none exists
			protocol = models.Protocol{
				Name: "Test Protocol",
			}
			config.DB.Create(&protocol)
		}

		deviceType = models.DeviceType{
			Name:       "Test Device Type",
			ProtocolId: protocol.Id,
		}
		config.DB.Create(&deviceType)
	}

	device := models.ClientDevice{
		ClientId:     clientId,
		DeviceTypeId: deviceType.Id,
		Status:       status,
	}

	result = config.DB.Create(&device)
	assert.NoError(t, result.Error)

	return device
}

func createBackendAlertsTestAlert(t *testing.T, deviceId uint, timestamp time.Time) models.Alert {
	message := "Test alert message"
	alertType := "speed"

	alert := models.Alert{
		ClientDeviceId: deviceId,
		AlertTimestamp: timestamp,
		Message:        &message,
		AlertType:      alertType,
	}

	result := config.DB.Create(&alert)
	assert.NoError(t, result.Error)

	return alert
}

// Test GetAllAlerts functionality
func TestBackendGetAllAlerts(t *testing.T) {
	r, token := setupBackendAlertsTest(t)

	// Create test client, device and alerts
	client := createBackendAlertsTestClient(t)
	device := createBackendAlertsTestDevice(t, client.Id)
	timestamp1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	timestamp2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)

	_ = createBackendAlertsTestAlert(t, device.Id, timestamp1)
	_ = createBackendAlertsTestAlert(t, device.Id, timestamp2)

	t.Run("Get all alerts successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2)
		assert.Equal(t, float64(2), response["total"])
	})

	t.Run("Get all alerts without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetAllAlerts with date filtering
func TestBackendGetAllAlertsWithDateFiltering(t *testing.T) {
	r, token := setupBackendAlertsTest(t)

	// Create test client, device and alerts with different dates
	client := createBackendAlertsTestClient(t)
	device := createBackendAlertsTestDevice(t, client.Id)
	timestamp1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	timestamp2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)
	timestamp3 := time.Date(2024, 2, 1, 9, 0, 0, 0, time.UTC)

	_ = createBackendAlertsTestAlert(t, device.Id, timestamp1)
	_ = createBackendAlertsTestAlert(t, device.Id, timestamp2)
	_ = createBackendAlertsTestAlert(t, device.Id, timestamp3)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedCount  int
	}{
		{
			name:           "Get alerts for specific date",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get alerts for date range",
			queryParams:    "?start_date=2024-01-15&end_date=2024-01-25",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:           "Get alerts with search and date filter",
			queryParams:    "?s=speed&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get alerts with device filter and date",
			queryParams:    "?client_device_id=" + strconv.FormatUint(uint64(device.Id), 10) + "&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.Contains(t, response, "data")
				assert.Contains(t, response, "total")

				// Verify data count - handle nil data safely
				if data, ok := response["data"].([]interface{}); ok {
					assert.Len(t, data, tt.expectedCount)
				} else {
					// If data is nil or not an array, check if it's empty
					assert.Nil(t, response["data"])
				}
			}
		})
	}
}

// Test GetAlertById functionality
func TestBackendGetAlertById(t *testing.T) {
	r, token := setupBackendAlertsTest(t)

	// Create test client, device and alert
	client := createBackendAlertsTestClient(t)
	device := createBackendAlertsTestDevice(t, client.Id)
	timestamp := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	alert := createBackendAlertsTestAlert(t, device.Id, timestamp)

	t.Run("Get alert by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts/"+strconv.FormatUint(uint64(alert.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify alert data - handle nil data safely
		if data, ok := response["data"].(map[string]interface{}); ok {
			assert.Equal(t, float64(alert.Id), data["id"])
			assert.Equal(t, float64(device.Id), data["client_device_id"])

			// Verify that the alert is now marked as read
			assert.NotNil(t, data["read"])
			assert.True(t, data["read"].(bool))
			assert.NotNil(t, data["read_at"])
		} else {
			t.Fatal("Expected data to be a map[string]interface{}")
		}
	})

	t.Run("Get alert by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Get alert by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts/"+strconv.FormatUint(uint64(alert.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Get alert by ID and verify auto-mark as read", func(t *testing.T) {
		// Create a new unread alert
		unreadAlert := createBackendAlertsTestAlert(t, device.Id, timestamp)

		// Explicitly set the alert as unread
		read := false
		config.DB.Model(&unreadAlert).Updates(map[string]interface{}{
			"read":    &read,
			"read_at": nil,
		})

		// Verify it's initially unread
		var initialAlert models.Alert
		err := config.DB.First(&initialAlert, unreadAlert.Id).Error
		assert.NoError(t, err)
		assert.NotNil(t, initialAlert.Read)
		assert.False(t, *initialAlert.Read)
		assert.Nil(t, initialAlert.ReadAt)

		// Retrieve the alert
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts/"+strconv.FormatUint(uint64(unreadAlert.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		// Verify the alert is now marked as read in the database
		var updatedAlert models.Alert
		err = config.DB.First(&updatedAlert, unreadAlert.Id).Error
		assert.NoError(t, err)
		assert.NotNil(t, updatedAlert.Read)
		assert.True(t, *updatedAlert.Read)
		assert.NotNil(t, updatedAlert.ReadAt)
	})
}

// Test CreateAlert functionality
func TestBackendCreateAlert(t *testing.T) {
	r, token := setupBackendAlertsTest(t)

	// Create test client and device
	client := createBackendAlertsTestClient(t)
	device := createBackendAlertsTestDevice(t, client.Id)

	t.Run("Create alert successfully", func(t *testing.T) {
		timestamp := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

		alertData := map[string]interface{}{
			"client_device_id": device.Id,
			"alert_timestamp":  timestamp.Format(time.RFC3339),
			"message":          "New test alert message",
			"alert_type":       "speed",
		}

		jsonData, _ := json.Marshal(alertData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/alerts", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "Alert created successfully", response["message"])
	})

	t.Run("Create alert with missing required fields", func(t *testing.T) {
		alertData := map[string]interface{}{
			"client_device_id": device.Id,
			// Missing alert_timestamp and message
		}

		jsonData, _ := json.Marshal(alertData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/alerts", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create alert with non-existent device", func(t *testing.T) {
		timestamp := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

		alertData := map[string]interface{}{
			"client_device_id": 99999,
			"alert_timestamp":  timestamp.Format(time.RFC3339),
			"message":          "New test alert message",
		}

		jsonData, _ := json.Marshal(alertData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/alerts", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create alert without authorization", func(t *testing.T) {
		timestamp := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

		alertData := map[string]interface{}{
			"client_device_id": device.Id,
			"alert_timestamp":  timestamp.Format(time.RFC3339),
			"message":          "New test alert message",
		}

		jsonData, _ := json.Marshal(alertData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/backend/alerts", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test SearchAlerts functionality
func TestBackendSearchAlerts(t *testing.T) {
	r, token := setupBackendAlertsTest(t)

	// Create test client, device and alerts
	client := createBackendAlertsTestClient(t)
	device := createBackendAlertsTestDevice(t, client.Id)
	timestamp1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	timestamp2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)

	_ = createBackendAlertsTestAlert(t, device.Id, timestamp1)
	_ = createBackendAlertsTestAlert(t, device.Id, timestamp2)

	t.Run("Search alerts by alert type", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts/search?alert_type=speed", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2) // Both alerts have alert_type=speed
	})

	t.Run("Search alerts with date filter", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts/search?start_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Only one alert on 2024-01-15
	})

	t.Run("Search alerts with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts/search?alert_type=invalid", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // No results
	})
}

// Test pagination functionality
func TestBackendAlertsPagination(t *testing.T) {
	r, token := setupBackendAlertsTest(t)

	// Create test client, device and multiple alerts for pagination testing
	client := createBackendAlertsTestClient(t)
	device := createBackendAlertsTestDevice(t, client.Id)
	for i := 1; i <= 15; i++ {
		timestamp := time.Date(2024, 1, i, 10, 0, 0, 0, time.UTC)
		createBackendAlertsTestAlert(t, device.Id, timestamp)
	}

	t.Run("First page with default pagination", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")
		assert.Contains(t, response, "total_pages")

		// Verify pagination values
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.Equal(t, float64(15), response["total"])
		assert.Equal(t, float64(2), response["total_pages"]) // 15 items, 10 per page = 2 pages

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 10)
	})

	t.Run("Second page", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 5) // Remaining 5 items on second page
	})
}

// Test edge cases and error handling
func TestBackendAlertsEdgeCases(t *testing.T) {
	r, token := setupBackendAlertsTest(t)

	t.Run("Invalid date format in query", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts?start_date=invalid-date", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("End date before start date", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts?start_date=2024-01-20&end_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid alert ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/backend/alerts/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
