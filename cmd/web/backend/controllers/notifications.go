package controllers

import (
	"encoding/json"
	"errors"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/response"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SendPushNotification sends a push notification to a specific device
func SendPushNotification(c *gin.Context) {
	var req models.PushNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Validate notification type
	if !services.ValidateNotificationType(req.Type) {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid notification type",
		})
		return
	}

	// Get current user from context
	userValue, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return
	}

	user := userValue.(models.User)

	// Create push notification service
	pushService := services.NewPushNotificationService()

	// Send the notification
	firebaseResp, err := pushService.SendPushNotification(services.PushNotificationRequest{
		To:             req.To,
		Title:          req.Title,
		Body:           req.Body,
		Type:           req.Type,
		Data:           req.Data,
		ClientDeviceID: req.ClientDeviceID,
		Timestamp:      req.Timestamp,
	})

	// Log the notification attempt
	notificationLog := models.NotificationLog{
		UserId:         &user.Id,
		ClientDeviceId: req.ClientDeviceID,
		FirebaseToken:  req.To,
		Title:          req.Title,
		Body:           req.Body,
		Type:           req.Type,
		Status:         "sent",
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Only set ClientId if it's not nil
	if user.ClientId != nil {
		notificationLog.ClientId = *user.ClientId
	}

	// Add data as JSON string if provided
	if req.Data != nil {
		dataJSON, _ := json.Marshal(req.Data)
		notificationLog.Data = string(dataJSON)
	}

	if err != nil {
		notificationLog.Status = "failed"
		errorMsg := err.Error()
		notificationLog.ErrorMessage = &errorMsg
	} else if firebaseResp != nil {
		if firebaseResp.Success > 0 && len(firebaseResp.Results) > 0 {
			notificationLog.Status = "sent"
			if firebaseResp.Results[0].MessageID != "" {
				notificationLog.FirebaseMessageId = &firebaseResp.Results[0].MessageID
			}
		} else {
			notificationLog.Status = "failed"
			if len(firebaseResp.Results) > 0 && firebaseResp.Results[0].Error != "" {
				notificationLog.ErrorMessage = &firebaseResp.Results[0].Error
			}
		}
	}

	// Save notification log
	config.DB.Create(&notificationLog)

	// Return response
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to send notification",
			"error":   err.Error(),
		})
		return
	}

	response := models.PushNotificationResponse{
		Success: firebaseResp.Success > 0,
		Message: "Notification sent successfully",
		Data: struct {
			Success int `json:"success"`
			Failure int `json:"failure"`
			Results []struct {
				MessageID string `json:"message_id,omitempty"`
				Error     string `json:"error,omitempty"`
			} `json:"results"`
		}{
			Success: firebaseResp.Success,
			Failure: firebaseResp.Failure,
			Results: firebaseResp.Results,
		},
	}

	c.JSON(http.StatusOK, response)
}

// SendBulkPushNotification sends push notifications to multiple devices
func SendBulkPushNotification(c *gin.Context) {
	var req models.BulkPushNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Validate notification type
	if !services.ValidateNotificationType(req.Type) {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid notification type",
		})
		return
	}

	// Get current user from context
	userValue, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return
	}

	user := userValue.(models.User)

	// Create push notification service
	pushService := services.NewPushNotificationService()

	// Convert bulk request to individual requests
	var individualRequests []services.PushNotificationRequest
	for _, token := range req.Tokens {
		individualRequests = append(individualRequests, services.PushNotificationRequest{
			To:    token,
			Title: req.Title,
			Body:  req.Body,
			Type:  req.Type,
			Data:  req.Data,
		})
	}

	// Send bulk notifications
	firebaseResp, err := pushService.SendBulkPushNotification(individualRequests)

	// Log each notification attempt
	for i, token := range req.Tokens {
		notificationLog := models.NotificationLog{
			UserId:        &user.Id,
			FirebaseToken: token,
			Title:         req.Title,
			Body:          req.Body,
			Type:          req.Type,
			Status:        "sent",
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}

		// Only set ClientId if it's not nil
		if user.ClientId != nil {
			notificationLog.ClientId = *user.ClientId
		}

		// Add data as JSON string if provided
		if req.Data != nil {
			dataJSON, _ := json.Marshal(req.Data)
			notificationLog.Data = string(dataJSON)
		}

		if err != nil {
			notificationLog.Status = "failed"
			errorMsg := err.Error()
			notificationLog.ErrorMessage = &errorMsg
		} else if firebaseResp != nil && i < len(firebaseResp.Results) {
			if firebaseResp.Results[i].MessageID != "" {
				notificationLog.Status = "sent"
				notificationLog.FirebaseMessageId = &firebaseResp.Results[i].MessageID
			} else if firebaseResp.Results[i].Error != "" {
				notificationLog.Status = "failed"
				notificationLog.ErrorMessage = &firebaseResp.Results[i].Error
			}
		}

		// Save notification log
		config.DB.Create(&notificationLog)
	}

	// Return response
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to send bulk notifications",
			"error":   err.Error(),
		})
		return
	}

	response := models.PushNotificationResponse{
		Success: firebaseResp.Success > 0,
		Message: "Bulk notifications sent successfully",
		Data: struct {
			Success int `json:"success"`
			Failure int `json:"failure"`
			Results []struct {
				MessageID string `json:"message_id,omitempty"`
				Error     string `json:"error,omitempty"`
			} `json:"results"`
		}{
			Success: firebaseResp.Success,
			Failure: firebaseResp.Failure,
			Results: firebaseResp.Results,
		},
	}

	c.JSON(http.StatusOK, response)
}

// GetNotificationLogs retrieves notification logs with pagination
func GetNotificationLogs(c *gin.Context) {
	var logs []models.NotificationLog
	var total int64

	// Get current user from context
	userValue, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return
	}

	user := userValue.(models.User)

	// Build filter
	filter := map[string]interface{}{}
	if user.ClientId != nil {
		filter["client_id"] = *user.ClientId
	}

	// Get query parameters
	notificationType := c.Query("type")
	if notificationType != "" {
		filter["type"] = notificationType
	}

	status := c.Query("status")
	if status != "" {
		filter["status"] = status
	}

	// Get pagination parameters
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}
	perPage, _ := strconv.Atoi(c.Query("per_page"))
	switch {
	case perPage > 100:
		perPage = 100
	case perPage <= 0:
		perPage = 10
	}

	// Query with pagination
	query := config.DB.Where(filter).Preload("User").Preload("ClientDevice")
	query.Scopes(utils.Paginate(c)).Order("created_at desc").Find(&logs)
	config.DB.Model(&models.NotificationLog{}).Where(filter).Count(&total)

	// Convert to response format
	var logResponses []response.NotificationLogResponse
	for _, log := range logs {
		logResponse := response.NotificationLogResponse{
			Id:                log.Id,
			ClientId:          log.ClientId,
			UserId:            log.UserId,
			ClientDeviceId:    log.ClientDeviceId,
			FirebaseToken:     log.FirebaseToken,
			Title:             log.Title,
			Body:              log.Body,
			Type:              log.Type,
			Data:              log.Data,
			Status:            log.Status,
			FirebaseMessageId: log.FirebaseMessageId,
			ErrorMessage:      log.ErrorMessage,
			CreatedAt:         utils.FormatTimeOrEmptyString(&log.CreatedAt),
			UpdatedAt:         utils.FormatTimeOrEmptyString(&log.UpdatedAt),
		}

		if log.User != nil {
			logResponse.User = &response.UserResponse{
				Id:    log.User.Id,
				Name:  log.User.Name,
				Email: log.User.Email,
			}
		}

		if log.ClientDevice != nil {
			logResponse.ClientDevice = &response.ClientDeviceResponse{
				Id:          log.ClientDevice.Id,
				Name:        log.ClientDevice.Name,
				DeviceId:    log.ClientDevice.DeviceId,
				PhoneNumber: log.ClientDevice.PhoneNumber,
				Status:      log.ClientDevice.Status,
			}
		}

		logResponses = append(logResponses, logResponse)
	}

	// Calculate total pages
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))

	c.JSON(http.StatusOK, gin.H{
		"data":         logResponses,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
		"total_pages":  totalPages,
	})
}

// GetNotificationLogById retrieves a specific notification log
func GetNotificationLogById(c *gin.Context) {
	id := c.Param("id")
	var log models.NotificationLog

	// Get current user from context
	userValue, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return
	}

	user := userValue.(models.User)

	// Find the log
	var result *gorm.DB
	if user.ClientId != nil {
		result = config.DB.Where("id = ? AND client_id = ?", id, *user.ClientId).
			Preload("User").Preload("ClientDevice").First(&log)
	} else {
		result = config.DB.Where("id = ?", id).
			Preload("User").Preload("ClientDevice").First(&log)
	}

	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Notification log not found",
		})
		return
	} else if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	// Convert to response format
	logResponse := response.NotificationLogResponse{
		Id:                log.Id,
		ClientId:          log.ClientId,
		UserId:            log.UserId,
		ClientDeviceId:    log.ClientDeviceId,
		FirebaseToken:     log.FirebaseToken,
		Title:             log.Title,
		Body:              log.Body,
		Type:              log.Type,
		Data:              log.Data,
		Status:            log.Status,
		FirebaseMessageId: log.FirebaseMessageId,
		ErrorMessage:      log.ErrorMessage,
		CreatedAt:         utils.FormatTimeOrEmptyString(&log.CreatedAt),
		UpdatedAt:         utils.FormatTimeOrEmptyString(&log.UpdatedAt),
	}

	if log.User != nil {
		logResponse.User = &response.UserResponse{
			Id:    log.User.Id,
			Name:  log.User.Name,
			Email: log.User.Email,
		}
	}

	if log.ClientDevice != nil {
		logResponse.ClientDevice = &response.ClientDeviceResponse{
			Id:          log.ClientDevice.Id,
			Name:        log.ClientDevice.Name,
			DeviceId:    log.ClientDevice.DeviceId,
			PhoneNumber: log.ClientDevice.PhoneNumber,
			Status:      log.ClientDevice.Status,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": logResponse,
	})
}

// GetNotificationTypes returns available notification types
func GetNotificationTypes(c *gin.Context) {
	types := []map[string]string{
		{"value": "ignition_alert", "label": "Ignition Alert", "description": "Vehicle ignition status change"},
		{"value": "admin_message", "label": "Admin Message", "description": "Administrative message"},
		{"value": "geofence_alert", "label": "Geofence Alert", "description": "Geofence entry/exit alert"},
		{"value": "trip_summary", "label": "Trip Summary", "description": "Trip summary report"},
		{"value": "system_notice", "label": "System Notice", "description": "System notification"},
		{"value": "new_chat_message", "label": "New Chat Message", "description": "New chat message received"},
		{"value": "maintenance_alert", "label": "Maintenance Alert", "description": "Vehicle maintenance reminder"},
		{"value": "speed_alert", "label": "Speed Alert", "description": "Speed limit violation"},
		{"value": "fuel_alert", "label": "Fuel Alert", "description": "Fuel level alert"},
		{"value": "battery_alert", "label": "Battery Alert", "description": "Battery level alert"},
		{"value": "panic_alert", "label": "Panic Alert", "description": "Panic button activated"},
		{"value": "driver_alert", "label": "Driver Alert", "description": "Driver-related alert"},
		{"value": "device_offline", "label": "Device Offline", "description": "Device went offline"},
		{"value": "device_online", "label": "Device Online", "description": "Device came online"},
	}

	c.JSON(http.StatusOK, gin.H{
		"data": types,
	})
}

// SendClientNotification sends push notifications to all users of a specific client or all clients
func SendClientNotification(c *gin.Context) {
	var req models.ClientNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Validate notification type
	if !services.ValidateNotificationType(req.Type) {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid notification type",
		})
		return
	}

	// Get current user from context
	userValue, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return
	}

	_ = userValue.(models.User) // Admin user for future use

	// Create push notification service
	pushService := services.NewPushNotificationService()

	// Get users with Firebase tokens
	var users []models.User
	query := config.DB.Where("firebase_push_token IS NOT NULL AND firebase_push_token != ''")

	if len(req.ClientIDs) > 0 {
		// Send to specific clients
		query = query.Where("client_id IN ?", req.ClientIDs)
	}
	// If ClientIDs is empty, send to all clients (no additional WHERE clause)

	if err := query.Find(&users).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to fetch users",
			"error":   err.Error(),
		})
		return
	}

	if len(users) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "No users with Firebase tokens found",
			"data": gin.H{
				"success": 0,
				"failure": 0,
				"results": []gin.H{},
			},
		})
		return
	}

	// Convert users to individual push notification requests
	var individualRequests []services.PushNotificationRequest
	for _, user := range users {
		if user.FirebasePushToken != nil && *user.FirebasePushToken != "" {
			individualRequests = append(individualRequests, services.PushNotificationRequest{
				To:             *user.FirebasePushToken,
				Title:          req.Title,
				Body:           req.Body,
				Type:           req.Type,
				Data:           req.Data,
				ClientDeviceID: req.ClientDeviceID,
				Timestamp:      req.Timestamp,
			})
		}
	}

	if len(individualRequests) == 0 {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"message": "No valid Firebase tokens found",
			"data": gin.H{
				"success": 0,
				"failure": 0,
				"results": []gin.H{},
			},
		})
		return
	}

	// Send bulk notifications
	firebaseResp, err := pushService.SendBulkPushNotification(individualRequests)

	// Log each notification attempt
	var totalSuccess, totalFailure int
	var allResults []gin.H

	for i, user := range users {
		if user.FirebasePushToken != nil && *user.FirebasePushToken != "" && user.ClientId != nil {
			notificationLog := models.NotificationLog{
				ClientId:      *user.ClientId,
				UserId:        &user.Id,
				FirebaseToken: *user.FirebasePushToken,
				Title:         req.Title,
				Body:          req.Body,
				Type:          req.Type,
				Status:        "sent",
				CreatedAt:     time.Now(),
				UpdatedAt:     time.Now(),
			}

			// Add data as JSON string if provided
			if req.Data != nil {
				dataJSON, _ := json.Marshal(req.Data)
				notificationLog.Data = string(dataJSON)
			}

			// Check if this notification was successful
			if err != nil {
				notificationLog.Status = "failed"
				errorMsg := err.Error()
				notificationLog.ErrorMessage = &errorMsg
				totalFailure++
				allResults = append(allResults, gin.H{
					"user_id": user.Id,
					"error":   errorMsg,
				})
			} else if firebaseResp != nil && i < len(firebaseResp.Results) {
				if firebaseResp.Results[i].MessageID != "" {
					notificationLog.Status = "sent"
					notificationLog.FirebaseMessageId = &firebaseResp.Results[i].MessageID
					totalSuccess++
					allResults = append(allResults, gin.H{
						"user_id":    user.Id,
						"message_id": firebaseResp.Results[i].MessageID,
					})
				} else {
					notificationLog.Status = "failed"
					if firebaseResp.Results[i].Error != "" {
						notificationLog.ErrorMessage = &firebaseResp.Results[i].Error
					}
					totalFailure++
					allResults = append(allResults, gin.H{
						"user_id": user.Id,
						"error":   firebaseResp.Results[i].Error,
					})
				}
			}

			// Save notification log
			config.DB.Create(&notificationLog)
		}
	}

	// Return response
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to send notifications",
			"error":   err.Error(),
		})
		return
	}

	response := models.PushNotificationResponse{
		Success: totalSuccess > 0,
		Message: "Client notifications sent successfully",
		Data: struct {
			Success int `json:"success"`
			Failure int `json:"failure"`
			Results []struct {
				MessageID string `json:"message_id,omitempty"`
				Error     string `json:"error,omitempty"`
			} `json:"results"`
		}{
			Success: totalSuccess,
			Failure: totalFailure,
			Results: []struct {
				MessageID string `json:"message_id,omitempty"`
				Error     string `json:"error,omitempty"`
			}{},
		},
	}

	// Convert results to the expected format
	for _, result := range allResults {
		if messageID, exists := result["message_id"]; exists {
			response.Data.Results = append(response.Data.Results, struct {
				MessageID string `json:"message_id,omitempty"`
				Error     string `json:"error,omitempty"`
			}{
				MessageID: messageID.(string),
			})
		} else if errorMsg, exists := result["error"]; exists {
			response.Data.Results = append(response.Data.Results, struct {
				MessageID string `json:"message_id,omitempty"`
				Error     string `json:"error,omitempty"`
			}{
				Error: errorMsg.(string),
			})
		}
	}

	c.JSON(http.StatusOK, response)
}
