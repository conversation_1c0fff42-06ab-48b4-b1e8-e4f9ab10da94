package controllers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

// TripReplayResponse represents the response for trip replay
type TripReplayResponse struct {
	Trip    models.Trip      `json:"trip"`
	GPSData []models.GPSData `json:"gps_data"`
	Stats   TripReplayStats  `json:"stats"`
}

// TripReplayStats provides statistics about the trip replay data
type TripReplayStats struct {
	TotalPoints      int     `json:"total_points"`
	MovingPoints     int     `json:"moving_points"`
	StationaryPoints int     `json:"stationary_points"`
	MaxSpeed         float64 `json:"max_speed"`
	AvgSpeed         float64 `json:"avg_speed"`
	Duration         string  `json:"duration"`
}

func GetAllTrips(c *gin.Context) {
	var total int64
	var trips []models.Trip

	// Build query with preloads
	query := config.DB.Preload("ClientDevice").Preload("Driver")

	// Apply filters
	if clientId := c.Query("client_id"); clientId != "" {
		query = query.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
			Where("client_devices.client_id = ?", clientId)
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		query = query.Where("trips.client_device_id = ?", deviceId)
	}
	if driverId := c.Query("driver_id"); driverId != "" {
		query = query.Where("trips.driver_id = ?", driverId)
	}
	if status := c.Query("status"); status != "" {
		query = query.Where("trips.status = ?", status)
	}
	if tripType := c.Query("trip_type"); tripType != "" {
		query = query.Where("trips.trip_type = ?", tripType)
	}
	// Add date range filtering
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			query.Where("DATE(trips.start_time) >= ? AND DATE(trips.start_time) <= ?", startDate, endDate)
		} else {
			query.Where("DATE(trips.start_time) = ?", startDate)
		}
	}
	if minDistance := c.Query("min_distance"); minDistance != "" {
		if parsed, err := strconv.ParseFloat(minDistance, 64); err == nil {
			query = query.Where("trips.distance >= ?", parsed)
		}
	}
	if maxDistance := c.Query("max_distance"); maxDistance != "" {
		if parsed, err := strconv.ParseFloat(maxDistance, 64); err == nil {
			query = query.Where("trips.distance <= ?", parsed)
		}
	}
	if minDuration := c.Query("min_duration"); minDuration != "" {
		if parsed, err := strconv.Atoi(minDuration); err == nil {
			query = query.Where("trips.duration >= ?", parsed)
		}
	}
	if maxDuration := c.Query("max_duration"); maxDuration != "" {
		if parsed, err := strconv.Atoi(maxDuration); err == nil {
			query = query.Where("trips.duration <= ?", parsed)
		}
	}

	// Get total count
	query.Model(&models.Trip{}).Count(&total)

	// Apply pagination
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))

	// Validate pagination parameters
	if page < 1 || perPage < 1 || perPage > 100 {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid pagination parameters. Page must be >= 1, per_page must be between 1 and 100",
		})
		return
	}

	offset := (page - 1) * perPage

	// Apply sorting (default by start_time desc)
	sortBy := c.DefaultQuery("sort_by", "start_time")
	sortOrder := c.DefaultQuery("sort_order", "desc")
	query = query.Order("trips." + sortBy + " " + sortOrder)

	// Execute query with pagination
	if err := query.Offset(offset).Limit(perPage).Find(&trips).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to retrieve trips",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":         trips,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
		"total_pages":  (int(total) + perPage - 1) / perPage,
	})
}

func GetTripById(c *gin.Context) {
	var trip models.Trip

	// Validate ID format
	tripId := c.Param("id")
	if _, err := strconv.ParseUint(tripId, 10, 32); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid trip ID format",
		})
		return
	}

	// Build query with preloads
	query := config.DB.Preload("ClientDevice").Preload("Driver").Where("id = ?", tripId)

	if err := query.First(&trip).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Trip not found",
		})
		return
	}

	// Get GPS data for this trip, ordered by timestamp for smooth replay
	var gpsData []models.GPSData
	gpsQuery := config.DB.Where("trip_id = ?", tripId).Order("gps_timestamp ASC")

	// Optional: Add pagination for very large trips
	if limit := c.Query("limit"); limit != "" {
		if limitInt, err := strconv.Atoi(limit); err == nil && limitInt > 0 {
			gpsQuery = gpsQuery.Limit(limitInt)
		}
	}

	if offset := c.Query("offset"); offset != "" {
		if offsetInt, err := strconv.Atoi(offset); err == nil && offsetInt >= 0 {
			gpsQuery = gpsQuery.Offset(offsetInt)
		}
	}

	if err := gpsQuery.Find(&gpsData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to fetch GPS data",
			"error":   err.Error(),
		})
		return
	}

	// Calculate statistics
	stats := calculateTripReplayStats(gpsData)

	// Return comprehensive trip data including GPS data and statistics
	c.JSON(http.StatusOK, gin.H{
		"data": TripReplayResponse{
			Trip:    trip,
			GPSData: gpsData,
			Stats:   stats,
		},
	})
}

func CreateTrip(c *gin.Context) {
	var req models.CreateTripRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Verify that the client device exists and belongs to a valid client
	var clientDevice models.ClientDevice
	if err := config.DB.Preload("Client").First(&clientDevice, req.ClientDeviceId).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid client device",
		})
		return
	}

	// Verify driver if provided
	if req.DriverId != nil {
		var driver models.Driver
		if err := config.DB.First(&driver, req.DriverId).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Invalid driver",
			})
			return
		}
	}

	token := strings.ReplaceAll(c.GetHeader("Authorization"), "Bearer ", "")
	_, _ = services.VerifyToken(token, "access")

	trip := models.Trip{
		ClientDeviceId: req.ClientDeviceId,
		DriverId:       req.DriverId,
		StartTime:      req.StartTime,
		StartLatitude:  req.StartLatitude,
		StartLongitude: req.StartLongitude,
		StartLocation:  req.StartLocation,
		TripType:       req.TripType,
		Notes:          req.Notes,
		Status:         "active",
	}

	if err := config.DB.Create(&trip).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to create trip",
			"error":   err.Error(),
		})
		return
	}

	// Reload with preloads
	config.DB.Preload("ClientDevice").Preload("Driver").First(&trip, trip.Id)

	c.JSON(http.StatusCreated, gin.H{
		"message": "Trip created successfully",
		"data":    trip,
	})
}

func UpdateTrip(c *gin.Context) {
	var req models.UpdateTripRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	var trip models.Trip
	if err := config.DB.First(&trip, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Trip not found",
		})
		return
	}

	// Verify driver if provided
	if req.DriverId != nil {
		var driver models.Driver
		if err := config.DB.First(&driver, req.DriverId).Error; err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Invalid driver",
			})
			return
		}
		trip.DriverId = req.DriverId
	}

	// Update fields if provided
	if req.EndTime != nil {
		trip.EndTime = req.EndTime
	}
	if req.EndLatitude != nil {
		trip.EndLatitude = req.EndLatitude
	}
	if req.EndLongitude != nil {
		trip.EndLongitude = req.EndLongitude
	}
	if req.EndLocation != nil {
		trip.EndLocation = req.EndLocation
	}
	if req.Distance != nil {
		trip.Distance = *req.Distance
	}
	if req.MaxSpeed != nil {
		trip.MaxSpeed = req.MaxSpeed
	}
	if req.AvgSpeed != nil {
		trip.AvgSpeed = req.AvgSpeed
	}
	if req.IdleTime != nil {
		trip.IdleTime = req.IdleTime
	}
	if req.Duration != nil {
		trip.Duration = req.Duration
	}
	if req.Status != nil {
		trip.Status = *req.Status
	}
	if req.TripType != nil {
		trip.TripType = req.TripType
	}
	if req.Notes != nil {
		trip.Notes = req.Notes
	}

	if err := config.DB.Save(&trip).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to update trip",
			"error":   err.Error(),
		})
		return
	}

	// Reload with preloads
	config.DB.Preload("ClientDevice").Preload("Driver").First(&trip, trip.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Trip updated successfully",
		"data":    trip,
	})
}

func DeleteTrip(c *gin.Context) {
	var trip models.Trip
	if err := config.DB.First(&trip, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Trip not found",
		})
		return
	}

	if err := config.DB.Delete(&trip).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to delete trip",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusNoContent, gin.H{
		"message": "Trip deleted successfully",
	})
}

func SearchTrips(c *gin.Context) {
	var trips []models.Trip

	// Build query with preloads
	query := config.DB.Preload("ClientDevice").Preload("Driver")

	// Apply client filter if provided
	if clientId := c.Query("client_id"); clientId != "" {
		query = query.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
			Where("client_devices.client_id = ?", clientId)
	}

	// Apply search filters
	if search := c.Query("s"); search != "" {
		query = query.Where(`
			trips.id LIKE ? OR 
			trips.status LIKE ? OR 
			trips.trip_type LIKE ? OR
			client_devices.name LIKE ? OR
			client_devices.plate_number LIKE ?
		`, "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply additional filters
	if deviceId := c.Query("device_id"); deviceId != "" {
		query = query.Where("trips.client_device_id = ?", deviceId)
	}
	if driverId := c.Query("driver_id"); driverId != "" {
		query = query.Where("trips.driver_id = ?", driverId)
	}
	if status := c.Query("status"); status != "" {
		query = query.Where("trips.status = ?", status)
	}
	if tripType := c.Query("trip_type"); tripType != "" {
		query = query.Where("trips.trip_type = ?", tripType)
	}

	// Apply date range filters
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			query.Where("DATE(trips.start_time) >= ? AND DATE(trips.start_time) <= ?", startDate, endDate)
		} else {
			query.Where("DATE(trips.start_time) = ?", startDate)
		}
	}

	// Apply sorting
	sortBy := c.DefaultQuery("sort_by", "start_time")
	sortOrder := c.DefaultQuery("sort_order", "desc")
	query = query.Order("trips." + sortBy + " " + sortOrder)

	// Limit results for search
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	query = query.Limit(limit)

	if err := query.Find(&trips).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to search trips",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": trips,
	})
}

// GetTripReplaySimplified returns simplified GPS data for trip replay (reduced data for performance)
func GetTripReplaySimplified(c *gin.Context) {
	tripIDStr := c.Param("id")
	tripID, err := strconv.ParseUint(tripIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid trip ID"})
		return
	}

	// Get simplified GPS data (every Nth point for performance)
	interval := 5 // Show every 5th point by default
	if intervalStr := c.Query("interval"); intervalStr != "" {
		if intervalInt, err := strconv.Atoi(intervalStr); err == nil && intervalInt > 0 {
			interval = intervalInt
		}
	}

	var gpsData []models.GPSData
	// Use ROW_NUMBER() to get every Nth record
	err = config.DB.Raw(`
		SELECT * FROM (
			SELECT *, ROW_NUMBER() OVER (ORDER BY gps_timestamp) as row_num
			FROM gps_data 
			WHERE trip_id = ?
		) as numbered_data
		WHERE row_num % ? = 1
		ORDER BY gps_timestamp ASC
	`, tripID, interval).Scan(&gpsData).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch GPS data"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"trip_id":      tripID,
		"gps_data":     gpsData,
		"interval":     interval,
		"total_points": len(gpsData),
	})
}

// calculateTripReplayStats calculates statistics for the trip replay
func calculateTripReplayStats(gpsData []models.GPSData) TripReplayStats {
	stats := TripReplayStats{
		TotalPoints: len(gpsData),
	}

	if len(gpsData) == 0 {
		return stats
	}

	var totalSpeed float64
	var speedCount int
	var maxSpeed float64

	for _, point := range gpsData {
		if point.Speed != nil {
			speed := *point.Speed
			if speed > 0 {
				stats.MovingPoints++
				totalSpeed += speed
				speedCount++
				if speed > maxSpeed {
					maxSpeed = speed
				}
			} else {
				stats.StationaryPoints++
			}
		} else {
			stats.StationaryPoints++
		}
	}

	stats.MaxSpeed = maxSpeed
	if speedCount > 0 {
		stats.AvgSpeed = totalSpeed / float64(speedCount)
	}

	// Calculate duration
	if len(gpsData) > 1 {
		start := gpsData[0].GPSTimestamp
		end := gpsData[len(gpsData)-1].GPSTimestamp
		if start != nil && end != nil {
			duration := end.Sub(*start)
			hours := int(duration.Hours())
			minutes := int(duration.Minutes()) % 60
			if hours > 0 {
				stats.Duration = fmt.Sprintf("%dh %dm", hours, minutes)
			} else {
				stats.Duration = fmt.Sprintf("%dm", minutes)
			}
		}
	}

	return stats
}
