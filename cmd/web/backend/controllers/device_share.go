package controllers

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

func CreateDeviceShareToken(c *gin.Context) {
	var req models.CreateDeviceShareTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get device ID from URL parameter
	deviceIdParam := c.Param("id")
	deviceId, err := strconv.ParseUint(deviceIdParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid device ID",
		})
		return
	}

	// Verify the device exists and belongs to the user's client
	var clientDevice models.ClientDevice
	userInterface, _ := c.Get("user")
	user := userInterface.(models.User)

	if err := config.DB.Where("id = ? AND client_id = ?", deviceId, user.ClientId).First(&clientDevice).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Device not found or access denied",
		})
		return
	}

	// Validate expiration date is in the future
	if req.ExpiresAt.Before(time.Now()) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Expiration date must be in the future",
		})
		return
	}

	// Generate the share token
	shareToken, err := services.GenerateDeviceShareToken(
		uint(deviceId),
		clientDevice.DeviceId,
		user.Id,
		req.ExpiresAt,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate share token",
		})
		return
	}

	// Generate share URL
	baseURL := os.Getenv("APP_URL")
	if baseURL == "" {
		baseURL = "http://localhost:8080"
	}
	shareUrl := fmt.Sprintf("%s/temp_location?token=%s", baseURL, shareToken)

	// Prepare response (no DB fields)
	response := gin.H{
		"client_device_id": uint(deviceId),
		"device_name":      *clientDevice.Name,
		"token":            shareToken,
		"expires_at":       req.ExpiresAt,
		"share_url":        shareUrl,
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Device share token created successfully",
		"data":    response,
	})
}

func GetDeviceShareTokens(c *gin.Context) {
	// Get device ID from URL parameter
	deviceIdParam := c.Param("id")
	deviceId, err := strconv.ParseUint(deviceIdParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid device ID",
		})
		return
	}

	// Verify the device exists and belongs to the user's client
	var clientDevice models.ClientDevice
	userInterface, _ := c.Get("user")
	user := userInterface.(models.User)

	if err := config.DB.Where("id = ? AND client_id = ?", deviceId, user.ClientId).First(&clientDevice).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Device not found or access denied",
		})
		return
	}

	// Get all active share tokens for this device
	var shareTokens []models.DeviceShareToken
	config.DB.Where("client_device_id = ? AND status = ?", deviceId, "active").
		Order("created_at DESC").
		Find(&shareTokens)

	// Prepare response
	var responses []models.DeviceShareTokenResponse
	baseURL := os.Getenv("APP_URL")
	if baseURL == "" {
		baseURL = "http://localhost:8080"
	}

	for _, token := range shareTokens {
		shareUrl := fmt.Sprintf("%s/api/v1/public/device/%s/location?token=%s", baseURL, clientDevice.DeviceId, token.Token)
		responses = append(responses, models.DeviceShareTokenResponse{
			Id:             token.Id,
			ClientDeviceId: token.ClientDeviceId,
			DeviceName:     *clientDevice.Name,
			Token:          token.Token,
			ExpiresAt:      token.ExpiresAt,
			Status:         token.Status,
			CreatedAt:      token.CreatedAt,
			ShareUrl:       shareUrl,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data": responses,
	})
}

func RevokeDeviceShareToken(c *gin.Context) {
	// Get token ID from URL parameter
	tokenIdParam := c.Param("token_id")
	tokenId, err := strconv.ParseUint(tokenIdParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid token ID",
		})
		return
	}

	// Get device ID from URL parameter
	deviceIdParam := c.Param("id")
	deviceId, err := strconv.ParseUint(deviceIdParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid device ID",
		})
		return
	}

	// Verify the device exists and belongs to the user's client
	var clientDevice models.ClientDevice
	userInterface, _ := c.Get("user")
	user := userInterface.(models.User)

	if err := config.DB.Where("id = ? AND client_id = ?", deviceId, user.ClientId).First(&clientDevice).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Device not found or access denied",
		})
		return
	}

	// Find and revoke the token
	var shareToken models.DeviceShareToken
	if err := config.DB.Where("id = ? AND client_device_id = ?", tokenId, deviceId).First(&shareToken).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Share token not found",
		})
		return
	}

	// Update token status to revoked
	shareToken.Status = "revoked"
	if err := config.DB.Save(&shareToken).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to revoke token",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Share token revoked successfully",
	})
}
