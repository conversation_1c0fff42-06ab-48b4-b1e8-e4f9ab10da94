package controllers

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"yotracker/config"
	"yotracker/internal/mail"
	"yotracker/internal/models"
	"yotracker/internal/response"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

func GetAllUsers(c *gin.Context) {
	var users []models.User
	var total int64
	filter := map[string]interface{}{}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}

	// Extract current_page and per_page from query params (same logic as utils.Paginate)
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}
	perPage, _ := strconv.Atoi(c.Query("per_page"))
	switch {
	case perPage > 100:
		perPage = 100
	case perPage <= 0:
		perPage = 10
	}

	config.DB.Scopes(utils.Paginate(c)).Where(filter).Preload("Client").Order("id desc").Find(&users)
	totalResult := config.DB.Model(&models.User{}).Where(filter).Count(&total)
	if totalResult.Error != nil {
		fmt.Println("Failed to get count")
	}
	// Print all users
	var userResponses []response.UserResponse
	for _, user := range users {
		userResponse := response.UserResponse{
			Id:            user.Id,
			Name:          user.Name,
			Email:         user.Email,
			Gender:        user.Gender,
			Description:   user.Description,
			CreatedAt:     utils.FormatTimeOrEmptyString(&user.CreatedAt),
			LastLoginDate: utils.FormatTimeOrEmptyString(user.LastLoginDate),
			Status:        user.Status,
			UserType:      user.UserType,
			Client:        user.Client,
		}
		userResponses = append(userResponses, userResponse)
	}
	c.JSON(200, gin.H{
		"data":         userResponses,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
	})
}
func GetUserById(c *gin.Context) {
	userId, _ := strconv.Atoi(c.Param("id"))
	user := models.User{
		Id: uint(userId),
	}
	result := config.DB.Preload("Client").First(&user)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		c.JSON(400, gin.H{
			"message": "user not found",
		})
		return
	} else if result.Error != nil {
		c.JSON(400, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	userResponse := response.UserResponse{
		Id:              user.Id,
		ClientId:        user.ClientId,
		Name:            user.Name,
		Email:           user.Email,
		Gender:          user.Gender,
		Description:     user.Description,
		CreatedAt:       utils.FormatTimeOrEmptyString(&user.CreatedAt),
		LastLoginDate:   utils.FormatTimeOrEmptyString(user.LastLoginDate),
		Status:          user.Status,
		SlackWebhookUrl: user.SlackWebhookUrl,
		TelegramUserId:  user.TelegramUserId,
		Client:          user.Client,
		UserType:        user.UserType,
	}
	c.JSON(200, gin.H{
		"data": userResponse,
	})
}
func Profile(c *gin.Context) {
	token := strings.ReplaceAll(c.GetHeader("Authorization"), "Bearer ", "")
	claims, err := services.VerifyToken(token, "access")
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return
	}

	user := models.User{
		Id: uint(claims.UserId),
	}
	result := config.DB.Preload("Client").First(&user)
	if errors.Is(result.Error, gorm.ErrRecordNotFound) {
		c.JSON(400, gin.H{
			"message": "user not found",
		})
		return
	} else if result.Error != nil {
		c.JSON(400, gin.H{
			"message": result.Error.Error(),
		})
		return
	}
	userResponse := response.UserResponse{
		Id:              user.Id,
		Name:            user.Name,
		Email:           user.Email,
		Gender:          user.Gender,
		Description:     user.Description,
		CreatedAt:       utils.FormatTimeOrEmptyString(&user.CreatedAt),
		LastLoginDate:   utils.FormatTimeOrEmptyString(user.LastLoginDate),
		Status:          user.Status,
		SlackWebhookUrl: user.SlackWebhookUrl,
		TelegramUserId:  user.TelegramUserId,
		UserType:        user.UserType,
		Client:          user.Client,
	}
	c.JSON(200, gin.H{
		"data": userResponse,
	})
}
func CreateUser(c *gin.Context) {
	var req models.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	hashedPassword := services.HashPassword(req.Password)
	userIDValue, _ := c.Get("userID")
	userID := userIDValue.(uint)
	user := models.User{
		CreatedById:     userID,
		ClientId:        req.ClientId,
		UserType:        req.UserType,
		Name:            req.Name,
		Email:           req.Email,
		Password:        hashedPassword,
		Status:          req.Status,
		Gender:          req.Gender,
		TelegramUserId:  req.TelegramUserId,
		SlackWebhookUrl: req.SlackWebhookUrl,
		Description:     req.Description,
	}

	result := config.DB.Create(&user)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	// Send new account email with credentials (non-blocking)
	go func() {
		err := mail.SendNewAccountEmail(user.Email, user.Name, req.Password)
		if err != nil {
			// Log error but don't fail the request
			log.Printf("Failed to send new account email to %s: %v", user.Email, err)
		}
	}()

	c.JSON(http.StatusCreated, gin.H{
		"message": "User created successfully",
		"email":   "Account credentials will be sent via email",
	})
}
func UpdateUser(c *gin.Context) {
	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	var user models.User
	userId, _ := strconv.Atoi(c.Param("id"))
	if err := config.DB.First(&user, userId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "User not found",
		})
		return
	}
	user.Name = req.Name
	user.Status = req.Status
	user.Email = req.Email
	user.Gender = req.Gender
	user.TelegramUserId = req.TelegramUserId
	user.SlackWebhookUrl = req.SlackWebhookUrl
	user.Description = req.Description
	user.UserType = req.UserType
	user.ClientId = req.ClientId
	if req.Password != "" {
		hashedPassword := services.HashPassword(req.Password)
		user.Password = hashedPassword
	}

	result := config.DB.Save(&user)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User updated successfully"})
}
func DeleteUser(c *gin.Context) {
	id := c.Param("id")
	var user models.User

	// Check if the user exists
	if err := config.DB.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		}
		return
	}

	// Delete the user
	if err := config.DB.Delete(&user).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Could not delete user"})
		return
	}

	c.JSON(http.StatusNoContent, gin.H{"message": "User deleted successfully"})
}
func SearchUsers(c *gin.Context) {
	var users []models.User
	filter := map[string]interface{}{}
	if userType := c.Query("user_type"); userType != "" {
		filter["user_type"] = userType
	}
	if status := c.Query("status"); status != "" {
		filter["status"] = status
	}
	if clientId := c.Query("client_id"); clientId != "" {
		filter["client_id"] = clientId
	}

	query := config.DB.Where(filter).Preload("Client")
	if search := c.Query("s"); search != "" {
		query = query.Where("name like ? or id like ? or email like ?", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}
	query.Find(&users)
	var userResponses []response.UserResponse
	for _, user := range users {
		userResponse := response.UserResponse{
			Id:              user.Id,
			ClientId:        user.ClientId,
			Name:            user.Name,
			Email:           user.Email,
			Gender:          user.Gender,
			Description:     user.Description,
			CreatedAt:       utils.FormatTimeOrEmptyString(&user.CreatedAt),
			LastLoginDate:   utils.FormatTimeOrEmptyString(user.LastLoginDate),
			Status:          user.Status,
			SlackWebhookUrl: user.SlackWebhookUrl,
			TelegramUserId:  user.TelegramUserId,
			UserType:        user.UserType,
			Client:          user.Client,
		}
		userResponses = append(userResponses, userResponse)
	}
	c.JSON(http.StatusOK, gin.H{
		"data": userResponses,
	})
}

func ChangePassword(c *gin.Context) {
	var req models.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Validate password confirmation
	if req.Password != req.PasswordConfirmation {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Password and password confirmation do not match",
		})
		return
	}

	// Get current user from context
	userValue, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return
	}

	user := userValue.(models.User)

	// Verify current password
	if !services.CheckPassword(user.Password, req.CurrentPassword) {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Current password is incorrect",
		})
		return
	}

	// Hash the new password
	hashedPassword := services.HashPassword(req.Password)

	// Update the user's password
	err := config.DB.Model(&user).Where("id = ?", user.Id).Update("password", hashedPassword).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to update password",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Password changed successfully",
	})
}

func UpdateProfile(c *gin.Context) {
	var req models.UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Get current user from context
	userValue, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return
	}

	user := userValue.(models.User)

	// Check if email is being changed and if it already exists for another user
	if req.Email != user.Email {
		var existingUser models.User
		err := config.DB.Where("email = ? AND id != ?", req.Email, user.Id).First(&existingUser).Error
		if err == nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Email already exists",
			})
			return
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusInternalServerError, gin.H{
				"message": "Failed to validate email",
			})
			return
		}
	}

	// Check if username is being changed and if it already exists for another user
	if req.Username != nil && (user.Username == nil || *req.Username != *user.Username) {
		var existingUser models.User
		err := config.DB.Where("username = ? AND id != ?", *req.Username, user.Id).First(&existingUser).Error
		if err == nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Username already exists",
			})
			return
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusInternalServerError, gin.H{
				"message": "Failed to validate username",
			})
			return
		}
	}

	// Update user profile fields
	user.Name = req.Name
	user.Email = req.Email
	user.Username = req.Username
	user.Gender = req.Gender
	user.TelegramUserId = req.TelegramUserId
	user.SlackWebhookUrl = req.SlackWebhookUrl
	user.Description = req.Description

	// Save the updated user
	err := config.DB.Model(&user).Where("id = ?", user.Id).Updates(map[string]interface{}{
		"name":              user.Name,
		"email":             user.Email,
		"username":          user.Username,
		"gender":            user.Gender,
		"telegram_user_id":  user.TelegramUserId,
		"slack_webhook_url": user.SlackWebhookUrl,
		"description":       user.Description,
	}).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to update profile",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Profile updated successfully",
	})
}
