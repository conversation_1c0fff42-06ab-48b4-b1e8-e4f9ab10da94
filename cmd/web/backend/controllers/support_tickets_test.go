package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/migrations"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestBackendSupportTicketControllers(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Initialize test database
	config.InitTestDB()

	// Run migrations
	migrations.Migrate()

	// Clean up any existing data (in reverse dependency order)
	config.DB.Exec("DELETE FROM support_ticket_replies")
	config.DB.Exec("DELETE FROM support_tickets")
	config.DB.Exec("DELETE FROM geofence_events")
	config.DB.Exec("DELETE FROM client_devices")
	config.DB.Exec("DELETE FROM users")
	config.DB.Exec("DELETE FROM clients")
	config.DB.Exec("DELETE FROM device_types")
	config.DB.Exec("DELETE FROM protocols")

	// Create test data
	client := models.Client{
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	user := models.User{
		Name:     "Test User",
		Email:    "<EMAIL>",
		Password: "password123",
		UserType: "backend",
	}
	config.DB.Create(&user)

	adminUser := models.User{
		Name:     "Admin User",
		Email:    "<EMAIL>",
		Password: "password123",
		UserType: "backend",
	}
	config.DB.Create(&adminUser)

	// Create a protocol first
	protocol := models.Protocol{
		Name: "Test Protocol",
	}
	config.DB.Create(&protocol)

	// Create a device type first
	deviceType := models.DeviceType{
		ProtocolId: protocol.Id,
		Name:       "Test Device Type",
	}
	config.DB.Create(&deviceType)

	deviceName := "Test Device"
	device := models.ClientDevice{
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "TEST001",
		Name:         &deviceName,
	}
	config.DB.Create(&device)

	t.Run("GetAllSupportTickets", func(t *testing.T) {
		// Create test tickets
		ticket1 := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "technical",
			Priority:    "high",
			Subject:     "Test Ticket 1",
			Description: "Test description 1",
			Status:      "open",
		}
		config.DB.Create(&ticket1)

		ticket2 := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "billing",
			Priority:    "medium",
			Subject:     "Test Ticket 2",
			Description: "Test description 2",
			Status:      "assigned",
		}
		config.DB.Create(&ticket2)

		// Test as admin (no client restriction)
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)

		req, _ := http.NewRequest("GET", "/api/v1/backend/support_tickets", nil)
		c.Request = req

		GetAllSupportTickets(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2)

		// Test with filters
		w = httptest.NewRecorder()
		c, _ = gin.CreateTestContext(w)

		req, _ = http.NewRequest("GET", "/api/v1/backend/support_tickets?department=technical&priority=high", nil)
		c.Request = req

		GetAllSupportTickets(c)

		assert.Equal(t, http.StatusOK, w.Code)

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data = response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 1)
	})

	t.Run("GetSupportTicketById", func(t *testing.T) {
		// Create a test ticket
		ticket := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "general",
			Priority:    "low",
			Subject:     "Get By ID Test",
			Description: "Test for getting by ID",
			Status:      "open",
		}
		config.DB.Create(&ticket)

		// Test as admin
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Params = gin.Params{{Key: "id", Value: strconv.FormatUint(uint64(ticket.Id), 10)}}

		GetSupportTicketById(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(ticket.Id), data["id"])
		assert.Equal(t, "Get By ID Test", data["subject"])

		// Test non-existent ticket
		w = httptest.NewRecorder()
		c, _ = gin.CreateTestContext(w)
		c.Params = gin.Params{{Key: "id", Value: "999"}}

		GetSupportTicketById(c)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("GetSupportTicketReplies", func(t *testing.T) {
		// Create a test ticket
		ticket := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "technical",
			Priority:    "medium",
			Subject:     "Replies Test",
			Description: "Test for replies",
			Status:      "open",
		}
		config.DB.Create(&ticket)

		// Create test replies
		reply1 := models.SupportTicketReply{
			TicketId:    ticket.Id,
			CreatedById: user.Id,
			IsInternal:  false,
			Message:     "First reply",
		}
		config.DB.Create(&reply1)

		reply2 := models.SupportTicketReply{
			TicketId:    ticket.Id,
			CreatedById: adminUser.Id,
			IsInternal:  true,
			Message:     "Internal reply",
		}
		config.DB.Create(&reply2)

		// Test as admin
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Params = gin.Params{{Key: "id", Value: strconv.FormatUint(uint64(ticket.Id), 10)}}

		GetSupportTicketReplies(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 2)
	})

	t.Run("CreateSupportTicket", func(t *testing.T) {
		requestBody := models.CreateSupportTicketRequest{
			ClientId:       client.Id,
			ClientDeviceId: &device.Id,
			Department:     "technical",
			Priority:       "high",
			Subject:        "Create Test Ticket",
			Description:    "Test for creating ticket",
		}

		jsonBody, _ := json.Marshal(requestBody)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user", adminUser)
		c.Request = httptest.NewRequest("POST", "/api/v1/backend/support_tickets", bytes.NewBuffer(jsonBody))
		c.Request.Header.Set("Content-Type", "application/json")

		CreateSupportTicket(c)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Equal(t, "Support ticket created successfully", response["message"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "Create Test Ticket", data["subject"])
		assert.Equal(t, "technical", data["department"])
		assert.Equal(t, "high", data["priority"])
		assert.Equal(t, "open", data["status"])
	})

	t.Run("UpdateSupportTicket", func(t *testing.T) {
		// Create a test ticket
		ticket := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "general",
			Priority:    "low",
			Subject:     "Update Test",
			Description: "Test for updating",
			Status:      "open",
		}
		config.DB.Create(&ticket)

		requestBody := models.UpdateSupportTicketRequest{
			AssignedToId: &adminUser.Id,
			Priority:     stringPtrForTest("urgent"),
			Status:       stringPtrForTest("in_progress"),
			Subject:      stringPtrForTest("Updated Subject"),
			Description:  stringPtrForTest("Updated description"),
		}

		jsonBody, _ := json.Marshal(requestBody)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Params = gin.Params{{Key: "id", Value: strconv.FormatUint(uint64(ticket.Id), 10)}}
		c.Request = httptest.NewRequest("PUT", "/api/v1/backend/support_tickets/"+strconv.FormatUint(uint64(ticket.Id), 10), bytes.NewBuffer(jsonBody))
		c.Request.Header.Set("Content-Type", "application/json")

		UpdateSupportTicket(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Equal(t, "Support ticket updated successfully", response["message"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "Updated Subject", data["subject"])
		assert.Equal(t, "urgent", data["priority"])
		assert.Equal(t, "in_progress", data["status"])
	})

	t.Run("CreateSupportTicketReply", func(t *testing.T) {
		// Create a test ticket
		ticket := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "technical",
			Priority:    "medium",
			Subject:     "Reply Test",
			Description: "Test for creating replies",
			Status:      "open",
		}
		config.DB.Create(&ticket)

		requestBody := models.CreateSupportTicketReplyRequest{
			TicketId:   ticket.Id,
			IsInternal: false,
			Message:    "This is a test reply",
		}

		jsonBody, _ := json.Marshal(requestBody)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user", adminUser)
		c.Request = httptest.NewRequest("POST", "/api/v1/backend/support_tickets/1/replies", bytes.NewBuffer(jsonBody))
		c.Request.Header.Set("Content-Type", "application/json")

		CreateSupportTicketReply(c)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Equal(t, "Reply added successfully", response["message"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "This is a test reply", data["message"])
		assert.Equal(t, false, data["is_internal"])
	})

	t.Run("GetSupportTicketStats", func(t *testing.T) {
		// Create tickets with different statuses
		tickets := []models.SupportTicket{
			{
				ClientId:    client.Id,
				CreatedById: user.Id,
				Department:  "technical",
				Priority:    "urgent",
				Subject:     "Urgent Ticket",
				Description: "Urgent issue",
				Status:      "open",
			},
			{
				ClientId:    client.Id,
				CreatedById: user.Id,
				Department:  "billing",
				Priority:    "high",
				Subject:     "High Priority",
				Description: "High priority issue",
				Status:      "assigned",
			},
			{
				ClientId:    client.Id,
				CreatedById: user.Id,
				Department:  "general",
				Priority:    "medium",
				Subject:     "Resolved Ticket",
				Description: "Resolved issue",
				Status:      "resolved",
			},
		}

		for _, ticket := range tickets {
			config.DB.Create(&ticket)
		}

		// Test stats with client filter
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/api/v1/backend/support_tickets/stats?client_id="+strconv.FormatUint(uint64(client.Id), 10), nil)

		GetSupportTicketStats(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].(map[string]interface{})
		assert.GreaterOrEqual(t, data["total_tickets"], float64(3))
		assert.GreaterOrEqual(t, data["open_tickets"], float64(1))
		assert.GreaterOrEqual(t, data["urgent_tickets"], float64(1))

		// Test stats with assigned_to filter
		w = httptest.NewRecorder()
		c, _ = gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/api/v1/backend/support_tickets/stats?assigned_to_id="+strconv.FormatUint(uint64(adminUser.Id), 10), nil)

		GetSupportTicketStats(c)

		assert.Equal(t, http.StatusOK, w.Code)

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data = response["data"].(map[string]interface{})
		assert.GreaterOrEqual(t, data["total_tickets"], float64(0))

		// Test stats without filters (all tickets)
		w = httptest.NewRecorder()
		c, _ = gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/api/v1/backend/support_tickets/stats", nil)

		GetSupportTicketStats(c)

		assert.Equal(t, http.StatusOK, w.Code)

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data = response["data"].(map[string]interface{})
		assert.GreaterOrEqual(t, data["total_tickets"], float64(3))
	})

	t.Run("DeleteSupportTicket", func(t *testing.T) {
		// Create a test ticket
		ticket := models.SupportTicket{
			ClientId:    client.Id,
			CreatedById: user.Id,
			Department:  "general",
			Priority:    "low",
			Subject:     "Delete Test",
			Description: "Test for deleting",
			Status:      "open",
		}
		config.DB.Create(&ticket)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Params = gin.Params{{Key: "id", Value: strconv.FormatUint(uint64(ticket.Id), 10)}}

		DeleteSupportTicket(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Equal(t, "Support ticket deleted successfully", response["message"])

		// Verify ticket is deleted
		var deletedTicket models.SupportTicket
		err = config.DB.First(&deletedTicket, ticket.Id).Error
		assert.Error(t, err) // Should not find the ticket
	})

	t.Run("SearchSupportTickets", func(t *testing.T) {
		// Create test tickets
		tickets := []models.SupportTicket{
			{
				ClientId:    client.Id,
				CreatedById: user.Id,
				Department:  "technical",
				Priority:    "high",
				Subject:     "Device GPS Issue",
				Description: "GPS signal problems with device",
				Status:      "open",
			},
			{
				ClientId:    client.Id,
				CreatedById: user.Id,
				Department:  "billing",
				Priority:    "medium",
				Subject:     "Billing Question",
				Description: "Question about monthly billing",
				Status:      "assigned",
			},
		}

		for _, ticket := range tickets {
			config.DB.Create(&ticket)
		}

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/api/v1/backend/support_tickets/search?q=device", nil)

		SearchSupportTickets(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].([]interface{})
		assert.GreaterOrEqual(t, len(data), 1)
	})

	t.Run("ErrorCases", func(t *testing.T) {
		// Test invalid ticket ID
		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Params = gin.Params{{Key: "id", Value: "invalid"}}

		GetSupportTicketById(c)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		// Test missing user context - this should now work since we removed user context requirement
		w = httptest.NewRecorder()
		c, _ = gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("GET", "/api/v1/backend/support_tickets", nil)

		GetAllSupportTickets(c)

		assert.Equal(t, http.StatusOK, w.Code)

		// Test invalid JSON in create request
		w = httptest.NewRecorder()
		c, _ = gin.CreateTestContext(w)
		c.Set("user", adminUser)
		c.Request = httptest.NewRequest("POST", "/api/v1/backend/support_tickets", bytes.NewBufferString("invalid json"))
		c.Request.Header.Set("Content-Type", "application/json")

		CreateSupportTicket(c)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	// Clean up
	config.DB.Exec("DELETE FROM support_ticket_replies")
	config.DB.Exec("DELETE FROM support_tickets")
	config.DB.Exec("DELETE FROM client_devices")
	config.DB.Exec("DELETE FROM users")
	config.DB.Exec("DELETE FROM clients")
}

// Helper function to create string pointers for testing
func stringPtrForTest(s string) *string {
	return &s
}
