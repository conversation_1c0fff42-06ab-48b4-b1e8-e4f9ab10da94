package routes

import (
	"net/http"
	"strconv"
	"time"

	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

// GetAlertsWithLocation retrieves alerts with their GPS data
func GetAlertsWithLocation(c *gin.Context) {
	// Parse query parameters
	clientDeviceIdStr := c.Query("client_device_id")
	alertType := c.Query("alert_type")
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")
	limitStr := c.Query("limit")

	var clientDeviceId *uint
	var startDate, endDate *time.Time
	var limit int

	// Parse client device ID
	if clientDeviceIdStr != "" {
		if id, err := strconv.ParseUint(clientDeviceIdStr, 10, 32); err == nil {
			deviceId := uint(id)
			clientDeviceId = &deviceId
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client_device_id"})
			return
		}
	}

	// Parse alert type
	var alertTypePtr *string
	if alertType != "" {
		alertTypePtr = &alertType
	}

	// Parse start date
	if startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format. Use YYYY-MM-DD"})
			return
		}
	}

	// Parse end date
	if endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format. Use YYYY-MM-DD"})
			return
		}
	}

	// Parse limit
	if limitStr != "" {
		if parsed, err := strconv.Atoi(limitStr); err == nil {
			limit = parsed
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit"})
			return
		}
	}

	// Get alerts with location
	service := services.NewAlertWithLocationService()
	alerts, err := service.GetAlertsWithLocation(clientDeviceId, alertTypePtr, startDate, endDate, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Alerts with location retrieved successfully",
		"data":    alerts,
		"count":   len(alerts),
	})
}

// GetAlertWithLocation retrieves a specific alert with its GPS data
func GetAlertWithLocation(c *gin.Context) {
	alertIdStr := c.Param("alertId")
	alertId, err := strconv.ParseUint(alertIdStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid alert ID"})
		return
	}

	service := services.NewAlertWithLocationService()
	alert, err := service.GetAlertWithLocation(uint(alertId))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Alert with location retrieved successfully",
		"data":    alert,
	})
}

// GetAlertsByLocation retrieves alerts within a specific geographic area
func GetAlertsByLocation(c *gin.Context) {
	// Parse query parameters
	minLatStr := c.Query("min_lat")
	maxLatStr := c.Query("max_lat")
	minLngStr := c.Query("min_lng")
	maxLngStr := c.Query("max_lng")
	alertType := c.Query("alert_type")
	limitStr := c.Query("limit")

	// Validate required parameters
	if minLatStr == "" || maxLatStr == "" || minLngStr == "" || maxLngStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "min_lat, max_lat, min_lng, and max_lng are required"})
		return
	}

	// Parse coordinates
	minLat, err := strconv.ParseFloat(minLatStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid min_lat"})
		return
	}

	maxLat, err := strconv.ParseFloat(maxLatStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid max_lat"})
		return
	}

	minLng, err := strconv.ParseFloat(minLngStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid min_lng"})
		return
	}

	maxLng, err := strconv.ParseFloat(maxLngStr, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid max_lng"})
		return
	}

	// Parse alert type
	var alertTypePtr *string
	if alertType != "" {
		alertTypePtr = &alertType
	}

	// Parse limit
	limit := 0
	if limitStr != "" {
		if parsed, err := strconv.Atoi(limitStr); err == nil {
			limit = parsed
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit"})
			return
		}
	}

	// Get alerts by location
	service := services.NewAlertWithLocationService()
	alerts, err := service.GetAlertsByLocation(minLat, maxLat, minLng, maxLng, alertTypePtr, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Alerts by location retrieved successfully",
		"data":    alerts,
		"count":   len(alerts),
	})
}

// GetAlertsByDeviceWithLocation retrieves alerts for a specific device with GPS data
func GetAlertsByDeviceWithLocation(c *gin.Context) {
	deviceId := c.Param("deviceId")
	if deviceId == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Device ID is required"})
		return
	}

	alertType := c.Query("alert_type")
	limitStr := c.Query("limit")

	// Parse alert type
	var alertTypePtr *string
	if alertType != "" {
		alertTypePtr = &alertType
	}

	// Parse limit
	limit := 0
	if limitStr != "" {
		if parsed, err := strconv.Atoi(limitStr); err == nil {
			limit = parsed
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit"})
			return
		}
	}

	// Get alerts by device with location
	service := services.NewAlertWithLocationService()
	alerts, err := service.GetAlertsByDeviceWithLocation(deviceId, alertTypePtr, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Alerts by device with location retrieved successfully",
		"data":    alerts,
		"count":   len(alerts),
	})
}

// GetAlertStatisticsWithLocation provides statistics about alerts with location data
func GetAlertStatisticsWithLocation(c *gin.Context) {
	// Parse query parameters
	clientDeviceIdStr := c.Query("client_device_id")
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var clientDeviceId *uint
	var startDate, endDate *time.Time

	// Parse client device ID
	if clientDeviceIdStr != "" {
		if id, err := strconv.ParseUint(clientDeviceIdStr, 10, 32); err == nil {
			deviceId := uint(id)
			clientDeviceId = &deviceId
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client_device_id"})
			return
		}
	}

	// Parse start date
	if startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format. Use YYYY-MM-DD"})
			return
		}
	}

	// Parse end date
	if endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format. Use YYYY-MM-DD"})
			return
		}
	}

	// Get alert statistics
	service := services.NewAlertWithLocationService()
	stats, err := service.GetAlertStatisticsWithLocation(clientDeviceId, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Alert statistics with location retrieved successfully",
		"data":    stats,
	})
}

// GetAlertsForMapView retrieves alerts optimized for map visualization
func GetAlertsForMapView(c *gin.Context) {
	// Parse query parameters
	clientDeviceIdStr := c.Query("client_device_id")
	alertType := c.Query("alert_type")
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	var clientDeviceId *uint
	var startDate, endDate *time.Time

	// Parse client device ID
	if clientDeviceIdStr != "" {
		if id, err := strconv.ParseUint(clientDeviceIdStr, 10, 32); err == nil {
			deviceId := uint(id)
			clientDeviceId = &deviceId
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client_device_id"})
			return
		}
	}

	// Parse alert type
	var alertTypePtr *string
	if alertType != "" {
		alertTypePtr = &alertType
	}

	// Parse start date
	if startDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", startDateStr); err == nil {
			startDate = &parsed
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format. Use YYYY-MM-DD"})
			return
		}
	}

	// Parse end date
	if endDateStr != "" {
		if parsed, err := time.Parse("2006-01-02", endDateStr); err == nil {
			endDate = &parsed
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format. Use YYYY-MM-DD"})
			return
		}
	}

	// Get alerts for map view
	service := services.NewAlertWithLocationService()
	alerts, err := service.GetAlertsForMapView(clientDeviceId, alertTypePtr, startDate, endDate)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Alerts for map view retrieved successfully",
		"data":    alerts,
		"count":   len(alerts),
	})
}
