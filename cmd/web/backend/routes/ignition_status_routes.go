package routes

import (
	"net/http"
	"strconv"
	"time"

	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

// GetIgnitionStatusInfo returns ignition status information for a specific device
func GetIgnitionStatusInfo(c *gin.Context) {
	deviceIdStr := c.Param("deviceId")
	deviceId, err := strconv.ParseUint(deviceIdStr, 10, 32)
	if err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid device ID"})
		return
	}

	ignitionService := services.NewIgnitionStatusService()
	info, err := ignitionService.GetIgnitionStatusInfo(uint(deviceId))
	if err != nil {
		c.J<PERSON>(http.StatusNotFound, gin.H{"error": "Device not found or error retrieving ignition status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Ignition status retrieved successfully", "data": info})
}

// GetDevicesByIgnitionStatus returns devices filtered by ignition status
func GetDevicesByIgnitionStatus(c *gin.Context) {
	statusStr := c.Query("status")
	if statusStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Status parameter is required (on/off)"})
		return
	}

	var ignitionOn bool
	switch statusStr {
	case "on", "true", "1":
		ignitionOn = true
	case "off", "false", "0":
		ignitionOn = false
	default:
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid status parameter. Use 'on' or 'off'"})
		return
	}

	ignitionService := services.NewIgnitionStatusService()
	devices, err := ignitionService.GetDevicesByIgnitionStatus(ignitionOn)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error retrieving devices"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Devices retrieved successfully", "data": devices})
}

// GetDevicesWithIgnitionOnForDuration returns devices that have had ignition ON for a specific duration
func GetDevicesWithIgnitionOnForDuration(c *gin.Context) {
	durationStr := c.Query("duration")
	if durationStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Duration parameter is required (e.g., '2h', '30m', '1800s')"})
		return
	}

	duration, err := time.ParseDuration(durationStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid duration format. Use formats like '2h', '30m', '1800s'"})
		return
	}

	ignitionService := services.NewIgnitionStatusService()
	devices, err := ignitionService.GetDevicesWithIgnitionOnForDuration(duration)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error retrieving devices"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Devices retrieved successfully", "data": devices})
}

// GetDevicesWithIgnitionOffForDuration returns devices that have had ignition OFF for a specific duration
func GetDevicesWithIgnitionOffForDuration(c *gin.Context) {
	durationStr := c.Query("duration")
	if durationStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Duration parameter is required (e.g., '2h', '30m', '1800s')"})
		return
	}

	duration, err := time.ParseDuration(durationStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid duration format. Use formats like '2h', '30m', '1800s'"})
		return
	}

	ignitionService := services.NewIgnitionStatusService()
	devices, err := ignitionService.GetDevicesWithIgnitionOffForDuration(duration)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error retrieving devices"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Devices retrieved successfully", "data": devices})
}

// UpdateIgnitionStatus manually updates ignition status for a device
func UpdateIgnitionStatus(c *gin.Context) {
	deviceIdStr := c.Param("deviceId")
	deviceId, err := strconv.ParseUint(deviceIdStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid device ID"})
		return
	}

	var request struct {
		IgnitionStatus bool       `json:"ignition_status" binding:"required"`
		Timestamp      *time.Time `json:"timestamp,omitempty"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	ignitionService := services.NewIgnitionStatusService()
	err = ignitionService.UpdateIgnitionStatus(uint(deviceId), request.IgnitionStatus, request.Timestamp)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Error updating ignition status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "Ignition status updated successfully"})
}
