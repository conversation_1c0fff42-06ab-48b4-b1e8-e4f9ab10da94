package routes

import (
	"yotracker/cmd/web/backend/controllers"
	"yotracker/cmd/web/middleware"
	publiccontrollers "yotracker/cmd/web/public/controllers"

	"github.com/gin-gonic/gin"
)

func BackendRoutes(hub *controllers.Hub, r *gin.Engine) {
	v1 := r.Group("/api/v1/backend")
	v1.GET("", controllers.Home)

	// Public device share route
	v1.GET("/public/device/temp_location", middleware.PublicDeviceShareMiddleware(), publiccontrollers.GetSharedDeviceLocation)

	// Public media file serving route
	v1.GET("/media/:filename", publiccontrollers.ServeMediaFile)

	v1.POST("/login", controllers.Login)
	v1.POST("/register", controllers.Register)
	// Password reset routes (no auth required)
	v1.POST("/password_reset/request", controllers.RequestPasswordReset)
	v1.POST("/password_reset/verify", controllers.VerifyPasswordResetToken)
	v1.POST("/password_reset/confirm", controllers.ResetPassword)
	webhook := v1.Group("/webhook")
	webhook.Any("/paynow", controllers.PaynowWebhook)
	v1.Use(middleware.AuthMiddleware())
	v1.Use(middleware.CheckIfUserIsBackend())
	//websocket
	v1.GET("/ws/:channel", func(c *gin.Context) {
		controllers.HandleWebsocketRequest(hub, c)
	})
	//users
	users := v1.Group("/users")
	users.GET("", controllers.GetAllUsers)
	users.GET("/search", controllers.SearchUsers)
	users.GET("/:id", controllers.GetUserById)
	users.POST("", controllers.CreateUser)
	users.PUT("/:id", controllers.UpdateUser)
	users.DELETE("/:id", controllers.DeleteUser)
	users.GET("/profile", controllers.Profile)
	users.PUT("/profile", controllers.UpdateProfile)
	users.PUT("/change_password", controllers.ChangePassword)
	//roles
	roles := v1.Group("/roles")
	roles.GET("", controllers.GetAllRoles)
	roles.GET("/search", controllers.SearchRoles)
	roles.GET("/:id", controllers.GetRoleById)
	roles.POST("", controllers.CreateRole)
	roles.PUT("/:id", controllers.UpdateRole)
	roles.DELETE("/:id", controllers.DeleteRole)
	//clients
	clients := v1.Group("/clients")
	clients.GET("", controllers.GetAllClients)
	clients.GET("/search", controllers.SearchClients)
	clients.GET("/:id", controllers.GetClientById)
	clients.POST("", controllers.CreateClient)
	clients.PUT("/:id", controllers.UpdateClient)
	clients.DELETE("/:id", controllers.DeleteClient)
	clients.GET("/:id/fleets", controllers.GetAllFleets)
	clients.GET("/fleets/:id", controllers.GetFleetById)
	clients.POST("/:id/fleets", controllers.CreateFleet)
	clients.PUT("/fleets/:id", controllers.UpdateFleet)
	clients.DELETE("/fleets/:id", controllers.DeleteFleet)

	//fleet alert preferences routes (using different path structure to avoid conflicts)
	clients.GET("/fleet/:fleetId/alert_preferences", controllers.GetFleetAlertPreferences)
	clients.GET("/fleet/:fleetId/alert_preferences/:id", controllers.GetFleetAlertPreferenceById)
	clients.POST("/fleet/:fleetId/alert_preferences", controllers.CreateFleetAlertPreference)
	clients.PUT("/fleet/:fleetId/alert_preferences/:id", controllers.UpdateFleetAlertPreference)
	clients.DELETE("/fleet/:fleetId/alert_preferences/:id", controllers.DeleteFleetAlertPreference)
	clients.POST("/:id/users", controllers.CreateClientUser)
	clients.PUT("/:id/users", controllers.UpdateClientUser)
	clients.DELETE("/users/:id", controllers.DeleteClientUser)

	//protocols
	protocols := v1.Group("/protocols")
	protocols.GET("", controllers.GetAllProtocols)
	protocols.GET("/search", controllers.SearchProtocols)
	protocols.GET("/:id", controllers.GetProtocolById)
	protocols.POST("", controllers.CreateProtocol)
	protocols.PUT("/:id", controllers.UpdateProtocol)
	protocols.DELETE("/:id", controllers.DeleteProtocol)
	//device types
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("", controllers.GetAllDeviceTypes)
	deviceTypes.GET("/search", controllers.SearchDeviceTypes)
	deviceTypes.GET("/:id", controllers.GetDeviceTypeById)
	deviceTypes.POST("", controllers.CreateDeviceType)
	deviceTypes.PUT("/:id", controllers.UpdateDeviceType)
	deviceTypes.DELETE("/:id", controllers.DeleteDeviceType)
	//client device
	clientDevices := v1.Group("/client_devices")
	clientDevices.GET("", controllers.GetAllClientDevices)
	clientDevices.GET("/search", controllers.SearchClientDevices)
	clientDevices.GET("/:id", controllers.GetClientDeviceById)
	clientDevices.POST("", controllers.CreateClientDevice)
	clientDevices.PUT("/:id", controllers.UpdateClientDevice)
	clientDevices.DELETE("/:id", controllers.DeleteClientDevice)
	clientDevices.POST("/command_log/:id", controllers.CreateClientDevice)
	clientDevices.GET("/command_log/:id", controllers.CreateClientDevice)

	//device alert preferences routes (using different path structure to avoid conflicts)
	clientDevices.GET("/device/:deviceId/alert_preferences", controllers.GetDeviceAlertPreferences)
	clientDevices.GET("/device/:deviceId/alert_preferences/:id", controllers.GetDeviceAlertPreferenceById)
	clientDevices.POST("/device/:deviceId/alert_preferences", controllers.CreateDeviceAlertPreference)
	clientDevices.PUT("/device/:deviceId/alert_preferences/:id", controllers.UpdateDeviceAlertPreference)
	clientDevices.DELETE("/device/:deviceId/alert_preferences/:id", controllers.DeleteDeviceAlertPreference)

	//ignition status routes
	ignitionStatus := v1.Group("/ignition_status")
	ignitionStatus.GET("/device/:deviceId", GetIgnitionStatusInfo)
	ignitionStatus.GET("/devices", GetDevicesByIgnitionStatus)
	ignitionStatus.GET("/devices/on_for_duration", GetDevicesWithIgnitionOnForDuration)
	ignitionStatus.GET("/devices/off_for_duration", GetDevicesWithIgnitionOffForDuration)
	ignitionStatus.PUT("/device/:deviceId", UpdateIgnitionStatus)

	//gps data
	gpsData := v1.Group("/gps_data")
	gpsData.GET("/search", controllers.SearchGpsData)
	gpsData.GET("/last_location", controllers.GetLastLocation)
	gpsData.GET("/:id", controllers.GetGpsDataById)
	gpsData.POST("", controllers.CreateGpsData)
	gpsData.PUT("/:id", controllers.UpdateGpsData)
	gpsData.DELETE("/:id", controllers.DeleteGpsData)
	//alerts
	alerts := v1.Group("/alerts")
	alerts.GET("", controllers.GetAllAlerts)
	alerts.GET("/search", controllers.SearchAlerts)
	alerts.GET("/:id", controllers.GetAlertById)
	alerts.POST("", controllers.CreateAlert)
	alerts.PUT("/:id", controllers.UpdateAlert)
	alerts.DELETE("/:id", controllers.DeleteAlert)
	alerts.PUT("/:id/mark_read", controllers.MarkAlertAsRead)
	alerts.PUT("/:id/mark_unread", controllers.MarkAlertAsUnread)
	alerts.PUT("/bulk/mark_read", controllers.BulkMarkAlertsAsRead)
	alerts.PUT("/mark_all_read", controllers.MarkAllAlertsAsRead)

	//alert location routes
	alerts.GET("/with_location", GetAlertsWithLocation)
	alerts.GET("/:id/with_location", GetAlertWithLocation)
	alerts.GET("/by_location", GetAlertsByLocation)
	alerts.GET("/by_device/:deviceId/with_location", GetAlertsByDeviceWithLocation)
	alerts.GET("/statistics/with_location", GetAlertStatisticsWithLocation)
	alerts.GET("/map_view", GetAlertsForMapView)

	//alert preferences routes
	alertPreferences := v1.Group("/alert_preferences")
	alertPreferences.GET("", controllers.GetAlertPreferences)
	alertPreferences.GET("/:id", controllers.GetAlertPreferenceById)
	alertPreferences.POST("", controllers.CreateAlertPreference)
	alertPreferences.PUT("/:id", controllers.UpdateAlertPreference)
	alertPreferences.DELETE("/:id", controllers.DeleteAlertPreference)
	alertPreferences.GET("/client/:clientId", controllers.GetAlertPreferencesByClient)

	//geofences
	geofences := v1.Group("/geofences")
	geofences.GET("", controllers.GetAllGeofences)
	geofences.GET("/search", controllers.SearchGeofences)
	geofences.GET("/:id", controllers.GetGeofenceById)
	geofences.POST("", controllers.CreateGeofence)
	geofences.PUT("/:id", controllers.UpdateGeofence)
	geofences.DELETE("/:id", controllers.DeleteGeofence)
	geofences.GET("/events", controllers.GetGeofenceEvents)
	geofences.GET("/device/:device_id", controllers.GetGeofencesByDevice)

	//drivers
	drivers := v1.Group("/drivers")
	drivers.GET("", controllers.GetAllDrivers)
	drivers.GET("/search", controllers.SearchDrivers)
	drivers.GET("/:id", controllers.GetDriverById)
	drivers.POST("", controllers.CreateDriver)
	drivers.PUT("/:id", controllers.UpdateDriver)
	drivers.DELETE("/:id", controllers.DeleteDriver)

	//driver device assignments
	driverAssignments := v1.Group("/driver_device_assignments")
	driverAssignments.GET("", controllers.GetAllDriverDeviceAssignments)
	driverAssignments.GET("/search", controllers.SearchDriverDeviceAssignments)
	driverAssignments.GET("/:id", controllers.GetDriverDeviceAssignmentById)
	driverAssignments.POST("", controllers.CreateDriverDeviceAssignment)
	driverAssignments.POST("/rfid", controllers.CreateDriverDeviceAssignmentByRfid)
	driverAssignments.PUT("/:id", controllers.UpdateDriverDeviceAssignment)
	driverAssignments.DELETE("/:id", controllers.DeleteDriverDeviceAssignment)
	driverAssignments.GET("/current", controllers.GetCurrentDriverAssignment)
	//command logs
	commandLogs := v1.Group("/command_logs")
	commandLogs.GET("", controllers.GetAllCommandLogs)
	commandLogs.GET("/search", controllers.SearchCommandLogs)
	commandLogs.GET("/:id", controllers.GetCommandLogById)
	commandLogs.POST("", controllers.CreateCommandLog)
	commandLogs.PUT("/:id", controllers.UpdateCommandLog)
	commandLogs.DELETE("/:id", controllers.DeleteCommandLog)
	//invoices
	invoices := v1.Group("/invoices")
	invoices.GET("", controllers.GetAllInvoices)
	invoices.GET("/search", controllers.SearchInvoices)
	invoices.GET("/:id", controllers.GetInvoiceById)
	invoices.POST("", controllers.CreateInvoice)
	invoices.PUT("/:id", controllers.UpdateInvoice)
	invoices.DELETE("/:id", controllers.DeleteInvoice)
	invoices.GET("/:id/pdf", controllers.GenerateInvoicePDF)
	invoices.POST("/:id/email", controllers.SendInvoiceEmail)
	//payments
	payments := v1.Group("/payments")
	payments.GET("", controllers.GetAllPayments)
	payments.GET("/search", controllers.SearchPayments)
	payments.GET("/:id", controllers.GetPaymentById)
	payments.POST("", controllers.CreatePayment)
	payments.PUT("/:id", controllers.UpdatePayment)
	payments.DELETE("/:id", controllers.DeletePayment)
	//payment types
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("", controllers.GetAllPaymentTypes)
	paymentTypes.GET("/search", controllers.SearchPaymentTypes)
	paymentTypes.GET("/:id", controllers.GetPaymentTypeById)
	paymentTypes.POST("", controllers.CreatePaymentType)
	paymentTypes.PUT("/:id", controllers.UpdatePaymentType)
	paymentTypes.DELETE("/:id", controllers.DeletePaymentType)

	//trips
	trips := v1.Group("/trips")
	trips.GET("", controllers.GetAllTrips)
	trips.GET("/search", controllers.SearchTrips)
	trips.GET("/:id", controllers.GetTripById)
	trips.POST("", controllers.CreateTrip)
	trips.PUT("/:id", controllers.UpdateTrip)
	trips.DELETE("/:id", controllers.DeleteTrip)
	trips.GET("/replay_simplified/:id", controllers.GetTripReplaySimplified)

	//currencies
	currencies := v1.Group("/currencies")
	currencies.GET("", controllers.GetAllCurrencies)
	currencies.GET("/search", controllers.SearchCurrencies)
	currencies.GET("/:id", controllers.GetCurrencyById)
	currencies.POST("", controllers.CreateCurrency)
	currencies.PUT("/:id", controllers.UpdateCurrency)
	currencies.DELETE("/:id", controllers.DeleteCurrency)
	//tax rates
	taxRates := v1.Group("/tax_rates")
	taxRates.GET("", controllers.GetAllTaxRates)
	taxRates.GET("/search", controllers.SearchTaxRates)
	taxRates.GET("/:id", controllers.GetTaxRateById)
	taxRates.POST("", controllers.CreateTaxRate)
	taxRates.PUT("/:id", controllers.UpdateTaxRate)
	taxRates.DELETE("/:id", controllers.DeleteTaxRate)
	//countries
	countries := v1.Group("/countries")
	countries.GET("", controllers.GetAllCountries)

	//settings
	settings := v1.Group("/settings")
	settings.GET("", controllers.GetAllSettings)
	settings.GET("/allowed", controllers.GetAllowedSettings)
	settings.GET("/:key", controllers.GetSettingByKey)
	settings.GET("/:key/value", controllers.GetSettingValue)
	settings.POST("", controllers.CreateSetting)
	settings.PUT("", controllers.UpdateAllSettings)
	settings.PUT("/:key", controllers.UpdateSetting)
	settings.DELETE("/:key", controllers.DeleteSetting)

	//communication campaign logs
	communicationLogsController := controllers.NewCommunicationCampaignLogController()
	communicationLogs := v1.Group("/communication_campaign_logs")
	communicationLogs.GET("", communicationLogsController.GetCommunicationCampaignLogs)
	communicationLogs.GET("/statistics", communicationLogsController.GetCommunicationCampaignLogStatistics)
	communicationLogs.GET("/:id", communicationLogsController.GetCommunicationCampaignLog)
	communicationLogs.POST("", communicationLogsController.CreateCommunicationCampaignLog)
	communicationLogs.PUT("/:id", communicationLogsController.UpdateCommunicationCampaignLog)
	communicationLogs.DELETE("/:id", communicationLogsController.DeleteCommunicationCampaignLog)
	communicationLogs.GET("/client/:clientId", communicationLogsController.GetCommunicationCampaignLogsByClient)

	//dashboard
	dashboard := v1.Group("/dashboard")
	dashboard.GET("/dashboard_stats", controllers.GetDashboardStats)
	dashboard.GET("/server_stats", controllers.GetServerStats)

	//support tickets
	supportTickets := v1.Group("/support_tickets")
	supportTickets.GET("", controllers.GetAllSupportTickets)
	supportTickets.GET("/search", controllers.SearchSupportTickets)
	supportTickets.GET("/stats", controllers.GetSupportTicketStats)
	supportTickets.GET("/:id", controllers.GetSupportTicketById)
	supportTickets.GET("/:id/replies", controllers.GetSupportTicketReplies)
	supportTickets.POST("", controllers.CreateSupportTicket)
	supportTickets.PUT("/:id", controllers.UpdateSupportTicket)
	supportTickets.PUT("/:id/status", controllers.ChangeSupportTicketStatus)
	supportTickets.PUT("/:id/assign", controllers.AssignSupportTicket)
	supportTickets.DELETE("/:id", controllers.DeleteSupportTicket)
	supportTickets.POST("/:id/replies", controllers.CreateSupportTicketReply)

	// Reports routes (admin)
	reports := v1.Group("/reports")
	{
		// Report management (admin)
		reports.GET("", controllers.GetAllReports)
		reports.POST("", controllers.CreateReport)
		reports.PUT("/:id", controllers.UpdateReport)
		reports.DELETE("/:id", controllers.DeleteReport)

		// Scheduled reports management (admin)
		reports.GET("/scheduled", controllers.GetAllScheduledReports)
		reports.GET("/scheduled/:id", controllers.GetScheduledReportById)
		reports.POST("/scheduled/:id/run", controllers.RunScheduledReport)

		// Report analytics
		reports.GET("/categories", controllers.GetReportCategories)
		reports.GET("/stats", controllers.GetReportStats)
		reports.GET("/execution_history", controllers.GetReportExecutionHistory)
	}

	// File upload routes
	files := v1.Group("/files")
	files.POST("/upload", controllers.UploadFile)
	files.GET("", controllers.GetFileUploads)
	files.GET("/:id", controllers.GetFileUploadById)
	files.DELETE("/:id", controllers.DeleteFileUpload)

	// Push notification routes
	notifications := v1.Group("/notifications")
	notifications.POST("/push", controllers.SendPushNotification)
	notifications.POST("/push/bulk", controllers.SendBulkPushNotification)
	notifications.POST("/client", controllers.SendClientNotification)
	notifications.GET("/logs", controllers.GetNotificationLogs)
	notifications.GET("/logs/:id", controllers.GetNotificationLogById)
	notifications.GET("/types", controllers.GetNotificationTypes)

}
