package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupPaymentsTest(t *testing.T) (*gin.Engine, string, models.Client) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupPaymentsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test client and user
	client := createPaymentsTestClient(t)
	user := createPaymentsTestUser(t, client.Id)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())

	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/frontend")
	v1.Use(middleware.AuthMiddleware())
	v1.Use(middleware.CheckForClient())

	// Payments routes
	payments := v1.Group("/payments")
	payments.GET("", GetAllPayments)
	payments.GET("/search", SearchPayments)
	payments.GET("/:id", GetPaymentById)
	payments.POST("", CreatePayment)

	return r, token, client
}

func setupPaymentsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         getEnvOrDefault("DB_HOST", "localhost"),
		"DB_PORT":         getEnvOrDefault("DB_PORT", "3306"),
		"DB_USERNAME":     getEnvOrDefault("DB_USERNAME", "admin"),
		"DB_PASSWORD":     getEnvOrDefault("DB_PASSWORD", "password"),
		"TESTING_DB_NAME": getEnvOrDefault("TESTING_DB_NAME", "testing"),
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createPaymentsTestClient(t *testing.T) models.Client {
	// Clean up existing test client
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.Client{})

	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Payments Test Client",
		Email:      "<EMAIL>",
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createPaymentsTestUser(t *testing.T, clientId uint) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Payments Admin",
		UserType: "frontend",
		Status:   &status,
		ClientId: &clientId,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func createPaymentsTestInvoice(t *testing.T, clientId uint, date time.Time) models.Invoice {
	amount := 100.0
	reference := "INV-PAY-001"
	status := "pending"

	invoice := models.Invoice{
		ClientId:  clientId,
		Date:      &date,
		Amount:    &amount,
		Status:    status,
		Reference: &reference,
	}

	result := config.DB.Create(&invoice)
	assert.NoError(t, result.Error)

	return invoice
}

func createPaymentsTestPayment(t *testing.T, invoiceId uint, date time.Time) models.InvoicePayment {
	// Get existing payment type from seeded data
	var paymentType models.PaymentType
	if err := config.DB.First(&paymentType).Error; err != nil {
		t.Fatalf("Failed to get payment type: %v", err)
	}

	amount := 100.0
	transId := "TXN-PAY-001"

	payment := models.InvoicePayment{
		InvoiceId:     invoiceId,
		PaymentTypeId: paymentType.Id,
		Date:          &date,
		Amount:        amount,
		TransId:       &transId,
	}

	result := config.DB.Create(&payment)
	assert.NoError(t, result.Error)

	return payment
}

// Test GetAllPayments functionality
func TestGetAllPayments(t *testing.T) {
	r, token, client := setupPaymentsTest(t)

	// Create test invoice and payments
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)

	invoice1 := createPaymentsTestInvoice(t, client.Id, date1)
	invoice2 := createPaymentsTestInvoice(t, client.Id, date2)

	_ = createPaymentsTestPayment(t, invoice1.Id, date1)
	_ = createPaymentsTestPayment(t, invoice2.Id, date2)

	t.Run("Get all payments successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2)
		assert.Equal(t, float64(2), response["total"])
	})

	t.Run("Get all payments without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetAllPayments with date filtering
func TestGetAllPaymentsWithDateFiltering(t *testing.T) {
	r, token, client := setupPaymentsTest(t)

	// Create test invoices and payments with different dates
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)
	date3 := time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC)

	invoice1 := createPaymentsTestInvoice(t, client.Id, date1)
	invoice2 := createPaymentsTestInvoice(t, client.Id, date2)
	invoice3 := createPaymentsTestInvoice(t, client.Id, date3)

	_ = createPaymentsTestPayment(t, invoice1.Id, date1)
	_ = createPaymentsTestPayment(t, invoice2.Id, date2)
	_ = createPaymentsTestPayment(t, invoice3.Id, date3)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedCount  int
	}{
		{
			name:           "Get payments for specific date",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get payments for date range",
			queryParams:    "?start_date=2024-01-15&end_date=2024-01-25",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:           "Get payments with search and date filter",
			queryParams:    "?s=TXN-PAY-001&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get payments with invoice filter and date",
			queryParams:    "?invoice_id=" + strconv.FormatUint(uint64(invoice1.Id), 10) + "&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.Contains(t, response, "data")
				assert.Contains(t, response, "total")

				// Verify data count
				data := response["data"].([]interface{})
				assert.Len(t, data, tt.expectedCount)
			}
		})
	}
}

// Test GetPaymentById functionality
func TestGetPaymentById(t *testing.T) {
	r, token, client := setupPaymentsTest(t)

	// Create test invoice and payment
	date := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	invoice := createPaymentsTestInvoice(t, client.Id, date)
	payment := createPaymentsTestPayment(t, invoice.Id, date)

	t.Run("Get payment by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments/"+strconv.FormatUint(uint64(payment.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify payment data
		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(payment.Id), data["id"])
		assert.Equal(t, float64(invoice.Id), data["invoice_id"])
	})

	t.Run("Get payment by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Get payment by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments/"+strconv.FormatUint(uint64(payment.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test CreatePayment functionality
func TestCreatePayment(t *testing.T) {
	r, token, client := setupPaymentsTest(t)

	// Create test invoice
	date := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	invoice := createPaymentsTestInvoice(t, client.Id, date)

	t.Run("Create payment successfully", func(t *testing.T) {
		paymentData := map[string]interface{}{
			"invoice_id":      invoice.Id,
			"payment_type_id": 1, // Cash payment type
			"currency_id":     1, // Default currency
			"date":            "2024-01-15T00:00:00Z",
			"amount":          150.0,
			"trans_id":        "TXN-CREATE-001",
		}

		jsonData, _ := json.Marshal(paymentData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/payments", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "Payment created successfully", response["message"])
	})

	t.Run("Create payment with missing required fields", func(t *testing.T) {
		paymentData := map[string]interface{}{
			"invoice_id": invoice.Id,
			// Missing date and amount
		}

		jsonData, _ := json.Marshal(paymentData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/payments", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create payment with non-existent invoice", func(t *testing.T) {
		paymentData := map[string]interface{}{
			"invoice_id":      99999,
			"payment_type_id": 1, // Cash payment type
			"currency_id":     1, // Default currency
			"date":            "2024-01-15T00:00:00Z",
			"amount":          150.0,
		}

		jsonData, _ := json.Marshal(paymentData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/payments", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Create payment without authorization", func(t *testing.T) {
		paymentData := map[string]interface{}{
			"invoice_id":      invoice.Id,
			"payment_type_id": 1, // Cash payment type
			"currency_id":     1, // Default currency
			"date":            "2024-01-15T00:00:00Z",
			"amount":          150.0,
		}

		jsonData, _ := json.Marshal(paymentData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/payments", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test SearchPayments functionality
func TestSearchPayments(t *testing.T) {
	r, token, client := setupPaymentsTest(t)

	// Create test invoices and payments
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)

	invoice1 := createPaymentsTestInvoice(t, client.Id, date1)
	invoice2 := createPaymentsTestInvoice(t, client.Id, date2)

	_ = createPaymentsTestPayment(t, invoice1.Id, date1)
	_ = createPaymentsTestPayment(t, invoice2.Id, date2)

	t.Run("Search payments by transaction ID", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments/search?s=TXN-PAY-001", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2) // Both payments have TXN-PAY-001 in trans_id
	})

	t.Run("Search payments with date filter", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments/search?start_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Only one payment on 2024-01-15
	})

	t.Run("Search payments with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments/search?s=NONEXISTENT", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // No results
	})
}

// Test pagination functionality
func TestPaymentsPagination(t *testing.T) {
	r, token, client := setupPaymentsTest(t)

	// Create multiple invoices and payments for pagination testing
	for i := 1; i <= 15; i++ {
		date := time.Date(2024, 1, i, 0, 0, 0, 0, time.UTC)
		invoice := createPaymentsTestInvoice(t, client.Id, date)
		createPaymentsTestPayment(t, invoice.Id, date)
	}

	t.Run("First page with default pagination", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")
		assert.Contains(t, response, "total_pages")

		// Verify pagination values
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.Equal(t, float64(15), response["total"])
		assert.Equal(t, float64(2), response["total_pages"]) // 15 items, 10 per page = 2 pages

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 10)
	})

	t.Run("Second page", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 5) // Remaining 5 items on second page
	})
}

// Test edge cases and error handling
func TestPaymentsEdgeCases(t *testing.T) {
	r, token, _ := setupPaymentsTest(t)

	t.Run("Invalid date format in query", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments?start_date=invalid-date", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("End date before start date", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments?start_date=2024-01-20&end_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid payment ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/payments/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
