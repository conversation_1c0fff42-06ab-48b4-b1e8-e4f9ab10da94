package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/seed"
	"yotracker/internal/services"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func setupUpdateProfileTest() {
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}
	if os.Getenv("APP_KEY") == "" {
		os.Setenv("APP_KEY", "test-secret-key")
	}

	// Refresh test database for consistent state
	services.FastCleanupTestData()
	migrations.Migrate()
	seed.Seed()
	gin.SetMode(gin.TestMode)
}

func TestUpdateProfileFrontend(t *testing.T) {
	// Setup test database
	setupUpdateProfileTest()

	// Create test client first with unique email
	timestamp := time.Now().UnixNano()
	client := models.Client{
		Name:        "Test Client",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber: "1234567890",
		ClientType:  "individual",
		Status:      "active",
	}
	result := config.DB.Create(&client)
	if result.Error != nil {
		t.Fatalf("Failed to create test client: %v", result.Error)
	}

	// Create test user with unique email
	hashedPassword := services.HashPassword("password123")
	user := models.User{
		Name:     "Test User",
		Email:    fmt.Sprintf("<EMAIL>", timestamp),
		Password: hashedPassword,
		UserType: "client",
		ClientId: &client.Id,
	}
	result = config.DB.Create(&user)
	if result.Error != nil {
		t.Fatalf("Failed to create test user: %v", result.Error)
	}

	// Generate token for authentication
	token, _ := services.GenerateToken(&user, "access")

	t.Run("successful profile update", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]interface{}{
			"name":              "Updated Name",
			"email":             fmt.Sprintf("<EMAIL>", timestamp),
			"gender":            "male",
			"telegram_user_id":  "123456789",
			"slack_webhook_url": "https://hooks.slack.com/test",
			"description":       "Updated description",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/profile", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		// Mock middleware to set user in context
		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/profile", UpdateProfile)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Profile updated successfully", response["message"])

		// Verify profile was actually updated
		var updatedUser models.User
		config.DB.First(&updatedUser, user.Id)
		assert.Equal(t, "Updated Name", updatedUser.Name)
		assert.Equal(t, fmt.Sprintf("<EMAIL>", timestamp), updatedUser.Email)
		assert.Equal(t, "male", *updatedUser.Gender)
		assert.Equal(t, "123456789", *updatedUser.TelegramUserId)
		assert.Equal(t, "https://hooks.slack.com/test", *updatedUser.SlackWebhookUrl)
		assert.Equal(t, "Updated description", *updatedUser.Description)
	})

	t.Run("email already exists", func(t *testing.T) {
		// Create another user with a different email
		anotherUser := models.User{
			Name:     "Another User",
			Email:    fmt.Sprintf("<EMAIL>", timestamp),
			Password: hashedPassword,
			UserType: "client",
			ClientId: &client.Id,
		}
		config.DB.Create(&anotherUser)

		router := gin.New()
		reqBody := map[string]interface{}{
			"name":  "Updated Name",
			"email": fmt.Sprintf("<EMAIL>", timestamp), // Try to use existing email
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/profile", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/profile", UpdateProfile)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Email already exists", response["message"])

		// Cleanup
		config.DB.Delete(&anotherUser)
	})

	t.Run("username already exists", func(t *testing.T) {
		// Create another user with a username
		username := "existinguser"
		anotherUser := models.User{
			Name:     "Another User",
			Email:    "<EMAIL>",
			Username: &username,
			Password: hashedPassword,
			UserType: "client",
			ClientId: &client.Id,
		}
		config.DB.Create(&anotherUser)

		router := gin.New()
		reqBody := map[string]interface{}{
			"name":     "Updated Name",
			"email":    "<EMAIL>",
			"username": "existinguser", // Try to use existing username
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/profile", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/profile", UpdateProfile)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Username already exists", response["message"])

		// Cleanup
		config.DB.Delete(&anotherUser)
	})

	t.Run("missing required fields", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]interface{}{
			// Missing name and email
			"gender": "female",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/profile", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/profile", UpdateProfile)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("unauthorized access", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]interface{}{
			"name":  "Updated Name",
			"email": "<EMAIL>",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/profile", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		// No middleware to set user in context
		router.PUT("/profile", UpdateProfile)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Unauthorized", response["message"])
	})

	// Cleanup
	config.DB.Delete(&user)
	config.DB.Delete(&client)
}
