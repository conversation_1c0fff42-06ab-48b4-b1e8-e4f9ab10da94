package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"

	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupClientsTest(t *testing.T) (*gin.Engine, string, models.Client) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupClientsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test client and user
	client := createClientsTestClient(t)
	user := createClientsTestUser(t, client.Id)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())

	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/frontend")
	v1.Use(middleware.AuthMiddleware())
	v1.Use(middleware.CheckForClient())

	// Clients routes
	clients := v1.Group("/clients")
	clients.GET("", GetAllClients)
	clients.GET("/search", SearchClients)
	clients.GET("/:id", GetClientById)
	clients.POST("", CreateClient)
	clients.PUT("/:id", UpdateClient)
	clients.DELETE("/:id", DeleteClient)

	return r, token, client
}

func setupClientsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         getEnvOrDefault("DB_HOST", "localhost"),
		"DB_PORT":         getEnvOrDefault("DB_PORT", "3306"),
		"DB_USERNAME":     getEnvOrDefault("DB_USERNAME", "admin"),
		"DB_PASSWORD":     getEnvOrDefault("DB_PASSWORD", "password"),
		"TESTING_DB_NAME": getEnvOrDefault("TESTING_DB_NAME", "testing"),
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createClientsTestClient(t *testing.T) models.Client {
	status := "active"
	clientType := "individual"

	// Clean up existing test client first
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.Client{})

	client := models.Client{
		Name:       "Clients Test Client",
		Email:      "<EMAIL>",
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createClientsTestUser(t *testing.T, clientId uint) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Clients Test User",
		UserType: "frontend",
		Status:   &status,
		ClientId: &clientId,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

// Test GetAllClients functionality
func TestGetAllClients(t *testing.T) {
	r, token, client := setupClientsTest(t)

	t.Run("Get all clients successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data - frontend users only see their own client
		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Only 1 client (their own)
		assert.Equal(t, float64(1), response["total"])

		// Verify it's their own client
		clientData := data[0].(map[string]interface{})
		assert.Equal(t, float64(client.Id), clientData["id"])
	})

	t.Run("Get all clients without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetClientById functionality
func TestGetClientById(t *testing.T) {
	r, token, client := setupClientsTest(t)

	t.Run("Get client by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients/"+strconv.FormatUint(uint64(client.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify client data
		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(client.Id), data["id"])
		assert.Equal(t, client.Email, data["email"])
		assert.Equal(t, client.Name, data["name"])
	})

	t.Run("Get client by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Frontend users can't access other clients' data, so it returns 403 Forbidden
		assert.Equal(t, http.StatusForbidden, w.Code)
	})

	t.Run("Get client by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients/"+strconv.FormatUint(uint64(client.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test CreateClient functionality
func TestCreateClient(t *testing.T) {
	r, token, _ := setupClientsTest(t)

	t.Run("Create client not allowed for frontend users", func(t *testing.T) {
		clientData := map[string]interface{}{
			"name":        "New Test Client",
			"email":       "<EMAIL>",
			"client_type": "individual",
			"status":      "active",
		}

		jsonData, _ := json.Marshal(clientData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/clients", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "Frontend users cannot create clients", response["message"])
	})

	t.Run("Create client with missing required fields", func(t *testing.T) {
		clientData := map[string]interface{}{
			"name": "Incomplete Client",
			// Missing email, client_type
		}

		jsonData, _ := json.Marshal(clientData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/clients", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
	})

	t.Run("Create client with duplicate email", func(t *testing.T) {
		clientData := map[string]interface{}{
			"name":        "Duplicate Client",
			"email":       "<EMAIL>", // Already exists
			"client_type": "individual",
			"status":      "active",
		}

		jsonData, _ := json.Marshal(clientData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/clients", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
	})

	t.Run("Create client without authorization", func(t *testing.T) {
		clientData := map[string]interface{}{
			"name":        "Unauthorized Client",
			"email":       "<EMAIL>",
			"client_type": "individual",
			"status":      "active",
		}

		jsonData, _ := json.Marshal(clientData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/clients", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test UpdateClient functionality
func TestUpdateClient(t *testing.T) {
	r, token, client := setupClientsTest(t)

	t.Run("Update client successfully", func(t *testing.T) {
		updateData := map[string]interface{}{
			"name":         "Updated Client Name",
			"email":        client.Email, // Keep the same email
			"phone_number": "1234567890", // Provide required phone number
		}

		jsonData, _ := json.Marshal(updateData)
		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/clients/"+strconv.FormatUint(uint64(client.Id), 10), bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "Client updated successfully", response["message"])
	})

	t.Run("Update client with different ID still updates own client", func(t *testing.T) {
		updateData := map[string]interface{}{
			"name":         "Updated Client Name",
			"email":        client.Email, // Keep same email to avoid conflicts
			"phone_number": "1234567890",
		}

		jsonData, _ := json.Marshal(updateData)
		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/clients/99999", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Frontend users always update their own client regardless of URL ID
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Update client without authorization", func(t *testing.T) {
		updateData := map[string]interface{}{
			"name":         "Updated Client Name",
			"email":        "<EMAIL>",
			"phone_number": "1234567890",
		}

		jsonData, _ := json.Marshal(updateData)
		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/clients/"+strconv.FormatUint(uint64(client.Id), 10), bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test DeleteClient functionality
func TestDeleteClient(t *testing.T) {
	r, token, client := setupClientsTest(t)

	t.Run("Delete client not allowed for frontend users", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodDelete, "/api/v1/frontend/clients/"+strconv.FormatUint(uint64(client.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "Frontend users cannot delete clients", response["message"])
	})

	t.Run("Delete client not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodDelete, "/api/v1/frontend/clients/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
	})

	t.Run("Delete client without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodDelete, "/api/v1/frontend/clients/"+strconv.FormatUint(uint64(client.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test SearchClients functionality
func TestSearchClients(t *testing.T) {
	r, token, client := setupClientsTest(t)

	t.Run("Search clients by name", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients/search?s=Clients Test Client", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data - frontend users only see their own client
		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Only their own client

		// Verify it's their own client
		clientData := data[0].(map[string]interface{})
		assert.Equal(t, float64(client.Id), clientData["id"])
	})

	t.Run("Search clients by email", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients/search?s=<EMAIL>", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data - frontend users only see their own client if it matches
		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Only their own client (if it matches the search)

		// Verify it's their own client
		clientData := data[0].(map[string]interface{})
		assert.Equal(t, float64(client.Id), clientData["id"])
	})

	t.Run("Search clients with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients/search?s=NONEXISTENT", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // No results
	})
}

// Test pagination functionality
func TestClientsPagination(t *testing.T) {
	r, token, client := setupClientsTest(t)

	t.Run("First page with default pagination", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify pagination values - frontend users only see their own client
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.Equal(t, float64(1), response["total"]) // Only 1 client (their own)

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Only their own client

		// Verify it's their own client
		clientData := data[0].(map[string]interface{})
		assert.Equal(t, float64(client.Id), clientData["id"])
	})

	t.Run("Second page (should be empty)", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count - no data on second page
		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // No data on second page
	})
}

// Test edge cases and error handling
func TestClientsEdgeCases(t *testing.T) {
	r, token, _ := setupClientsTest(t)

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid client ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/clients/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Invalid JSON in request body", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/clients", bytes.NewBufferString("invalid json"))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Frontend users can't create clients, so it returns 403 Forbidden regardless of JSON validity
		assert.Equal(t, http.StatusForbidden, w.Code)
	})
}
