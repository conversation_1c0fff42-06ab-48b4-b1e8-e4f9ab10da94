package controllers

import (
	"net/http"
	"strconv"
	"time"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

type OperatingCostController struct {
	operatingCostService *services.OperatingCostService
}

func NewOperatingCostController() *OperatingCostController {
	return &OperatingCostController{
		operatingCostService: services.NewOperatingCostService(),
	}
}

// GetOperatingCosts returns all operating costs for the client
func (c *OperatingCostController) GetOperatingCosts(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(ctx.DefaultQuery("per_page", "20"))
	costType := ctx.Query("cost_type")
	clientDeviceId := ctx.Query("client_device_id")
	startDate := ctx.Query("start_date")
	endDate := ctx.Query("end_date")

	// Build filters
	filters := models.OperatingCostFilters{
		ClientId:       clientId,
		Page:           page,
		PerPage:        perPage,
		CostType:       costType,
		ClientDeviceId: clientDeviceId,
	}

	// Parse dates if provided
	if startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			filters.StartDate = &date
		}
	}
	if endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			filters.EndDate = &date
		}
	}

	costs, total, err := c.operatingCostService.GetOperatingCosts(filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data": costs,
		"pagination": gin.H{
			"page":        page,
			"per_page":    perPage,
			"total":       total,
			"total_pages": (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// GetOperatingCost returns a specific operating cost record
func (c *OperatingCostController) GetOperatingCost(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	cost, err := c.operatingCostService.GetOperatingCost(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Operating cost record not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": cost})
}

// CreateOperatingCost creates a new operating cost record
func (c *OperatingCostController) CreateOperatingCost(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	var cost models.OperatingCost
	if err := ctx.ShouldBindJSON(&cost); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set client ID from context
	cost.ClientId = clientId

	createdCost, err := c.operatingCostService.CreateOperatingCost(cost)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{"data": createdCost})
}

// UpdateOperatingCost updates an existing operating cost record
func (c *OperatingCostController) UpdateOperatingCost(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var cost models.OperatingCost
	if err := ctx.ShouldBindJSON(&cost); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	cost.Id = uint(id)
	cost.ClientId = clientId

	updatedCost, err := c.operatingCostService.UpdateOperatingCost(cost)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": updatedCost})
}

// DeleteOperatingCost deletes an operating cost record
func (c *OperatingCostController) DeleteOperatingCost(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	err = c.operatingCostService.DeleteOperatingCost(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Operating cost record deleted successfully"})
}

// GetOperatingCostStats returns operating cost statistics
func (c *OperatingCostController) GetOperatingCostStats(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	stats, err := c.operatingCostService.GetOperatingCostStats(clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": stats})
}
