package controllers

import (
	"net/http"
	"time"
	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
)

func GetAllDriverDeviceAssignments(c *gin.Context) {
	var assignments []models.DriverDeviceAssignment
	clientId, _ := c.Get("client_id")

	query := config.DB.Joins("JOIN drivers ON drivers.id = driver_device_assignments.driver_id").
		Joins("JOIN client_devices ON client_devices.id = driver_device_assignments.client_device_id").
		Where("drivers.client_id = ? OR client_devices.client_id = ?", clientId, clientId)

	// Include relations
	query = query.Preload("Driver").Preload("ClientDevice")

	// Apply date filter if provided
	if date := c.Query("date"); date != "" {
		assignmentDate, err := time.Parse("2006-01-02", date)
		if err == nil {
			query = query.Where("assignment_date = ?", assignmentDate)
		}
	}

	// Apply driver filter if provided
	if driverId := c.Query("driver_id"); driverId != "" {
		query = query.Where("driver_id = ?", driverId)
	}

	// Apply device filter if provided
	if deviceId := c.Query("client_device_id"); deviceId != "" {
		query = query.Where("client_device_id = ?", deviceId)
	}

	// Apply status filter if provided
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply date range filter if provided
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			start, err1 := time.Parse("2006-01-02", startDate)
			end, err2 := time.Parse("2006-01-02", endDate)
			if err1 == nil && err2 == nil {
				query = query.Where("assignment_date BETWEEN ? AND ?", start, end)
			}
		}
	}

	result := query.Order("assignment_date DESC, start_time DESC").Find(&assignments)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to fetch driver device assignments",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": assignments,
	})
}

func SearchDriverDeviceAssignments(c *gin.Context) {
	var assignments []models.DriverDeviceAssignment
	clientId, _ := c.Get("client_id")

	query := config.DB.Joins("JOIN drivers ON drivers.id = driver_device_assignments.driver_id").
		Joins("JOIN client_devices ON client_devices.id = driver_device_assignments.client_device_id").
		Where("drivers.client_id = ? OR client_devices.client_id = ?", clientId, clientId)

	// Include relations
	query = query.Preload("Driver").Preload("ClientDevice")

	// Apply search filter
	if search := c.Query("search"); search != "" {
		query = query.Joins("JOIN drivers ON drivers.id = driver_device_assignments.driver_id").
			Joins("JOIN client_devices ON client_devices.id = driver_device_assignments.client_device_id").
			Where("drivers.name LIKE ? OR client_devices.name LIKE ? OR drivers.driver_license_no LIKE ?",
				"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply date filter if provided
	if date := c.Query("date"); date != "" {
		assignmentDate, err := time.Parse("2006-01-02", date)
		if err == nil {
			query = query.Where("assignment_date = ?", assignmentDate)
		}
	}

	// Apply driver filter if provided
	if driverId := c.Query("driver_id"); driverId != "" {
		query = query.Where("driver_id = ?", driverId)
	}

	// Apply device filter if provided
	if deviceId := c.Query("client_device_id"); deviceId != "" {
		query = query.Where("client_device_id = ?", deviceId)
	}

	// Apply status filter if provided
	if status := c.Query("status"); status != "" {
		query = query.Where("status = ?", status)
	}

	// Apply pagination
	page := c.DefaultQuery("page", "1")
	limit := c.DefaultQuery("limit", "10")

	var offset int
	if page == "1" {
		offset = 0
	} else {
		offset = (parseInt(page) - 1) * parseInt(limit)
	}

	result := query.Offset(offset).Limit(parseInt(limit)).Order("assignment_date DESC, start_time DESC").Find(&assignments)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to search driver device assignments",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": assignments,
	})
}

func GetDriverDeviceAssignmentById(c *gin.Context) {
	var assignment models.DriverDeviceAssignment
	clientId, _ := c.Get("client_id")

	if err := config.DB.Joins("JOIN drivers ON drivers.id = driver_device_assignments.driver_id").
		Joins("JOIN client_devices ON client_devices.id = driver_device_assignments.client_device_id").
		Where("drivers.client_id = ? OR client_devices.client_id = ?", clientId, clientId).
		Preload("Driver").Preload("ClientDevice").
		First(&assignment, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Driver device assignment not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": assignment,
	})
}

// CreateDriverDeviceAssignmentByRfid creates a new driver device assignment using RFID
func CreateDriverDeviceAssignmentByRfid(c *gin.Context) {
	var req models.CreateDriverDeviceAssignmentByRfidRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Get current user ID
	userId, _ := c.Get("user_id")
	clientId, _ := c.Get("client_id")

	// Find driver by RFID
	var driver models.Driver
	if err := config.DB.Where("rfid = ? AND client_id = ?", req.DriverRfid, clientId).First(&driver).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Driver not found with the provided RFID",
		})
		return
	}

	// Verify the device belongs to the client
	var device models.ClientDevice
	if err := config.DB.Where("id = ? AND client_id = ?", req.ClientDeviceId, clientId).First(&device).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Device not found or not accessible",
		})
		return
	}

	// Check if there's already an active assignment for this device on this date
	var existingAssignment models.DriverDeviceAssignment
	err := config.DB.Where("client_device_id = ? AND assignment_date = ? AND status = 'active'",
		req.ClientDeviceId, req.AssignmentDate).First(&existingAssignment).Error

	if err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "There is already an active assignment for this device on this date",
		})
		return
	}

	// Create assignment
	assignment := models.DriverDeviceAssignment{
		CreatedById:    userId.(uint),
		DriverId:       driver.Id,
		ClientDeviceId: req.ClientDeviceId,
		AssignmentDate: req.AssignmentDate,
		StartTime:      req.StartTime,
		EndTime:        req.EndTime,
		Status:         req.Status,
		Notes:          req.Notes,
	}

	result := config.DB.Create(&assignment)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Failed to create driver device assignment: " + result.Error.Error(),
		})
		return
	}

	// Preload relations for response
	config.DB.Preload("Driver").Preload("ClientDevice").First(&assignment, assignment.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Driver device assignment created successfully via RFID",
		"data":    assignment,
	})
}

func CreateDriverDeviceAssignment(c *gin.Context) {
	var req models.CreateDriverDeviceAssignmentRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Get current user ID
	userId, _ := c.Get("user_id")

	// Check if there's already an active assignment for this device on this date
	var existingAssignment models.DriverDeviceAssignment
	err := config.DB.Where("client_device_id = ? AND assignment_date = ? AND status = 'active'",
		req.ClientDeviceId, req.AssignmentDate).First(&existingAssignment).Error

	if err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "There is already an active assignment for this device on this date",
		})
		return
	}

	// Create assignment
	assignment := models.DriverDeviceAssignment{
		CreatedById:    userId.(uint),
		DriverId:       req.DriverId,
		ClientDeviceId: req.ClientDeviceId,
		AssignmentDate: req.AssignmentDate,
		StartTime:      req.StartTime,
		EndTime:        req.EndTime,
		Status:         req.Status,
		Notes:          req.Notes,
	}

	result := config.DB.Create(&assignment)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Failed to create driver device assignment: " + result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Driver device assignment created successfully",
		"data":    assignment,
	})
}

func UpdateDriverDeviceAssignment(c *gin.Context) {
	var req models.UpdateDriverDeviceAssignmentRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	clientId, _ := c.Get("client_id")

	var assignment models.DriverDeviceAssignment
	if err := config.DB.Joins("JOIN drivers ON drivers.id = driver_device_assignments.driver_id").
		Joins("JOIN client_devices ON client_devices.id = driver_device_assignments.client_device_id").
		Where("drivers.client_id = ? OR client_devices.client_id = ?", clientId, clientId).
		First(&assignment, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Driver device assignment not found",
		})
		return
	}

	// Check if there's already an active assignment for this device on this date (excluding current assignment)
	var existingAssignment models.DriverDeviceAssignment
	err := config.DB.Where("client_device_id = ? AND assignment_date = ? AND status = 'active' AND id != ?",
		req.ClientDeviceId, req.AssignmentDate, assignment.Id).First(&existingAssignment).Error

	if err == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "There is already an active assignment for this device on this date",
		})
		return
	}

	// Update assignment fields
	assignment.DriverId = req.DriverId
	assignment.ClientDeviceId = req.ClientDeviceId
	assignment.AssignmentDate = req.AssignmentDate
	assignment.StartTime = req.StartTime
	assignment.EndTime = req.EndTime
	assignment.Status = req.Status
	assignment.Notes = req.Notes

	result := config.DB.Save(&assignment)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Failed to update driver device assignment: " + result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Driver device assignment updated successfully",
		"data":    assignment,
	})
}

func DeleteDriverDeviceAssignment(c *gin.Context) {
	clientId, _ := c.Get("client_id")

	var assignment models.DriverDeviceAssignment
	if err := config.DB.Joins("JOIN drivers ON drivers.id = driver_device_assignments.driver_id").
		Joins("JOIN client_devices ON client_devices.id = driver_device_assignments.client_device_id").
		Where("drivers.client_id = ? OR client_devices.client_id = ?", clientId, clientId).
		First(&assignment, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Driver device assignment not found",
		})
		return
	}

	result := config.DB.Delete(&assignment)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to delete driver device assignment",
		})
		return
	}

	c.JSON(http.StatusNoContent, gin.H{
		"message": "Driver device assignment deleted successfully",
	})
}

// GetCurrentDriverAssignment returns the current active driver assignment for a device
func GetCurrentDriverAssignment(c *gin.Context) {
	clientId, _ := c.Get("client_id")
	deviceId := c.Query("client_device_id")

	if deviceId == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "client_device_id is required",
		})
		return
	}

	var assignment models.DriverDeviceAssignment
	today := time.Now().Format("2006-01-02")

	err := config.DB.Joins("JOIN drivers ON drivers.id = driver_device_assignments.driver_id").
		Where("drivers.client_id = ? AND client_device_id = ? AND assignment_date = ? AND status = 'active'",
			clientId, deviceId, today).
		Preload("Driver").Preload("ClientDevice").
		First(&assignment).Error

	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "No active driver assignment found for this device today",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": assignment,
	})
}
