package controllers

import (
	"testing"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
)

// TestAllActiveReportsComprehensive tests all active reports to find any SQL errors
func TestAllActiveReportsComprehensive(t *testing.T) {
	// Use the test database and seed it with reports
	services.FastCleanupTestData()

	// Get all active reports from the database
	var reports []models.Report
	err := config.DB.Where("status = ?", "active").Order("id").Find(&reports).Error
	if err != nil {
		t.Fatalf("Failed to get active reports: %v", err)
	}

	t.Logf("Found %d active reports to test", len(reports))

	// Test each active report
	for _, report := range reports {
		t.Run(report.ReportType, func(t *testing.T) {
			testReportComprehensive(t, report.Id, report.Name, report.ReportType)
		})
	}
}

func testReportComprehensive(t *testing.T, reportID uint, reportName, reportType string) {
	// Create filters
	filters := models.ReportFilters{
		StartDate: func() *time.Time { t := time.Now().AddDate(0, 0, -7); return &t }(),
		EndDate:   func() *time.Time { t := time.Now(); return &t }(),
		ClientId:  1, // Use client ID 1
		PerPage:   10,
		Page:      1,
	}

	// Create report service
	reportService := services.NewReportService()

	// Generate report
	reportData, err := reportService.GenerateReport(reportID, filters, "json")

	if err != nil {
		t.Logf("❌ Report %d (%s) failed: %v", reportID, reportName, err)
		return
	}

	t.Logf("✅ Report %d (%s) generated successfully", reportID, reportName)

	// Log some basic info about the report
	if reportData != nil {
		t.Logf("   Report data type: %T", reportData)
	}
}
