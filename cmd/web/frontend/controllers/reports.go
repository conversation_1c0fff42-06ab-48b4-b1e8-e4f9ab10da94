package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// GetReportsList returns all available reports organized by category
func GetReportsList(c *gin.Context) {
	var reports []models.Report
	err := config.DB.Where("status = ?", "active").
		Order("category, name").
		Find(&reports).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch reports"})
		return
	}

	// Group reports by category
	reportsByCategory := make(map[string][]models.Report)
	for _, report := range reports {
		reportsByCategory[report.Category] = append(reportsByCategory[report.Category], report)
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"reports_by_category": reportsByCategory,
			"total_reports":       len(reports),
		},
	})
}

// GetReportDetails returns details of a specific report
func GetReportDetails(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	var report models.Report
	err = config.DB.Session(&gorm.Session{Logger: config.DB.Logger.LogMode(logger.Silent)}).
		First(&report, uint(reportID)).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    report,
	})
}

// GenerateReport generates and returns report data
func GenerateReport(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	// Get client_id from context (set by authentication middleware)
	clientId, exists := c.Get("client_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found in context"})
		return
	}

	// Parse filters from request
	var filters models.ReportFilters
	if err := c.ShouldBindJSON(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid filter parameters"})
		return
	}

	// Set default date range if not provided (last 30 days)
	if filters.StartDate == nil || filters.EndDate == nil {
		endDate := time.Now()
		startDate := endDate.AddDate(0, 0, -30)
		if filters.StartDate == nil {
			filters.StartDate = &startDate
		}
		if filters.EndDate == nil {
			filters.EndDate = &endDate
		}
	}

	// Set default pagination
	if filters.PerPage == 0 {
		filters.PerPage = 100
	}

	// CRITICAL: Set client_id from context for security isolation
	var clientIdValue uint
	if clientIdPtr, ok := clientId.(*uint); ok && clientIdPtr != nil {
		clientIdValue = *clientIdPtr
	} else if clientIdVal, ok := clientId.(uint); ok {
		clientIdValue = clientIdVal
	} else {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid client ID"})
		return
	}
	filters.ClientId = clientIdValue

	// Generate report
	reportService := services.NewReportService()
	reportData, err := reportService.GenerateReport(uint(reportID), filters, "json")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate report: %v", err)})
		return
	}

	// Debug: Log the report data structure
	if reportData == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data":    gin.H{"error": "No report data generated"},
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    reportData,
	})
}

// ExportReport exports report to PDF or Excel
func ExportReport(c *gin.Context) {
	reportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report ID"})
		return
	}

	// Get format from query parameter first, then from JSON body as fallback
	format := c.Query("format")

	// Read the request body to check for format
	body, err := c.GetRawData()
	if err == nil && len(body) > 0 {
		var requestBody map[string]interface{}
		if json.Unmarshal(body, &requestBody) == nil {
			if formatFromBody, exists := requestBody["format"]; exists {
				if formatStr, ok := formatFromBody.(string); ok && format == "" {
					format = formatStr
				}
			}
		}
		// Restore the body for later use
		c.Request.Body = io.NopCloser(bytes.NewBuffer(body))
	}

	if format == "" {
		format = "pdf"
	}

	// Validate format
	if format != "pdf" && format != "excel" && format != "csv" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid format. Supported: pdf, excel, csv"})
		return
	}

	// Get client_id from context (set by authentication middleware)
	clientId, exists := c.Get("client_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found in context"})
		return
	}

	// Parse filters from request body
	var filters models.ReportFilters
	if err := c.ShouldBindJSON(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid filter parameters"})
		return
	}

	// Set default date range if not provided
	if filters.StartDate == nil || filters.EndDate == nil {
		endDate := time.Now()
		startDate := endDate.AddDate(0, 0, -30)
		if filters.StartDate == nil {
			filters.StartDate = &startDate
		}
		if filters.EndDate == nil {
			filters.EndDate = &endDate
		}
	}

	// CRITICAL: Set client_id from context for security isolation
	var clientIdValue uint
	if clientIdPtr, ok := clientId.(*uint); ok && clientIdPtr != nil {
		clientIdValue = *clientIdPtr
	} else if clientIdVal, ok := clientId.(uint); ok {
		clientIdValue = clientIdVal
	} else {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid client ID"})
		return
	}
	filters.ClientId = clientIdValue

	// Generate report data
	reportService := services.NewReportService()
	reportData, err := reportService.GenerateReport(uint(reportID), filters, "json")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate report: %v", err)})
		return
	}

	// Export report using export service
	exportService := services.NewReportExportService()
	exportData, contentType, err := exportService.ExportReport(reportData, format)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to export report: %v", err)})
		return
	}

	// Generate filename
	timestamp := time.Now().Format("2006-01-02_15-04-05")
	filename := fmt.Sprintf("%s_%s.%s",
		sanitizeFilename(reportData.ReportInfo.Name), timestamp, format)

	// Set appropriate headers and return file
	c.Header("Content-Type", contentType)
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Length", strconv.Itoa(len(exportData)))

	c.Data(http.StatusOK, contentType, exportData)
}

// GetScheduledReports returns all scheduled reports for the user
func GetScheduledReports(c *gin.Context) {
	var scheduledReports []models.ScheduledReport
	err := config.DB.Preload("Report").
		Where("status != ?", "inactive").
		Order("created_at DESC").
		Find(&scheduledReports).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch scheduled reports"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    scheduledReports,
	})
}

// TestDTOEndpoint tests DTO functionality without authentication
func TestDTOEndpoint(c *gin.Context) {
	// Check if format is specified in query parameter
	format := c.Query("format")

	// Create test filters
	filters := models.ReportFilters{
		ClientId: 1, // Test client ID
	}

	// Test DTO generation
	reportService := services.NewReportService()

	// Get report type and format from request body (read only once)
	var requestBody map[string]interface{}
	reportType := "position_log" // default

	if err := c.ShouldBindJSON(&requestBody); err == nil {
		// Extract format from request body if not in query
		if format == "" {
			if formatFromBody, exists := requestBody["format"]; exists {
				if formatStr, ok := formatFromBody.(string); ok {
					format = formatStr
				}
			}
		}

		// Extract report_type from request body
		if rt, exists := requestBody["report_type"]; exists {
			if rtStr, ok := rt.(string); ok {
				reportType = rtStr
			}
		}
	}

	var reportData *models.ReportData
	var totalRecords, filteredRecords int

	// Test different report types
	switch reportType {
	case "last_location":
		// Test Last Location Report using GenerateReport
		reportData, err := reportService.GenerateReport(33, filters, format) // Report ID 33 is last_location
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate Last Location Report: %v", err)})
			return
		}
		totalRecords = reportData.Metadata.TotalRecords
		filteredRecords = reportData.Metadata.FilteredRecords
	case "trip_detail_delta":
		// Test Trip Detail Delta Report
		reportData, err := reportService.GenerateReport(74, filters, format) // Report ID 74 is trip_detail_delta
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate Trip Detail Delta Report: %v", err)})
			return
		}
		totalRecords = reportData.Metadata.TotalRecords
		filteredRecords = reportData.Metadata.FilteredRecords
	case "position_log_driver":
		// Test Position Log Driver Report
		reportData, err := reportService.GenerateReport(73, filters, format) // Report ID 73 is position_log_driver
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate Position Log Driver Report: %v", err)})
			return
		}
		totalRecords = reportData.Metadata.TotalRecords
		filteredRecords = reportData.Metadata.FilteredRecords
	case "trip_mileage":
		// Test Trip Mileage Report
		reportData, err := reportService.GenerateReport(75, filters, format) // Report ID 75 is trip_mileage
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate Trip Mileage Report: %v", err)})
			return
		}
		totalRecords = reportData.Metadata.TotalRecords
		filteredRecords = reportData.Metadata.FilteredRecords
	case "fuel_estimation":
		// Test Fuel Estimation Report
		reportData, err := reportService.GenerateReport(65, filters, format) // Report ID 65 is fuel_estimation
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate Fuel Estimation Report: %v", err)})
			return
		}
		totalRecords = reportData.Metadata.TotalRecords
		filteredRecords = reportData.Metadata.FilteredRecords
	default:
		// Test Position Log DTO (default)
		positionLogDTO, totalRec, filteredRec, err := reportService.GeneratePositionLogDTOExample(filters)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to generate Position Log DTO: %v", err)})
			return
		}
		totalRecords = totalRec
		filteredRecords = filteredRec

		reportData = &models.ReportData{
			ReportInfo: models.Report{
				Name:        "Position Log Report",
				Description: "Raw GPS location data with timestamps, coordinates, speed, and device information for detailed tracking analysis.",
				Category:    "Detail",
				Status:      "active",
			},
			Filters: filters,
			Data:    positionLogDTO,
			Metadata: models.ReportMetadata{
				GeneratedAt:     time.Now(),
				TotalRecords:    totalRecords,
				FilteredRecords: filteredRecords,
				ExecutionTime:   "150ms",
				Format:          format,
			},
		}
	}

	// If format is specified, export the report
	if format != "" {
		exportService := services.NewReportExportService()
		exportData, contentType, err := exportService.ExportReport(reportData, format)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("Failed to export report: %v", err)})
			return
		}

		// Generate filename
		timestamp := time.Now().Format("2006-01-02_15-04-05")
		filename := fmt.Sprintf("position_log_report_%s.%s", timestamp, format)

		// Set appropriate headers and return file
		c.Header("Content-Type", contentType)
		c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
		c.Header("Content-Length", strconv.Itoa(len(exportData)))

		c.Data(http.StatusOK, contentType, exportData)
		return
	}

	// Return JSON response
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    reportData,
		"message": "DTO test completed successfully",
	})
}

// CreateScheduledReport creates a new scheduled report
func CreateScheduledReport(c *gin.Context) {
	var request models.CreateScheduledReportRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Validate report exists
	var report models.Report
	err := config.DB.First(&report, request.ReportId).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Report not found"})
		return
	}

	// Calculate next run time based on frequency
	nextRunAt := calculateNextRunTime(request.Frequency, request.CronPattern, request.Timezone)

	// Create scheduled report
	scheduledReport := models.ScheduledReport{
		ReportId:    request.ReportId,
		Name:        request.Name,
		Description: request.Description,
		Frequency:   request.Frequency,
		CronPattern: request.CronPattern,
		Timezone:    request.Timezone,
		Filters:     request.Filters,
		Recipients:  request.Recipients,
		Format:      request.Format,
		NextRunAt:   &nextRunAt,
		Status:      models.ScheduledReportStatusActive,
		CreatedBy:   1, // TODO: Get from authenticated user
	}

	err = config.DB.Create(&scheduledReport).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create scheduled report"})
		return
	}

	// Load the report relationship
	config.DB.Preload("Report").First(&scheduledReport, scheduledReport.Id)

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    scheduledReport,
		"message": "Scheduled report created successfully",
	})
}

// UpdateScheduledReport updates an existing scheduled report
func UpdateScheduledReport(c *gin.Context) {
	scheduledReportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scheduled report ID"})
		return
	}

	var scheduledReport models.ScheduledReport
	err = config.DB.First(&scheduledReport, uint(scheduledReportID)).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Scheduled report not found"})
		return
	}

	var request models.CreateScheduledReportRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request data"})
		return
	}

	// Update fields
	scheduledReport.Name = request.Name
	scheduledReport.Description = request.Description
	scheduledReport.Frequency = request.Frequency
	scheduledReport.CronPattern = request.CronPattern
	scheduledReport.Timezone = request.Timezone
	scheduledReport.Filters = request.Filters
	scheduledReport.Recipients = request.Recipients
	scheduledReport.Format = request.Format

	// Recalculate next run time
	nextRunAt := calculateNextRunTime(request.Frequency, request.CronPattern, request.Timezone)
	scheduledReport.NextRunAt = &nextRunAt

	err = config.DB.Save(&scheduledReport).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update scheduled report"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    scheduledReport,
		"message": "Scheduled report updated successfully",
	})
}

// DeleteScheduledReport deletes a scheduled report
func DeleteScheduledReport(c *gin.Context) {
	scheduledReportID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid scheduled report ID"})
		return
	}

	err = config.DB.Delete(&models.ScheduledReport{}, uint(scheduledReportID)).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete scheduled report"})
		return
	}

	c.JSON(http.StatusNoContent, gin.H{
		"success": true,
		"message": "Scheduled report deleted successfully",
	})
}

// Helper functions
func sanitizeFilename(filename string) string {
	// Remove special characters and replace spaces with underscores
	result := ""
	for _, char := range filename {
		if (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') || (char >= '0' && char <= '9') || char == '_' || char == '-' {
			result += string(char)
		} else if char == ' ' {
			result += "_"
		}
	}
	return result
}

func calculateNextRunTime(frequency, cronPattern, timezone string) time.Time {
	now := time.Now()

	switch frequency {
	case models.ReportFrequencyDaily:
		return now.AddDate(0, 0, 1)
	case models.ReportFrequencyWeekly:
		return now.AddDate(0, 0, 7)
	case models.ReportFrequencyMonthly:
		return now.AddDate(0, 1, 0)
	case models.ReportFrequencyCustom:
		// TODO: Implement cron pattern parsing
		return now.AddDate(0, 0, 1) // Default to daily
	default:
		return now.AddDate(0, 0, 1)
	}
}

// Maintenance Schedule Controller Methods

// GetMaintenanceSchedules returns all maintenance schedules for the client
func GetMaintenanceSchedules(c *gin.Context) {
	maintenanceController := NewMaintenanceScheduleController()
	maintenanceController.GetMaintenanceSchedules(c)
}

// GetMaintenanceSchedule returns a specific maintenance schedule
func GetMaintenanceSchedule(c *gin.Context) {
	maintenanceController := NewMaintenanceScheduleController()
	maintenanceController.GetMaintenanceSchedule(c)
}

// CreateMaintenanceSchedule creates a new maintenance schedule
func CreateMaintenanceSchedule(c *gin.Context) {
	maintenanceController := NewMaintenanceScheduleController()
	maintenanceController.CreateMaintenanceSchedule(c)
}

// UpdateMaintenanceSchedule updates an existing maintenance schedule
func UpdateMaintenanceSchedule(c *gin.Context) {
	maintenanceController := NewMaintenanceScheduleController()
	maintenanceController.UpdateMaintenanceSchedule(c)
}

// DeleteMaintenanceSchedule deletes a maintenance schedule
func DeleteMaintenanceSchedule(c *gin.Context) {
	maintenanceController := NewMaintenanceScheduleController()
	maintenanceController.DeleteMaintenanceSchedule(c)
}

// GetUpcomingMaintenance returns maintenance schedules due soon
func GetUpcomingMaintenance(c *gin.Context) {
	maintenanceController := NewMaintenanceScheduleController()
	maintenanceController.GetUpcomingMaintenance(c)
}

// GetMaintenanceStats returns maintenance statistics
func GetMaintenanceStats(c *gin.Context) {
	maintenanceController := NewMaintenanceScheduleController()
	maintenanceController.GetMaintenanceStats(c)
}

// Customer Visit Controller Methods

// GetCustomerVisits returns all customer visits for the client
func GetCustomerVisits(c *gin.Context) {
	customerVisitController := NewCustomerVisitController()
	customerVisitController.GetCustomerVisits(c)
}

// GetCustomerVisit returns a specific customer visit
func GetCustomerVisit(c *gin.Context) {
	customerVisitController := NewCustomerVisitController()
	customerVisitController.GetCustomerVisit(c)
}

// CreateCustomerVisit creates a new customer visit
func CreateCustomerVisit(c *gin.Context) {
	customerVisitController := NewCustomerVisitController()
	customerVisitController.CreateCustomerVisit(c)
}

// UpdateCustomerVisit updates an existing customer visit
func UpdateCustomerVisit(c *gin.Context) {
	customerVisitController := NewCustomerVisitController()
	customerVisitController.UpdateCustomerVisit(c)
}

// DeleteCustomerVisit deletes a customer visit
func DeleteCustomerVisit(c *gin.Context) {
	customerVisitController := NewCustomerVisitController()
	customerVisitController.DeleteCustomerVisit(c)
}

// GetCustomerVisitStats returns customer visit statistics
func GetCustomerVisitStats(c *gin.Context) {
	customerVisitController := NewCustomerVisitController()
	customerVisitController.GetCustomerVisitStats(c)
}

// Driver Training Controller Methods

// GetDriverTrainings returns all driver training records for the client
func GetDriverTrainings(c *gin.Context) {
	driverTrainingController := NewDriverTrainingController()
	driverTrainingController.GetDriverTrainings(c)
}

// GetDriverTraining returns a specific driver training record
func GetDriverTraining(c *gin.Context) {
	driverTrainingController := NewDriverTrainingController()
	driverTrainingController.GetDriverTraining(c)
}

// CreateDriverTraining creates a new driver training record
func CreateDriverTraining(c *gin.Context) {
	driverTrainingController := NewDriverTrainingController()
	driverTrainingController.CreateDriverTraining(c)
}

// UpdateDriverTraining updates an existing driver training record
func UpdateDriverTraining(c *gin.Context) {
	driverTrainingController := NewDriverTrainingController()
	driverTrainingController.UpdateDriverTraining(c)
}

// DeleteDriverTraining deletes a driver training record
func DeleteDriverTraining(c *gin.Context) {
	driverTrainingController := NewDriverTrainingController()
	driverTrainingController.DeleteDriverTraining(c)
}

// GetExpiringTrainings returns training records that are expiring soon
func GetExpiringTrainings(c *gin.Context) {
	driverTrainingController := NewDriverTrainingController()
	driverTrainingController.GetExpiringTrainings(c)
}

// GetDriverTrainingStats returns driver training statistics
func GetDriverTrainingStats(c *gin.Context) {
	driverTrainingController := NewDriverTrainingController()
	driverTrainingController.GetDriverTrainingStats(c)
}

// Operating Cost Controller Methods

// GetOperatingCosts returns all operating costs for the client
func GetOperatingCosts(c *gin.Context) {
	operatingCostController := NewOperatingCostController()
	operatingCostController.GetOperatingCosts(c)
}

// GetOperatingCost returns a specific operating cost record
func GetOperatingCost(c *gin.Context) {
	operatingCostController := NewOperatingCostController()
	operatingCostController.GetOperatingCost(c)
}

// CreateOperatingCost creates a new operating cost record
func CreateOperatingCost(c *gin.Context) {
	operatingCostController := NewOperatingCostController()
	operatingCostController.CreateOperatingCost(c)
}

// UpdateOperatingCost updates an existing operating cost record
func UpdateOperatingCost(c *gin.Context) {
	operatingCostController := NewOperatingCostController()
	operatingCostController.UpdateOperatingCost(c)
}

// DeleteOperatingCost deletes an operating cost record
func DeleteOperatingCost(c *gin.Context) {
	operatingCostController := NewOperatingCostController()
	operatingCostController.DeleteOperatingCost(c)
}

// GetOperatingCostStats returns operating cost statistics
func GetOperatingCostStats(c *gin.Context) {
	operatingCostController := NewOperatingCostController()
	operatingCostController.GetOperatingCostStats(c)
}

// Safety Compliance Controller Methods

// GetSafetyCompliances returns all safety compliance records for the client
func GetSafetyCompliances(c *gin.Context) {
	safetyComplianceController := NewSafetyComplianceController()
	safetyComplianceController.GetSafetyCompliances(c)
}

// GetSafetyCompliance returns a specific safety compliance record
func GetSafetyCompliance(c *gin.Context) {
	safetyComplianceController := NewSafetyComplianceController()
	safetyComplianceController.GetSafetyCompliance(c)
}

// CreateSafetyCompliance creates a new safety compliance record
func CreateSafetyCompliance(c *gin.Context) {
	safetyComplianceController := NewSafetyComplianceController()
	safetyComplianceController.CreateSafetyCompliance(c)
}

// UpdateSafetyCompliance updates an existing safety compliance record
func UpdateSafetyCompliance(c *gin.Context) {
	safetyComplianceController := NewSafetyComplianceController()
	safetyComplianceController.UpdateSafetyCompliance(c)
}

// DeleteSafetyCompliance deletes a safety compliance record
func DeleteSafetyCompliance(c *gin.Context) {
	safetyComplianceController := NewSafetyComplianceController()
	safetyComplianceController.DeleteSafetyCompliance(c)
}

// GetExpiringCompliances returns compliance records that are expiring soon
func GetExpiringCompliances(c *gin.Context) {
	safetyComplianceController := NewSafetyComplianceController()
	safetyComplianceController.GetExpiringCompliances(c)
}

// GetSafetyComplianceStats returns safety compliance statistics
func GetSafetyComplianceStats(c *gin.Context) {
	safetyComplianceController := NewSafetyComplianceController()
	safetyComplianceController.GetSafetyComplianceStats(c)
}

// Service Technician Controller Methods

// GetServiceTechnicians returns all service technicians for the client
func GetServiceTechnicians(c *gin.Context) {
	serviceTechnicianController := NewServiceTechnicianController()
	serviceTechnicianController.GetServiceTechnicians(c)
}

// GetServiceTechnician returns a specific service technician
func GetServiceTechnician(c *gin.Context) {
	serviceTechnicianController := NewServiceTechnicianController()
	serviceTechnicianController.GetServiceTechnician(c)
}

// CreateServiceTechnician creates a new service technician
func CreateServiceTechnician(c *gin.Context) {
	serviceTechnicianController := NewServiceTechnicianController()
	serviceTechnicianController.CreateServiceTechnician(c)
}

// UpdateServiceTechnician updates an existing service technician
func UpdateServiceTechnician(c *gin.Context) {
	serviceTechnicianController := NewServiceTechnicianController()
	serviceTechnicianController.UpdateServiceTechnician(c)
}

// DeleteServiceTechnician deletes a service technician
func DeleteServiceTechnician(c *gin.Context) {
	serviceTechnicianController := NewServiceTechnicianController()
	serviceTechnicianController.DeleteServiceTechnician(c)
}

// GetAvailableTechnicians returns available service technicians
func GetAvailableTechnicians(c *gin.Context) {
	serviceTechnicianController := NewServiceTechnicianController()
	serviceTechnicianController.GetAvailableTechnicians(c)
}

// GetServiceTechnicianStats returns service technician statistics
func GetServiceTechnicianStats(c *gin.Context) {
	serviceTechnicianController := NewServiceTechnicianController()
	serviceTechnicianController.GetServiceTechnicianStats(c)
}
