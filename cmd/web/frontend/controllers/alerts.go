package controllers

import (
	"net/http"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
)

func GetAllAlerts(c *gin.Context) {
	var alerts []models.Alert
	var total int64
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		filter["alert_type"] = alertType
	}
	if read := c.Query("read"); read != "" {
		filter["read"] = read
	}

	query := config.DB.Scopes(utils.Paginate(c)).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Where("client_devices.client_id = ?", clientId).Preload("ClientDevice").Preload("ClientDevice.Client").Where(filter)

	// Add date range filtering
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			query.Where("DATE(alert_timestamp) >= ? AND DATE(alert_timestamp) <= ?", startDate, endDate)
		} else {
			query.Where("DATE(alert_timestamp) = ?", startDate)
		}
	}

	query.Order("id desc").Find(&alerts)

	// Count with same filters
	countQuery := config.DB.Model(&models.Alert{}).Where(filter).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Where("client_devices.client_id = ?", clientId)
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			countQuery.Where("DATE(alert_timestamp) >= ? AND DATE(alert_timestamp) <= ?", startDate, endDate)
		} else {
			countQuery.Where("DATE(alert_timestamp) = ?", startDate)
		}
	}
	countQuery.Count(&total)
	// Extract current_page and per_page from query params (same logic as utils.Paginate)
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}
	perPage, _ := strconv.Atoi(c.Query("per_page"))
	switch {
	case perPage > 100:
		perPage = 100
	case perPage <= 0:
		perPage = 10
	}
	c.JSON(http.StatusOK, gin.H{
		"data":         alerts,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
	})
}

func GetAlertById(c *gin.Context) {
	var alert models.Alert
	clientId, _ := c.Get("client_id")
	if err := config.DB.Preload("GPSData").Preload("ClientDevice").Joins("left join client_devices on client_devices.id = alerts.client_device_id").Where("client_devices.client_id = ?", clientId).Preload("ClientDevice.Client").First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Auto-mark as read if not already read
	if alert.Read == nil || !*alert.Read {
		read := true
		now := time.Now()
		alert.Read = &read
		alert.ReadAt = &now

		// Save the updated alert
		if err := config.DB.Save(&alert).Error; err != nil {
			// Log error but continue with the response
			// In production, you might want to use a proper logger
			// For now, we'll just continue
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": alert,
	})
}

func SearchAlerts(c *gin.Context) {
	var alerts []models.Alert
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		filter["client_device_id"] = clientDeviceId
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		filter["device_id"] = deviceId
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		filter["alert_type"] = alertType
	}
	if read := c.Query("read"); read != "" {
		filter["read"] = read
	}

	query := config.DB.Where(filter).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Where("client_devices.client_id = ?", clientId).Preload("ClientDevice").Preload("ClientDevice.Client")

	// Add date range filtering
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			query.Where("DATE(alert_timestamp) >= ? AND DATE(alert_timestamp) <= ?", startDate, endDate)
		} else {
			query.Where("DATE(alert_timestamp) = ?", startDate)
		}
	}

	query.Order("id desc").Find(&alerts)
	c.JSON(http.StatusOK, gin.H{
		"data": alerts,
	})
}

// MarkAlertAsRead marks a single alert as read for frontend users
func MarkAlertAsRead(c *gin.Context) {
	var alert models.Alert
	clientId, _ := c.Get("client_id")

	if err := config.DB.Preload("GPSData").Joins("left join client_devices on client_devices.id = alerts.client_device_id").
		Where("client_devices.client_id = ?", clientId).
		First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Mark as read
	read := true
	now := time.Now()
	alert.Read = &read
	alert.ReadAt = &now

	result := config.DB.Save(&alert)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert marked as read successfully",
		"data":    alert,
	})
}

// MarkAlertAsUnread marks a single alert as unread for frontend users
func MarkAlertAsUnread(c *gin.Context) {
	var alert models.Alert
	clientId, _ := c.Get("client_id")

	if err := config.DB.Preload("GPSData").Joins("left join client_devices on client_devices.id = alerts.client_device_id").
		Where("client_devices.client_id = ?", clientId).
		First(&alert, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert not found",
		})
		return
	}

	// Mark as unread
	read := false
	alert.Read = &read
	alert.ReadAt = nil

	result := config.DB.Save(&alert)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert marked as unread successfully",
		"data":    alert,
	})
}

// BulkMarkAlertsAsRead marks multiple alerts as read for frontend users
func BulkMarkAlertsAsRead(c *gin.Context) {
	type BulkMarkRequest struct {
		AlertIds []uint `json:"alert_ids" binding:"required"`
	}

	var req BulkMarkRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	if len(req.AlertIds) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "No alert IDs provided",
		})
		return
	}

	clientId, _ := c.Get("client_id")

	// Update multiple alerts with client restriction using subquery
	read := true
	now := time.Now()
	result := config.DB.Model(&models.Alert{}).
		Where("id IN ?", req.AlertIds).
		Where("client_device_id IN (?)",
			config.DB.Table("client_devices").Select("id").Where("client_id = ?", clientId)).
		Updates(map[string]interface{}{
			"read":    &read,
			"read_at": &now,
		})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "Alerts marked as read successfully",
		"updated_count": result.RowsAffected,
	})
}

// MarkAllAlertsAsRead marks all alerts as read for frontend users (with optional filters)
func MarkAllAlertsAsRead(c *gin.Context) {
	clientId, _ := c.Get("client_id")

	query := config.DB.Model(&models.Alert{}).
		Where("client_device_id IN (?)",
			config.DB.Table("client_devices").Select("id").Where("client_id = ?", clientId))

	// Apply filters if provided
	if clientDeviceId := c.Query("client_device_id"); clientDeviceId != "" {
		if id, err := strconv.Atoi(clientDeviceId); err == nil {
			query = query.Where("alerts.client_device_id = ?", id)
		}
	}
	if deviceId := c.Query("device_id"); deviceId != "" {
		query = query.Where("alerts.device_id = ?", deviceId)
	}
	if alertType := c.Query("alert_type"); alertType != "" {
		query = query.Where("alerts.alert_type = ?", alertType)
	}

	// Only update unread alerts (escape 'read' as it's a reserved word in MySQL)
	query = query.Where("alerts.`read` IS NULL OR alerts.`read` = ?", false)

	read := true
	now := time.Now()
	result := query.Updates(map[string]interface{}{
		"read":    &read,
		"read_at": &now,
	})

	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":       "All alerts marked as read successfully",
		"updated_count": result.RowsAffected,
	})
}

// CreateAlert creates a new alert for frontend users
func CreateAlert(c *gin.Context) {
	var req models.CreateAlertRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	clientId, _ := c.Get("client_id")

	// Verify that the client device belongs to the current client
	var clientDevice models.ClientDevice
	if err := config.DB.Where("id = ? AND client_id = ?", req.ClientDeviceId, clientId).First(&clientDevice).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client device not found",
		})
		return
	}

	alert := models.Alert{
		ClientDeviceId: req.ClientDeviceId,
		DeviceId:       req.DeviceId,
		AlertType:      req.AlertType,
		AlertName:      req.AlertName,
		Message:        req.Message,
		RawData:        req.RawData,
		AdditionalData: req.AdditionalData,
		Speed:          req.Speed,
		Direction:      req.Direction,
		AlertTimestamp: req.AlertTimestamp,
	}

	result := config.DB.Create(&alert)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert created successfully",
		"data":    alert,
	})
}
