package controllers

import (
	"net/http"
	"strconv"
	"time"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

type MaintenanceScheduleController struct {
	maintenanceService *services.MaintenanceScheduleService
}

func NewMaintenanceScheduleController() *MaintenanceScheduleController {
	return &MaintenanceScheduleController{
		maintenanceService: services.NewMaintenanceScheduleService(),
	}
}

// GetMaintenanceSchedules returns all maintenance schedules for the client
func (c *MaintenanceScheduleController) GetMaintenanceSchedules(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(ctx.DefaultQuery("per_page", "20"))
	status := ctx.Query("status")
	priority := ctx.Query("priority")
	maintenanceType := ctx.Query("maintenance_type")
	startDate := ctx.Query("start_date")
	endDate := ctx.Query("end_date")
	clientDeviceId := ctx.Query("client_device_id")

	// Build filters
	filters := models.MaintenanceScheduleFilters{
		ClientId:        clientId,
		Page:            page,
		PerPage:         perPage,
		Status:          status,
		Priority:        priority,
		MaintenanceType: maintenanceType,
		ClientDeviceId:  clientDeviceId,
	}

	// Parse dates if provided
	if startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			filters.StartDate = &date
		}
	}
	if endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			filters.EndDate = &date
		}
	}

	schedules, total, err := c.maintenanceService.GetMaintenanceSchedules(filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data": schedules,
		"pagination": gin.H{
			"page":        page,
			"per_page":    perPage,
			"total":       total,
			"total_pages": (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// GetMaintenanceSchedule returns a specific maintenance schedule
func (c *MaintenanceScheduleController) GetMaintenanceSchedule(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	schedule, err := c.maintenanceService.GetMaintenanceSchedule(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Maintenance schedule not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": schedule})
}

// CreateMaintenanceSchedule creates a new maintenance schedule
func (c *MaintenanceScheduleController) CreateMaintenanceSchedule(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	var schedule models.MaintenanceSchedule
	if err := ctx.ShouldBindJSON(&schedule); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set client ID from context
	schedule.ClientId = clientId

	createdSchedule, err := c.maintenanceService.CreateMaintenanceSchedule(schedule)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{"data": createdSchedule})
}

// UpdateMaintenanceSchedule updates an existing maintenance schedule
func (c *MaintenanceScheduleController) UpdateMaintenanceSchedule(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var schedule models.MaintenanceSchedule
	if err := ctx.ShouldBindJSON(&schedule); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	schedule.Id = uint(id)
	schedule.ClientId = clientId

	updatedSchedule, err := c.maintenanceService.UpdateMaintenanceSchedule(schedule)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": updatedSchedule})
}

// DeleteMaintenanceSchedule deletes a maintenance schedule
func (c *MaintenanceScheduleController) DeleteMaintenanceSchedule(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	err = c.maintenanceService.DeleteMaintenanceSchedule(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Maintenance schedule deleted successfully"})
}

// GetUpcomingMaintenance returns maintenance schedules due soon
func (c *MaintenanceScheduleController) GetUpcomingMaintenance(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	days, _ := strconv.Atoi(ctx.DefaultQuery("days", "30"))

	schedules, err := c.maintenanceService.GetUpcomingMaintenance(clientId, days)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": schedules})
}

// GetMaintenanceStats returns maintenance statistics
func (c *MaintenanceScheduleController) GetMaintenanceStats(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	stats, err := c.maintenanceService.GetMaintenanceStats(clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": stats})
}
