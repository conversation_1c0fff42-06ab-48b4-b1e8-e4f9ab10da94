package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/payment_gateways"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
)

func GetAllPayments(c *gin.Context) {
	var payments []models.InvoicePayment
	var total int64
	clientId, _ := c.Get("client_id")
	filter := map[string]interface{}{}
	if invoiceId := c.Query("invoice_id"); invoiceId != "" {
		filter["invoice_id"] = invoiceId
	}
	if paymentTypeId := c.Query("payment_type_id"); paymentTypeId != "" {
		filter["payment_type_id"] = paymentTypeId
	}
	if currencyId := c.Query("currency_id"); currencyId != "" {
		filter["currency_id"] = currencyId
	}

	query := config.DB.Scopes(utils.Paginate(c)).Where("client_id = ?", clientId).Joins("left join invoices on invoices.id = invoice_payments.invoice_id").Preload("PaymentType").Preload("Currency").Preload("Invoice").Preload("Invoice.Client").Where(filter)

	// Add date range filtering
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			query.Where("DATE(invoice_payments.date) >= ? AND DATE(invoice_payments.date) <= ?", startDate, endDate)
		} else {
			query.Where("DATE(invoice_payments.date) = ?", startDate)
		}
	}

	query.Order("id desc").Find(&payments)

	// Count with same filters
	countQuery := config.DB.Where("client_id = ?", clientId).Joins("left join invoices on invoices.id = invoice_payments.invoice_id").Model(&models.InvoicePayment{}).Where(filter)
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			countQuery.Where("DATE(invoice_payments.date) >= ? AND DATE(invoice_payments.date) <= ?", startDate, endDate)
		} else {
			countQuery.Where("DATE(invoice_payments.date) = ?", startDate)
		}
	}
	countQuery.Count(&total)
	// Extract current_page and per_page from query params (same logic as utils.Paginate)
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}
	perPage, _ := strconv.Atoi(c.Query("per_page"))
	switch {
	case perPage > 100:
		perPage = 100
	case perPage <= 0:
		perPage = 10
	}
	// Calculate total pages
	totalPages := int((total + int64(perPage) - 1) / int64(perPage))

	c.JSON(http.StatusOK, gin.H{
		"data":         payments,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
		"total_pages":  totalPages,
	})
}

func GetPaymentById(c *gin.Context) {
	var payment models.InvoicePayment
	clientId, _ := c.Get("client_id")

	// Validate ID format
	paymentId := c.Param("id")
	if _, err := strconv.ParseUint(paymentId, 10, 32); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid payment ID format",
		})
		return
	}

	if err := config.DB.Joins("left join invoices on invoices.id = invoice_payments.invoice_id").Where("client_id = ?", clientId).Preload("PaymentType").Preload("Currency").Preload("Invoice").Preload("Invoice.Client").First(&payment, paymentId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Payment not found",
		})
		return
	}
	c.JSON(http.StatusOK, gin.H{
		"data": payment,
	})
}

func CreatePayment(c *gin.Context) {
	var req models.InvoicePaymentRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	clientId, _ := c.Get("client_id")
	var invoice models.Invoice
	if err := config.DB.Preload("Currency").Preload("Client").Preload("InvoiceItems").Where("client_id = ?", clientId).First(&invoice, req.InvoiceId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Invoice not found",
		})
		return
	}
	var paymentType models.PaymentType
	if err := config.DB.First(&paymentType, req.PaymentTypeId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Payment type not found",
		})
		return
	}
	systemName := paymentType.SystemName
	if *systemName == "paynow" {

		var options map[string]string
		err := json.Unmarshal(*paymentType.Options, &options)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Error unmarshalling options",
			})
			return
		}
		rtgsIntegrationKey := options["rtgs_integration_key"]
		rtgsIntegrationIdValue := options["rtgs_integration_id"]
		rtgsIntegrationId, err := strconv.Atoi(rtgsIntegrationIdValue)
		usdIntegrationKey := options["usd_integration_key"]
		usdIntegrationIdValue := options["usd_integration_id"]
		usdIntegrationId, err := strconv.Atoi(usdIntegrationIdValue)
		var integrationId int
		var integrationKey string
		if invoice.Currency.Code == "USD" {
			integrationId = usdIntegrationId
			integrationKey = usdIntegrationKey
		} else {
			integrationId = rtgsIntegrationId
			integrationKey = rtgsIntegrationKey
		}
		resultUrl := os.Getenv("API_URL") + "/v1/backend/webhook/paynow"
		returnUrl := os.Getenv("APP_URL") + "/invoices/" + strconv.Itoa(int(invoice.Id)) + "?paid=true"
		paynow := payment_gateways.Paynow{
			IntegrationKey: integrationKey,
			IntegrationId:  integrationId,
			ReturnUrl:      returnUrl,
			ResultUrl:      resultUrl,
			Reference:      strconv.Itoa(int(invoice.Id)),
			Status:         "Message",
			AdditionalInfo: "Invoice",
		}
		for _, item := range invoice.InvoiceItems {
			paynow.AddItem(*item.Name, *item.Total)
		}
		response, err := paynow.SendPaynowPayment()
		if err != nil {
			fmt.Println(err)
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Error processing payment",
			})
			return
		}
		invoice.PaymentData = &response.PollUrl
		config.DB.Save(&invoice)
		paymentGatewayResponse := payment_gateways.PaymentGatewayResponse{
			Url:          response.BrowserUrl,
			ResponseType: "redirect",
		}
		c.JSON(http.StatusOK, gin.H{
			"message": "Payment initiated successfully",
			"data":    paymentGatewayResponse,
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Payment created successfully",
	})
}

func SearchPayments(c *gin.Context) {
	var payments []models.InvoicePayment
	clientId, _ := c.Get("client_id")

	query := config.DB.Joins("left join invoices on invoices.id = invoice_payments.invoice_id").Where("invoices.client_id = ?", clientId).Preload("PaymentType").Preload("Currency").Preload("Invoice")

	if invoiceId := c.Query("invoice_id"); invoiceId != "" {
		query = query.Where("invoice_payments.invoice_id = ?", invoiceId)
	}
	if paymentTypeId := c.Query("payment_type_id"); paymentTypeId != "" {
		query = query.Where("invoice_payments.payment_type_id = ?", paymentTypeId)
	}
	if currencyId := c.Query("currency_id"); currencyId != "" {
		query = query.Where("invoice_payments.currency_id = ?", currencyId)
	}
	if search := c.Query("s"); search != "" {
		query = query.Where("invoice_payments.trans_id LIKE ? or invoice_payments.id LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// Add date range filtering
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			query.Where("DATE(invoice_payments.date) >= ? AND DATE(invoice_payments.date) <= ?", startDate, endDate)
		} else {
			query.Where("DATE(invoice_payments.date) = ?", startDate)
		}
	}

	query.Order("id desc").Find(&payments)
	c.JSON(http.StatusOK, gin.H{
		"data": payments,
	})
}
