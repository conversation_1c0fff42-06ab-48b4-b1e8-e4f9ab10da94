package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"

	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupInvoicesTest(t *testing.T) (*gin.Engine, string, models.Client) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test client and user
	client := createTestClient(t)
	user := createInvoicesTestUser(t, client.Id)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())
	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/frontend")
	v1.Use(middleware.AuthMiddleware())
	v1.Use(middleware.CheckForClient())

	// Invoices routes
	invoices := v1.Group("/invoices")
	invoices.GET("", GetAllInvoices)
	invoices.GET("/search", SearchInvoices)
	invoices.GET("/:id", GetInvoiceById)

	return r, token, client
}

func setupInvoicesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createTestClient(t *testing.T) models.Client {
	// Clean up existing test client
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.Client{})

	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Test Client",
		Email:      "<EMAIL>",
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createInvoicesTestUser(t *testing.T, clientId uint) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Admin",
		UserType: "frontend",
		Status:   &status,
		ClientId: &clientId,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func createTestInvoice(t *testing.T, clientId uint, date time.Time) models.Invoice {
	amount := 100.0
	reference := "INV-001"
	status := "pending"

	invoice := models.Invoice{
		ClientId:  clientId,
		Date:      &date,
		Amount:    &amount,
		Status:    status,
		Reference: &reference,
	}

	result := config.DB.Create(&invoice)
	assert.NoError(t, result.Error)

	return invoice
}

// Test GetAllInvoices functionality
func TestGetAllInvoices(t *testing.T) {
	r, token, client := setupInvoicesTest(t)

	// Create test invoices
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)

	_ = createTestInvoice(t, client.Id, date1)
	_ = createTestInvoice(t, client.Id, date2)

	t.Run("Get all invoices successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2)
		assert.Equal(t, float64(2), response["total"])
	})

	t.Run("Get all invoices without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetAllInvoices with date filtering
func TestGetAllInvoicesWithDateFiltering(t *testing.T) {
	r, token, client := setupInvoicesTest(t)

	// Create invoices with different dates
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)
	date3 := time.Date(2024, 2, 1, 0, 0, 0, 0, time.UTC)

	_ = createTestInvoice(t, client.Id, date1)
	_ = createTestInvoice(t, client.Id, date2)
	_ = createTestInvoice(t, client.Id, date3)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedCount  int
	}{
		{
			name:           "Get invoices for specific date",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get invoices for date range",
			queryParams:    "?start_date=2024-01-15&end_date=2024-01-25",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:           "Get invoices with search and date filter",
			queryParams:    "?s=INV-001&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get invoices with status and date filter",
			queryParams:    "?status=pending&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.Contains(t, response, "data")
				assert.Contains(t, response, "total")

				// Verify data count
				data := response["data"].([]interface{})
				assert.Len(t, data, tt.expectedCount)
			}
		})
	}
}

// Test GetInvoiceById functionality
func TestGetInvoiceById(t *testing.T) {
	r, token, client := setupInvoicesTest(t)

	// Create test invoice
	date := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	invoice := createTestInvoice(t, client.Id, date)

	t.Run("Get invoice by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices/"+strconv.FormatUint(uint64(invoice.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify invoice data
		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(invoice.Id), data["id"])
		assert.Equal(t, float64(client.Id), data["client_id"])
	})

	t.Run("Get invoice by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Get invoice by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices/"+strconv.FormatUint(uint64(invoice.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test CreateInvoice functionality
func TestCreateInvoice(t *testing.T) {
	r, token, client := setupInvoicesTest(t)

	t.Run("Create invoice not allowed for frontend users", func(t *testing.T) {
		invoiceData := map[string]interface{}{
			"client_id": client.Id,
			"date":      "2024-01-15T00:00:00Z",
			"amount":    150.0,
			"reference": "INV-TEST-001",
			"status":    "draft",
		}

		jsonData, _ := json.Marshal(invoiceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/invoices", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Frontend users should not be allowed to create invoices
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Create invoice with missing required fields not allowed", func(t *testing.T) {
		invoiceData := map[string]interface{}{
			"client_id": client.Id,
			// Missing date and amount
		}

		jsonData, _ := json.Marshal(invoiceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/invoices", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Frontend users should not be allowed to create invoices
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Create invoice without authorization not allowed", func(t *testing.T) {
		invoiceData := map[string]interface{}{
			"client_id": client.Id,
			"date":      "2024-01-15T00:00:00Z",
			"amount":    150.0,
		}

		jsonData, _ := json.Marshal(invoiceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/invoices", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Route doesn't exist, so even without auth it returns 404
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

// Test UpdateInvoice functionality
func TestUpdateInvoice(t *testing.T) {
	r, token, client := setupInvoicesTest(t)

	// Create test invoice
	date := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	invoice := createTestInvoice(t, client.Id, date)

	t.Run("Update invoice not allowed for frontend users", func(t *testing.T) {
		updateData := map[string]interface{}{
			"amount":    200.0,
			"status":    "paid",
			"reference": "INV-UPDATED-001",
		}

		jsonData, _ := json.Marshal(updateData)
		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/invoices/"+strconv.FormatUint(uint64(invoice.Id), 10), bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Frontend users should not be allowed to update invoices
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Update invoice not found", func(t *testing.T) {
		updateData := map[string]interface{}{
			"amount": 200.0,
		}

		jsonData, _ := json.Marshal(updateData)
		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/invoices/99999", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Update invoice without authorization not allowed", func(t *testing.T) {
		updateData := map[string]interface{}{
			"amount": 200.0,
		}

		jsonData, _ := json.Marshal(updateData)
		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/invoices/"+strconv.FormatUint(uint64(invoice.Id), 10), bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Route doesn't exist, so even without auth it returns 404
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

// Test DeleteInvoice functionality
func TestDeleteInvoice(t *testing.T) {
	r, token, client := setupInvoicesTest(t)

	// Create test invoice
	date := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	invoice := createTestInvoice(t, client.Id, date)

	t.Run("Delete invoice not allowed for frontend users", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodDelete, "/api/v1/frontend/invoices/"+strconv.FormatUint(uint64(invoice.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Frontend users should not be allowed to delete invoices
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Delete invoice not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodDelete, "/api/v1/frontend/invoices/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Delete invoice without authorization not allowed", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodDelete, "/api/v1/frontend/invoices/"+strconv.FormatUint(uint64(invoice.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Route doesn't exist, so even without auth it returns 404
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

// Test SearchInvoices functionality
func TestSearchInvoices(t *testing.T) {
	r, token, client := setupInvoicesTest(t)

	// Create test invoices
	date1 := time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC)
	date2 := time.Date(2024, 1, 20, 0, 0, 0, 0, time.UTC)

	_ = createTestInvoice(t, client.Id, date1)
	_ = createTestInvoice(t, client.Id, date2)

	t.Run("Search invoices by reference", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices/search?s=INV-001", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2) // Both invoices have INV-001 in reference
	})

	t.Run("Search invoices with date filter", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices/search?start_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Only one invoice on 2024-01-15
	})

	t.Run("Search invoices with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices/search?s=NONEXISTENT", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // No results
	})
}

// Test pagination functionality
func TestInvoicesPagination(t *testing.T) {
	r, token, client := setupInvoicesTest(t)

	// Create multiple invoices for pagination testing
	for i := 1; i <= 15; i++ {
		date := time.Date(2024, 1, i, 0, 0, 0, 0, time.UTC)
		createTestInvoice(t, client.Id, date)
	}

	t.Run("First page with default pagination", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")
		assert.Contains(t, response, "total_pages")

		// Verify pagination values
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.Equal(t, float64(15), response["total"])
		assert.Equal(t, float64(2), response["total_pages"]) // 15 items, 10 per page = 2 pages

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 10)
	})

	t.Run("Second page", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 5) // Remaining 5 items on second page
	})
}

// Test edge cases and error handling
func TestInvoicesEdgeCases(t *testing.T) {
	r, token, _ := setupInvoicesTest(t)

	t.Run("Invalid date format in query", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices?start_date=invalid-date", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("End date before start date", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices?start_date=2024-01-20&end_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid invoice ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/invoices/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
