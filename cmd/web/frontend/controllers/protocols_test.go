package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupProtocolsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func TestGetAllProtocols(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupProtocolsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test protocols
	protocol1 := models.Protocol{
		Name: "Test Protocol 1",
	}
	protocol2 := models.Protocol{
		Name: "Test Protocol 2",
	}
	config.DB.Create(&protocol1)
	config.DB.Create(&protocol2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	protocols := v1.Group("/protocols")
	protocols.GET("", GetAllProtocols)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/frontend/protocols", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})

	// Get the actual count of protocols in the database
	var countBefore int64
	config.DB.Model(&models.Protocol{}).Count(&countBefore)

	// Assert that the response data length matches the database count
	assert.Len(t, data, int(countBefore)) // Dynamic count based on actual database state
}

func TestGetProtocolById(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupProtocolsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test protocol
	protocol := models.Protocol{
		Name: "Test Protocol",
	}
	config.DB.Create(&protocol)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	protocols := v1.Group("/protocols")
	protocols.GET("/:id", GetProtocolById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/frontend/protocols/%d", protocol.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Protocol", data["name"])
}

func TestGetProtocolByIdNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupProtocolsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	protocols := v1.Group("/protocols")
	protocols.GET("/:id", GetProtocolById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/frontend/protocols/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Protocol not found", response["message"])
}

func TestCreateProtocol(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupProtocolsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	protocols := v1.Group("/protocols")
	protocols.POST("", CreateProtocol)

	// Test data
	protocolData := models.ProtocolRequest{
		Name: "New Test Protocol",
	}

	jsonData, _ := json.Marshal(protocolData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/frontend/protocols", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Protocol created successfully", response["message"])

	// Verify protocol was created in database
	var protocol models.Protocol
	err = config.DB.Where("name = ?", "New Test Protocol").First(&protocol).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Protocol", protocol.Name)
}

func TestCreateProtocolInvalidRequest(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupProtocolsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	protocols := v1.Group("/protocols")
	protocols.POST("", CreateProtocol)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Protocol", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/frontend/protocols", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateProtocol(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupProtocolsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test protocol
	protocol := models.Protocol{
		Name: "Original Protocol",
	}
	config.DB.Create(&protocol)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	protocols := v1.Group("/protocols")
	protocols.PUT("/:id", UpdateProtocol)

	// Test data
	updateData := models.ProtocolRequest{
		Name: "Updated Protocol",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/frontend/protocols/%d", protocol.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Protocol updated successfully", response["message"])

	// Verify protocol was updated in database
	var updatedProtocol models.Protocol
	err = config.DB.First(&updatedProtocol, protocol.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "Updated Protocol", updatedProtocol.Name)
}

func TestUpdateProtocolNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupProtocolsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	protocols := v1.Group("/protocols")
	protocols.PUT("/:id", UpdateProtocol)

	// Test data
	updateData := models.ProtocolRequest{
		Name: "Updated Protocol",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/frontend/protocols/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Protocol not found", response["message"])
}

func TestDeleteProtocol(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupProtocolsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test protocol
	protocol := models.Protocol{
		Name: "Test Protocol to Delete",
	}
	config.DB.Create(&protocol)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	protocols := v1.Group("/protocols")
	protocols.DELETE("/:id", DeleteProtocol)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/frontend/protocols/%d", protocol.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)
	assert.Empty(t, w.Body.String()) // 204 No Content should not have a response body

	// Verify protocol was deleted from database
	var deletedProtocol models.Protocol
	err := config.DB.First(&deletedProtocol, protocol.Id).Error
	assert.Error(t, err) // Should not find the protocol
}

func TestDeleteProtocolNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupProtocolsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	protocols := v1.Group("/protocols")
	protocols.DELETE("/:id", DeleteProtocol)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/frontend/protocols/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Protocol not found", response["message"])
}

func TestSearchProtocols(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupProtocolsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test protocols
	protocol1 := models.Protocol{
		Name: "Searchable Protocol One",
	}
	protocol2 := models.Protocol{
		Name: "Another Protocol",
	}
	config.DB.Create(&protocol1)
	config.DB.Create(&protocol2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	protocols := v1.Group("/protocols")
	protocols.GET("/search", SearchProtocols)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/frontend/protocols/search?s=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 1) // Should only find "Searchable Protocol One"
}

func TestSearchProtocolsNoResults(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupProtocolsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test protocol
	protocol := models.Protocol{
		Name: "Test Protocol",
	}
	config.DB.Create(&protocol)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	protocols := v1.Group("/protocols")
	protocols.GET("/search", SearchProtocols)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/frontend/protocols/search?s=Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 0) // Should find no results
}
