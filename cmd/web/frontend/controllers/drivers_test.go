package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupDriversTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         getEnvOrDefault("DB_HOST", "localhost"),
		"DB_PORT":         getEnvOrDefault("DB_PORT", "3306"),
		"DB_USERNAME":     getEnvOrDefault("DB_USERNAME", "admin"),
		"DB_PASSWORD":     getEnvOrDefault("DB_PASSWORD", "password"),
		"TESTING_DB_NAME": getEnvOrDefault("TESTING_DB_NAME", "testing"),
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func TestCreateDriver(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupDriversTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test client and user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.Client{})
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "**********",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Setup router
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("userID", uint(1))
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	drivers := v1.Group("/drivers")
	drivers.POST("", CreateDriver)

	// Test data
	dob := time.Date(1990, 1, 1, 0, 0, 0, 0, time.UTC)
	licenseExpiry := time.Date(2025, 12, 31, 0, 0, 0, 0, time.UTC)

	driverData := models.CreateDriverRequest{
		ClientId:          client.Id,
		Name:              "John Doe",
		Dob:               &dob,
		Gender:            stringPtr("male"),
		BloodType:         stringPtr("O+"),
		DriverLicenseNo:   stringPtr("DL123456"),
		LicenseExpiryDate: &licenseExpiry,
		PhoneNumber:       stringPtr("**********"),
		Email:             stringPtr("<EMAIL>"),
		Address:           stringPtr("123 Main St, City"),
		Rfid:              stringPtr("RFID123"),
		Description:       stringPtr("Test driver"),
		Status:            "active",
	}

	jsonData, _ := json.Marshal(driverData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/frontend/drivers", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Driver created successfully", response["message"])

	// Verify driver was created in database
	var driver models.Driver
	err = config.DB.Where("name = ?", "John Doe").First(&driver).Error
	assert.NoError(t, err)
	assert.Equal(t, "John Doe", driver.Name)
	assert.NotZero(t, driver.ClientId) // Just verify that ClientId is set

	// Verify user was created if email was provided
	if driverData.Email != nil {
		var user models.User
		err = config.DB.Where("email = ?", *driverData.Email).First(&user).Error
		assert.NoError(t, err)
		assert.Equal(t, "driver", user.UserType)
		assert.NotNil(t, user.DriverId) // Just verify that DriverId is set
	}
}

func TestGetAllDrivers(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupDriversTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create a test client with unique email
	timestamp := time.Now().UnixNano()
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber: "**********",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test drivers
	driver1 := models.Driver{
		CreatedById: 1,
		ClientId:    client.Id,
		Name:        "Driver 1",
		Status:      "active",
	}
	driver2 := models.Driver{
		CreatedById: 1,
		ClientId:    client.Id,
		Name:        "Driver 2",
		Status:      "active",
	}
	config.DB.Create(&driver1)
	config.DB.Create(&driver2)

	// Setup router
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	drivers := v1.Group("/drivers")
	drivers.GET("", GetAllDrivers)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/frontend/drivers", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	driversData := response["data"].([]interface{})
	assert.Len(t, driversData, 2)
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}
