package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupAlertsTest(t *testing.T) (*gin.Engine, string, models.Client) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupAlertsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test client and user
	client := createAlertsTestClient(t)
	user := createAlertsTestUser(t, client.Id)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())

	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/frontend")
	v1.Use(middleware.AuthMiddleware())
	v1.Use(middleware.CheckForClient())

	// Alerts routes
	alerts := v1.Group("/alerts")
	alerts.GET("", GetAllAlerts)
	alerts.GET("/search", SearchAlerts)
	alerts.GET("/:id", GetAlertById)
	alerts.POST("", CreateAlert)
	alerts.PUT("/:id/mark_read", MarkAlertAsRead)
	alerts.PUT("/:id/mark_unread", MarkAlertAsUnread)
	alerts.PUT("/bulk/mark_read", BulkMarkAlertsAsRead)
	alerts.PUT("/mark_all_read", MarkAllAlertsAsRead)

	return r, token, client
}

func setupAlertsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         getEnvOrDefault("DB_HOST", "localhost"),
		"DB_PORT":         getEnvOrDefault("DB_PORT", "3306"),
		"DB_USERNAME":     getEnvOrDefault("DB_USERNAME", "admin"),
		"DB_PASSWORD":     getEnvOrDefault("DB_PASSWORD", "password"),
		"TESTING_DB_NAME": getEnvOrDefault("TESTING_DB_NAME", "testing"),
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createAlertsTestClient(t *testing.T) models.Client {
	// Use unique email to avoid conflicts
	timestamp := time.Now().UnixNano()
	email := fmt.Sprintf("<EMAIL>", timestamp)

	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Alerts Test Client",
		Email:      email,
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createAlertsTestUser(t *testing.T, clientId uint) models.User {
	// Use unique email to avoid conflicts
	timestamp := time.Now().UnixNano()
	email := fmt.Sprintf("<EMAIL>", timestamp)

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    email,
		Password: password,
		Name:     "Alerts Admin",
		UserType: "frontend",
		Status:   &status,
		ClientId: &clientId,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func createAlertsTestDevice(t *testing.T, clientId uint) models.ClientDevice {
	// Get the first available device type
	var deviceType models.DeviceType
	err := config.DB.First(&deviceType).Error
	if err != nil {
		// Create a fallback device type if none exists
		var protocol models.Protocol
		err = config.DB.First(&protocol).Error
		if err != nil {
			// Create a fallback protocol if none exists
			protocol = models.Protocol{
				Name:   "Test Protocol",
				Active: true,
			}
			config.DB.Create(&protocol)
		}

		deviceType = models.DeviceType{
			ProtocolId: protocol.Id,
			Name:       "Test Device Type",
			Active:     true,
		}
		config.DB.Create(&deviceType)
	}

	status := "active"
	device := models.ClientDevice{
		ClientId:     clientId,
		DeviceTypeId: deviceType.Id,
		Status:       status,
	}

	result := config.DB.Create(&device)
	assert.NoError(t, result.Error)

	return device
}

func createAlertsTestAlert(t *testing.T, deviceId uint, timestamp time.Time) models.Alert {
	message := "Test alert message"
	alertType := "speed"

	alert := models.Alert{
		ClientDeviceId: deviceId,
		AlertTimestamp: timestamp,
		Message:        &message,
		AlertType:      alertType,
	}

	result := config.DB.Create(&alert)
	assert.NoError(t, result.Error)

	return alert
}

// Test GetAllAlerts functionality
func TestGetAllAlerts(t *testing.T) {
	r, token, client := setupAlertsTest(t)

	// Create test device and alerts
	device := createAlertsTestDevice(t, client.Id)
	timestamp1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	timestamp2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)

	_ = createAlertsTestAlert(t, device.Id, timestamp1)
	_ = createAlertsTestAlert(t, device.Id, timestamp2)

	t.Run("Get all alerts successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2)
		assert.Equal(t, float64(2), response["total"])
	})

	t.Run("Get all alerts without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetAllAlerts with date filtering
func TestGetAllAlertsWithDateFiltering(t *testing.T) {
	r, token, client := setupAlertsTest(t)

	// Create test device and alerts with different dates
	device := createAlertsTestDevice(t, client.Id)
	timestamp1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	timestamp2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)
	timestamp3 := time.Date(2024, 2, 1, 9, 0, 0, 0, time.UTC)

	_ = createAlertsTestAlert(t, device.Id, timestamp1)
	_ = createAlertsTestAlert(t, device.Id, timestamp2)
	_ = createAlertsTestAlert(t, device.Id, timestamp3)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedCount  int
	}{
		{
			name:           "Get alerts for specific date",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get alerts for date range",
			queryParams:    "?start_date=2024-01-15&end_date=2024-01-25",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:           "Get alerts with device filter and date",
			queryParams:    "?client_device_id=" + strconv.FormatUint(uint64(device.Id), 10) + "&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get alerts with alert type and date filter",
			queryParams:    "?alert_type=speed&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.Contains(t, response, "data")
				assert.Contains(t, response, "total")

				// Verify data count
				data := response["data"].([]interface{})
				assert.Len(t, data, tt.expectedCount)
			}
		})
	}
}

// Test GetAlertById functionality
func TestGetAlertById(t *testing.T) {
	r, token, client := setupAlertsTest(t)

	// Create test device and alert
	device := createAlertsTestDevice(t, client.Id)
	timestamp := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	alert := createAlertsTestAlert(t, device.Id, timestamp)

	t.Run("Get alert by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts/"+strconv.FormatUint(uint64(alert.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify alert data
		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(alert.Id), data["id"])
		assert.Equal(t, float64(device.Id), data["client_device_id"])

		// Verify that the alert is now marked as read
		assert.NotNil(t, data["read"])
		assert.True(t, data["read"].(bool))
		assert.NotNil(t, data["read_at"])
	})

	t.Run("Get alert by ID and verify auto-mark as read", func(t *testing.T) {
		// Create a new unread alert
		unreadAlert := createAlertsTestAlert(t, device.Id, timestamp)

		// Explicitly set the alert as unread
		read := false
		config.DB.Model(&unreadAlert).Updates(map[string]interface{}{
			"read":    &read,
			"read_at": nil,
		})

		// Verify it's initially unread
		var initialAlert models.Alert
		err := config.DB.First(&initialAlert, unreadAlert.Id).Error
		assert.NoError(t, err)
		assert.NotNil(t, initialAlert.Read)
		assert.False(t, *initialAlert.Read)
		assert.Nil(t, initialAlert.ReadAt)

		// Retrieve the alert
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts/"+strconv.FormatUint(uint64(unreadAlert.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		// Verify the alert is now marked as read in the database
		var updatedAlert models.Alert
		err = config.DB.First(&updatedAlert, unreadAlert.Id).Error
		assert.NoError(t, err)
		assert.NotNil(t, updatedAlert.Read)
		assert.True(t, *updatedAlert.Read)
		assert.NotNil(t, updatedAlert.ReadAt)
	})

	t.Run("Get alert by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Get alert by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts/"+strconv.FormatUint(uint64(alert.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test CreateAlert functionality
func TestCreateAlert(t *testing.T) {
	r, token, client := setupAlertsTest(t)

	// Create test device
	device := createAlertsTestDevice(t, client.Id)

	t.Run("Create alert successfully", func(t *testing.T) {
		timestamp := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

		alertData := map[string]interface{}{
			"client_device_id": device.Id,
			"alert_timestamp":  timestamp.Format(time.RFC3339),
			"message":          "Test alert message",
			"alert_type":       "speed",
		}

		jsonData, _ := json.Marshal(alertData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/alerts", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "Alert created successfully", response["message"])
	})

	t.Run("Create alert with missing required fields", func(t *testing.T) {
		alertData := map[string]interface{}{
			"client_device_id": device.Id,
			// Missing alert_timestamp and message
		}

		jsonData, _ := json.Marshal(alertData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/alerts", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create alert with non-existent device", func(t *testing.T) {
		timestamp := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

		alertData := map[string]interface{}{
			"client_device_id": 99999,
			"alert_type":       "speed",
			"alert_timestamp":  timestamp.Format(time.RFC3339),
			"message":          "Test alert message",
		}

		jsonData, _ := json.Marshal(alertData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/alerts", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Create alert without authorization", func(t *testing.T) {
		timestamp := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)

		alertData := map[string]interface{}{
			"client_device_id": device.Id,
			"alert_timestamp":  timestamp.Format(time.RFC3339),
			"message":          "Test alert message",
		}

		jsonData, _ := json.Marshal(alertData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/alerts", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test SearchAlerts functionality
func TestSearchAlerts(t *testing.T) {
	r, token, client := setupAlertsTest(t)

	// Create test device and alerts
	device := createAlertsTestDevice(t, client.Id)
	timestamp1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	timestamp2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)

	_ = createAlertsTestAlert(t, device.Id, timestamp1)
	_ = createAlertsTestAlert(t, device.Id, timestamp2)

	t.Run("Search alerts by alert type", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts/search?alert_type=speed", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2) // Both alerts have alert_type=speed
	})

	t.Run("Search alerts with date filter", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts/search?start_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Only one alert on 2024-01-15
	})

	t.Run("Search alerts with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts/search?alert_type=invalid", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // No results
	})
}

// Test pagination functionality
func TestAlertsPagination(t *testing.T) {
	r, token, client := setupAlertsTest(t)

	// Create test device and multiple alerts for pagination testing
	device := createAlertsTestDevice(t, client.Id)
	for i := 1; i <= 15; i++ {
		timestamp := time.Date(2024, 1, i, 10, 0, 0, 0, time.UTC)
		createAlertsTestAlert(t, device.Id, timestamp)
	}

	t.Run("First page with default pagination", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify pagination values
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.Equal(t, float64(15), response["total"])

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 10)
	})

	t.Run("Second page", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 5) // Remaining 5 items on second page
	})
}

// Test edge cases and error handling
func TestAlertsEdgeCases(t *testing.T) {
	r, token, _ := setupAlertsTest(t)

	t.Run("Invalid date format in query", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts?start_date=invalid-date", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("End date before start date", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts?start_date=2024-01-20&end_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid alert ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/alerts/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}
