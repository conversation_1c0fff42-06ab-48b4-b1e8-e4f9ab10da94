package controllers

import (
	"bytes"
	"encoding/json"
	"math"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"

	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupGeofencesTest(t *testing.T) (*gin.Engine, string, models.Client) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupGeofencesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test client and user
	client := createGeofencesTestClient(t)
	user := createGeofencesTestUser(t, client.Id)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())
	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/frontend")
	v1.Use(middleware.AuthMiddleware())
	v1.Use(middleware.CheckForClient())

	// Geofences routes
	geofences := v1.Group("/geofences")
	geofences.GET("", GetAllGeofences)
	geofences.GET("/search", SearchGeofences)
	geofences.GET("/:id", GetGeofenceById)
	geofences.POST("", CreateGeofence)
	geofences.PUT("/:id", UpdateGeofence)
	geofences.DELETE("/:id", DeleteGeofence)
	geofences.GET("/:id/events", GetGeofenceEvents)

	return r, token, client
}

func setupGeofencesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createGeofencesTestClient(t *testing.T) models.Client {
	// Find existing client first to clean up related data
	var existingClient models.Client
	if config.DB.Where("email = ?", "<EMAIL>").First(&existingClient).Error == nil {
		// Clean up any existing geofences for this client
		// First delete geofence events via geofences
		config.DB.Where("geofence_id IN (SELECT id FROM geofences WHERE client_id = ?)", existingClient.Id).Delete(&models.GeofenceEvent{})
		config.DB.Where("client_id = ?", existingClient.Id).Delete(&models.Geofence{})
	}

	// Clean up existing test client
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.Client{})

	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Geofences Test Client",
		Email:      "<EMAIL>",
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createGeofencesTestUser(t *testing.T, clientId uint) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Geofences Admin",
		UserType: "frontend",
		Status:   &status,
		ClientId: &clientId,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func createGeofencesTestDevice(t *testing.T, clientId uint) models.ClientDevice {
	status := "active"

	device := models.ClientDevice{
		ClientId:     clientId,
		Status:       status,
		DeviceTypeId: 1, // Use a valid device type ID
	}

	result := config.DB.Create(&device)
	assert.NoError(t, result.Error)

	return device
}

func createGeofencesTestGeofence(t *testing.T, clientId uint) models.Geofence {
	name := "Test Geofence"
	geofenceType := "circle"
	latitude := 40.7128
	longitude := -74.0060
	radius := 100.0

	geofence := models.Geofence{
		ClientId:     clientId,
		Name:         name,
		GeofenceType: geofenceType,
		Latitude:     &latitude,
		Longitude:    &longitude,
		Radius:       &radius,
	}

	result := config.DB.Create(&geofence)
	assert.NoError(t, result.Error)

	return geofence
}

func createGeofencesTestEvent(t *testing.T, deviceId uint, geofenceId uint, timestamp time.Time) models.GeofenceEvent {
	eventType := "enter"

	event := models.GeofenceEvent{
		ClientDeviceId: deviceId,
		GeofenceId:     geofenceId,
		EventTimestamp: timestamp,
		EventType:      eventType,
	}

	result := config.DB.Create(&event)
	assert.NoError(t, result.Error)

	return event
}

// Test GetAllGeofences functionality
func TestGetAllGeofences(t *testing.T) {
	r, token, client := setupGeofencesTest(t)

	// Create test geofences
	_ = createGeofencesTestGeofence(t, client.Id)
	_ = createGeofencesTestGeofence(t, client.Id)

	t.Run("Get all geofences successfully", func(t *testing.T) {
		// Get count before the test
		var countBefore int64
		config.DB.Model(&models.Geofence{}).Where("client_id = ?", client.Id).Count(&countBefore)

		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data - use the actual count from database
		data := response["data"].([]interface{})
		assert.Len(t, data, int(countBefore))
		assert.Equal(t, float64(countBefore), response["total"])
	})

	t.Run("Get all geofences without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetGeofenceById functionality
func TestGetGeofenceById(t *testing.T) {
	r, token, client := setupGeofencesTest(t)

	// Create test geofence
	geofence := createGeofencesTestGeofence(t, client.Id)

	t.Run("Get geofence by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/"+strconv.FormatUint(uint64(geofence.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify geofence data
		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(geofence.Id), data["id"])
		assert.Equal(t, float64(client.Id), data["client_id"])
	})

	t.Run("Get geofence by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Get geofence by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/"+strconv.FormatUint(uint64(geofence.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test CreateGeofence functionality
func TestCreateGeofence(t *testing.T) {
	r, token, client := setupGeofencesTest(t)

	t.Run("Create geofence successfully", func(t *testing.T) {
		geofenceData := map[string]interface{}{
			"client_id":     client.Id,
			"name":          "New Test Geofence",
			"geofence_type": "circle",
			"applies_to":    "client",
			"latitude":      40.7128,
			"longitude":     -74.0060,
			"radius":        100.0,
		}

		jsonData, _ := json.Marshal(geofenceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/geofences", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "Geofence created successfully", response["message"])
	})

	t.Run("Create geofence with missing required fields", func(t *testing.T) {
		geofenceData := map[string]interface{}{
			"client_id": client.Id,
			"name":      "New Test Geofence",
			// Missing latitude, longitude, radius
		}

		jsonData, _ := json.Marshal(geofenceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/geofences", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create geofence without authorization", func(t *testing.T) {
		geofenceData := map[string]interface{}{
			"client_id":     client.Id,
			"name":          "New Test Geofence",
			"geofence_type": "circle",
			"applies_to":    "client",
			"latitude":      40.7128,
			"longitude":     -74.0060,
			"radius":        100.0,
		}

		jsonData, _ := json.Marshal(geofenceData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/geofences", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetGeofenceEvents functionality
func TestGetGeofenceEvents(t *testing.T) {
	r, token, client := setupGeofencesTest(t)

	// Create test device, geofence, and events
	device := createGeofencesTestDevice(t, client.Id)
	geofence := createGeofencesTestGeofence(t, client.Id)
	timestamp1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	timestamp2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)

	_ = createGeofencesTestEvent(t, device.Id, geofence.Id, timestamp1)
	_ = createGeofencesTestEvent(t, device.Id, geofence.Id, timestamp2)

	t.Run("Get geofence events successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/"+strconv.FormatUint(uint64(geofence.Id), 10)+"/events", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2)
		assert.Equal(t, float64(2), response["total"])
	})

	t.Run("Get geofence events with date filtering", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/"+strconv.FormatUint(uint64(geofence.Id), 10)+"/events?start_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Only one event on 2024-01-15
	})

	t.Run("Get geofence events with date range", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/"+strconv.FormatUint(uint64(geofence.Id), 10)+"/events?start_date=2024-01-15&end_date=2024-01-25", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2) // Both events in date range
	})

	t.Run("Get geofence events without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/"+strconv.FormatUint(uint64(geofence.Id), 10)+"/events", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test SearchGeofences functionality
func TestSearchGeofences(t *testing.T) {
	r, token, client := setupGeofencesTest(t)

	// Create test geofences
	_ = createGeofencesTestGeofence(t, client.Id)
	_ = createGeofencesTestGeofence(t, client.Id)

	t.Run("Search geofences by name", func(t *testing.T) {
		// Get count before the test
		var countBefore int64
		config.DB.Model(&models.Geofence{}).Where("client_id = ? AND name LIKE ?", client.Id, "%Test Geofence%").Count(&countBefore)

		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/search?s=Test Geofence", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data - use the actual count from database
		data := response["data"].([]interface{})
		assert.Len(t, data, int(countBefore))
	})

	t.Run("Search geofences with geofence type filter", func(t *testing.T) {
		// Get count before the test
		var countBefore int64
		config.DB.Model(&models.Geofence{}).Where("client_id = ? AND geofence_type = ?", client.Id, "circle").Count(&countBefore)

		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/search?geofence_type=circle", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data - use the actual count from database
		data := response["data"].([]interface{})
		assert.Len(t, data, int(countBefore))
	})

	t.Run("Search geofences with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/search?s=NONEXISTENT", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // No results
	})
}

// Test pagination functionality
func TestGeofencesPagination(t *testing.T) {
	r, token, client := setupGeofencesTest(t)

	// Create multiple geofences for pagination testing
	for i := 1; i <= 15; i++ {
		createGeofencesTestGeofence(t, client.Id)
	}

	t.Run("First page with default pagination", func(t *testing.T) {
		// Get total count before the test
		var totalCount int64
		config.DB.Model(&models.Geofence{}).Where("client_id = ?", client.Id).Count(&totalCount)

		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")
		assert.Contains(t, response, "total_pages")

		// Verify pagination values
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.Equal(t, float64(totalCount), response["total"])

		// Calculate expected total pages
		expectedTotalPages := int(math.Ceil(float64(totalCount) / 10.0))
		assert.Equal(t, float64(expectedTotalPages), response["total_pages"])

		// Verify data count
		data := response["data"].([]interface{})
		expectedDataCount := int(math.Min(10.0, float64(totalCount)))
		assert.Len(t, data, expectedDataCount)
	})

	t.Run("Second page", func(t *testing.T) {
		// Get total count before the test
		var totalCount int64
		config.DB.Model(&models.Geofence{}).Where("client_id = ?", client.Id).Count(&totalCount)

		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count - calculate expected items on second page
		data := response["data"].([]interface{})
		expectedSecondPageItems := int(math.Max(0, float64(totalCount-10)))
		assert.Len(t, data, expectedSecondPageItems)
	})
}

// Test edge cases and error handling
func TestGeofencesEdgeCases(t *testing.T) {
	r, token, _ := setupGeofencesTest(t)

	t.Run("Invalid date format in query", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/1/events?start_date=invalid-date", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("End date before start date", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/1/events?start_date=2024-01-20&end_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid geofence ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/geofences/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
