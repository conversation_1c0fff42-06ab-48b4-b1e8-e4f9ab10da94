package controllers

import (
	"fmt"
	"net/http"
	"strconv"
	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
)

// TripReplayResponse represents the response for trip replay
type TripReplayResponse struct {
	Trip    models.Trip      `json:"trip"`
	GPSData []models.GPSData `json:"gps_data"`
	Stats   TripReplayStats  `json:"stats"`
}

// TripReplayStats provides statistics about the trip replay data
type TripReplayStats struct {
	TotalPoints      int     `json:"total_points"`
	MovingPoints     int     `json:"moving_points"`
	StationaryPoints int     `json:"stationary_points"`
	MaxSpeed         float64 `json:"max_speed"`
	AvgSpeed         float64 `json:"avg_speed"`
	Duration         string  `json:"duration"`
}

func GetAllTrips(c *gin.Context) {
	var total int64
	var trips []models.Trip

	// Get client ID from context
	clientId, _ := c.Get("client_id")

	// Build query with preloads
	query := config.DB.Preload("ClientDevice").Preload("Driver")

	// Filter by client devices that belong to this client
	query = query.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ?", clientId)

	// Apply filters
	if deviceId := c.Query("device_id"); deviceId != "" {
		query = query.Where("trips.client_device_id = ?", deviceId)
	}
	if driverId := c.Query("driver_id"); driverId != "" {
		query = query.Where("trips.driver_id = ?", driverId)
	}
	if status := c.Query("status"); status != "" {
		query = query.Where("trips.status = ?", status)
	}
	if tripType := c.Query("trip_type"); tripType != "" {
		query = query.Where("trips.trip_type = ?", tripType)
	}
	// Add date range filtering
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			query.Where("DATE(trips.start_time) >= ? AND DATE(trips.start_time) <= ?", startDate, endDate)
		} else {
			query.Where("DATE(trips.start_time) = ?", startDate)
		}
	}
	if minDistance := c.Query("min_distance"); minDistance != "" {
		if parsed, err := strconv.ParseFloat(minDistance, 64); err == nil {
			query = query.Where("trips.distance >= ?", parsed)
		}
	}
	if maxDistance := c.Query("max_distance"); maxDistance != "" {
		if parsed, err := strconv.ParseFloat(maxDistance, 64); err == nil {
			query = query.Where("trips.distance <= ?", parsed)
		}
	}
	if minDuration := c.Query("min_duration"); minDuration != "" {
		if parsed, err := strconv.Atoi(minDuration); err == nil {
			query = query.Where("trips.duration >= ?", parsed)
		}
	}
	if maxDuration := c.Query("max_duration"); maxDuration != "" {
		if parsed, err := strconv.Atoi(maxDuration); err == nil {
			query = query.Where("trips.duration <= ?", parsed)
		}
	}

	// Get total count
	query.Model(&models.Trip{}).Count(&total)

	// Apply pagination with validation
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	if page <= 0 {
		page = 1
	}
	perPage, _ := strconv.Atoi(c.DefaultQuery("per_page", "20"))
	switch {
	case perPage > 100:
		perPage = 100
	case perPage <= 0:
		perPage = 20
	}
	offset := (page - 1) * perPage

	// Apply sorting (default by start_time desc)
	sortBy := c.DefaultQuery("sort_by", "start_time")
	sortOrder := c.DefaultQuery("sort_order", "desc")
	query = query.Order("trips." + sortBy + " " + sortOrder)

	// Execute query with pagination
	if err := query.Offset(offset).Limit(perPage).Find(&trips).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to retrieve trips",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":         trips,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
		"total_pages":  (int(total) + perPage - 1) / perPage,
	})
}

func GetTripById(c *gin.Context) {
	var trip models.Trip
	clientId, _ := c.Get("client_id")

	// Validate ID format
	tripId := c.Param("id")
	if _, err := strconv.ParseUint(tripId, 10, 32); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid trip ID format",
		})
		return
	}

	// Build query with preloads and client filter
	query := config.DB.Preload("ClientDevice").Preload("Driver").
		Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ? AND trips.id = ?", clientId, tripId)

	if err := query.First(&trip).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Trip not found",
		})
		return
	}

	// Get GPS data for this trip, ordered by timestamp for smooth replay
	var gpsData []models.GPSData
	gpsQuery := config.DB.Where("trip_id = ?", tripId).Order("gps_timestamp ASC")

	// Optional: Add pagination for very large trips
	if limit := c.Query("limit"); limit != "" {
		if limitInt, err := strconv.Atoi(limit); err == nil && limitInt > 0 {
			gpsQuery = gpsQuery.Limit(limitInt)
		}
	}

	if offset := c.Query("offset"); offset != "" {
		if offsetInt, err := strconv.Atoi(offset); err == nil && offsetInt >= 0 {
			gpsQuery = gpsQuery.Offset(offsetInt)
		}
	}

	if err := gpsQuery.Find(&gpsData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to fetch GPS data",
			"error":   err.Error(),
		})
		return
	}

	// Calculate statistics
	stats := calculateTripReplayStats(gpsData)

	// Return comprehensive trip data including GPS data and statistics
	c.JSON(http.StatusOK, gin.H{
		"data": TripReplayResponse{
			Trip:    trip,
			GPSData: gpsData,
			Stats:   stats,
		},
	})
}

func SearchTrips(c *gin.Context) {
	var trips []models.Trip
	clientId, _ := c.Get("client_id")

	// Build query with preloads
	query := config.DB.Preload("ClientDevice").Preload("Driver").
		Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ?", clientId)

	// Apply search filters
	if search := c.Query("s"); search != "" {
		query = query.Where(`
			trips.id LIKE ? OR 
			trips.status LIKE ? OR 
			trips.trip_type LIKE ? OR
			client_devices.name LIKE ? OR
			client_devices.plate_number LIKE ?
		`, "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	// Apply additional filters
	if deviceId := c.Query("device_id"); deviceId != "" {
		query = query.Where("trips.client_device_id = ?", deviceId)
	}
	if driverId := c.Query("driver_id"); driverId != "" {
		query = query.Where("trips.driver_id = ?", driverId)
	}
	if status := c.Query("status"); status != "" {
		query = query.Where("trips.status = ?", status)
	}
	if tripType := c.Query("trip_type"); tripType != "" {
		query = query.Where("trips.trip_type = ?", tripType)
	}

	// Apply date range filters
	if startDate := c.Query("start_date"); startDate != "" {
		if endDate := c.Query("end_date"); endDate != "" {
			query.Where("DATE(trips.start_time) >= ? AND DATE(trips.start_time) <= ?", startDate, endDate)
		} else {
			query.Where("DATE(trips.start_time) = ?", startDate)
		}
	}

	// Apply sorting
	sortBy := c.DefaultQuery("sort_by", "start_time")
	sortOrder := c.DefaultQuery("sort_order", "desc")
	query = query.Order("trips." + sortBy + " " + sortOrder)

	// Limit results for search
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "50"))
	query = query.Limit(limit)

	if err := query.Find(&trips).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to search trips",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": trips,
	})
}

// GetTripReplaySimplified returns simplified GPS data for trip replay (reduced data for performance) - frontend version
func GetTripReplaySimplified(c *gin.Context) {
	tripIDStr := c.Param("id")
	tripID, err := strconv.ParseUint(tripIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid trip ID"})
		return
	}

	// Get client_id from context (frontend pattern)
	clientId, _ := c.Get("client_id")

	// Verify the trip belongs to the client
	var trip models.Trip
	if err := config.DB.Where("id = ? AND client_device_id IN (SELECT id FROM client_devices WHERE client_id = ?)", uint(tripID), clientId).First(&trip).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Trip not found"})
		return
	}

	// Get simplified GPS data (every Nth point for performance)
	interval := 5 // Show every 5th point by default
	if intervalStr := c.Query("interval"); intervalStr != "" {
		if intervalInt, err := strconv.Atoi(intervalStr); err == nil && intervalInt > 0 {
			interval = intervalInt
		}
	}

	var gpsData []models.GPSData
	// Use ROW_NUMBER() to get every Nth record
	err = config.DB.Raw(`
		SELECT * FROM (
			SELECT *, ROW_NUMBER() OVER (ORDER BY gps_timestamp) as row_num
			FROM gps_data 
			WHERE trip_id = ?
		) as numbered_data
		WHERE row_num % ? = 1
		ORDER BY gps_timestamp ASC
	`, tripID, interval).Scan(&gpsData).Error

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch GPS data"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"trip_id":      tripID,
		"gps_data":     gpsData,
		"interval":     interval,
		"total_points": len(gpsData),
	})
}

// GetTripReplayByDevice returns trip replay data for a specific device (frontend version)
func GetTripReplayByDevice(c *gin.Context) {
	deviceIDStr := c.Param("device_id")
	deviceID, err := strconv.ParseUint(deviceIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid device ID"})
		return
	}

	// Get client_id from context (frontend pattern)
	clientId, _ := c.Get("client_id")

	// Verify the device belongs to the client
	var device models.ClientDevice
	if err := config.DB.Where("id = ? AND client_id = ?", uint(deviceID), clientId).First(&device).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Device not found"})
		return
	}

	// Get the most recent trip for this device
	var trip models.Trip
	if err := config.DB.Where("client_device_id = ?", deviceID).Order("created_at DESC").First(&trip).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "No trips found for this device"})
		return
	}

	// Get GPS data for this trip
	var gpsData []models.GPSData
	if err := config.DB.Where("trip_id = ?", trip.Id).Order("gps_timestamp ASC").Find(&gpsData).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch GPS data"})
		return
	}

	// Calculate statistics
	stats := calculateTripReplayStats(gpsData)

	response := TripReplayResponse{
		Trip:    trip,
		GPSData: gpsData,
		Stats:   stats,
	}

	c.JSON(http.StatusOK, response)
}

// GetTripReplayByDateRange returns trip replay data for a specific date range (frontend version)
func GetTripReplayByDateRange(c *gin.Context) {
	startDate := c.Query("start_date")
	endDate := c.Query("end_date")
	deviceIDStr := c.Query("device_id")

	if startDate == "" || endDate == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Start date and end date are required"})
		return
	}

	// Get client_id from context (frontend pattern)
	clientId, _ := c.Get("client_id")

	// Build query for trips within date range
	query := config.DB.Table("trips").
		Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ? AND trips.created_at BETWEEN ? AND ?", clientId, startDate, endDate)

	// Add device filter if provided
	if deviceIDStr != "" {
		deviceID, err := strconv.ParseUint(deviceIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid device ID"})
			return
		}
		query = query.Where("trips.client_device_id = ?", deviceID)
	}

	var trips []models.Trip
	if err := query.Find(&trips).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch trips"})
		return
	}

	// Get GPS data for all trips
	var allGPSData []models.GPSData
	for _, trip := range trips {
		var gpsData []models.GPSData
		if err := config.DB.Where("trip_id = ?", trip.Id).Order("gps_timestamp ASC").Find(&gpsData).Error; err == nil {
			allGPSData = append(allGPSData, gpsData...)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"trips":        trips,
		"gps_data":     allGPSData,
		"total_trips":  len(trips),
		"total_points": len(allGPSData),
	})
}

// calculateTripReplayStats calculates statistics for the trip replay
func calculateTripReplayStats(gpsData []models.GPSData) TripReplayStats {
	stats := TripReplayStats{
		TotalPoints: len(gpsData),
	}

	if len(gpsData) == 0 {
		return stats
	}

	var totalSpeed float64
	var speedCount int
	var maxSpeed float64

	for _, point := range gpsData {
		if point.Speed != nil {
			speed := *point.Speed
			if speed > 0 {
				stats.MovingPoints++
				totalSpeed += speed
				speedCount++
				if speed > maxSpeed {
					maxSpeed = speed
				}
			} else {
				stats.StationaryPoints++
			}
		} else {
			stats.StationaryPoints++
		}
	}

	stats.MaxSpeed = maxSpeed
	if speedCount > 0 {
		stats.AvgSpeed = totalSpeed / float64(speedCount)
	}

	// Calculate duration
	if len(gpsData) > 1 {
		start := gpsData[0].GPSTimestamp
		end := gpsData[len(gpsData)-1].GPSTimestamp
		if start != nil && end != nil {
			duration := end.Sub(*start)
			hours := int(duration.Hours())
			minutes := int(duration.Minutes()) % 60
			if hours > 0 {
				stats.Duration = fmt.Sprintf("%dh %dm", hours, minutes)
			} else {
				stats.Duration = fmt.Sprintf("%dm", minutes)
			}
		}
	}

	return stats
}
