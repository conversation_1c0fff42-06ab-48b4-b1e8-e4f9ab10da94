package controllers

import (
	"net/http"
	"strconv"
	"time"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

type DriverTrainingController struct {
	driverTrainingService *services.DriverTrainingService
}

func NewDriverTrainingController() *DriverTrainingController {
	return &DriverTrainingController{
		driverTrainingService: services.NewDriverTrainingService(),
	}
}

// GetDriverTrainings returns all driver training records for the client
func (c *DriverTrainingController) GetDriverTrainings(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(ctx.DefaultQuery("per_page", "20"))
	status := ctx.Query("status")
	driverId := ctx.Query("driver_id")
	trainingType := ctx.Query("training_type")
	startDate := ctx.Query("start_date")
	endDate := ctx.Query("end_date")

	// Build filters
	filters := models.DriverTrainingFilters{
		ClientId:     clientId,
		Page:         page,
		PerPage:      perPage,
		Status:       status,
		DriverId:     driverId,
		TrainingType: trainingType,
	}

	// Parse dates if provided
	if startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			filters.StartDate = &date
		}
	}
	if endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			filters.EndDate = &date
		}
	}

	trainings, total, err := c.driverTrainingService.GetDriverTrainings(filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data": trainings,
		"pagination": gin.H{
			"page":        page,
			"per_page":    perPage,
			"total":       total,
			"total_pages": (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// GetDriverTraining returns a specific driver training record
func (c *DriverTrainingController) GetDriverTraining(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	training, err := c.driverTrainingService.GetDriverTraining(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Driver training record not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": training})
}

// CreateDriverTraining creates a new driver training record
func (c *DriverTrainingController) CreateDriverTraining(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	var training models.DriverTraining
	if err := ctx.ShouldBindJSON(&training); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set client ID from context
	training.ClientId = clientId

	createdTraining, err := c.driverTrainingService.CreateDriverTraining(training)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{"data": createdTraining})
}

// UpdateDriverTraining updates an existing driver training record
func (c *DriverTrainingController) UpdateDriverTraining(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var training models.DriverTraining
	if err := ctx.ShouldBindJSON(&training); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	training.Id = uint(id)
	training.ClientId = clientId

	updatedTraining, err := c.driverTrainingService.UpdateDriverTraining(training)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": updatedTraining})
}

// DeleteDriverTraining deletes a driver training record
func (c *DriverTrainingController) DeleteDriverTraining(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	err = c.driverTrainingService.DeleteDriverTraining(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Driver training record deleted successfully"})
}

// GetExpiringTrainings returns training records that are expiring soon
func (c *DriverTrainingController) GetExpiringTrainings(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	days, _ := strconv.Atoi(ctx.DefaultQuery("days", "30"))

	trainings, err := c.driverTrainingService.GetExpiringTrainings(clientId, days)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": trainings})
}

// GetDriverTrainingStats returns driver training statistics
func (c *DriverTrainingController) GetDriverTrainingStats(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	stats, err := c.driverTrainingService.GetDriverTrainingStats(clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": stats})
}
