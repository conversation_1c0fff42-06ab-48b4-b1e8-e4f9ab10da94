package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupTaxRatesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func TestGetAllTaxRates(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupTaxRatesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up all existing tax rates to get predictable results
	config.DB.Where("1=1").Delete(&models.TaxRate{})

	// Create test tax rates
	taxRate1 := models.TaxRate{
		Name:   "Test Tax Rate 1",
		Amount: 10.0,
		Active: true,
	}
	taxRate2 := models.TaxRate{
		Name:   "Test Tax Rate 2",
		Amount: 15.0,
		Active: false,
	}
	config.DB.Create(&taxRate1)
	config.DB.Create(&taxRate2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	taxRates := v1.Group("/tax-rates")
	taxRates.GET("", GetAllTaxRates)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/frontend/tax-rates", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 2) // 2 test tax rates (after cleanup)
}

func TestGetAllTaxRatesWithActiveFilter(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupTaxRatesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up all existing tax rates to get predictable results
	config.DB.Where("1=1").Delete(&models.TaxRate{})

	// Create test tax rates
	taxRate1 := models.TaxRate{
		Name:   "Active Tax Rate",
		Amount: 10.0,
		Active: true,
	}
	taxRate2 := models.TaxRate{
		Name:   "Inactive Tax Rate",
		Amount: 15.0,
		Active: true, // Create as active first due to GORM default
	}
	config.DB.Create(&taxRate1)
	config.DB.Create(&taxRate2)

	// Explicitly set as inactive after creation
	config.DB.Model(&taxRate2).Update("active", false)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	taxRates := v1.Group("/tax-rates")
	taxRates.GET("", GetAllTaxRates)

	// Create request with active filter
	req, _ := http.NewRequest("GET", "/api/v1/frontend/tax-rates?active=true", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 1) // 1 test active tax rate (after cleanup)
}

func TestGetTaxRateById(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupTaxRatesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test tax rate
	taxRate := models.TaxRate{
		Name:   "Test Tax Rate",
		Amount: 10.0,
		Active: true,
	}
	config.DB.Create(&taxRate)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	taxRates := v1.Group("/tax-rates")
	taxRates.GET("/:id", GetTaxRateById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/frontend/tax-rates/%d", taxRate.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Tax Rate", data["name"])
}

func TestGetTaxRateByIdNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupTaxRatesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	taxRates := v1.Group("/tax-rates")
	taxRates.GET("/:id", GetTaxRateById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/frontend/tax-rates/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Tax rate not found", response["message"])
}

func TestSearchTaxRates(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupTaxRatesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test tax rates
	config.DB.Where("name LIKE ?", "%Searchable%").Delete(&models.TaxRate{})
	config.DB.Where("name = ?", "Another Tax Rate").Delete(&models.TaxRate{})

	// Create test tax rates
	taxRate1 := models.TaxRate{
		Name:   "Searchable Tax Rate One",
		Amount: 10.0,
		Active: true,
	}
	taxRate2 := models.TaxRate{
		Name:   "Another Tax Rate",
		Amount: 15.0,
		Active: true,
	}
	config.DB.Create(&taxRate1)
	config.DB.Create(&taxRate2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	taxRates := v1.Group("/tax-rates")
	taxRates.GET("/search", SearchTaxRates)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/frontend/tax-rates/search?s=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 1) // Should only find "Searchable Tax Rate One"
}

func TestSearchTaxRatesNoResults(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupTaxRatesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test tax rate
	taxRate := models.TaxRate{
		Name:   "Test Tax Rate",
		Amount: 10.0,
		Active: true,
	}
	config.DB.Create(&taxRate)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	taxRates := v1.Group("/tax-rates")
	taxRates.GET("/search", SearchTaxRates)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/frontend/tax-rates/search?s=Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 0) // Should find no results
}

func TestSearchTaxRatesWithActiveFilter(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupTaxRatesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up all existing tax rates to get predictable results
	config.DB.Where("1=1").Delete(&models.TaxRate{})

	// Create test tax rates
	taxRate1 := models.TaxRate{
		Name:   "Active Tax Rate",
		Amount: 10.0,
		Active: true,
	}
	taxRate2 := models.TaxRate{
		Name:   "Inactive Tax Rate",
		Amount: 15.0,
		Active: true, // Create as active first due to GORM default
	}
	config.DB.Create(&taxRate1)
	config.DB.Create(&taxRate2)

	// Explicitly set as inactive after creation
	config.DB.Model(&taxRate2).Update("active", false)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	taxRates := v1.Group("/tax-rates")
	taxRates.GET("/search", SearchTaxRates)

	// Test search with active filter
	req, _ := http.NewRequest("GET", "/api/v1/frontend/tax-rates/search?active=true", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 1) // Should only return active tax rates
}
