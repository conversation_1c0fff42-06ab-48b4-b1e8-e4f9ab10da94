package controllers

import (
	"net/http"
	"strconv"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

// GetAllSupportTickets returns all support tickets for the current client
func GetAllSupportTickets(c *gin.Context) {
	// Get current user's client ID first
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	var filters models.SupportTicketFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Set default pagination if not provided
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.PerPage == 0 {
		filters.PerPage = 20
	}

	filters.ClientId = *currentUser.ClientId

	supportTicketService := services.NewSupportTicketService()
	var tickets []models.SupportTicket
	var total int64
	var err error

	// If search parameter is provided, use search functionality
	if filters.Search != "" {
		tickets, total, err = supportTicketService.SearchSupportTicketsWithFilters(filters.Search, filters)
	} else {
		tickets, total, err = supportTicketService.GetSupportTickets(filters)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	response := gin.H{
		"data":         tickets,
		"total":        total,
		"current_page": filters.Page,
		"per_page":     filters.PerPage,
		"last_page":    (total + int64(filters.PerPage) - 1) / int64(filters.PerPage),
	}

	// Add search query to response if search was performed
	if filters.Search != "" {
		response["search_query"] = filters.Search
	}

	c.JSON(http.StatusOK, response)
}

// GetSupportTicketById returns a specific support ticket for the current client
func GetSupportTicketById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid ticket ID",
		})
		return
	}

	// Get current user's client ID
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	supportTicketService := services.NewSupportTicketService()
	ticket, err := supportTicketService.GetSupportTicket(uint(id), *currentUser.ClientId)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Ticket not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": ticket,
	})
}

// GetSupportTicketReplies returns all replies for a ticket
func GetSupportTicketReplies(c *gin.Context) {
	ticketId, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid ticket ID",
		})
		return
	}

	// Get current user's client ID
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	supportTicketService := services.NewSupportTicketService()
	replies, err := supportTicketService.GetSupportTicketReplies(uint(ticketId), *currentUser.ClientId)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": replies,
	})
}

// CreateSupportTicket creates a new support ticket for the current client
func CreateSupportTicket(c *gin.Context) {
	var req models.CreateSupportTicketClientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get current user
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	// Create ticket using client_id from context
	ticket := models.SupportTicket{
		ClientId:       *currentUser.ClientId,
		CreatedById:    currentUser.Id,
		ClientDeviceId: req.ClientDeviceId,
		Department:     req.Department,
		Priority:       req.Priority,
		Subject:        req.Subject,
		Description:    req.Description,
		Status:         "open",
	}

	supportTicketService := services.NewSupportTicketService()
	createdTicket, err := supportTicketService.CreateSupportTicket(ticket)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Support ticket created successfully",
		"data":    createdTicket,
	})
}

// UpdateSupportTicket updates an existing support ticket (limited fields for clients)
func UpdateSupportTicket(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid ticket ID",
		})
		return
	}

	var req models.UpdateSupportTicketRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get current user
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	// Get existing ticket to check permissions
	var existingTicket models.SupportTicket
	err = config.DB.Where("id = ? AND client_id = ?", id, *currentUser.ClientId).First(&existingTicket).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Ticket not found",
		})
		return
	}

	// Clients can only update certain fields
	updateTicket := models.SupportTicket{
		Id:       uint(id),
		ClientId: existingTicket.ClientId,
	}

	// Only allow clients to update subject and description
	if req.Subject != nil {
		updateTicket.Subject = *req.Subject
	}
	if req.Description != nil {
		updateTicket.Description = *req.Description
	}

	supportTicketService := services.NewSupportTicketService()
	updatedTicket, err := supportTicketService.UpdateSupportTicket(updateTicket)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Support ticket updated successfully",
		"data":    updatedTicket,
	})
}

// ChangeSupportTicketStatus changes the status of a support ticket
func ChangeSupportTicketStatus(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid ticket ID",
		})
		return
	}

	var req models.ChangeSupportTicketStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get current user
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	// Verify the ticket belongs to the current client
	var existingTicket models.SupportTicket
	err = config.DB.Where("id = ? AND client_id = ?", id, *currentUser.ClientId).First(&existingTicket).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Ticket not found",
		})
		return
	}

	// Clients can only change status to certain values
	allowedStatuses := []string{"waiting_for_customer", "resolved", "closed"}
	isAllowed := false
	for _, status := range allowedStatuses {
		if req.Status == status {
			isAllowed = true
			break
		}
	}

	if !isAllowed {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Clients can only change status to: waiting_for_customer, resolved, or closed",
		})
		return
	}

	supportTicketService := services.NewSupportTicketService()
	updatedTicket, err := supportTicketService.ChangeSupportTicketStatus(uint(id), *currentUser.ClientId, req.Status)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Support ticket status changed successfully",
		"data":    updatedTicket,
	})
}

// CreateSupportTicketReply creates a new reply to a support ticket
func CreateSupportTicketReply(c *gin.Context) {
	var req models.CreateSupportTicketReplyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get current user
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	// Verify the ticket belongs to the current client
	var ticket models.SupportTicket
	err := config.DB.Where("id = ? AND client_id = ?", c.Param("id"), *currentUser.ClientId).First(&ticket).Error
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Ticket not found",
		})
		return
	}

	// Clients cannot create internal replies
	req.IsInternal = false
	// Create reply
	reply := models.SupportTicketReply{
		TicketId:    ticket.Id,
		CreatedById: currentUser.Id,
		IsInternal:  req.IsInternal,
		Message:     req.Message,
	}

	supportTicketService := services.NewSupportTicketService()
	createdReply, err := supportTicketService.CreateSupportTicketReply(reply)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Reply added successfully",
		"data":    createdReply,
	})
}

// GetSupportTicketStats returns ticket statistics for the current client
func GetSupportTicketStats(c *gin.Context) {
	// Get current user's client ID
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	supportTicketService := services.NewSupportTicketService()
	stats, err := supportTicketService.GetSupportTicketStats(*currentUser.ClientId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}

// SearchSupportTickets searches support tickets for the current client with filters
func SearchSupportTickets(c *gin.Context) {
	query := c.Query("s")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Search query is required",
		})
		return
	}

	// Get current user's client ID
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	// Parse additional filters from query parameters
	var filters models.SupportTicketFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Set default pagination if not provided
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.PerPage == 0 {
		filters.PerPage = 20
	}

	// Set client ID from current user
	filters.ClientId = *currentUser.ClientId

	// Add search query to filters (we'll need to extend the service to handle this)
	supportTicketService := services.NewSupportTicketService()
	tickets, total, err := supportTicketService.SearchSupportTicketsWithFilters(query, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data":         tickets,
		"total":        total,
		"current_page": filters.Page,
		"per_page":     filters.PerPage,
		"last_page":    (total + int64(filters.PerPage) - 1) / int64(filters.PerPage),
		"search_query": query,
	})
}
