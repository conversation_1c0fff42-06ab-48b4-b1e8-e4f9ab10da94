package controllers

import (
	"net/http"
	"strconv"
	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
)

// UpdateClient allows frontend users to update their own client information
func UpdateClient(c *gin.Context) {
	var req models.UpdateClientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}

	// Get current user from context
	userValue, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return
	}

	user := userValue.(models.User)

	// Get the user's client
	var client models.Client
	if err := config.DB.First(&client, user.ClientId).Error; err != nil {
		c.J<PERSON>(http.StatusNotFound, gin.H{
			"message": "Client not found",
		})
		return
	}

	// Check if email is being changed and if it already exists for another client
	if req.Email != client.Email {
		var count int64
		err := config.DB.Model(&models.Client{}).
			Where("email = ? AND id != ?", req.Email, client.Id).
			Count(&count).Error
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"message": "Failed to validate email",
			})
			return
		}
		if count > 0 {
			c.JSON(http.StatusBadRequest, gin.H{
				"message": "Email already exists",
			})
			return
		}
	}

	// Update client fields that frontend users are allowed to modify
	client.Name = req.Name
	client.Email = req.Email
	client.PhoneNumber = req.PhoneNumber
	client.Company = req.Company
	client.State = req.State
	client.City = req.City
	client.Town = req.Town
	client.Address = req.Address
	client.CountryId = req.CountryId
	client.Gender = req.Gender
	client.Description = req.Description
	client.MapsProvider = req.MapsProvider
	client.DefaultLandingPage = req.DefaultLandingPage
	// Frontend can only update alert contact info, not enable/disable alerts
	client.WhatsappPhoneNumber = req.WhatsappPhoneNumber
	client.SmsPhoneNumber = req.SmsPhoneNumber
	client.AlertsEmail = req.AlertsEmail

	// Save the updated client
	result := config.DB.Save(&client)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to update client",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Client updated successfully",
	})
}

// GetAllClients allows frontend users to see client information (restricted to own client data)
func GetAllClients(c *gin.Context) {
	clientId, _ := c.Get("client_id")

	var clients []models.Client
	var total int64

	// Frontend users can only see their own client
	config.DB.Where("id = ?", clientId).Find(&clients)
	total = int64(len(clients))

	// Extract current_page and per_page from query params for consistency
	page, _ := strconv.Atoi(c.Query("page"))
	if page <= 0 {
		page = 1
	}
	perPage, _ := strconv.Atoi(c.Query("per_page"))
	switch {
	case perPage > 100:
		perPage = 100
	case perPage <= 0:
		perPage = 10
	}

	// For frontend users, implement simple pagination logic
	// If page > 1, return empty data (since there's only 1 client)
	if page > 1 {
		clients = []models.Client{}
	}

	c.JSON(http.StatusOK, gin.H{
		"data":         clients,
		"total":        total,
		"current_page": page,
		"per_page":     perPage,
	})
}

// GetClientById allows frontend users to get their own client information
func GetClientById(c *gin.Context) {
	clientId, _ := c.Get("client_id")
	requestedId := c.Param("id")

	// Parse the requested ID
	requestedClientId, err := strconv.ParseUint(requestedId, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid client ID",
		})
		return
	}

	// Frontend users can only access their own client data
	if uint(requestedClientId) != clientId.(uint) {
		c.JSON(http.StatusForbidden, gin.H{
			"message": "Access denied",
		})
		return
	}

	var client models.Client
	if err := config.DB.First(&client, clientId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Client not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": client,
	})
}

// CreateClient is not allowed for frontend users (they can't create new clients)
func CreateClient(c *gin.Context) {
	c.JSON(http.StatusForbidden, gin.H{
		"message": "Frontend users cannot create clients",
	})
}

// DeleteClient is not allowed for frontend users (they can't delete clients)
func DeleteClient(c *gin.Context) {
	c.JSON(http.StatusForbidden, gin.H{
		"message": "Frontend users cannot delete clients",
	})
}

// SearchClients allows frontend users to search their own client data
func SearchClients(c *gin.Context) {
	clientId, _ := c.Get("client_id")

	var clients []models.Client
	query := config.DB.Where("id = ?", clientId)

	if search := c.Query("s"); search != "" {
		query = query.Where("(name like ? or email like ? or phone_number like ?)", "%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	query.Find(&clients)

	c.JSON(http.StatusOK, gin.H{
		"data": clients,
	})
}
