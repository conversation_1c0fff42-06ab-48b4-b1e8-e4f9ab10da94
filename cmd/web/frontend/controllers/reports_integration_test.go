package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"yotracker/cmd/web/middleware"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/require"
)

// TestAllActiveReports tests all active reports via the actual frontend route
func TestAllActiveReports(t *testing.T) {
	// Set up test environment
	gin.SetMode(gin.TestMode)

	// Use the test database and seed it with reports (same as the working test)
	services.FastCleanupTestData()

	// Define all active reports with their IDs
	activeReports := []struct {
		ID         int
		Name       string
		ReportType string
	}{
		{1, "Position Log Report", "position_log"},
		{2, "Trip Detail Report", "trip_detail"},
		{3, "Daily Trip Summary", "trip_summary"},
		{4, "Monthly Mileage Report", "mileage_month_summary"},
		{5, "Vehicle Utilization Report", "vehicle_utilization"},
		{6, "Route Efficiency Analysis", "route_deviation_report"},
		{7, "Driver Safety Scorecard", "driver_safety_scorecard"},
		{8, "Speeding Violations Report", "speeding_violations"},
		{9, "Harsh Driving Events", "rash_drive_detail"},
		{10, "Driver Performance Ranking", "driver_performance_summary"},
		{11, "Fatigue & Overtime Alert", "fatigue_report"},
		{12, "Geofence Activity Report", "geofence_activity"},
		{13, "Customer Visit Report", "destination_arrival"},
		{14, "Unauthorized Location Alerts", "exception_detail"},
		{15, "Job Site Productivity", "stop_detail"},
		{16, "Fuel Consumption Analysis", "fuel_consumption_analysis"},
		{17, "Operating Cost Report", "fuel_estimation_month_summary"},
		{18, "Idle Time Cost Analysis", "idle_detail"},
		{19, "Fleet ROI Dashboard", "fleet_roi_dashboard"},
		{20, "Maintenance Due Report", "maintenance_schedule"},
		{21, "Vehicle Health Dashboard", "vehicle_health_dashboard"},
		{22, "Breakdown & Repair History", "maintenance_detail"},
		{23, "Hours of Service Compliance", "working_time_day_summary"},
		{24, "Vehicle Security Report", "after_hour_month_summary"},
		{25, "Speed Limit Compliance", "geofence_speeding_detail"},
		{26, "Executive Fleet Summary", "executive_fleet_summary"},
		{27, "Fleet Performance Trends", "fleet_performance_trends"},
		{28, "Cost Center Analysis", "cost_center_analysis"},
		{29, "Environmental Impact Report", "environmental_impact"},
		{30, "Daily Operations Dashboard", "daily_operations_dashboard"},
		{31, "Vehicle Availability Report", "vehicle_availability"},
		{32, "Emergency Response Report", "emergency_response"},
		{33, "Asset Tracking Report", "last_location"},
		{34, "Delivery Performance Report", "delivery_performance"},
		{35, "Service Technician Report", "service_technician"},
		{38, "Driver Behavior Analysis", "driver_behavior_analysis"},
		{39, "Fuel Efficiency Trends", "fuel_efficiency_trends"},
		{41, "Fleet Productivity Report", "fleet_productivity"},
		{65, "Fuel Estimation", "fuel_estimation"},
		{85, "Real-time Fleet Dashboard", "real_time_dashboard"},
		{86, "Driver Scorecard", "driver_scorecard"},
		{87, "Fleet Health Monitor", "fleet_health_monitor"},
		{88, "Cost Analysis Dashboard", "cost_analysis_dashboard"},
		{89, "Compliance Dashboard", "compliance_dashboard"},
		{90, "Route Analysis Report", "route_analysis"},
		{91, "Predictive Maintenance Report", "predictive_maintenance"},
		{92, "Fleet Optimization Report", "fleet_optimization"},
		{93, "Driver Training Needs Report", "driver_training_needs"},
		{94, "Environmental Impact Dashboard", "environmental_dashboard"},
	}

	// Test each active report
	for _, report := range activeReports {
		t.Run(fmt.Sprintf("Report_%d_%s", report.ID, report.ReportType), func(t *testing.T) {
			testReportGenerationIntegration(t, report.ID, report.Name, report.ReportType)
		})
	}
}

// testReportGenerationIntegration tests a single report generation
func testReportGenerationIntegration(t *testing.T, reportID int, reportName, reportType string) {
	// Set up test environment
	gin.SetMode(gin.TestMode)

	// Database is already initialized and seeded by the parent test

	// Create test user (which also creates a client)
	user := createTestUser(t)
	token, _ := services.GenerateToken(&user, "access")

	// Set up router
	router := gin.New()
	router.Use(middleware.AuthMiddleware())

	// Add the reports route
	router.POST("/api/v1/frontend/reports/:id/generate", GenerateReport)

	// Create request body with basic filters
	requestBody := map[string]interface{}{
		"filters": map[string]interface{}{
			"start_date":        time.Now().AddDate(0, 0, -7).Format("2006-01-02"),
			"end_date":          time.Now().Format("2006-01-02"),
			"client_device_ids": []int{},
			"driver_ids":        []int{},
			"fleet_ids":         []int{},
			"per_page":          10,
			"page":              1,
		},
	}

	// Convert request body to JSON
	jsonBody, err := json.Marshal(requestBody)
	require.NoError(t, err)

	// Create HTTP request
	req, err := http.NewRequest("POST", fmt.Sprintf("/api/v1/frontend/reports/%d/generate", reportID), bytes.NewBuffer(jsonBody))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	// Create response recorder
	w := httptest.NewRecorder()

	// Execute request
	router.ServeHTTP(w, req)

	// Log the response status for debugging (without full response body)
	t.Logf("Report %d (%s): Status=%d", reportID, reportName, w.Code)

	// Check if request was successful
	if w.Code != http.StatusOK {
		t.Logf("❌ Report %d (%s) failed with status %d", reportID, reportName, w.Code)
		return
	}

	// Parse response
	var response map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &response)
	if err != nil {
		t.Logf("❌ Report %d (%s) failed to parse response: %v", reportID, reportName, err)
		return
	}

	// Check if response has success field
	if success, exists := response["success"]; exists {
		if successBool, ok := success.(bool); ok && !successBool {
			if errorMsg, exists := response["error"]; exists {
				t.Logf("❌ Report %d (%s) returned error: %v", reportID, reportName, errorMsg)
			} else {
				t.Logf("❌ Report %d (%s) failed but no error message", reportID, reportName)
			}
			return
		}
	}

	// Check if response has data field
	if data, exists := response["data"]; exists {
		if dataMap, ok := data.(map[string]interface{}); ok {
			// Check if report_info exists
			if reportInfo, exists := dataMap["report_info"]; exists {
				if infoMap, ok := reportInfo.(map[string]interface{}); ok {
					if name, exists := infoMap["name"]; exists {
						t.Logf("✅ Report %d (%s) generated successfully: %v", reportID, reportName, name)
					} else {
						t.Logf("✅ Report %d (%s) generated successfully but no name", reportID, reportName)
					}
				}
			} else {
				t.Logf("⚠️ Report %d (%s) generated but no report_info", reportID, reportName)
			}
		}
	} else {
		t.Logf("⚠️ Report %d (%s) generated but no data field", reportID, reportName)
	}
}

// TestSpecificFailingReports tests specific reports that are known to fail
// NOTE: This test is redundant since TestAllActiveReports already covers all reports
// Keeping it for now but it should be removed once we're confident in the main test
func TestSpecificFailingReports(t *testing.T) {
	t.Skip("This test is redundant - TestAllActiveReports already covers all reports including these specific ones")

	// Set up test environment
	gin.SetMode(gin.TestMode)

	// Use the test database and seed it with reports (same as the working test)
	services.FastCleanupTestData()

	// Test specific reports that might be failing
	failingReports := []struct {
		ID         int
		Name       string
		ReportType string
	}{
		{65, "Fuel Estimation", "fuel_estimation"},                      // Known to have table alias issues
		{23, "Hours of Service Compliance", "working_time_day_summary"}, // Recently fixed
		{4, "Monthly Mileage Report", "mileage_month_summary"},          // Recently converted to raw SQL
	}

	for _, report := range failingReports {
		t.Run(fmt.Sprintf("Failing_Report_%d_%s", report.ID, report.ReportType), func(t *testing.T) {
			testReportGenerationIntegration(t, report.ID, report.Name, report.ReportType)
		})
	}
}
