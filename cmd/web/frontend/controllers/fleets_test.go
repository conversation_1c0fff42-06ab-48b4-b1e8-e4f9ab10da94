package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupFleetsTestEnvVars() {
	os.Setenv("DB_HOST", "localhost")
	os.Setenv("DB_PORT", "3306")
	os.Setenv("DB_USER", "root")
	os.Setenv("DB_PASSWORD", "password")
	os.Setenv("DB_NAME", "yotracker_test")
	os.Setenv("JWT_SECRET", "test-secret")
}

func TestGetAllFleets(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleets
	fleet1 := models.Fleet{
		ClientId: client.Id,
		Name:     "Test Fleet 1",
	}
	fleet2 := models.Fleet{
		ClientId: client.Id,
		Name:     "Test Fleet 2",
	}
	config.DB.Create(&fleet1)
	config.DB.Create(&fleet2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	fleets := v1.Group("/fleets")
	fleets.GET("", GetAllFleets)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/frontend/fleets", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 2)
}

func TestGetFleetById(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleet
	fleet := models.Fleet{
		ClientId: client.Id,
		Name:     "Test Fleet",
	}
	config.DB.Create(&fleet)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	fleets := v1.Group("/fleets")
	fleets.GET("/:id", GetFleetById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/frontend/fleets/%d", fleet.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Fleet", data["name"])
}

func TestGetFleetByIdNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	fleets := v1.Group("/fleets")
	fleets.GET("/:id", GetFleetById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/frontend/fleets/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Fleet not found", response["message"])
}

func TestCreateFleet(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupFleetsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test client
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.Client{})

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Setup router
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	fleets := v1.Group("/fleets")
	fleets.POST("", CreateFleet)

	// Test data
	fleetData := models.FleetRequest{
		Name: "New Test Fleet",
	}

	jsonData, _ := json.Marshal(fleetData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/frontend/fleets", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Fleet created successfully", response["message"])

	// Verify fleet was created in database
	var fleet models.Fleet
	err = config.DB.Where("name = ?", "New Test Fleet").First(&fleet).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Fleet", fleet.Name)
	assert.NotZero(t, fleet.ClientId)
}

func TestCreateFleetInvalidRequest(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", &client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	fleets := v1.Group("/fleets")
	fleets.POST("", CreateFleet)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Fleet", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/frontend/fleets", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateFleet(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleet
	fleet := models.Fleet{
		ClientId: client.Id,
		Name:     "Original Fleet",
	}
	config.DB.Create(&fleet)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	fleets := v1.Group("/fleets")
	fleets.PUT("/:id", UpdateFleet)

	// Test data
	updateData := models.FleetRequest{
		Name: "Updated Fleet",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/frontend/fleets/%d", fleet.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Fleet updated successfully", response["message"])

	// Verify fleet was updated in database
	var updatedFleet models.Fleet
	err = config.DB.First(&updatedFleet, fleet.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "Updated Fleet", updatedFleet.Name)
}

func TestUpdateFleetNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	fleets := v1.Group("/fleets")
	fleets.PUT("/:id", UpdateFleet)

	// Test data
	updateData := models.FleetRequest{
		Name: "Updated Fleet",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/frontend/fleets/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Fleet not found", response["message"])
}

func TestDeleteFleet(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupFleetsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test client
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.Client{})

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleet
	fleet := models.Fleet{
		ClientId: client.Id,
		Name:     "Test Fleet to Delete",
	}
	config.DB.Create(&fleet)

	// Setup router
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	fleets := v1.Group("/fleets")
	fleets.DELETE("/:id", DeleteFleet)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/frontend/fleets/%d", fleet.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)

	// 204 No Content should not have a response body
	assert.Empty(t, w.Body.String())

	// Verify fleet was deleted from database
	var deletedFleet models.Fleet
	err := config.DB.First(&deletedFleet, fleet.Id).Error
	assert.Error(t, err) // Should not find the fleet
}

func TestDeleteFleetNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	fleets := v1.Group("/fleets")
	fleets.DELETE("/:id", DeleteFleet)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/frontend/fleets/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Fleet not found", response["message"])
}

func TestSearchFleets(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleets
	fleet1 := models.Fleet{
		ClientId: client.Id,
		Name:     "Searchable Fleet One",
	}
	fleet2 := models.Fleet{
		ClientId: client.Id,
		Name:     "Another Fleet",
	}
	config.DB.Create(&fleet1)
	config.DB.Create(&fleet2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	fleets := v1.Group("/fleets")
	fleets.GET("/search", SearchFleets)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/frontend/fleets/search?s=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 1) // Should only find "Searchable Fleet One"
}

func TestSearchFleetsNoResults(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Create a test client first
	client := models.Client{
		CreatedById: 1,
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	// Create test fleet
	fleet := models.Fleet{
		ClientId: client.Id,
		Name:     "Test Fleet",
	}
	config.DB.Create(&fleet)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
	})

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	fleets := v1.Group("/fleets")
	fleets.GET("/search", SearchFleets)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/frontend/fleets/search?s=Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 0) // Should find no results
}
