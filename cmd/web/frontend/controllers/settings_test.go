package controllers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupSettingsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func TestGetAllowedSettings(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupSettingsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test settings
	setting1 := models.Setting{
		Name:         "Test Setting 1",
		SettingKey:   "test_setting_1",
		SettingValue: "test_value_1",
	}
	setting2 := models.Setting{
		Name:         "Test Setting 2",
		SettingKey:   "test_setting_2",
		SettingValue: "test_value_2",
	}
	config.DB.Create(&setting1)
	config.DB.Create(&setting2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	settings := v1.Group("/settings")
	settings.GET("", GetAllowedSettings)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/frontend/settings", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Verify response structure
	assert.Contains(t, response, "data")
}

func TestGetSettingByKey(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupSettingsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing setting to prevent duplicate key errors
	config.DB.Where("setting_key = ?", "company_name").Delete(&models.Setting{})

	// Create test setting using an allowed key
	setting := models.Setting{
		Name:         "Company Name",
		SettingKey:   "company_name",
		SettingValue: "Test Company",
	}
	config.DB.Create(&setting)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	settings := v1.Group("/settings")
	settings.GET("/:key", GetSettingByKey)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/frontend/settings/company_name", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Verify response structure
	assert.Contains(t, response, "data")
}

func TestGetSettingByKeyNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupSettingsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up the setting we're testing to ensure it doesn't exist
	config.DB.Where("setting_key = ?", "company_phone").Delete(&models.Setting{})

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	settings := v1.Group("/settings")
	settings.GET("/:key", GetSettingByKey)

	// Create request with allowed but non-existent key
	req, _ := http.NewRequest("GET", "/api/v1/frontend/settings/company_phone", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Setting not found", response["message"])
}

func TestGetSettingValue(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupSettingsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing setting to prevent duplicate key errors
	config.DB.Where("setting_key = ?", "company_email").Delete(&models.Setting{})

	// Create test setting using an allowed key
	setting := models.Setting{
		Name:         "Company Email",
		SettingKey:   "company_email",
		SettingValue: "<EMAIL>",
	}
	config.DB.Create(&setting)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	settings := v1.Group("/settings")
	settings.GET("/:key/value", GetSettingValue)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/frontend/settings/company_email/value", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Verify response structure
	assert.Contains(t, response, "value")
}

func TestGetSettingValueNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupSettingsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up the setting we're testing to ensure it doesn't exist
	config.DB.Where("setting_key = ?", "company_website").Delete(&models.Setting{})

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	settings := v1.Group("/settings")
	settings.GET("/:key/value", GetSettingValue)

	// Create request with allowed but non-existent key
	req, _ := http.NewRequest("GET", "/api/v1/frontend/settings/company_website/value", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Setting not found", response["message"])
}
