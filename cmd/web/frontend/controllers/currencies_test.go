package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupCurrenciesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func TestGetAllCurrencies(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupCurrenciesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test currencies
	config.DB.Where("code IN (?)", []string{"TST1", "TST2"}).Delete(&models.Currency{})

	// Create test currencies
	currency1 := models.Currency{
		Name:   "Test Currency 1",
		Code:   "TST1",
		Symbol: "$",
	}
	currency2 := models.Currency{
		Name:   "Test Currency 2",
		Code:   "TST2",
		Symbol: "€",
	}
	config.DB.Create(&currency1)
	config.DB.Create(&currency2)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	currencies := v1.Group("/currencies")
	currencies.GET("", GetAllCurrencies)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/frontend/currencies", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})

	// Get the actual count of currencies in the database
	var countBefore int64
	config.DB.Model(&models.Currency{}).Count(&countBefore)

	// Assert that the response data length matches the database count
	assert.Len(t, data, int(countBefore)) // Dynamic count based on actual database state
}

func TestGetCurrencyById(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupCurrenciesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test currency
	currency := models.Currency{
		Name:   "Test Currency",
		Code:   "TST",
		Symbol: "$",
	}
	config.DB.Create(&currency)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	currencies := v1.Group("/currencies")
	currencies.GET("/:id", GetCurrencyById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/frontend/currencies/%d", currency.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Currency", data["name"])
}

func TestGetCurrencyByIdNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupCurrenciesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	currencies := v1.Group("/currencies")
	currencies.GET("/:id", GetCurrencyById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/frontend/currencies/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Currency not found", response["message"])
}

func TestCreateCurrency(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupCurrenciesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	currencies := v1.Group("/currencies")
	currencies.POST("", CreateCurrency)

	// Test data
	currencyData := models.CurrencyRequest{
		Name:   "New Test Currency",
		Code:   "NTC",
		Symbol: "¥",
	}

	jsonData, _ := json.Marshal(currencyData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/frontend/currencies", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Currency created successfully", response["message"])

	// Verify currency was created in database
	var currency models.Currency
	err = config.DB.Where("name = ?", "New Test Currency").First(&currency).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Currency", currency.Name)
}

func TestCreateCurrencyInvalidRequest(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupCurrenciesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	currencies := v1.Group("/currencies")
	currencies.POST("", CreateCurrency)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Currency", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/frontend/currencies", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateCurrency(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupCurrenciesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test currency
	config.DB.Where("code = ?", "ORG").Delete(&models.Currency{})

	// Create test currency
	currency := models.Currency{
		Name:   "Original Currency",
		Code:   "ORG",
		Symbol: "$",
	}
	config.DB.Create(&currency)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	currencies := v1.Group("/currencies")
	currencies.PUT("/:id", UpdateCurrency)

	// Test data
	updateData := models.CurrencyRequest{
		Name:   "Updated Currency",
		Code:   "UPD",
		Symbol: "€",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/frontend/currencies/%d", currency.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Currency updated successfully", response["message"])

	// Verify currency was updated in database
	var updatedCurrency models.Currency
	err = config.DB.First(&updatedCurrency, currency.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "Updated Currency", updatedCurrency.Name)
}

func TestUpdateCurrencyNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupCurrenciesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	currencies := v1.Group("/currencies")
	currencies.PUT("/:id", UpdateCurrency)

	// Test data
	updateData := models.CurrencyRequest{
		Name:   "Updated Currency",
		Code:   "UPD",
		Symbol: "€",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/frontend/currencies/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Currency not found", response["message"])
}

func TestDeleteCurrency(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupCurrenciesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test currency
	config.DB.Where("code = ?", "DEL").Delete(&models.Currency{})

	// Create test currency
	currency := models.Currency{
		Name:   "Test Currency to Delete",
		Code:   "DEL",
		Symbol: "$",
	}
	config.DB.Create(&currency)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	currencies := v1.Group("/currencies")
	currencies.DELETE("/:id", DeleteCurrency)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/frontend/currencies/%d", currency.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)

	// 204 No Content should not have a response body
	assert.Empty(t, w.Body.String())

	// Verify currency was deleted from database
	var deletedCurrency models.Currency
	err := config.DB.First(&deletedCurrency, currency.Id).Error
	assert.Error(t, err) // Should not find the currency
}

func TestDeleteCurrencyNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupCurrenciesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	currencies := v1.Group("/currencies")
	currencies.DELETE("/:id", DeleteCurrency)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/frontend/currencies/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Currency not found", response["message"])
}

func TestSearchCurrencies(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupCurrenciesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test currencies
	config.DB.Where("code IN (?)", []string{"SCO", "AC"}).Delete(&models.Currency{})

	// Create test currencies
	currency1 := models.Currency{
		Name:   "Searchable Currency One",
		Code:   "SCO",
		Symbol: "$",
	}
	currency2 := models.Currency{
		Name:   "Another Currency",
		Code:   "AC",
		Symbol: "€",
	}
	config.DB.Create(&currency1)
	config.DB.Create(&currency2)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	currencies := v1.Group("/currencies")
	currencies.GET("/search", SearchCurrencies)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/frontend/currencies/search?s=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 1) // Should only find "Searchable Currency One"
}

func TestSearchCurrenciesNoResults(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupCurrenciesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test currency
	config.DB.Where("code = ?", "TST").Delete(&models.Currency{})

	// Create test currency
	currency := models.Currency{
		Name:   "Test Currency",
		Code:   "TST",
		Symbol: "$",
	}
	config.DB.Create(&currency)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	currencies := v1.Group("/currencies")
	currencies.GET("/search", SearchCurrencies)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/frontend/currencies/search?s=Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 0) // Should find no results
}
