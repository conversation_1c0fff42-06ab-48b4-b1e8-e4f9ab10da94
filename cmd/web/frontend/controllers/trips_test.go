package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupTripsTest(t *testing.T) (*gin.Engine, string, models.Client) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupTripsTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test client and user
	client := createTripsTestClient(t)
	user := createTripsTestUser(t, client.Id)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())

	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/frontend")
	v1.Use(middleware.AuthMiddleware())
	v1.Use(middleware.CheckForClient())

	// Trips routes
	trips := v1.Group("/trips")
	trips.GET("", GetAllTrips)
	trips.GET("/search", SearchTrips)
	trips.GET("/:id", GetTripById)

	return r, token, client
}

func setupTripsTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         getEnvOrDefault("DB_HOST", "localhost"),
		"DB_PORT":         getEnvOrDefault("DB_PORT", "3306"),
		"DB_USERNAME":     getEnvOrDefault("DB_USERNAME", "admin"),
		"DB_PASSWORD":     getEnvOrDefault("DB_PASSWORD", "password"),
		"TESTING_DB_NAME": getEnvOrDefault("TESTING_DB_NAME", "testing"),
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createTripsTestClient(t *testing.T) models.Client {
	// Use unique email to avoid conflicts
	timestamp := time.Now().UnixNano()
	email := fmt.Sprintf("<EMAIL>", timestamp)

	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Trips Test Client",
		Email:      email,
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createTripsTestUser(t *testing.T, clientId uint) models.User {
	// Use unique email to avoid conflicts
	timestamp := time.Now().UnixNano()
	email := fmt.Sprintf("<EMAIL>", timestamp)

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    email,
		Password: password,
		Name:     "Trips Admin",
		UserType: "frontend",
		Status:   &status,
		ClientId: &clientId,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

func createTripsTestDevice(t *testing.T, clientId uint) models.ClientDevice {
	status := "active"

	device := models.ClientDevice{
		ClientId:     clientId,
		Status:       status,
		DeviceTypeId: 1, // Use a valid device type ID
	}

	result := config.DB.Create(&device)
	assert.NoError(t, result.Error)

	return device
}

func createTripsTestTrip(t *testing.T, deviceId uint, startTime time.Time) models.Trip {
	endTime := startTime.Add(2 * time.Hour)
	tripType := "normal"

	trip := models.Trip{
		ClientDeviceId: deviceId,
		StartTime:      startTime,
		EndTime:        &endTime,
		TripType:       &tripType,
	}

	result := config.DB.Create(&trip)
	assert.NoError(t, result.Error)

	return trip
}

// Test GetAllTrips functionality
func TestGetAllTrips(t *testing.T) {
	r, token, client := setupTripsTest(t)

	// Create test device and trips
	device := createTripsTestDevice(t, client.Id)
	startTime1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	startTime2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)

	_ = createTripsTestTrip(t, device.Id, startTime1)
	_ = createTripsTestTrip(t, device.Id, startTime2)

	t.Run("Get all trips successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2)
		assert.Equal(t, float64(2), response["total"])
	})

	t.Run("Get all trips without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetAllTrips with date filtering
func TestGetAllTripsWithDateFiltering(t *testing.T) {
	r, token, client := setupTripsTest(t)

	// Create test device and trips with different dates
	device := createTripsTestDevice(t, client.Id)
	startTime1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	startTime2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)
	startTime3 := time.Date(2024, 2, 1, 9, 0, 0, 0, time.UTC)

	_ = createTripsTestTrip(t, device.Id, startTime1)
	_ = createTripsTestTrip(t, device.Id, startTime2)
	_ = createTripsTestTrip(t, device.Id, startTime3)

	tests := []struct {
		name           string
		queryParams    string
		expectedStatus int
		expectedCount  int
	}{
		{
			name:           "Get trips for specific date",
			queryParams:    "?start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get trips for date range",
			queryParams:    "?start_date=2024-01-15&end_date=2024-01-25",
			expectedStatus: http.StatusOK,
			expectedCount:  2,
		},
		{
			name:           "Get trips with device filter and date",
			queryParams:    "?device_id=" + strconv.FormatUint(uint64(device.Id), 10) + "&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
		{
			name:           "Get trips with trip type and date filter",
			queryParams:    "?trip_type=normal&start_date=2024-01-15",
			expectedStatus: http.StatusOK,
			expectedCount:  1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips"+tt.queryParams, nil)
			req.Header.Set("Authorization", "Bearer "+token)
			w := httptest.NewRecorder()
			r.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedStatus == http.StatusOK {
				var response map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Verify response structure
				assert.Contains(t, response, "data")
				assert.Contains(t, response, "total")

				// Verify data count
				data := response["data"].([]interface{})
				assert.Len(t, data, tt.expectedCount)
			}
		})
	}
}

// Test GetTripById functionality
func TestGetTripById(t *testing.T) {
	r, token, client := setupTripsTest(t)

	// Create test device and trip
	device := createTripsTestDevice(t, client.Id)
	startTime := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	trip := createTripsTestTrip(t, device.Id, startTime)

	t.Run("Get trip by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips/"+strconv.FormatUint(uint64(trip.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify trip data structure (now includes GPS data and stats)
		data := response["data"].(map[string]interface{})
		assert.Contains(t, data, "trip")
		assert.Contains(t, data, "gps_data")
		assert.Contains(t, data, "stats")

		// Verify trip details
		tripData := data["trip"].(map[string]interface{})
		assert.Equal(t, float64(trip.Id), tripData["id"])
		assert.Equal(t, float64(device.Id), tripData["client_device_id"])

		// Verify GPS data is an array
		gpsData := data["gps_data"].([]interface{})
		assert.NotNil(t, gpsData)

		// Verify stats structure
		stats := data["stats"].(map[string]interface{})
		assert.Contains(t, stats, "total_points")
		assert.Contains(t, stats, "moving_points")
		assert.Contains(t, stats, "stationary_points")
		assert.Contains(t, stats, "max_speed")
		assert.Contains(t, stats, "avg_speed")
		assert.Contains(t, stats, "duration")
	})

	t.Run("Get trip by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Get trip by ID with invalid format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips/invalid", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Get trip by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips/"+strconv.FormatUint(uint64(trip.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test CreateTrip functionality
func TestCreateTrip(t *testing.T) {
	r, token, client := setupTripsTest(t)

	// Create test device
	device := createTripsTestDevice(t, client.Id)

	t.Run("Create trip not allowed for frontend users", func(t *testing.T) {
		startTime := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
		endTime := startTime.Add(2 * time.Hour)

		tripData := map[string]interface{}{
			"client_device_id": device.Id,
			"start_time":       startTime.Format(time.RFC3339),
			"end_time":         endTime.Format(time.RFC3339),
			"trip_type":        "normal",
		}

		jsonData, _ := json.Marshal(tripData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/trips", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Frontend users should not be allowed to create trips (trips are auto-generated)
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Create trip with missing required fields not allowed", func(t *testing.T) {
		tripData := map[string]interface{}{
			"client_device_id": device.Id,
			// Missing start_time and end_time
		}

		jsonData, _ := json.Marshal(tripData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/trips", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Frontend users should not be allowed to create trips
		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Create trip with non-existent device", func(t *testing.T) {
		startTime := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
		endTime := startTime.Add(2 * time.Hour)

		tripData := map[string]interface{}{
			"client_device_id": 99999,
			"start_time":       startTime.Format(time.RFC3339),
			"end_time":         endTime.Format(time.RFC3339),
		}

		jsonData, _ := json.Marshal(tripData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/trips", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Create trip without authorization not allowed", func(t *testing.T) {
		startTime := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
		endTime := startTime.Add(2 * time.Hour)

		tripData := map[string]interface{}{
			"client_device_id": device.Id,
			"start_time":       startTime.Format(time.RFC3339),
			"end_time":         endTime.Format(time.RFC3339),
		}

		jsonData, _ := json.Marshal(tripData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/trips", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Route doesn't exist, so even without auth it returns 404
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

// Test SearchTrips functionality
func TestSearchTrips(t *testing.T) {
	r, token, client := setupTripsTest(t)

	// Create test device and trips
	device := createTripsTestDevice(t, client.Id)
	startTime1 := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	startTime2 := time.Date(2024, 1, 20, 14, 0, 0, 0, time.UTC)

	_ = createTripsTestTrip(t, device.Id, startTime1)
	_ = createTripsTestTrip(t, device.Id, startTime2)

	t.Run("Search trips by trip type", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips/search?trip_type=normal", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 2) // Both trips have trip_type=normal
	})

	t.Run("Search trips with date filter", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips/search?start_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Only one trip on 2024-01-15
	})

	t.Run("Search trips with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips/search?trip_type=invalid", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 0) // No results
	})
}

// Test pagination functionality
func TestTripsPagination(t *testing.T) {
	r, token, client := setupTripsTest(t)

	// Create test device and multiple trips for pagination testing
	device := createTripsTestDevice(t, client.Id)
	for i := 1; i <= 15; i++ {
		startTime := time.Date(2024, 1, i, 10, 0, 0, 0, time.UTC)
		createTripsTestTrip(t, device.Id, startTime)
	}

	t.Run("First page with default pagination", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")
		assert.Contains(t, response, "total_pages")

		// Verify pagination values
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.Equal(t, float64(15), response["total"])
		assert.Equal(t, float64(2), response["total_pages"]) // 15 items, 10 per page = 2 pages

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 10)
	})

	t.Run("Second page", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 5) // Remaining 5 items on second page
	})
}

// Test edge cases and error handling
func TestTripsEdgeCases(t *testing.T) {
	r, token, _ := setupTripsTest(t)

	t.Run("Invalid date format in query", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips?start_date=invalid-date", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("End date before start date", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips?start_date=2024-01-20&end_date=2024-01-15", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid trip ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/trips/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}
