package controllers

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupFrontendAuthTest(t *testing.T) (*gin.Engine, models.User, models.Client) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupFrontendAuthTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test client and user
	client := createFrontendAuthTestClient(t)
	user := createFrontendAuthTestUser(t, client.Id)

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())

	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/frontend")

	// Auth routes
	v1.POST("/login", Login)
	v1.POST("/register", Register)
	v1.POST("/password_reset/request", RequestPasswordReset)
	v1.POST("/password_reset/verify", VerifyPasswordResetToken)
	v1.POST("/password_reset/confirm", ResetPassword)
	v1.GET("/home", Home)

	return r, user, client
}

func setupFrontendAuthTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":          getEnvOrDefault("DB_HOST", "localhost"),
		"DB_PORT":          getEnvOrDefault("DB_PORT", "3306"),
		"DB_USERNAME":      getEnvOrDefault("DB_USERNAME", "admin"),
		"DB_PASSWORD":      getEnvOrDefault("DB_PASSWORD", "password"),
		"TESTING_DB_NAME":  getEnvOrDefault("TESTING_DB_NAME", "testing"),
		"APP_KEY":          "test-secret-key",
		"APP_URL":          "http://localhost:3000",
		"SENDGRID_API_KEY": "test-key",
		"FROM_EMAIL":       "<EMAIL>",
		"FROM_NAME":        "Test",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createFrontendAuthTestClient(t *testing.T) models.Client {
	// Clean up existing test client
	config.DB.Where("name = ?", "Frontend Auth Test Client").Delete(&models.Client{})

	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Frontend Auth Test Client",
		Email:      "<EMAIL>",
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createFrontendAuthTestUser(t *testing.T, clientId uint) models.User {
	// Clean up existing test user
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    "<EMAIL>",
		Password: password,
		Name:     "Frontend Auth Test User",
		UserType: "client",
		Status:   &status,
		ClientId: &clientId,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

// Test login functionality
func TestFrontendLogin(t *testing.T) {
	r, _, _ := setupFrontendAuthTest(t)

	t.Run("Login with valid credentials", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "password",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "token")
		assert.NotEmpty(t, response["token"])
	})

	t.Run("Login with invalid password", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "wrongpassword",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnprocessableEntity, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify error message
		assert.Contains(t, response, "errors")
		errors := response["errors"].(map[string]interface{})
		assert.Contains(t, errors, "password")
	})

	t.Run("Login with non-existent email", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "password",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify error message
		assert.Contains(t, response, "errors")
		errors := response["errors"].(map[string]interface{})
		assert.Contains(t, errors, "email")
	})

	t.Run("Login with missing email", func(t *testing.T) {
		loginData := map[string]interface{}{
			"password": "password",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Login with missing password", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email": "<EMAIL>",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Login with invalid JSON", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/login", bytes.NewBufferString("invalid json"))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Login with backend user type (should fail)", func(t *testing.T) {
		// Create a backend user
		backendPassword := services.HashPassword("password")
		backendStatus := "active"
		backendUser := models.User{
			Email:    "<EMAIL>",
			Password: backendPassword,
			Name:     "Backend User",
			UserType: "backend",
			Status:   &backendStatus,
		}
		config.DB.Create(&backendUser)

		loginData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "password",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Should fail because frontend login only accepts client users
		assert.Equal(t, http.StatusNotFound, w.Code)

		// Clean up
		config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})
	})
}

// Test password reset functionality
func TestFrontendPasswordReset(t *testing.T) {
	r, _, _ := setupFrontendAuthTest(t)

	t.Run("Request password reset with valid email", func(t *testing.T) {
		resetData := map[string]interface{}{
			"email": "<EMAIL>",
		}

		jsonData, _ := json.Marshal(resetData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/password_reset/request", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Contains(t, response["message"], "If the email exists, a password reset link has been sent")
	})

	t.Run("Request password reset with non-existent email", func(t *testing.T) {
		resetData := map[string]interface{}{
			"email": "<EMAIL>",
		}

		jsonData, _ := json.Marshal(resetData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/password_reset/request", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Should still return success for security reasons
		assert.Contains(t, response, "message")
		assert.Contains(t, response["message"], "If the email exists, a password reset link has been sent")
	})

	t.Run("Request password reset with invalid email", func(t *testing.T) {
		resetData := map[string]interface{}{
			"email": "invalid-email",
		}

		jsonData, _ := json.Marshal(resetData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/password_reset/request", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Request password reset with missing email", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/password_reset/request", bytes.NewBufferString("{}"))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

// Test home endpoint
func TestFrontendHome(t *testing.T) {
	r, _, _ := setupFrontendAuthTest(t)

	t.Run("Home endpoint", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/home", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "You have successfully connected", response["message"])
	})
}

// Test edge cases and error handling
func TestFrontendAuthEdgeCases(t *testing.T) {
	r, _, _ := setupFrontendAuthTest(t)

	t.Run("Login with empty request body", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/login", bytes.NewBufferString(""))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Login with malformed JSON", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/login", bytes.NewBufferString(`{"email": "<EMAIL>", "password": "password"`))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Login with extra fields", func(t *testing.T) {
		loginData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "password",
			"extra":    "field",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Should still work as extra fields are ignored
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Login with inactive user", func(t *testing.T) {
		// Get the client from setup
		_, _, client := setupFrontendAuthTest(t)

		// Create an inactive user
		inactivePassword := services.HashPassword("password")
		inactiveStatus := "inactive"
		inactiveUser := models.User{
			Email:    "<EMAIL>",
			Password: inactivePassword,
			Name:     "Inactive User",
			UserType: "client",
			Status:   &inactiveStatus,
			ClientId: &client.Id,
		}
		config.DB.Create(&inactiveUser)

		loginData := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "password",
		}

		jsonData, _ := json.Marshal(loginData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/login", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		// Should still work as status is not checked in login
		assert.Equal(t, http.StatusOK, w.Code)

		// Clean up
		config.DB.Where("email = ?", "<EMAIL>").Delete(&models.User{})
	})
}
