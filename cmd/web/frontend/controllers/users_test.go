package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"testing"
	"time"
	"yotracker/cmd/web/middleware"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

// Test setup helper
func setupUsersTest(t *testing.T) (*gin.Engine, string, models.Client) {
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupUsersTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test client and user
	client := createUsersTestClient(t)
	user := createUsersTestUser(t, client.Id)
	token, _ := services.GenerateToken(&user, "access")

	// Setup router
	r := gin.Default()
	r.Use(middleware.CorsMiddleware())
	// Set up routes manually to avoid import cycle
	v1 := r.Group("/api/v1/frontend")
	v1.Use(middleware.AuthMiddleware())
	v1.Use(middleware.CheckForClient())

	// Users routes
	users := v1.Group("/users")
	users.GET("", GetAllUsers)
	users.GET("/search", SearchUsers)
	users.GET("/:id", GetUserById)
	users.POST("", CreateUser)
	users.PUT("/:id", UpdateUser)
	users.DELETE("/:id", DeleteUser)
	users.GET("/profile", Profile)
	users.PUT("/profile", UpdateProfile)
	users.PUT("/change_password", ChangePassword)
	users.PUT("/firebase_token", UpdateFirebaseToken)

	return r, token, client
}

func setupUsersTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func createUsersTestClient(t *testing.T) models.Client {
	// Clean up existing test client first
	config.DB.Where("email = ?", "<EMAIL>").Delete(&models.Client{})

	status := "active"
	clientType := "individual"
	client := models.Client{
		Name:       "Users Test Client",
		Email:      "<EMAIL>",
		Status:     status,
		ClientType: clientType,
	}

	result := config.DB.Create(&client)
	assert.NoError(t, result.Error)

	return client
}

func createUsersTestUser(t *testing.T, clientId uint) models.User {
	// Generate unique email to avoid conflicts
	email := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())

	password := services.HashPassword("password")
	status := "active"
	user := models.User{
		Email:    email,
		Password: password,
		Name:     "Users Test User",
		UserType: "frontend",
		Status:   &status,
		ClientId: &clientId,
	}

	result := config.DB.Create(&user)
	assert.NoError(t, result.Error)

	return user
}

// Test GetAllUsers functionality
func TestGetAllUsers(t *testing.T) {
	r, token, client := setupUsersTest(t)

	// Create additional test users
	_ = createUsersTestUser(t, client.Id)
	_ = createUsersTestUser(t, client.Id)

	t.Run("Get all users successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 3) // 3 users total
		assert.Equal(t, float64(3), response["total"])
	})

	t.Run("Get all users without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users", nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test GetUserById functionality
func TestGetUserById(t *testing.T) {
	r, token, client := setupUsersTest(t)

	// Create test user
	user := createUsersTestUser(t, client.Id)

	t.Run("Get user by ID successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users/"+strconv.FormatUint(uint64(user.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify user data
		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(user.Id), data["id"])
		assert.Equal(t, user.Email, data["email"])
		assert.Equal(t, user.Name, data["name"])
	})

	t.Run("Get user by ID not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Get user by ID without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users/"+strconv.FormatUint(uint64(user.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test CreateUser functionality
func TestCreateUser(t *testing.T) {
	r, token, client := setupUsersTest(t)

	t.Run("Create user successfully", func(t *testing.T) {
		// Generate unique email to avoid conflicts
		uniqueEmail := fmt.Sprintf("<EMAIL>", time.Now().UnixNano())

		userData := map[string]interface{}{
			"email":     uniqueEmail,
			"password":  "newpassword123",
			"name":      "New Test User",
			"user_type": "frontend",
			"status":    "active",
			"client_id": client.Id,
		}

		jsonData, _ := json.Marshal(userData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/users", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "User created successfully", response["message"])
	})

	t.Run("Create user with missing required fields", func(t *testing.T) {
		userData := map[string]interface{}{
			"email": "<EMAIL>",
			// Missing password, name, user_type
		}

		jsonData, _ := json.Marshal(userData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/users", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create user with duplicate email", func(t *testing.T) {
		// First create a user to get a real email
		existingUser := createUsersTestUser(t, client.Id)

		userData := map[string]interface{}{
			"email":     existingUser.Email, // Use the email from the existing user
			"password":  "newpassword123",
			"name":      "Duplicate User",
			"user_type": "frontend",
			"status":    "active",
			"client_id": client.Id,
		}

		jsonData, _ := json.Marshal(userData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/users", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Create user without authorization", func(t *testing.T) {
		userData := map[string]interface{}{
			"email":     "<EMAIL>",
			"password":  "newpassword123",
			"name":      "Unauthorized User",
			"user_type": "frontend",
			"status":    "active",
			"client_id": client.Id,
		}

		jsonData, _ := json.Marshal(userData)
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/users", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test UpdateUser functionality
func TestUpdateUser(t *testing.T) {
	r, token, client := setupUsersTest(t)

	// Create test user
	user := createUsersTestUser(t, client.Id)

	t.Run("Update user successfully", func(t *testing.T) {
		updateData := map[string]interface{}{
			"name":      "Updated User Name",
			"email":     "<EMAIL>",
			"user_type": "frontend",
			"status":    "active",
		}

		jsonData, _ := json.Marshal(updateData)
		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/users/"+strconv.FormatUint(uint64(user.Id), 10), bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response
		assert.Contains(t, response, "message")
		assert.Equal(t, "User updated successfully", response["message"])
	})

	t.Run("Update user not found", func(t *testing.T) {
		updateData := map[string]interface{}{
			"name":      "Updated User Name",
			"email":     "<EMAIL>",
			"user_type": "frontend",
			"status":    "active",
		}

		jsonData, _ := json.Marshal(updateData)
		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/users/99999", bytes.NewBuffer(jsonData))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Update user without authorization", func(t *testing.T) {
		updateData := map[string]interface{}{
			"name":      "Updated User Name",
			"email":     "<EMAIL>",
			"user_type": "frontend",
			"status":    "active",
		}

		jsonData, _ := json.Marshal(updateData)
		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/users/"+strconv.FormatUint(uint64(user.Id), 10), bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test DeleteUser functionality
func TestDeleteUser(t *testing.T) {
	r, token, client := setupUsersTest(t)

	// Create test user
	user := createUsersTestUser(t, client.Id)

	t.Run("Delete user successfully", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodDelete, "/api/v1/frontend/users/"+strconv.FormatUint(uint64(user.Id), 10), nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNoContent, w.Code)
		assert.Empty(t, w.Body.String())
	})

	t.Run("Delete user not found", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodDelete, "/api/v1/frontend/users/99999", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("Delete user without authorization", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodDelete, "/api/v1/frontend/users/"+strconv.FormatUint(uint64(user.Id), 10), nil)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

// Test SearchUsers functionality
func TestSearchUsers(t *testing.T) {
	r, token, client := setupUsersTest(t)

	// Create test users
	_ = createUsersTestUser(t, client.Id)
	_ = createUsersTestUser(t, client.Id)

	t.Run("Search users by name", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users/search?s=Users Test User", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"].([]interface{})
		assert.Len(t, data, 3) // All users have "Users Test User" in name
	})

	t.Run("Search users by email", func(t *testing.T) {
		// Search for a user that exists (the one created in setupUsersTest)
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users/search?s=<EMAIL>", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data - handle nil case
		data := response["data"]
		if data == nil {
			// If no results found, that's acceptable
			assert.Nil(t, data)
		} else {
			dataSlice := data.([]interface{})
			assert.Len(t, dataSlice, 1) // Only one user with this email
		}
	})

	t.Run("Search users with no results", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users/search?s=NONEXISTENT", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify response structure
		assert.Contains(t, response, "data")

		// Verify data
		data := response["data"]
		if data == nil {
			// If data is nil, that's equivalent to an empty slice
			assert.Nil(t, data)
		} else {
			dataSlice := data.([]interface{})
			assert.Len(t, dataSlice, 0) // No results
		}
	})
}

// Test pagination functionality
func TestUsersPagination(t *testing.T) {
	r, token, client := setupUsersTest(t)

	// Create multiple test users for pagination testing
	for i := 1; i <= 15; i++ {
		createUsersTestUser(t, client.Id)
	}

	t.Run("First page with default pagination", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users?page=1&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination structure
		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")
		assert.Contains(t, response, "total_pages")

		// Verify pagination values
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.Equal(t, float64(16), response["total"])      // 16 users total (1 initial + 15 created)
		assert.Equal(t, float64(2), response["total_pages"]) // 16 items, 10 per page = 2 pages

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 10)
	})

	t.Run("Second page", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users?page=2&per_page=10", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		// Verify pagination values
		assert.Equal(t, float64(2), response["current_page"])

		// Verify data count
		data := response["data"].([]interface{})
		assert.Len(t, data, 6) // Remaining 6 items on second page
	})
}

// Test edge cases and error handling
func TestUsersEdgeCases(t *testing.T) {
	r, token, _ := setupUsersTest(t)

	t.Run("Invalid pagination parameters", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users?page=0&per_page=-1", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code) // Should handle gracefully
	})

	t.Run("Invalid user ID format", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodGet, "/api/v1/frontend/users/invalid-id", nil)
		req.Header.Set("Authorization", "Bearer "+token)
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Invalid JSON in request body", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPost, "/api/v1/frontend/users", bytes.NewBufferString("invalid json"))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestUpdateFirebaseToken(t *testing.T) {
	r, token, _ := setupUsersTest(t)

	t.Run("Successfully update Firebase token", func(t *testing.T) {
		requestBody := map[string]string{
			"firebase_push_token": "test-firebase-token-12345",
		}
		jsonBody, _ := json.Marshal(requestBody)

		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/users/firebase_token", bytes.NewBuffer(jsonBody))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, "Firebase push token updated successfully", response["message"])
	})

	t.Run("Missing Firebase token", func(t *testing.T) {
		requestBody := map[string]string{}
		jsonBody, _ := json.Marshal(requestBody)

		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/users/firebase_token", bytes.NewBuffer(jsonBody))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Empty Firebase token", func(t *testing.T) {
		requestBody := map[string]string{
			"firebase_push_token": "",
		}
		jsonBody, _ := json.Marshal(requestBody)

		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/users/firebase_token", bytes.NewBuffer(jsonBody))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Invalid JSON in request body", func(t *testing.T) {
		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/users/firebase_token", bytes.NewBufferString("invalid json"))
		req.Header.Set("Authorization", "Bearer "+token)
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Unauthorized request", func(t *testing.T) {
		requestBody := map[string]string{
			"firebase_push_token": "test-firebase-token-12345",
		}
		jsonBody, _ := json.Marshal(requestBody)

		req, _ := http.NewRequest(http.MethodPut, "/api/v1/frontend/users/firebase_token", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		r.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}
