package controllers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestSearchSupportTickets(t *testing.T) {
	// Setup test database
	services.FastCleanupTestData()

	// Clean up any existing test data
	config.DB.Exec("DELETE FROM support_tickets WHERE subject LIKE '%test%'")
	config.DB.Exec("DELETE FROM users WHERE email LIKE '%test%'")
	config.DB.Exec("DELETE FROM clients WHERE email LIKE '%test%'")

	// Create test data
	client := models.Client{
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Address:     stringPtr("Test Address"),
	}
	config.DB.Create(&client)

	user := models.User{
		Name:     "Test User",
		Email:    "<EMAIL>",
		Password: "password",
		ClientId: &client.Id,
		UserType: "client",
	}
	config.DB.Create(&user)

	// Create test tickets
	ticket1 := models.SupportTicket{
		ClientId:    client.Id,
		CreatedById: user.Id,
		Subject:     "Test ticket with xyz123abc",
		Description: "This ticket contains xyz123abc",
		Department:  "technical",
		Priority:    "medium",
		Status:      "open",
	}
	config.DB.Create(&ticket1)

	ticket2 := models.SupportTicket{
		ClientId:    client.Id,
		CreatedById: user.Id,
		Subject:     "Another ticket",
		Description: "This ticket does not contain the search term",
		Department:  "billing",
		Priority:    "high",
		Status:      "open",
	}
	config.DB.Create(&ticket2)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("user", user)
		c.Next()
	})
	router.GET("/search", SearchSupportTickets)

	t.Run("Search with s parameter", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/search?s=xyz123abc", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "search_query")
		assert.Equal(t, "xyz123abc", response["search_query"])

		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Should find only ticket1
	})

	t.Run("Search with s parameter and filters", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/search?s=xyz123abc&department=technical&priority=medium", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "search_query")
		assert.Equal(t, "xyz123abc", response["search_query"])

		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Should find only ticket1 with filters
	})

	t.Run("Search with s parameter and pagination", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/search?s=xyz123abc&page=1&per_page=10", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")
		assert.Contains(t, response, "last_page")
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
	})

	t.Run("Search without s parameter should fail", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/search", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
		assert.Equal(t, "Search query is required", response["error"])
	})

	t.Run("Search with empty s parameter should fail", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/search?s=", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
		assert.Equal(t, "Search query is required", response["error"])
	})
}

func TestGetAllSupportTicketsWithSearch(t *testing.T) {
	// Setup test database
	services.FastCleanupTestData()

	// Clean up any existing test data
	config.DB.Exec("DELETE FROM support_tickets WHERE subject LIKE '%test%'")
	config.DB.Exec("DELETE FROM users WHERE email LIKE '%test%'")
	config.DB.Exec("DELETE FROM clients WHERE email LIKE '%test%'")

	// Create test data
	client := models.Client{
		Name:        "Test Client",
		Email:       "<EMAIL>",
		PhoneNumber: "1234567890",
		Address:     stringPtr("Test Address"),
	}
	config.DB.Create(&client)

	user := models.User{
		Name:     "Test User",
		Email:    "<EMAIL>",
		Password: "password",
		ClientId: &client.Id,
		UserType: "client",
	}
	config.DB.Create(&user)

	// Create test tickets
	ticket1 := models.SupportTicket{
		ClientId:    client.Id,
		CreatedById: user.Id,
		Subject:     "Test ticket with xyz123abc",
		Description: "This ticket contains xyz123abc",
		Department:  "technical",
		Priority:    "medium",
		Status:      "open",
	}
	config.DB.Create(&ticket1)

	ticket2 := models.SupportTicket{
		ClientId:    client.Id,
		CreatedById: user.Id,
		Subject:     "Another ticket",
		Description: "This ticket does not contain the search term",
		Department:  "billing",
		Priority:    "high",
		Status:      "open",
	}
	config.DB.Create(&ticket2)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.Use(func(c *gin.Context) {
		c.Set("user", user)
		c.Next()
	})
	router.GET("/tickets", GetAllSupportTickets)

	t.Run("Get all tickets without search", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/tickets", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.NotContains(t, response, "search_query")

		data := response["data"].([]interface{})
		assert.Len(t, data, 2) // Should find both tickets
	})

	t.Run("Get tickets with search parameter", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/tickets?s=xyz123abc", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "search_query")
		assert.Equal(t, "xyz123abc", response["search_query"])

		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Should find only ticket1
	})

	t.Run("Get tickets with search and filters", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/tickets?s=xyz123abc&department=technical&priority=medium", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "search_query")
		assert.Equal(t, "xyz123abc", response["search_query"])

		data := response["data"].([]interface{})
		assert.Len(t, data, 1) // Should find only ticket1 with filters
	})

	t.Run("Get tickets with search and pagination", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/tickets?s=xyz123abc&page=1&per_page=10", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Contains(t, response, "data")
		assert.Contains(t, response, "total")
		assert.Contains(t, response, "current_page")
		assert.Contains(t, response, "per_page")
		assert.Contains(t, response, "last_page")
		assert.Contains(t, response, "search_query")
		assert.Equal(t, float64(1), response["current_page"])
		assert.Equal(t, float64(10), response["per_page"])
		assert.Equal(t, "xyz123abc", response["search_query"])
	})
}
