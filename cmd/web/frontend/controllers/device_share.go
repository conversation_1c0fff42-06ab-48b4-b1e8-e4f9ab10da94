package controllers

import (
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

func CreateDeviceShareToken(c *gin.Context) {
	var req models.CreateDeviceShareTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Get device ID from URL parameter
	deviceIdParam := c.Param("id")
	deviceId, err := strconv.ParseUint(deviceIdParam, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid device ID",
		})
		return
	}

	// Verify the device exists and belongs to the user's client
	var clientDevice models.ClientDevice
	userInterface, _ := c.Get("user")
	user := userInterface.(models.User)
	if err := config.DB.Where("id = ? AND client_id = ?", deviceId, user.ClientId).First(&clientDevice).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Device not found or access denied",
		})
		return
	}

	// Validate expiration date is in the future
	if req.ExpiresAt.Before(time.Now()) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Expiration date must be in the future",
		})
		return
	}

	// Generate the share token
	shareToken, err := services.GenerateDeviceShareToken(
		uint(deviceId),
		clientDevice.DeviceId,
		user.Id,
		req.ExpiresAt,
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate share token",
		})
		return
	}

	// Generate share URL
	baseURL := os.Getenv("APP_URL")
	if baseURL == "" {
		baseURL = "http://localhost:8080"
	}
	shareUrl := fmt.Sprintf("%s/temp_location?token=%s", baseURL, shareToken)

	// Prepare response (no DB fields)
	response := gin.H{
		"client_device_id": uint(deviceId),
		"device_name":      *clientDevice.Name,
		"token":            shareToken,
		"expires_at":       req.ExpiresAt,
		"share_url":        shareUrl,
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Device share token created successfully",
		"data":    response,
	})
}
