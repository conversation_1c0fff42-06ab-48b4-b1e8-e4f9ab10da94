package controllers

import (
	"testing"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
)

// TestDirectReportGeneration tests reports directly via the service
func TestDirectReportGeneration(t *testing.T) {
	// Use the test database and seed it with reports
	services.FastCleanupTestData()

	// Define reports to test by their report_type (more reliable than hardcoded IDs)
	reportsToTest := []struct {
		ReportType string
		Name       string
	}{
		{"fuel_consumption_analysis", "Fuel Consumption Analysis"},
		{"driver_safety_scorecard", "Driver Safety Scorecard"},
		{"fuel_efficiency_trends", "Fuel Efficiency Trends"},
	}

	for _, report := range reportsToTest {
		t.Run(report.ReportType, func(t *testing.T) {
			testDirectReport(t, report.ReportType, report.Name)
		})
	}
}

func testDirectReport(t *testing.T, reportType, reportName string) {
	// Find the report by report_type
	var report models.Report
	err := config.DB.Where("report_type = ? AND status = ?", reportType, "active").First(&report).Error
	if err != nil {
		t.Logf("❌ Report %s (%s) not found: %v", reportType, reportName, err)
		return
	}

	// Create filters
	filters := models.ReportFilters{
		StartDate: func() *time.Time { t := time.Now().AddDate(0, 0, -7); return &t }(),
		EndDate:   func() *time.Time { t := time.Now(); return &t }(),
		ClientId:  1, // Use client ID 1
		PerPage:   10,
		Page:      1,
	}

	// Create report service
	reportService := services.NewReportService()

	// Generate report
	reportData, err := reportService.GenerateReport(report.Id, filters, "json")

	if err != nil {
		t.Logf("❌ Report %d (%s) failed: %v", report.Id, reportName, err)
		return
	}

	t.Logf("✅ Report %d (%s) generated successfully", report.Id, reportName)

	// Log basic report info
	if reportData != nil && reportData.Data != nil {
		if dataSlice, ok := reportData.Data.([]interface{}); ok {
			t.Logf("   Records: %d", len(dataSlice))
		}
	}
}
