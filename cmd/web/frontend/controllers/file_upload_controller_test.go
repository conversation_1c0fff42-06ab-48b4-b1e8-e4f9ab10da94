package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"strconv"
	"testing"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestFrontendFileUploadControllers(t *testing.T) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Initialize test database
	services.FastCleanupTestData()

	// Clean up any existing data (order matters due to foreign key constraints)
	config.DB.Exec("DELETE FROM file_uploads")
	config.DB.Exec("DELETE FROM support_ticket_replies")
	config.DB.Exec("DELETE FROM support_tickets")
	config.DB.Exec("DELETE FROM geofence_events")
	config.DB.Exec("DELETE FROM geofences")
	config.DB.Exec("DELETE FROM driver_device_assignments")
	config.DB.Exec("DELETE FROM users")
	config.DB.Exec("DELETE FROM drivers")
	config.DB.Exec("DELETE FROM clients")

	// Create test data with unique emails
	timestamp := time.Now().UnixNano()
	client := models.Client{
		Name:        "Test Client",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber: "1234567890",
		Status:      "active",
	}
	config.DB.Create(&client)

	clientUser := models.User{
		Name:     "Client User",
		Email:    fmt.Sprintf("<EMAIL>", timestamp),
		Password: "password123",
		UserType: "frontend",
		ClientId: &client.Id,
	}
	config.DB.Create(&clientUser)

	otherClient := models.Client{
		Name:        "Other Client",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber: "0987654321",
		Status:      "active",
	}
	config.DB.Create(&otherClient)

	otherClientUser := models.User{
		Name:     "Other Client User",
		Email:    fmt.Sprintf("<EMAIL>", timestamp),
		Password: "password123",
		UserType: "frontend",
		ClientId: &otherClient.Id,
	}
	config.DB.Create(&otherClientUser)

	t.Run("UploadFile", func(t *testing.T) {
		t.Skip("Skipping file upload test due to file system permissions")

		// Create multipart form data
		body := &bytes.Buffer{}
		writer := multipart.NewWriter(body)

		// Add file
		part, err := writer.CreateFormFile("file", "test.txt")
		assert.NoError(t, err)
		_, err = io.WriteString(part, "This is a test file")
		assert.NoError(t, err)

		// Add optional description
		writer.WriteField("description", "Test file description")
		writer.Close()

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user", clientUser)
		c.Request = httptest.NewRequest("POST", "/api/v1/frontend/files/upload", body)
		c.Request.Header.Set("Content-Type", writer.FormDataContentType())

		UploadFile(c)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Equal(t, "File uploaded successfully", response["message"])

		data := response["data"].(map[string]interface{})
		assert.Equal(t, "test.txt", data["original_name"])
		assert.Equal(t, "document", data["file_type"])
		assert.Equal(t, float64(client.Id), data["client_id"]) // Should be set to client's ID

		// Clean up uploaded file
		fileName := data["file_name"].(string)
		filePath := filepath.Join("/var/www/yotrackermedia", fileName)
		os.Remove(filePath)
	})

	t.Run("UploadFileNoFile", func(t *testing.T) {
		t.Skip("Skipping file upload test due to file system permissions")

		body := &bytes.Buffer{}
		writer := multipart.NewWriter(body)
		writer.WriteField("description", "No file provided")
		writer.Close()

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user", clientUser)
		c.Request = httptest.NewRequest("POST", "/api/v1/frontend/files/upload", body)
		c.Request.Header.Set("Content-Type", writer.FormDataContentType())

		UploadFile(c)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("UploadFileUnauthorized", func(t *testing.T) {
		t.Skip("Skipping file upload test due to file system permissions")

		body := &bytes.Buffer{}
		writer := multipart.NewWriter(body)
		part, err := writer.CreateFormFile("file", "test.txt")
		assert.NoError(t, err)
		_, err = io.WriteString(part, "Test content")
		assert.NoError(t, err)
		writer.Close()

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Request = httptest.NewRequest("POST", "/api/v1/frontend/files/upload", body)
		c.Request.Header.Set("Content-Type", writer.FormDataContentType())

		UploadFile(c)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("UploadFileNoClientAccess", func(t *testing.T) {
		t.Skip("Skipping file upload test due to file system permissions")

		// Create user without client access
		noClientUser := models.User{
			Name:     "No Client User",
			Email:    "<EMAIL>",
			Password: "password123",
			UserType: "frontend",
			ClientId: nil,
		}
		config.DB.Create(&noClientUser)

		body := &bytes.Buffer{}
		writer := multipart.NewWriter(body)
		part, err := writer.CreateFormFile("file", "test.txt")
		assert.NoError(t, err)
		_, err = io.WriteString(part, "Test content")
		assert.NoError(t, err)
		writer.Close()

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user", noClientUser)
		c.Request = httptest.NewRequest("POST", "/api/v1/frontend/files/upload", body)
		c.Request.Header.Set("Content-Type", writer.FormDataContentType())

		UploadFile(c)

		assert.Equal(t, http.StatusForbidden, w.Code)
	})

	t.Run("GetFileUploads", func(t *testing.T) {
		// Create test files for the client
		file1 := models.FileUpload{
			ClientId:     &client.Id,
			CreatedById:  clientUser.Id,
			FileName:     "test1.txt",
			OriginalName: "test1.txt",
			FileSize:     100,
			MimeType:     "text/plain",
			FileUrl:      "/media/test1.txt",
			FilePath:     "/var/www/yotrackermedia/test1.txt",
			FileType:     "document",
		}
		config.DB.Create(&file1)

		file2 := models.FileUpload{
			ClientId:     &client.Id,
			CreatedById:  clientUser.Id,
			FileName:     "test2.jpg",
			OriginalName: "test2.jpg",
			FileSize:     200,
			MimeType:     "image/jpeg",
			FileUrl:      "/media/test2.jpg",
			FilePath:     "/var/www/yotrackermedia/test2.jpg",
			FileType:     "image",
		}
		config.DB.Create(&file2)

		// Create file for other client (should not be visible)
		otherFile := models.FileUpload{
			ClientId:     &otherClient.Id,
			CreatedById:  otherClientUser.Id,
			FileName:     "other.txt",
			OriginalName: "other.txt",
			FileSize:     150,
			MimeType:     "text/plain",
			FileUrl:      "/media/other.txt",
			FilePath:     "/var/www/yotrackermedia/other.txt",
			FileType:     "document",
		}
		config.DB.Create(&otherFile)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user", clientUser)
		c.Request = httptest.NewRequest("GET", "/api/v1/frontend/files", nil)

		GetFileUploads(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].([]interface{})
		assert.Equal(t, 2, len(data)) // Should only see client's files

		// Test with file type filter
		w = httptest.NewRecorder()
		c, _ = gin.CreateTestContext(w)
		c.Set("user", clientUser)
		c.Request = httptest.NewRequest("GET", "/api/v1/frontend/files?file_type=image", nil)

		GetFileUploads(c)

		assert.Equal(t, http.StatusOK, w.Code)

		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data = response["data"].([]interface{})
		assert.Equal(t, 1, len(data)) // Should only see image files
		assert.Equal(t, "image", data[0].(map[string]interface{})["file_type"])
	})

	t.Run("GetFileUploadById", func(t *testing.T) {
		// Create a test file for the client
		file := models.FileUpload{
			ClientId:     &client.Id,
			CreatedById:  clientUser.Id,
			FileName:     "test3.txt",
			OriginalName: "test3.txt",
			FileSize:     150,
			MimeType:     "text/plain",
			FileUrl:      "/media/test3.txt",
			FilePath:     "/var/www/yotrackermedia/test3.txt",
			FileType:     "document",
		}
		config.DB.Create(&file)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user", clientUser)
		c.Params = gin.Params{{Key: "id", Value: strconv.FormatUint(uint64(file.Id), 10)}}

		GetFileUploadById(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		data := response["data"].(map[string]interface{})
		assert.Equal(t, float64(file.Id), data["id"])
		assert.Equal(t, "test3.txt", data["original_name"])
	})

	t.Run("GetFileUploadByIdOtherClient", func(t *testing.T) {
		// Create a test file for other client
		file := models.FileUpload{
			ClientId:     &otherClient.Id,
			CreatedById:  otherClientUser.Id,
			FileName:     "other2.txt",
			OriginalName: "other2.txt",
			FileSize:     150,
			MimeType:     "text/plain",
			FileUrl:      "/media/other2.txt",
			FilePath:     "/var/www/yotrackermedia/other2.txt",
			FileType:     "document",
		}
		config.DB.Create(&file)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user", clientUser)
		c.Params = gin.Params{{Key: "id", Value: strconv.FormatUint(uint64(file.Id), 10)}}

		GetFileUploadById(c)

		assert.Equal(t, http.StatusNotFound, w.Code) // Should not be able to access other client's file
	})

	t.Run("DeleteFileUpload", func(t *testing.T) {
		t.Skip("Skipping file upload test due to file system permissions")

		// Create a test file for the client
		file := models.FileUpload{
			ClientId:     &client.Id,
			CreatedById:  clientUser.Id,
			FileName:     "test4.txt",
			OriginalName: "test4.txt",
			FileSize:     100,
			MimeType:     "text/plain",
			FileUrl:      "/media/test4.txt",
			FilePath:     "/var/www/yotrackermedia/test4.txt",
			FileType:     "document",
		}
		config.DB.Create(&file)

		// Create the physical file
		filePath := filepath.Join("/var/www/yotrackermedia", file.FileName)
		os.MkdirAll(filepath.Dir(filePath), 0755)
		err := os.WriteFile(filePath, []byte("test content"), 0644)
		assert.NoError(t, err)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user", clientUser)
		c.Params = gin.Params{{Key: "id", Value: strconv.FormatUint(uint64(file.Id), 10)}}

		DeleteFileUpload(c)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		err = json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)

		assert.Equal(t, "File deleted successfully", response["message"])

		// Verify file was deleted from database
		var deletedFile models.FileUpload
		err = config.DB.First(&deletedFile, file.Id).Error
		assert.Error(t, err) // Should not find the file

		// Verify physical file was deleted
		_, err = os.Stat(filePath)
		assert.True(t, os.IsNotExist(err))
	})

	t.Run("DeleteFileUploadOtherClient", func(t *testing.T) {
		t.Skip("Skipping file upload test due to file system permissions")

		// Create a test file for other client
		file := models.FileUpload{
			ClientId:     &otherClient.Id,
			CreatedById:  otherClientUser.Id,
			FileName:     "other3.txt",
			OriginalName: "other3.txt",
			FileSize:     100,
			MimeType:     "text/plain",
			FileUrl:      "/media/other3.txt",
			FilePath:     "/var/www/yotrackermedia/other3.txt",
			FileType:     "document",
		}
		config.DB.Create(&file)

		w := httptest.NewRecorder()
		c, _ := gin.CreateTestContext(w)
		c.Set("user", clientUser)
		c.Params = gin.Params{{Key: "id", Value: strconv.FormatUint(uint64(file.Id), 10)}}

		DeleteFileUpload(c)

		assert.Equal(t, http.StatusInternalServerError, w.Code) // Should not be able to delete other client's file
	})
}
