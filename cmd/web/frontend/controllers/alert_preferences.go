package controllers

import (
	"net/http"
	"strconv"

	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
)

// GetClientAlertPreferences retrieves alert preferences for the authenticated client
func GetClientAlertPreferences(c *gin.Context) {
	clientId, exists := c.Get("client_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Client not authenticated",
		})
		return
	}

	var preferences []models.AlertPreference
	if err := config.DB.Where("client_id = ?", clientId).Preload("Client").Find(&preferences).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to retrieve alert preferences",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert preferences retrieved successfully",
		"data":    preferences,
	})
}

// GetClientAlertPreferenceById retrieves a specific alert preference by ID for the authenticated client
func GetClientAlertPreferenceById(c *gin.Context) {
	clientId, exists := c.Get("client_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Client not authenticated",
		})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid alert preference ID",
		})
		return
	}

	var preference models.AlertPreference
	if err := config.DB.Where("id = ? AND client_id = ?", id, clientId).Preload("Client").First(&preference).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert preference not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert preference retrieved successfully",
		"data":    preference,
	})
}

// CreateClientAlertPreference creates a new alert preference for the authenticated client
func CreateClientAlertPreference(c *gin.Context) {
	clientId, exists := c.Get("client_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Client not authenticated",
		})
		return
	}

	var req models.AlertPreferenceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	preference := models.AlertPreference{
		ClientId:  clientId.(uint),
		AlertType: req.AlertType,
		Enabled:   req.Enabled,
		Channels:  req.Channels,
		Priority:  req.Priority,
	}

	if err := config.DB.Create(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to create alert preference",
			"error":   err.Error(),
		})
		return
	}

	// Reload with client data
	config.DB.Preload("Client").First(&preference, preference.Id)

	c.JSON(http.StatusCreated, gin.H{
		"message": "Alert preference created successfully",
		"data":    preference,
	})
}

// UpdateClientAlertPreference updates an existing alert preference for the authenticated client
func UpdateClientAlertPreference(c *gin.Context) {
	clientId, exists := c.Get("client_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Client not authenticated",
		})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid alert preference ID",
		})
		return
	}

	var req models.AlertPreferenceRequest
	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid request data",
			"error":   err.Error(),
		})
		return
	}

	var preference models.AlertPreference
	if err := config.DB.Where("id = ? AND client_id = ?", id, clientId).First(&preference).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert preference not found",
		})
		return
	}

	// Update fields
	preference.AlertType = req.AlertType
	preference.Enabled = req.Enabled
	preference.Channels = req.Channels
	preference.Priority = req.Priority

	if err := config.DB.Save(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to update alert preference",
			"error":   err.Error(),
		})
		return
	}

	// Reload with client data
	config.DB.Preload("Client").First(&preference, preference.Id)

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert preference updated successfully",
		"data":    preference,
	})
}

// DeleteClientAlertPreference deletes an alert preference for the authenticated client
func DeleteClientAlertPreference(c *gin.Context) {
	clientId, exists := c.Get("client_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"message": "Client not authenticated",
		})
		return
	}

	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid alert preference ID",
		})
		return
	}

	var preference models.AlertPreference
	if err := config.DB.Where("id = ? AND client_id = ?", id, clientId).First(&preference).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Alert preference not found",
		})
		return
	}

	if err := config.DB.Delete(&preference).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": "Failed to delete alert preference",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Alert preference deleted successfully",
	})
}
