package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupDeviceTypesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func TestGetAllDeviceTypes(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupDeviceTypesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test device types
	config.DB.Where("name IN (?)", []string{"Test Device Type 1", "Test Device Type 2"}).Delete(&models.DeviceType{})

	// Get existing protocol from seeded data
	var protocol models.Protocol
	if err := config.DB.First(&protocol).Error; err != nil {
		t.Fatalf("Failed to get protocol: %v", err)
	}

	// Create test device types with valid protocol_id
	deviceType1 := models.DeviceType{
		Name:       "Test Device Type 1",
		Active:     true,
		ProtocolId: protocol.Id,
	}
	deviceType2 := models.DeviceType{
		Name:       "Test Device Type 2",
		Active:     true,
		ProtocolId: protocol.Id,
	}
	config.DB.Create(&deviceType1)
	config.DB.Create(&deviceType2)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("", GetAllDeviceTypes)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/frontend/device_types", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})

	// Get the actual count of device types in the database
	var countBefore int64
	config.DB.Model(&models.DeviceType{}).Count(&countBefore)

	// Assert that the response data length matches the database count
	assert.Len(t, data, int(countBefore)) // Dynamic count based on actual database state
}

func TestGetDeviceTypeById(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupDeviceTypesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test device type
	config.DB.Where("name = ?", "Test Device Type").Delete(&models.DeviceType{})

	// Get existing protocol from seeded data
	var protocol models.Protocol
	if err := config.DB.First(&protocol).Error; err != nil {
		t.Fatalf("Failed to get protocol: %v", err)
	}

	// Create test device type with valid protocol_id
	deviceType := models.DeviceType{
		Name:       "Test Device Type",
		Active:     true,
		ProtocolId: protocol.Id,
	}
	config.DB.Create(&deviceType)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("/:id", GetDeviceTypeById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/frontend/device_types/%d", deviceType.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Device Type", data["name"])
}

func TestGetDeviceTypeByIdNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("/:id", GetDeviceTypeById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/frontend/device_types/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Device type not found", response["message"])
}

func TestCreateDeviceType(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupDeviceTypesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.POST("", CreateDeviceType)

	// Test data
	deviceTypeData := models.DeviceTypeRequest{
		Name:       "New Test Device Type",
		ProtocolId: 1, // Use a valid protocol ID
		Active:     true,
	}

	jsonData, _ := json.Marshal(deviceTypeData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/frontend/device_types", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Device type created successfully", response["message"])

	// Verify device type was created in database
	var deviceType models.DeviceType
	err = config.DB.Where("name = ?", "New Test Device Type").First(&deviceType).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Device Type", deviceType.Name)
}

func TestCreateDeviceTypeInvalidRequest(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.POST("", CreateDeviceType)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Device Type", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/frontend/device_types", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateDeviceType(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupDeviceTypesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test device type
	config.DB.Where("name = ?", "Original Device Type").Delete(&models.DeviceType{})

	// Create test device type
	deviceType := models.DeviceType{
		Name:       "Original Device Type",
		ProtocolId: 1, // Use a valid protocol ID
		Active:     true,
	}
	config.DB.Create(&deviceType)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.PUT("/:id", UpdateDeviceType)

	// Test data
	updateData := models.DeviceTypeRequest{
		Name:       "Updated Device Type",
		ProtocolId: 1, // Use a valid protocol ID
		Active:     false,
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/frontend/device_types/%d", deviceType.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Device type updated successfully", response["message"])

	// Verify device type was updated in database
	var updatedDeviceType models.DeviceType
	err = config.DB.First(&updatedDeviceType, deviceType.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "Updated Device Type", updatedDeviceType.Name)
}

func TestUpdateDeviceTypeNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.PUT("/:id", UpdateDeviceType)

	// Test data
	updateData := models.DeviceTypeRequest{
		Name:       "Updated Device Type",
		ProtocolId: 1, // Use a valid protocol ID
		Active:     true,
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/frontend/device_types/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Device type not found", response["message"])
}

func TestDeleteDeviceType(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupDeviceTypesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test device type
	config.DB.Where("name = ?", "Test Device Type to Delete").Delete(&models.DeviceType{})

	// Create test device type
	deviceType := models.DeviceType{
		Name:       "Test Device Type to Delete",
		ProtocolId: 1, // Use a valid protocol ID
		Active:     true,
	}
	config.DB.Create(&deviceType)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.DELETE("/:id", DeleteDeviceType)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/frontend/device_types/%d", deviceType.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)

	// 204 No Content should not have a response body
	assert.Empty(t, w.Body.String())

	// Verify device type was deleted from database
	var deletedDeviceType models.DeviceType
	err := config.DB.First(&deletedDeviceType, deviceType.Id).Error
	assert.Error(t, err) // Should not find the device type
}

func TestDeleteDeviceTypeNotFound(t *testing.T) {
	// Setup test environment
	services.SetupTestEnvironment()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.DELETE("/:id", DeleteDeviceType)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/frontend/device_types/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Device type not found", response["message"])
}

func TestSearchDeviceTypes(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupDeviceTypesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test device types
	config.DB.Where("name IN (?)", []string{"Searchable Device Type One", "Another Device Type"}).Delete(&models.DeviceType{})

	// Create test device types
	deviceType1 := models.DeviceType{
		Name:       "Searchable Device Type One",
		ProtocolId: 1, // Use a valid protocol ID
		Active:     true,
	}
	deviceType2 := models.DeviceType{
		Name:       "Another Device Type",
		ProtocolId: 1, // Use a valid protocol ID
		Active:     true,
	}
	config.DB.Create(&deviceType1)
	config.DB.Create(&deviceType2)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("/search", SearchDeviceTypes)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/frontend/device_types/search?s=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 1) // Should only find "Searchable Device Type One"
}

func TestSearchDeviceTypesNoResults(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupDeviceTypesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test device type
	config.DB.Where("name = ?", "Test Device Type").Delete(&models.DeviceType{})

	// Create test device type
	deviceType := models.DeviceType{
		Name:       "Test Device Type",
		ProtocolId: 1, // Use a valid protocol ID
		Active:     true,
	}
	config.DB.Create(&deviceType)

	// Setup router
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	deviceTypes := v1.Group("/device_types")
	deviceTypes.GET("/search", SearchDeviceTypes)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/frontend/device_types/search?s=Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 0) // Should find no results
}
