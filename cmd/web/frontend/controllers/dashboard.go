package controllers

import (
	"context"
	"fmt"
	"net/http"
	"runtime"
	"time"
	"yotracker/config"
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/load"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/shirou/gopsutil/v3/net"
)

// ServerStats represents the server statistics response
type ServerStats struct {
	CPU     CPUStats     `json:"cpu"`
	Memory  MemoryStats  `json:"memory"`
	Storage StorageStats `json:"storage"`
	System  SystemStats  `json:"system"`
	Network NetworkStats `json:"network"`
	Runtime RuntimeStats `json:"runtime"`
}

// CPUStats represents CPU usage statistics
type CPUStats struct {
	UsagePercent  float64   `json:"usage_percent"`
	CoreCount     int       `json:"core_count"`
	LogicalCount  int       `json:"logical_count"`
	PerCoreUsage  []float64 `json:"per_core_usage"`
	LoadAverage1  float64   `json:"load_average_1m"`
	LoadAverage5  float64   `json:"load_average_5m"`
	LoadAverage15 float64   `json:"load_average_15m"`
}

// MemoryStats represents memory usage statistics
type MemoryStats struct {
	Total       uint64  `json:"total_bytes"`
	Used        uint64  `json:"used_bytes"`
	Available   uint64  `json:"available_bytes"`
	Free        uint64  `json:"free_bytes"`
	UsedPercent float64 `json:"used_percent"`
	TotalGB     float64 `json:"total_gb"`
	UsedGB      float64 `json:"used_gb"`
	AvailableGB float64 `json:"available_gb"`
}

// StorageStats represents storage usage statistics
type StorageStats struct {
	Total       uint64  `json:"total_bytes"`
	Used        uint64  `json:"used_bytes"`
	Free        uint64  `json:"free_bytes"`
	UsedPercent float64 `json:"used_percent"`
	TotalGB     float64 `json:"total_gb"`
	UsedGB      float64 `json:"used_gb"`
	FreeGB      float64 `json:"free_gb"`
	Path        string  `json:"path"`
}

// SystemStats represents general system statistics
type SystemStats struct {
	Hostname        string `json:"hostname"`
	Uptime          uint64 `json:"uptime_seconds"`
	UptimeFormatted string `json:"uptime_formatted"`
	OS              string `json:"os"`
	Platform        string `json:"platform"`
	Architecture    string `json:"architecture"`
	KernelVersion   string `json:"kernel_version"`
	ProcessCount    uint64 `json:"process_count"`
}

// NetworkStats represents network statistics
type NetworkStats struct {
	BytesSent     uint64 `json:"bytes_sent"`
	BytesReceived uint64 `json:"bytes_received"`
	PacketsSent   uint64 `json:"packets_sent"`
	PacketsRecv   uint64 `json:"packets_received"`
	ErrorsIn      uint64 `json:"errors_in"`
	ErrorsOut     uint64 `json:"errors_out"`
	DropIn        uint64 `json:"drop_in"`
	DropOut       uint64 `json:"drop_out"`
}

// RuntimeStats represents Go runtime statistics
type RuntimeStats struct {
	GoVersion  string `json:"go_version"`
	Goroutines int    `json:"goroutines"`
	HeapAlloc  uint64 `json:"heap_alloc_bytes"`
	HeapSys    uint64 `json:"heap_sys_bytes"`
	HeapInuse  uint64 `json:"heap_inuse_bytes"`
	StackInuse uint64 `json:"stack_inuse_bytes"`
	GCCycles   uint32 `json:"gc_cycles"`
	NextGC     uint64 `json:"next_gc_bytes"`
	LastGC     string `json:"last_gc_time"`
}
type DashboardStats struct {
	TotalDevices         uint64         `json:"total_devices"`
	TotalOnlineDevices   uint64         `json:"total_online_devices"`
	TotalOfflineDevices  uint64         `json:"total_offline_devices"`
	TotalMovingDevices   uint64         `json:"total_moving_devices"`
	TotalIdlingDevices   uint64         `json:"total_idling_devices"`
	TotalStoppedDevices  uint64         `json:"total_stopped_devices"`
	TotalUsers           uint64         `json:"total_users"`
	TotalActiveUsers     uint64         `json:"total_active_users"`
	TotalInactiveUsers   uint64         `json:"total_inactive_users"`
	TotalDistanceCovered float64        `json:"total_distance_covered"`
	TotalAlerts          uint64         `json:"total_alerts"`
	TotalWarningAlerts   uint64         `json:"total_warning_alerts"`
	TotalCriticalAlerts  uint64         `json:"total_critical_alerts"`
	TotalTrips           uint64         `json:"total_trips"`
	LatestAlerts         []models.Alert `json:"latest_alerts"`

	// Financial Summary
	TotalInvoiced float64 `json:"total_invoiced"`
	TotalPaid     float64 `json:"total_paid"`
	TotalBalance  float64 `json:"total_balance"`

	// Distance by time periods
	DistanceToday     float64 `json:"distance_today"`
	DistanceThisWeek  float64 `json:"distance_this_week"`
	DistanceThisMonth float64 `json:"distance_this_month"`
	DistanceTotal     float64 `json:"distance_total"`

	// Additional useful metrics
	TotalGeofences      uint64  `json:"total_geofences"`
	ActiveTrips         uint64  `json:"active_trips"`
	CompletedTrips      uint64  `json:"completed_trips"`
	AverageTripDistance float64 `json:"average_trip_distance"`
	AverageTripDuration float64 `json:"average_trip_duration"`

	// Geofence event statistics
	TotalGeofenceEvents  uint64                 `json:"total_geofence_events"`
	TotalGeofenceEntries uint64                 `json:"total_geofence_entries"`
	TotalGeofenceExits   uint64                 `json:"total_geofence_exits"`
	GeofenceEventsToday  uint64                 `json:"geofence_events_today"`
	LatestGeofenceEvents []models.GeofenceEvent `json:"latest_geofence_events"`
}

// GetServerStats returns comprehensive server statistics
func GetServerStats(c *gin.Context) {
	ctx := context.Background()

	// Get CPU stats
	cpuStats, err := getCPUStats(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to get CPU stats: %v", err),
		})
		return
	}

	// Get memory stats
	memoryStats, err := getMemoryStats(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to get memory stats: %v", err),
		})
		return
	}

	// Get storage stats
	storageStats, err := getStorageStats(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to get storage stats: %v", err),
		})
		return
	}

	// Get system stats
	systemStats, err := getSystemStats(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to get system stats: %v", err),
		})
		return
	}

	// Get network stats
	networkStats, err := getNetworkStats(ctx)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to get network stats: %v", err),
		})
		return
	}

	// Get runtime stats
	runtimeStats := getRuntimeStats()

	stats := ServerStats{
		CPU:     cpuStats,
		Memory:  memoryStats,
		Storage: storageStats,
		System:  systemStats,
		Network: networkStats,
		Runtime: runtimeStats,
	}

	c.JSON(http.StatusOK, gin.H{
		"data":      stats,
		"timestamp": time.Now().Unix(),
	})
}
func GetDashboardStats(c *gin.Context) {
	clientId, _ := c.Get("client_id")

	// Device statistics
	var totalDevices int64
	config.DB.Where("client_id = ?", clientId).Model(&models.ClientDevice{}).Count(&totalDevices)
	var totalOnlineDevices int64
	config.DB.Where("client_id = ?", clientId).Model(&models.ClientDevice{}).Where("connected = ?", true).Count(&totalOnlineDevices)
	var totalOfflineDevices int64
	config.DB.Where("client_id = ?", clientId).Model(&models.ClientDevice{}).Where("connected = ?", false).Count(&totalOfflineDevices)
	var totalMovingDevices int64
	config.DB.Where("client_id = ?", clientId).Model(&models.ClientDevice{}).Where("device_status = ?", "moving").Count(&totalMovingDevices)
	var totalIdlingDevices int64
	config.DB.Where("client_id = ?", clientId).Model(&models.ClientDevice{}).Where("device_status = ?", "idling").Count(&totalIdlingDevices)
	var totalStoppedDevices int64
	config.DB.Where("client_id = ?", clientId).Model(&models.ClientDevice{}).Where("device_status = ?", "stopped").Count(&totalStoppedDevices)

	// User statistics
	var totalUsers int64
	config.DB.Where("client_id = ?", clientId).Model(&models.User{}).Count(&totalUsers)
	var totalActiveUsers int64
	config.DB.Where("client_id = ?", clientId).Model(&models.User{}).Where("status = ?", "active").Count(&totalActiveUsers)
	var totalInactiveUsers int64
	config.DB.Where("client_id = ?", clientId).Model(&models.User{}).Where("status = ?", "inactive").Count(&totalInactiveUsers)

	// Trip statistics
	var totalTrips int64
	config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ?", clientId).
		Model(&models.Trip{}).Count(&totalTrips)

	var activeTrips int64
	config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ? AND trips.status = ?", clientId, "active").
		Model(&models.Trip{}).Count(&activeTrips)

	var completedTrips int64
	config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ? AND trips.status = ?", clientId, "completed").
		Model(&models.Trip{}).Count(&completedTrips)

	// Trip averages
	var avgTripDistance float64
	config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ? AND trips.status = ?", clientId, "completed").
		Model(&models.Trip{}).Select("COALESCE(AVG(distance), 0)").Scan(&avgTripDistance)

	var avgTripDuration float64
	config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ? AND trips.status = ? AND trips.duration IS NOT NULL", clientId, "completed").
		Model(&models.Trip{}).Select("COALESCE(AVG(duration), 0)").Scan(&avgTripDuration)

	// Distance statistics
	var totalDistanceCovered float64
	config.DB.Where("client_id = ?", clientId).Model(&models.ClientDevice{}).Select("COALESCE(SUM(distance_covered), 0)").Scan(&totalDistanceCovered)

	// Distance by time periods
	var distanceToday float64
	today := time.Now().Truncate(24 * time.Hour)
	config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ? AND DATE(trips.start_time) = DATE(?)", clientId, today).
		Model(&models.Trip{}).Select("COALESCE(SUM(distance), 0)").Scan(&distanceToday)

	var distanceThisWeek float64
	weekStart := time.Now().AddDate(0, 0, -int(time.Now().Weekday()))
	weekStart = weekStart.Truncate(24 * time.Hour)
	config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ? AND trips.start_time >= ?", clientId, weekStart).
		Model(&models.Trip{}).Select("COALESCE(SUM(distance), 0)").Scan(&distanceThisWeek)

	var distanceThisMonth float64
	monthStart := time.Now().AddDate(0, 0, -time.Now().Day()+1)
	monthStart = monthStart.Truncate(24 * time.Hour)
	config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ? AND trips.start_time >= ?", clientId, monthStart).
		Model(&models.Trip{}).Select("COALESCE(SUM(distance), 0)").Scan(&distanceThisMonth)

	var distanceTotal float64
	config.DB.Joins("JOIN client_devices ON trips.client_device_id = client_devices.id").
		Where("client_devices.client_id = ?", clientId).
		Model(&models.Trip{}).Select("COALESCE(SUM(distance), 0)").Scan(&distanceTotal)

	// Alert statistics
	var totalAlerts int64
	config.DB.Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Model(&models.Alert{}).Count(&totalAlerts)
	var totalWarningAlerts int64
	config.DB.Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Model(&models.Alert{}).Where("alert_level = ?", "warning").Count(&totalWarningAlerts)
	var totalCriticalAlerts int64
	config.DB.Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Model(&models.Alert{}).Where("alert_level = ?", "critical").Count(&totalCriticalAlerts)

	// Latest alerts (limit to 5 for mobile dashboard)
	var latestAlerts []models.Alert
	config.DB.Where("client_id = ?", clientId).Joins("left join client_devices on client_devices.id = alerts.client_device_id").Order("created_at desc").Preload("ClientDevice").Limit(5).Find(&latestAlerts)

	// Financial statistics
	var totalInvoiced float64
	config.DB.Where("client_id = ?", clientId).Model(&models.Invoice{}).Select("COALESCE(SUM(base_currency_amount), 0)").Scan(&totalInvoiced)

	var totalPaid float64
	config.DB.Joins("JOIN invoices ON invoice_payments.invoice_id = invoices.id").
		Where("invoices.client_id = ?", clientId).
		Model(&models.InvoicePayment{}).Select("COALESCE(SUM(invoice_payments.base_currency_amount), 0)").Scan(&totalPaid)

	totalBalance := totalInvoiced - totalPaid

	// Geofence statistics
	var totalGeofences int64
	config.DB.Where("client_id = ?", clientId).Model(&models.Geofence{}).Count(&totalGeofences)

	// Geofence event statistics
	var totalGeofenceEvents int64
	config.DB.Joins("JOIN geofences ON geofence_events.geofence_id = geofences.id").
		Where("geofences.client_id = ?", clientId).
		Model(&models.GeofenceEvent{}).Count(&totalGeofenceEvents)

	var totalGeofenceEntries int64
	config.DB.Joins("JOIN geofences ON geofence_events.geofence_id = geofences.id").
		Where("geofences.client_id = ? AND geofence_events.event_type = ?", clientId, "entry").
		Model(&models.GeofenceEvent{}).Count(&totalGeofenceEntries)

	var totalGeofenceExits int64
	config.DB.Joins("JOIN geofences ON geofence_events.geofence_id = geofences.id").
		Where("geofences.client_id = ? AND geofence_events.event_type = ?", clientId, "exit").
		Model(&models.GeofenceEvent{}).Count(&totalGeofenceExits)

	var geofenceEventsToday int64
	config.DB.Joins("JOIN geofences ON geofence_events.geofence_id = geofences.id").
		Where("geofences.client_id = ? AND DATE(geofence_events.event_timestamp) = DATE(?)", clientId, today).
		Model(&models.GeofenceEvent{}).Count(&geofenceEventsToday)

	// Latest geofence events (limit to 5 for mobile dashboard)
	var latestGeofenceEvents []models.GeofenceEvent
	config.DB.Joins("JOIN geofences ON geofence_events.geofence_id = geofences.id").
		Where("geofences.client_id = ?", clientId).
		Order("geofence_events.event_timestamp desc").
		Preload("Geofence").
		Preload("ClientDevice").
		Limit(5).
		Find(&latestGeofenceEvents)

	dashboardStats := DashboardStats{
		TotalDevices:         uint64(totalDevices),
		TotalOnlineDevices:   uint64(totalOnlineDevices),
		TotalOfflineDevices:  uint64(totalOfflineDevices),
		TotalMovingDevices:   uint64(totalMovingDevices),
		TotalIdlingDevices:   uint64(totalIdlingDevices),
		TotalStoppedDevices:  uint64(totalStoppedDevices),
		TotalUsers:           uint64(totalUsers),
		TotalActiveUsers:     uint64(totalActiveUsers),
		TotalInactiveUsers:   uint64(totalInactiveUsers),
		TotalDistanceCovered: totalDistanceCovered,
		TotalAlerts:          uint64(totalAlerts),
		TotalWarningAlerts:   uint64(totalWarningAlerts),
		TotalCriticalAlerts:  uint64(totalCriticalAlerts),
		TotalTrips:           uint64(totalTrips),
		LatestAlerts:         latestAlerts,
		TotalInvoiced:        totalInvoiced,
		TotalPaid:            totalPaid,
		TotalBalance:         totalBalance,
		DistanceToday:        distanceToday,
		DistanceThisWeek:     distanceThisWeek,
		DistanceThisMonth:    distanceThisMonth,
		DistanceTotal:        distanceTotal,
		TotalGeofences:       uint64(totalGeofences),
		ActiveTrips:          uint64(activeTrips),
		CompletedTrips:       uint64(completedTrips),
		AverageTripDistance:  avgTripDistance,
		AverageTripDuration:  avgTripDuration,
		TotalGeofenceEvents:  uint64(totalGeofenceEvents),
		TotalGeofenceEntries: uint64(totalGeofenceEntries),
		TotalGeofenceExits:   uint64(totalGeofenceExits),
		GeofenceEventsToday:  uint64(geofenceEventsToday),
		LatestGeofenceEvents: latestGeofenceEvents,
	}

	c.JSON(http.StatusOK, gin.H{
		"data":      dashboardStats,
		"timestamp": time.Now().Unix(),
	})
}

// getCPUStats retrieves CPU usage statistics
func getCPUStats(ctx context.Context) (CPUStats, error) {
	// Get CPU usage percentage
	cpuPercent, err := cpu.PercentWithContext(ctx, time.Second, false)
	if err != nil {
		return CPUStats{}, err
	}

	// Get per-core CPU usage
	perCorePercent, err := cpu.PercentWithContext(ctx, time.Second, true)
	if err != nil {
		return CPUStats{}, err
	}

	// Get CPU counts
	coreCount, err := cpu.CountsWithContext(ctx, false)
	if err != nil {
		return CPUStats{}, err
	}

	logicalCount, err := cpu.CountsWithContext(ctx, true)
	if err != nil {
		return CPUStats{}, err
	}

	// Get load average
	loadAvg, err := load.AvgWithContext(ctx)
	if err != nil {
		// Load average might not be available on all systems
		loadAvg = &load.AvgStat{}
	}

	var usage float64
	if len(cpuPercent) > 0 {
		usage = cpuPercent[0]
	}

	return CPUStats{
		UsagePercent:  usage,
		CoreCount:     coreCount,
		LogicalCount:  logicalCount,
		PerCoreUsage:  perCorePercent,
		LoadAverage1:  loadAvg.Load1,
		LoadAverage5:  loadAvg.Load5,
		LoadAverage15: loadAvg.Load15,
	}, nil
}

// getMemoryStats retrieves memory usage statistics
func getMemoryStats(ctx context.Context) (MemoryStats, error) {
	vmStat, err := mem.VirtualMemoryWithContext(ctx)
	if err != nil {
		return MemoryStats{}, err
	}

	return MemoryStats{
		Total:       vmStat.Total,
		Used:        vmStat.Used,
		Available:   vmStat.Available,
		Free:        vmStat.Free,
		UsedPercent: vmStat.UsedPercent,
		TotalGB:     float64(vmStat.Total) / 1024 / 1024 / 1024,
		UsedGB:      float64(vmStat.Used) / 1024 / 1024 / 1024,
		AvailableGB: float64(vmStat.Available) / 1024 / 1024 / 1024,
	}, nil
}

// getStorageStats retrieves storage usage statistics
func getStorageStats(ctx context.Context) (StorageStats, error) {
	// Get disk usage for root partition
	diskStat, err := disk.UsageWithContext(ctx, "/")
	if err != nil {
		return StorageStats{}, err
	}

	return StorageStats{
		Total:       diskStat.Total,
		Used:        diskStat.Used,
		Free:        diskStat.Free,
		UsedPercent: diskStat.UsedPercent,
		TotalGB:     float64(diskStat.Total) / 1024 / 1024 / 1024,
		UsedGB:      float64(diskStat.Used) / 1024 / 1024 / 1024,
		FreeGB:      float64(diskStat.Free) / 1024 / 1024 / 1024,
		Path:        "/",
	}, nil
}

// getSystemStats retrieves general system statistics
func getSystemStats(ctx context.Context) (SystemStats, error) {
	hostStat, err := host.InfoWithContext(ctx)
	if err != nil {
		return SystemStats{}, err
	}

	// Format uptime
	uptimeFormatted := formatUptime(hostStat.Uptime)

	return SystemStats{
		Hostname:        hostStat.Hostname,
		Uptime:          hostStat.Uptime,
		UptimeFormatted: uptimeFormatted,
		OS:              hostStat.OS,
		Platform:        hostStat.Platform,
		Architecture:    hostStat.KernelArch,
		KernelVersion:   hostStat.KernelVersion,
		ProcessCount:    hostStat.Procs,
	}, nil
}

// getNetworkStats retrieves network statistics
func getNetworkStats(ctx context.Context) (NetworkStats, error) {
	netStats, err := net.IOCountersWithContext(ctx, false)
	if err != nil {
		return NetworkStats{}, err
	}

	if len(netStats) == 0 {
		return NetworkStats{}, nil
	}

	// Aggregate all network interfaces
	var totalStats NetworkStats
	for _, stat := range netStats {
		totalStats.BytesSent += stat.BytesSent
		totalStats.BytesReceived += stat.BytesRecv
		totalStats.PacketsSent += stat.PacketsSent
		totalStats.PacketsRecv += stat.PacketsRecv
		totalStats.ErrorsIn += stat.Errin
		totalStats.ErrorsOut += stat.Errout
		totalStats.DropIn += stat.Dropin
		totalStats.DropOut += stat.Dropout
	}

	return totalStats, nil
}

// getRuntimeStats retrieves Go runtime statistics
func getRuntimeStats() RuntimeStats {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	lastGC := "never"
	if m.LastGC > 0 {
		lastGC = time.Unix(0, int64(m.LastGC)).Format(time.RFC3339)
	}

	return RuntimeStats{
		GoVersion:  runtime.Version(),
		Goroutines: runtime.NumGoroutine(),
		HeapAlloc:  m.HeapAlloc,
		HeapSys:    m.HeapSys,
		HeapInuse:  m.HeapInuse,
		StackInuse: m.StackInuse,
		GCCycles:   m.NumGC,
		NextGC:     m.NextGC,
		LastGC:     lastGC,
	}
}

// formatUptime formats uptime seconds into a human-readable string
func formatUptime(uptime uint64) string {
	days := uptime / 86400
	hours := (uptime % 86400) / 3600
	minutes := (uptime % 3600) / 60
	seconds := uptime % 60

	if days > 0 {
		return fmt.Sprintf("%dd %dh %dm %ds", days, hours, minutes, seconds)
	} else if hours > 0 {
		return fmt.Sprintf("%dh %dm %ds", hours, minutes, seconds)
	} else if minutes > 0 {
		return fmt.Sprintf("%dm %ds", minutes, seconds)
	} else {
		return fmt.Sprintf("%ds", seconds)
	}
}
