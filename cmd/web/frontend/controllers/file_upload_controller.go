package controllers

import (
	"net/http"
	"strconv"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

// UploadFile handles file upload for frontend/client users
func UploadFile(c *gin.Context) {
	// Get current user
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	// Parse multipart form
	if err := c.Request.ParseMultipartForm(50 << 20); err != nil { // 50MB max
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Failed to parse form data",
		})
		return
	}

	// Get uploaded file
	file, err := c.FormFile("file")
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"error": "No file uploaded or invalid file",
		})
		return
	}

	// Get optional description from form
	var description *string
	if desc := c.PostForm("description"); desc != "" {
		description = &desc
	}

	// Upload file with client_id from user context
	fileUploadService := services.NewFileUploadService()
	fileUpload, err := fileUploadService.UploadFile(file, currentUser.ClientId, currentUser.Id, description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Return response
	response := models.FileUploadResponse{
		Id:           fileUpload.Id,
		FileName:     fileUpload.FileName,
		OriginalName: fileUpload.OriginalName,
		FileSize:     fileUpload.FileSize,
		MimeType:     fileUpload.MimeType,
		FileUrl:      fileUpload.FileUrl,
		FileType:     fileUpload.FileType,
		Description:  fileUpload.Description,
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "File uploaded successfully",
		"data":    response,
	})
}

// GetFileUploads retrieves file uploads for the current client
func GetFileUploads(c *gin.Context) {
	// Get current user's client ID first
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	var filters models.FileUploadFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Set default pagination if not provided
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.PerPage == 0 {
		filters.PerPage = 20
	}

	// Set client_id from user context
	filters.ClientId = *currentUser.ClientId

	fileUploadService := services.NewFileUploadService()
	files, total, err := fileUploadService.GetFileUploads(filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Convert to response format
	var response []models.FileUploadResponse
	for _, file := range files {
		response = append(response, models.FileUploadResponse{
			Id:           file.Id,
			FileName:     file.FileName,
			OriginalName: file.OriginalName,
			FileSize:     file.FileSize,
			MimeType:     file.MimeType,
			FileUrl:      file.FileUrl,
			FileType:     file.FileType,
			Description:  file.Description,
		})
	}

	c.JSON(http.StatusOK, gin.H{
		"data":         response,
		"total":        total,
		"current_page": filters.Page,
		"per_page":     filters.PerPage,
		"last_page":    (total + int64(filters.PerPage) - 1) / int64(filters.PerPage),
	})
}

// GetFileUploadById retrieves a specific file upload for the current client
func GetFileUploadById(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid file ID",
		})
		return
	}

	// Get current user's client ID
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	fileUploadService := services.NewFileUploadService()
	file, err := fileUploadService.GetFileUploadById(uint(id), currentUser.ClientId)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "File not found",
		})
		return
	}

	response := models.FileUploadResponse{
		Id:           file.Id,
		FileName:     file.FileName,
		OriginalName: file.OriginalName,
		FileSize:     file.FileSize,
		MimeType:     file.MimeType,
		FileUrl:      file.FileUrl,
		FileType:     file.FileType,
		Description:  file.Description,
	}

	c.JSON(http.StatusOK, gin.H{
		"data": response,
	})
}

// DeleteFileUpload deletes a file upload for the current client
func DeleteFileUpload(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid file ID",
		})
		return
	}

	// Get current user's client ID
	user, exists := c.Get("user")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not found in context",
		})
		return
	}

	currentUser := user.(models.User)
	if currentUser.ClientId == nil {
		c.JSON(http.StatusForbidden, gin.H{
			"error": "Client access required",
		})
		return
	}

	fileUploadService := services.NewFileUploadService()
	err = fileUploadService.DeleteFileUpload(uint(id), currentUser.ClientId)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "File deleted successfully",
	})
}
