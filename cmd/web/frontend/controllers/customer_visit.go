package controllers

import (
	"net/http"
	"strconv"
	"time"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

type CustomerVisitController struct {
	customerVisitService *services.CustomerVisitService
}

func NewCustomerVisitController() *CustomerVisitController {
	return &CustomerVisitController{
		customerVisitService: services.NewCustomerVisitService(),
	}
}

// GetCustomerVisits returns all customer visits for the client
func (c *CustomerVisitController) GetCustomerVisits(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(ctx.DefaultQuery("per_page", "20"))
	visitType := ctx.Query("visit_type")
	status := ctx.Query("status")
	driverId := ctx.Query("driver_id")
	clientDeviceId := ctx.Query("client_device_id")
	startDate := ctx.Query("start_date")
	endDate := ctx.Query("end_date")

	// Build filters
	filters := models.CustomerVisitFilters{
		ClientId:       clientId,
		Page:           page,
		PerPage:        perPage,
		VisitType:      visitType,
		Status:         status,
		DriverId:       driverId,
		ClientDeviceId: clientDeviceId,
	}

	// Parse dates if provided
	if startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			filters.StartDate = &date
		}
	}
	if endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			filters.EndDate = &date
		}
	}

	visits, total, err := c.customerVisitService.GetCustomerVisits(filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data": visits,
		"pagination": gin.H{
			"page":        page,
			"per_page":    perPage,
			"total":       total,
			"total_pages": (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// GetCustomerVisit returns a specific customer visit
func (c *CustomerVisitController) GetCustomerVisit(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	visit, err := c.customerVisitService.GetCustomerVisit(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Customer visit not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": visit})
}

// CreateCustomerVisit creates a new customer visit
func (c *CustomerVisitController) CreateCustomerVisit(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	var visit models.CustomerVisit
	if err := ctx.ShouldBindJSON(&visit); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set client ID from context
	visit.ClientId = clientId

	createdVisit, err := c.customerVisitService.CreateCustomerVisit(visit)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{"data": createdVisit})
}

// UpdateCustomerVisit updates an existing customer visit
func (c *CustomerVisitController) UpdateCustomerVisit(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var visit models.CustomerVisit
	if err := ctx.ShouldBindJSON(&visit); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	visit.Id = uint(id)
	visit.ClientId = clientId

	updatedVisit, err := c.customerVisitService.UpdateCustomerVisit(visit)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": updatedVisit})
}

// DeleteCustomerVisit deletes a customer visit
func (c *CustomerVisitController) DeleteCustomerVisit(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	err = c.customerVisitService.DeleteCustomerVisit(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Customer visit deleted successfully"})
}

// GetCustomerVisitStats returns customer visit statistics
func (c *CustomerVisitController) GetCustomerVisitStats(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	stats, err := c.customerVisitService.GetCustomerVisitStats(clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": stats})
}
