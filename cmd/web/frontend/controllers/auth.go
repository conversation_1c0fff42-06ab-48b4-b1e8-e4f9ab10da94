package controllers

import (
	"errors"
	"fmt"
	"net/http"
	"os"
	"time"
	"yotracker/config"
	"yotracker/internal/mail"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type LoginInput struct {
	Email    string `form:"email" json:"email" binding:"required"`
	Password string `form:"password" json:"password" binding:"required"`
}

type PasswordResetRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type PasswordResetVerify struct {
	Token string `json:"token" binding:"required"`
}

type PasswordResetConfirm struct {
	Token    string `json:"token" binding:"required"`
	Password string `json:"password" binding:"required,min=6"`
}

func Home(c *gin.Context) {
	c.JSON(200, gin.H{
		"message": "You have successfully connected",
	})
}
func Login(c *gin.Context) {
	var login LoginInput
	allErrors := map[string][]string{}
	err := c.<PERSON>(&login)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}
	var result models.User
	userType := "client"
	err = config.DB.Preload("Client").Where(models.User{Email: login.Email, UserType: userType}).First(&result).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			allErrors["email"] = append(allErrors["email"], "Email not found")
			c.JSON(http.StatusNotFound, gin.H{
				"errors": allErrors,
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": err.Error(),
			})
		}
		return
	}
	if !services.CheckPassword(result.Password, login.Password) {
		allErrors["password"] = append(allErrors["password"], "Invalid password")
		c.JSON(422, gin.H{
			"errors": allErrors,
		})
		return
	}

	token, er := services.GenerateToken(&result, "access")
	if er != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": er.Error(),
		})
		return
	}
	//set last login date
	config.DB.Model(&result).Update("last_login_date", time.Now())
	c.JSON(200, gin.H{
		"token": token,
	})
}
func Register(c *gin.Context) {

}

// RequestPasswordReset initiates password reset process for frontend users
func RequestPasswordReset(c *gin.Context) {
	var req PasswordResetRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Find user by email (use silent logging to avoid "record not found" noise)
	var user models.User
	userType := "client"
	err := config.DB.Session(&gorm.Session{Logger: config.DB.Logger.LogMode(logger.Silent)}).
		Preload("Client").Where(models.User{Email: req.Email, UserType: userType}).First(&user).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Don't reveal if email exists or not for security
			c.JSON(http.StatusOK, gin.H{
				"message": "If the email exists, a password reset link has been sent",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Internal server error",
		})
		return
	}

	// Check if client is active
	if user.Client.Status != "active" {
		c.JSON(http.StatusOK, gin.H{
			"message": "If the email exists, a password reset link has been sent",
		})
		return
	}

	// Generate password reset token
	resetToken, err := services.GenerateToken(&user, "password_reset")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to generate reset token",
		})
		return
	}

	// Create reset link
	frontendURL := os.Getenv("APP_URL")
	if frontendURL == "" {
		frontendURL = "http://localhost:3000" // Default fallback
	}
	resetLink := fmt.Sprintf("%s/reset-password?token=%s", frontendURL, resetToken)

	// Send password reset email (non-blocking)
	go func() {
		err := mail.SendPasswordResetEmail(user.Email, user.Name, resetLink)
		if err != nil {
			// Log error but don't fail the request
			fmt.Printf("Failed to send password reset email to %s: %v\n", user.Email, err)
		}
	}()

	c.JSON(http.StatusOK, gin.H{
		"message": "If the email exists, a password reset link has been sent",
	})
}

// VerifyPasswordResetToken verifies if a password reset token is valid for frontend users
func VerifyPasswordResetToken(c *gin.Context) {
	var req PasswordResetVerify

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Verify the token
	claims, err := services.VerifyToken(req.Token, "password_reset")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid or expired reset token",
		})
		return
	}

	// Verify user still exists and is a client user
	var user models.User
	err = config.DB.Preload("Client").Where("id = ? AND user_type = ?", claims.UserId, "client").First(&user).Error
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid reset token",
		})
		return
	}

	// Check if client is still active
	if user.Client.Status != "active" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Account is not active",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Token is valid",
		"email":   user.Email,
	})
}

// ResetPassword resets the user's password using a valid reset token for frontend users
func ResetPassword(c *gin.Context) {
	var req PasswordResetConfirm

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": err.Error(),
		})
		return
	}

	// Verify the token
	claims, err := services.VerifyToken(req.Token, "password_reset")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid or expired reset token",
		})
		return
	}

	// Find the user
	var user models.User
	err = config.DB.Preload("Client").Where("id = ? AND user_type = ?", claims.UserId, "client").First(&user).Error
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid reset token",
		})
		return
	}

	// Check if client is still active
	if user.Client.Status != "active" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Account is not active",
		})
		return
	}

	// Hash the new password
	hashedPassword := services.HashPassword(req.Password)

	// Update the user's password
	err = config.DB.Model(&user).Update("password", hashedPassword).Error
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to update password",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Password has been reset successfully",
	})
}
