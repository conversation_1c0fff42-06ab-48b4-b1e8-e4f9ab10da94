package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"strconv"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/response"
)

func GetAllPaymentTypes(c *gin.Context) {
	var paymentTypes []models.PaymentType
	filter := map[string]interface{}{}
	filter["active"] = 1
	if isOnline := c.Query("is_online"); isOnline != "" {
		filter["is_online"] = isOnline
	}
	config.DB.Where(filter).Order("id desc").Find(&paymentTypes)
	//convert to payment type response
	var paymentTypeResponses []response.PaymentTypeResponse
	for _, paymentType := range paymentTypes {
		paymentTypeResponse := response.PaymentTypeResponse{
			Id:          paymentType.Id,
			Name:        paymentType.Name,
			SystemName:  paymentType.SystemName,
			Description: paymentType.Description,
			IsCash:      paymentType.IsCash,
			IsOnline:    paymentType.IsOnline,
			IsSystem:    paymentType.IsSystem,
			IsEft:       paymentType.IsEft,
			Active:      paymentType.Active,
			Position:    paymentType.Position,
			UniqueId:    paymentType.UniqueId,
			Logo:        paymentType.Logo,
			ReportColor: paymentType.ReportColor,
		}
		paymentTypeResponses = append(paymentTypeResponses, paymentTypeResponse)
	}
	c.JSON(http.StatusOK, gin.H{
		"data": paymentTypes,
	})
}

func GetPaymentTypeById(c *gin.Context) {
	var paymentType models.PaymentType

	// Validate ID format
	paymentTypeId := c.Param("id")
	if _, err := strconv.ParseUint(paymentTypeId, 10, 32); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "Invalid payment type ID format",
		})
		return
	}

	if err := config.DB.Where("active = 1").First(&paymentType, paymentTypeId).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Payment type not found",
		})
		return
	}
	paymentTypeResponse := response.PaymentTypeResponse{
		Id:          paymentType.Id,
		Name:        paymentType.Name,
		SystemName:  paymentType.SystemName,
		Description: paymentType.Description,
		IsCash:      paymentType.IsCash,
		IsOnline:    paymentType.IsOnline,
		IsSystem:    paymentType.IsSystem,
		IsEft:       paymentType.IsEft,
		Active:      paymentType.Active,
		Position:    paymentType.Position,
		UniqueId:    paymentType.UniqueId,
		Logo:        paymentType.Logo,
		ReportColor: paymentType.ReportColor,
	}
	c.JSON(http.StatusOK, gin.H{
		"data": paymentTypeResponse,
	})
}

func SearchPaymentTypes(c *gin.Context) {
	var paymentTypes []models.PaymentType
	filter := map[string]interface{}{}
	filter["active"] = 1
	if systemName := c.Query("system_name"); systemName != "" {
		filter["system_name"] = systemName
	}
	if isOnline := c.Query("is_online"); isOnline != "" {
		filter["is_online"] = isOnline
	}

	if name := c.Query("name"); name != "" {
		config.DB.Where(filter).Where("name LIKE ?", "%"+name+"%").Order("id desc").Find(&paymentTypes)
	} else {
		config.DB.Where(filter).Order("id desc").Find(&paymentTypes)
	}
	//convert to payment type response
	var paymentTypeResponses []response.PaymentTypeResponse
	for _, paymentType := range paymentTypes {
		paymentTypeResponse := response.PaymentTypeResponse{
			Id:          paymentType.Id,
			Name:        paymentType.Name,
			SystemName:  paymentType.SystemName,
			Description: paymentType.Description,
			IsCash:      paymentType.IsCash,
			IsOnline:    paymentType.IsOnline,
			IsSystem:    paymentType.IsSystem,
			IsEft:       paymentType.IsEft,
			Active:      paymentType.Active,
			Position:    paymentType.Position,
			UniqueId:    paymentType.UniqueId,
			Logo:        paymentType.Logo,
			ReportColor: paymentType.ReportColor,
		}
		paymentTypeResponses = append(paymentTypeResponses, paymentTypeResponse)
	}
	c.JSON(http.StatusOK, gin.H{
		"data": paymentTypeResponses,
	})
}
