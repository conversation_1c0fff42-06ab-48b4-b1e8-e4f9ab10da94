package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupRolesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         "localhost",
		"DB_PORT":         "3306",
		"DB_USERNAME":     "admin",
		"DB_PASSWORD":     "password",
		"TESTING_DB_NAME": "testing",
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func TestGetAllRoles(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test roles
	role1 := models.Role{
		Name: "Test Role 1",
	}
	role2 := models.Role{
		Name: "Test Role 2",
	}
	config.DB.Create(&role1)
	config.DB.Create(&role2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.GET("", GetAllRoles)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/frontend/roles", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})

	// Get the actual count of roles in the database
	var countBefore int64
	config.DB.Model(&models.Role{}).Count(&countBefore)

	// Assert that the response data length matches the database count
	assert.Len(t, data, int(countBefore)) // Dynamic count based on actual database state
}

func TestGetRoleById(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test role
	role := models.Role{
		Name: "Test Role",
	}
	config.DB.Create(&role)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.GET("/:id", GetRoleById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/frontend/roles/%d", role.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Role", data["name"])
}

func TestGetRoleByIdNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.GET("/:id", GetRoleById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/frontend/roles/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Role not found", response["message"])
}

func TestCreateRole(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.POST("", CreateRole)

	// Test data
	roleData := models.RoleRequest{
		Name: "New Test Role",
	}

	jsonData, _ := json.Marshal(roleData)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/frontend/roles", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 201 Created
	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Role created successfully", response["message"])

	// Verify role was created in database
	var role models.Role
	err = config.DB.Where("name = ?", "New Test Role").First(&role).Error
	assert.NoError(t, err)
	assert.Equal(t, "New Test Role", role.Name)
}

func TestCreateRoleInvalidRequest(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.POST("", CreateRole)

	// Test data with invalid JSON
	jsonData := []byte(`{"name": "Test Role", "invalid_field": "value"`)

	// Create request
	req, _ := http.NewRequest("POST", "/api/v1/frontend/roles", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)
}

func TestUpdateRole(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test role
	role := models.Role{
		Name: "Original Role",
	}
	config.DB.Create(&role)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.PUT("/:id", UpdateRole)

	// Test data
	updateData := models.RoleRequest{
		Name: "Updated Role",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/frontend/roles/%d", role.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Role updated successfully", response["message"])

	// Verify role was updated in database
	var updatedRole models.Role
	err = config.DB.First(&updatedRole, role.Id).Error
	assert.NoError(t, err)
	assert.Equal(t, "Updated Role", updatedRole.Name)
}

func TestUpdateRoleNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.PUT("/:id", UpdateRole)

	// Test data
	updateData := models.RoleRequest{
		Name: "Updated Role",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request with non-existent ID
	req, _ := http.NewRequest("PUT", "/api/v1/frontend/roles/999", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Role not found", response["message"])
}

func TestUpdateSystemRole(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test system role
	isSystem := true
	role := models.Role{
		Name:     "System Role",
		IsSystem: &isSystem,
	}
	config.DB.Create(&role)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.PUT("/:id", UpdateRole)

	// Test data
	updateData := models.RoleRequest{
		Name: "Updated System Role",
	}

	jsonData, _ := json.Marshal(updateData)

	// Create request
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/frontend/roles/%d", role.Id), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "System role cannot be updated", response["message"])
}

func TestDeleteRole(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test role
	role := models.Role{
		Name: "Test Role to Delete",
	}
	config.DB.Create(&role)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.DELETE("/:id", DeleteRole)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/frontend/roles/%d", role.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should return 204 No Content
	assert.Equal(t, http.StatusNoContent, w.Code)
	assert.Empty(t, w.Body.String()) // 204 No Content should not have a response body

	// Verify role was deleted from database
	var deletedRole models.Role
	err := config.DB.First(&deletedRole, role.Id).Error
	assert.Error(t, err) // Should not find the role
}

func TestDeleteRoleNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.DELETE("/:id", DeleteRole)

	// Create request with non-existent ID
	req, _ := http.NewRequest("DELETE", "/api/v1/frontend/roles/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Role not found", response["message"])
}

func TestDeleteSystemRole(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Create test system role
	isSystem := true
	role := models.Role{
		Name:     "System Role",
		IsSystem: &isSystem,
	}
	config.DB.Create(&role)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.DELETE("/:id", DeleteRole)

	// Create request
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/frontend/roles/%d", role.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "System role cannot be deleted", response["message"])
}

func TestSearchRoles(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test roles
	config.DB.Where("name LIKE ?", "%Searchable%").Delete(&models.Role{})
	config.DB.Where("name = ?", "Another Role").Delete(&models.Role{})

	// Create test roles
	role1 := models.Role{
		Name: "Searchable Role One",
	}
	role2 := models.Role{
		Name: "Another Role",
	}
	config.DB.Create(&role1)
	config.DB.Create(&role2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.GET("/search", SearchRoles)

	// Test search with query parameter
	req, _ := http.NewRequest("GET", "/api/v1/frontend/roles/search?s=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 1) // Should only find "Searchable Role One" (seeded roles don't match "Searchable")
}

func TestSearchRolesNoResults(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)

	// Set up test environment variables
	setupRolesTestEnvVars()

	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up existing test roles
	config.DB.Where("name = ?", "Test Role").Delete(&models.Role{})

	// Create test role
	role := models.Role{
		Name: "Test Role",
	}
	config.DB.Create(&role)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	roles := v1.Group("/roles")
	roles.GET("/search", SearchRoles)

	// Test search with non-matching query
	req, _ := http.NewRequest("GET", "/api/v1/frontend/roles/search?s=Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 0) // Should find no results (seeded roles don't match "Nonexistent")
}
