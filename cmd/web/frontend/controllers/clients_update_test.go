package controllers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/seed"
	"yotracker/internal/services"
	"yotracker/internal/utils"
	"yotracker/migrations"
)

func setupUpdateClientTest() {
	utils.ForceProjectRoot()

	// Set up test environment variables
	if os.Getenv("DB_HOST") == "" {
		os.Setenv("DB_HOST", "localhost")
	}
	if os.Getenv("DB_PORT") == "" {
		os.Setenv("DB_PORT", "3306")
	}
	if os.Getenv("DB_USERNAME") == "" {
		os.Setenv("DB_USERNAME", "admin")
	}
	if os.Getenv("DB_PASSWORD") == "" {
		os.Setenv("DB_PASSWORD", "password")
	}
	if os.Getenv("TESTING_DB_NAME") == "" {
		os.Setenv("TESTING_DB_NAME", "testing")
	}
	if os.Getenv("APP_KEY") == "" {
		os.Setenv("APP_KEY", "test-secret-key")
	}

	// Refresh test database for consistent state
	services.FastCleanupTestData()
	migrations.Migrate()
	seed.Seed()
	gin.SetMode(gin.TestMode)
}

func TestUpdateClientFrontend(t *testing.T) {
	// Setup test database
	setupUpdateClientTest()

	// Create test client with unique email
	timestamp := time.Now().UnixNano()
	client := models.Client{
		Name:        "Test Client",
		Email:       fmt.Sprintf("<EMAIL>", timestamp),
		PhoneNumber: "1234567890",
		ClientType:  "individual",
		Status:      "active",
		CreatedById: 1,
	}
	config.DB.Create(&client)

	// Create test user associated with the client with unique email
	hashedPassword := services.HashPassword("password123")
	user := models.User{
		Name:     "Test User",
		Email:    fmt.Sprintf("<EMAIL>", timestamp),
		Password: hashedPassword,
		UserType: "client",
		ClientId: &client.Id,
	}
	config.DB.Create(&user)

	// Generate token for authentication
	token, _ := services.GenerateToken(&user, "access")

	t.Run("successful client update", func(t *testing.T) {
		router := gin.New()
		company := "Updated Company"
		state := "Updated State"
		city := "Updated City"
		town := "Updated Town"
		address := "Updated Address"
		gender := "male"
		description := "Updated description"
		mapsProvider := "mapbox"
		defaultLandingPage := "devices"

		reqBody := map[string]interface{}{
			"name":                 "Updated Client Name",
			"email":                fmt.Sprintf("<EMAIL>", timestamp),
			"phone_number":         "**********",
			"company":              company,
			"state":                state,
			"city":                 city,
			"town":                 town,
			"address":              address,
			"gender":               gender,
			"description":          description,
			"maps_provider":        mapsProvider,
			"default_landing_page": defaultLandingPage,
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/clients", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		// Mock middleware to set user in context
		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/clients", UpdateClient)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Client updated successfully", response["message"])

		// Verify client was actually updated
		var updatedClient models.Client
		config.DB.First(&updatedClient, client.Id)
		assert.Equal(t, "Updated Client Name", updatedClient.Name)
		assert.Equal(t, fmt.Sprintf("<EMAIL>", timestamp), updatedClient.Email)
		assert.Equal(t, "**********", updatedClient.PhoneNumber)
		assert.Equal(t, company, *updatedClient.Company)
		assert.Equal(t, state, *updatedClient.State)
		assert.Equal(t, city, *updatedClient.City)
		assert.Equal(t, town, *updatedClient.Town)
		assert.Equal(t, address, *updatedClient.Address)
		assert.Equal(t, gender, *updatedClient.Gender)
		assert.Equal(t, description, *updatedClient.Description)
		assert.Equal(t, mapsProvider, *updatedClient.MapsProvider)
		assert.Equal(t, defaultLandingPage, *updatedClient.DefaultLandingPage)
	})

	t.Run("email already exists", func(t *testing.T) {
		// Create another client with a different email
		anotherClient := models.Client{
			Name:        "Another Client",
			Email:       fmt.Sprintf("<EMAIL>", timestamp),
			PhoneNumber: "**********",
			ClientType:  "individual",
			Status:      "active",
			CreatedById: 1,
		}
		config.DB.Create(&anotherClient)

		router := gin.New()
		reqBody := map[string]interface{}{
			"name":         "Updated Name",
			"email":        fmt.Sprintf("<EMAIL>", timestamp), // Try to use existing email
			"phone_number": "**********",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/clients", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/clients", UpdateClient)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Email already exists", response["message"])

		// Cleanup
		config.DB.Delete(&anotherClient)
	})

	t.Run("same email update allowed", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]interface{}{
			"name":         "Updated Name Same Email",
			"email":        client.Email, // Use same email - should be allowed
			"phone_number": "**********",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/clients", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/clients", UpdateClient)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Client updated successfully", response["message"])
	})

	t.Run("missing required fields", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]interface{}{
			// Missing name, email, and phone_number
			"company": "Some Company",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/clients", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/clients", UpdateClient)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("invalid email format", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]interface{}{
			"name":         "Updated Name",
			"email":        "invalid-email-format", // Invalid email
			"phone_number": "**********",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/clients", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Authorization", "Bearer "+token)

		router.Use(func(c *gin.Context) {
			c.Set("user", user)
			c.Next()
		})
		router.PUT("/clients", UpdateClient)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("unauthorized access", func(t *testing.T) {
		router := gin.New()
		reqBody := map[string]interface{}{
			"name":         "Updated Name",
			"email":        fmt.Sprintf("<EMAIL>", timestamp),
			"phone_number": "**********",
		}
		jsonBody, _ := json.Marshal(reqBody)

		req, _ := http.NewRequest("PUT", "/clients", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		// No middleware to set user in context
		router.PUT("/clients", UpdateClient)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		assert.Equal(t, "Unauthorized", response["message"])
	})

	// Cleanup
	config.DB.Delete(&user)
	config.DB.Delete(&client)
}
