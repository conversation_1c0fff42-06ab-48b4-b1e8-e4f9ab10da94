package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/seed"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGetAllClientDevicesWithLastGPS(t *testing.T) {
	// Setup test database - this will use Docker MySQL from docker-compose
	// Refresh test database for consistent state
	services.FastCleanupTestData()
	seed.Seed()

	// Create test client
	client := models.Client{
		Name:  "Test Client",
		Email: "<EMAIL>",
	}
	config.DB.Create(&client)

	// Get existing device type from seeded data
	var deviceType models.DeviceType
	if err := config.DB.First(&deviceType).Error; err != nil {
		t.Fatalf("Failed to get device type: %v", err)
	}

	// Create test client device
	clientDevice := models.ClientDevice{
		Name:         &[]string{"Test Device"}[0],
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "TEST001",
		AssetType:    "vehicle",
		Status:       "active",
	}
	config.DB.Create(&clientDevice)

	// Create test GPS data
	gpsData := models.GPSData{
		ClientDeviceId: &clientDevice.Id,
		DeviceId:       "TEST001",
		Latitude:       -17.8252,
		Longitude:      31.0335,
		Speed:          &[]float64{45.5}[0],
		GPSTimestamp:   &[]time.Time{time.Now()}[0],
		LocationName:   &[]string{"Test Location"}[0],
		IgnitionStatus: &[]bool{true}[0],
		VehicleStatus:  &[]string{"moving"}[0],
	}
	config.DB.Create(&gpsData)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add middleware to set client_id
	router.Use(func(c *gin.Context) {
		c.Set("client_id", client.Id)
		c.Next()
	})

	router.GET("/client-devices", GetAllClientDevices)

	// Create test request
	req, _ := http.NewRequest("GET", "/client-devices", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check that data is returned
	data, exists := response["data"]
	assert.True(t, exists)

	devices, ok := data.([]interface{})
	assert.True(t, ok)
	assert.Greater(t, len(devices), 0)

	// Check that the first device has last GPS data
	if len(devices) > 0 {
		device := devices[0].(map[string]interface{})

		// Check for last GPS data fields
		assert.Contains(t, device, "last_latitude")
		assert.Contains(t, device, "last_longitude")
		assert.Contains(t, device, "last_gps_timestamp")
		assert.Contains(t, device, "last_speed")
		assert.Contains(t, device, "last_location_name")
		assert.Contains(t, device, "last_ignition_status")
		assert.Contains(t, device, "last_vehicle_status")

		// Verify the GPS data values
		if device["last_latitude"] != nil {
			assert.Equal(t, -17.8252, device["last_latitude"])
		}
		if device["last_longitude"] != nil {
			assert.Equal(t, 31.0335, device["last_longitude"])
		}
		if device["last_speed"] != nil {
			assert.Equal(t, 45.5, device["last_speed"])
		}
		if device["last_location_name"] != nil {
			assert.Equal(t, "Test Location", device["last_location_name"])
		}
		if device["last_ignition_status"] != nil {
			assert.Equal(t, true, device["last_ignition_status"])
		}
		if device["last_vehicle_status"] != nil {
			assert.Equal(t, "moving", device["last_vehicle_status"])
		}
	}

	// Cleanup
	config.DB.Delete(&gpsData)
	config.DB.Delete(&clientDevice)
	config.DB.Delete(&client)
}

func TestGetClientDeviceByIdWithLastGPS(t *testing.T) {
	// Setup test database
	// Refresh test database for consistent state
	services.FastCleanupTestData()
	seed.Seed()

	// Create test client with unique email
	client := models.Client{
		Name:  "Test Client 2",
		Email: fmt.Sprintf("<EMAIL>", time.Now().UnixNano()),
	}
	if err := config.DB.Create(&client).Error; err != nil {
		t.Fatalf("Failed to create client: %v", err)
	}

	// Get existing device type from seeded data (use a different one if available)
	var deviceType models.DeviceType
	if err := config.DB.Offset(1).First(&deviceType).Error; err != nil {
		// If no second device type, use the first one
		if err := config.DB.First(&deviceType).Error; err != nil {
			t.Fatalf("Failed to get device type: %v", err)
		}
	}

	// Create test client device
	clientDevice := models.ClientDevice{
		Name:         &[]string{"Test Device 2"}[0],
		ClientId:     client.Id,
		DeviceTypeId: deviceType.Id,
		DeviceId:     "TEST002",
		AssetType:    "vehicle",
		Status:       "active",
	}
	if err := config.DB.Create(&clientDevice).Error; err != nil {
		t.Fatalf("Failed to create client device: %v", err)
	}

	// Create test GPS data
	gpsData := models.GPSData{
		ClientDeviceId: &clientDevice.Id,
		DeviceId:       "TEST002",
		Latitude:       -17.8300,
		Longitude:      31.0400,
		Speed:          &[]float64{60.0}[0],
		GPSTimestamp:   &[]time.Time{time.Now()}[0],
		LocationName:   &[]string{"Test Location 2"}[0],
		IgnitionStatus: &[]bool{false}[0],
		VehicleStatus:  &[]string{"parked"}[0],
	}
	if err := config.DB.Create(&gpsData).Error; err != nil {
		t.Fatalf("Failed to create GPS data: %v", err)
	}

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/client-devices/:id", GetClientDeviceById)

	// Create test request
	req, _ := http.NewRequest("GET", "/client-devices/"+fmt.Sprintf("%d", clientDevice.Id), nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assert response
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	// Check that data is returned
	data, exists := response["data"]
	assert.True(t, exists)

	// Check that last_gps_data is returned
	lastGPSData, exists := response["last_gps_data"]
	assert.True(t, exists)
	assert.NotNil(t, lastGPSData)

	device := data.(map[string]interface{})

	// Check for last GPS data fields in the device object
	assert.Contains(t, device, "last_latitude")
	assert.Contains(t, device, "last_longitude")
	assert.Contains(t, device, "last_gps_timestamp")
	assert.Contains(t, device, "last_speed")
	assert.Contains(t, device, "last_location_name")
	assert.Contains(t, device, "last_ignition_status")
	assert.Contains(t, device, "last_vehicle_status")

	// Cleanup
	config.DB.Delete(&gpsData)
	config.DB.Delete(&clientDevice)
	config.DB.Delete(deviceType)
	config.DB.Delete(&client)
}
