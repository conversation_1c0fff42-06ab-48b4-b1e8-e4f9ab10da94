package controllers

import (
	"net/http"
	"strconv"
	"time"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

type SafetyComplianceController struct {
	safetyComplianceService *services.SafetyComplianceService
}

func NewSafetyComplianceController() *SafetyComplianceController {
	return &SafetyComplianceController{
		safetyComplianceService: services.NewSafetyComplianceService(),
	}
}

// GetSafetyCompliances returns all safety compliance records for the client
func (c *SafetyComplianceController) GetSafetyCompliances(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(ctx.DefaultQuery("per_page", "20"))
	status := ctx.Query("status")
	complianceType := ctx.Query("compliance_type")
	driverId := ctx.Query("driver_id")
	startDate := ctx.Query("start_date")
	endDate := ctx.Query("end_date")

	// Build filters
	filters := models.SafetyComplianceFilters{
		ClientId:       clientId,
		Page:           page,
		PerPage:        perPage,
		Status:         status,
		ComplianceType: complianceType,
		DriverId:       driverId,
	}

	// Parse dates if provided
	if startDate != "" {
		if date, err := time.Parse("2006-01-02", startDate); err == nil {
			filters.StartDate = &date
		}
	}
	if endDate != "" {
		if date, err := time.Parse("2006-01-02", endDate); err == nil {
			filters.EndDate = &date
		}
	}

	compliances, total, err := c.safetyComplianceService.GetSafetyCompliances(filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data": compliances,
		"pagination": gin.H{
			"page":        page,
			"per_page":    perPage,
			"total":       total,
			"total_pages": (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// GetSafetyCompliance returns a specific safety compliance record
func (c *SafetyComplianceController) GetSafetyCompliance(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	compliance, err := c.safetyComplianceService.GetSafetyCompliance(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Safety compliance record not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": compliance})
}

// CreateSafetyCompliance creates a new safety compliance record
func (c *SafetyComplianceController) CreateSafetyCompliance(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	var compliance models.SafetyCompliance
	if err := ctx.ShouldBindJSON(&compliance); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set client ID from context
	compliance.ClientId = clientId

	createdCompliance, err := c.safetyComplianceService.CreateSafetyCompliance(compliance)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{"data": createdCompliance})
}

// UpdateSafetyCompliance updates an existing safety compliance record
func (c *SafetyComplianceController) UpdateSafetyCompliance(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var compliance models.SafetyCompliance
	if err := ctx.ShouldBindJSON(&compliance); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	compliance.Id = uint(id)
	compliance.ClientId = clientId

	updatedCompliance, err := c.safetyComplianceService.UpdateSafetyCompliance(compliance)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": updatedCompliance})
}

// DeleteSafetyCompliance deletes a safety compliance record
func (c *SafetyComplianceController) DeleteSafetyCompliance(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	err = c.safetyComplianceService.DeleteSafetyCompliance(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Safety compliance record deleted successfully"})
}

// GetExpiringCompliances returns compliance records that are expiring soon
func (c *SafetyComplianceController) GetExpiringCompliances(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	days, _ := strconv.Atoi(ctx.DefaultQuery("days", "30"))

	compliances, err := c.safetyComplianceService.GetExpiringCompliances(clientId, days)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": compliances})
}

// GetSafetyComplianceStats returns safety compliance statistics
func (c *SafetyComplianceController) GetSafetyComplianceStats(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	stats, err := c.safetyComplianceService.GetSafetyComplianceStats(clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": stats})
}
