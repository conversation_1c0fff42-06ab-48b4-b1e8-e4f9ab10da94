package controllers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
	"yotracker/internal/utils"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupPaymentTypesTestEnvVars() {
	envVars := map[string]string{
		"DB_HOST":         getEnvOrDefault("DB_HOST", "localhost"),
		"DB_PORT":         getEnvOrDefault("DB_PORT", "3306"),
		"DB_USERNAME":     getEnvOrDefault("DB_USERNAME", "admin"),
		"DB_PASSWORD":     getEnvOrDefault("DB_PASSWORD", "password"),
		"TESTING_DB_NAME": getEnvOrDefault("TESTING_DB_NAME", "testing"),
		"APP_KEY":         "test-secret-key",
	}

	for key, value := range envVars {
		if os.Getenv(key) == "" {
			os.Setenv(key, value)
		}
	}
}

func TestGetAllPaymentTypes(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupPaymentTypesTestEnvVars()
	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up any existing test data
	config.DB.Where("name LIKE ?", "Test Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Inactive Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Searchable Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Another Payment Type%").Delete(&models.PaymentType{})

	// Create test payment types
	paymentType1 := models.PaymentType{
		Name:   "Test Payment Type 1",
		Active: true,
	}
	paymentType2 := models.PaymentType{
		Name:   "Test Payment Type 2",
		Active: true,
	}
	config.DB.Create(&paymentType1)
	config.DB.Create(&paymentType2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("", GetAllPaymentTypes)

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/frontend/payment_types", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})

	// Get the actual count of active payment types in the database (API only returns active ones)
	var countBefore int64
	config.DB.Model(&models.PaymentType{}).Where("active = ?", true).Count(&countBefore)

	// Assert that the response data length matches the database count
	assert.Len(t, data, int(countBefore)) // Dynamic count based on actual database state
}

func TestGetPaymentTypeById(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupPaymentTypesTestEnvVars()
	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up any existing test data
	config.DB.Where("name LIKE ?", "Test Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Inactive Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Searchable Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Another Payment Type%").Delete(&models.PaymentType{})

	// Create test payment type
	paymentType := models.PaymentType{
		Name:   "Test Payment Type",
		Active: true,
	}
	config.DB.Create(&paymentType)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("/:id", GetPaymentTypeById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/frontend/payment_types/%d", paymentType.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].(map[string]interface{})
	assert.Equal(t, "Test Payment Type", data["name"])
}

func TestGetPaymentTypeByIdNotFound(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupPaymentTypesTestEnvVars()
	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("/:id", GetPaymentTypeById)

	// Create request with non-existent ID
	req, _ := http.NewRequest("GET", "/api/v1/frontend/payment_types/999", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Payment type not found", response["message"])
}

func TestGetPaymentTypeByIdInactive(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupPaymentTypesTestEnvVars()
	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up any existing test data
	config.DB.Where("name LIKE ?", "Test Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Inactive Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Searchable Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Another Payment Type%").Delete(&models.PaymentType{})

	// Create test payment type (inactive)
	paymentType := models.PaymentType{
		Name:   "Inactive Payment Type",
		Active: false,
	}
	config.DB.Create(&paymentType)

	// Explicitly set Active to false since the model has a default of true
	config.DB.Model(&paymentType).Update("active", false)

	// Verify the payment type was created as inactive
	var createdPaymentType models.PaymentType
	config.DB.First(&createdPaymentType, paymentType.Id)
	assert.False(t, createdPaymentType.Active, "Payment type should be inactive")

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("/:id", GetPaymentTypeById)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/frontend/payment_types/%d", paymentType.Id), nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions - Should not find inactive payment type
	assert.Equal(t, http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "Payment type not found", response["message"])
}

func TestSearchPaymentTypes(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupPaymentTypesTestEnvVars()
	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up any existing test data
	config.DB.Where("name LIKE ?", "Test Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Inactive Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Searchable Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Another Payment Type%").Delete(&models.PaymentType{})

	// Create test payment types
	paymentType1 := models.PaymentType{
		Name:   "Searchable Payment Type One",
		Active: true,
	}
	paymentType2 := models.PaymentType{
		Name:   "Another Payment Type",
		Active: true,
	}
	config.DB.Create(&paymentType1)
	config.DB.Create(&paymentType2)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("/search", SearchPaymentTypes)

	// Test search with name parameter
	req, _ := http.NewRequest("GET", "/api/v1/frontend/payment_types/search?name=Searchable", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"].([]interface{})
	assert.Len(t, data, 1) // Should only find "Searchable Payment Type One"
}

func TestSearchPaymentTypesNoResults(t *testing.T) {
	// Setup test environment
	utils.ForceProjectRoot()
	gin.SetMode(gin.TestMode)
	setupPaymentTypesTestEnvVars()
	// Refresh test database for consistent state
	services.FastCleanupTestData()

	// Clean up any existing test data
	config.DB.Where("name LIKE ?", "Test Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Inactive Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Searchable Payment Type%").Delete(&models.PaymentType{})
	config.DB.Where("name LIKE ?", "Another Payment Type%").Delete(&models.PaymentType{})

	// Create test payment type
	paymentType := models.PaymentType{
		Name:   "Test Payment Type",
		Active: true,
	}
	config.DB.Create(&paymentType)

	// Setup router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Add routes
	v1 := router.Group("/api/v1/frontend")
	paymentTypes := v1.Group("/payment_types")
	paymentTypes.GET("/search", SearchPaymentTypes)

	// Test search with non-matching name
	req, _ := http.NewRequest("GET", "/api/v1/frontend/payment_types/search?name=Nonexistent", nil)

	// Record response
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Assertions
	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	data := response["data"]
	if data == nil {
		// If data is nil, that's equivalent to an empty slice
		assert.Nil(t, data)
	} else {
		dataSlice := data.([]interface{})
		assert.Len(t, dataSlice, 0) // Should find no results
	}
}
