package controllers

import (
	"net/http"
	"strconv"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

type ServiceTechnicianController struct {
	serviceTechnicianService *services.ServiceTechnicianService
}

func NewServiceTechnicianController() *ServiceTechnicianController {
	return &ServiceTechnicianController{
		serviceTechnicianService: services.NewServiceTechnicianService(),
	}
}

// GetServiceTechnicians returns all service technicians for the client
func (c *ServiceTechnicianController) GetServiceTechnicians(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	// Parse query parameters
	page, _ := strconv.Atoi(ctx.DefaultQuery("page", "1"))
	perPage, _ := strconv.Atoi(ctx.DefaultQuery("per_page", "20"))
	availabilityStatus := ctx.Query("availability_status")
	specialization := ctx.Query("specialization")

	// Build filters
	filters := models.ServiceTechnicianFilters{
		ClientId:           clientId,
		Page:               page,
		PerPage:            perPage,
		AvailabilityStatus: availabilityStatus,
		Specialization:     specialization,
	}

	technicians, total, err := c.serviceTechnicianService.GetServiceTechnicians(filters)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data": technicians,
		"pagination": gin.H{
			"page":        page,
			"per_page":    perPage,
			"total":       total,
			"total_pages": (total + int64(perPage) - 1) / int64(perPage),
		},
	})
}

// GetServiceTechnician returns a specific service technician
func (c *ServiceTechnicianController) GetServiceTechnician(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	technician, err := c.serviceTechnicianService.GetServiceTechnician(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusNotFound, gin.H{"error": "Service technician not found"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": technician})
}

// CreateServiceTechnician creates a new service technician
func (c *ServiceTechnicianController) CreateServiceTechnician(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	var technician models.ServiceTechnician
	if err := ctx.ShouldBindJSON(&technician); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set client ID from context
	technician.ClientId = clientId

	createdTechnician, err := c.serviceTechnicianService.CreateServiceTechnician(technician)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, gin.H{"data": createdTechnician})
}

// UpdateServiceTechnician updates an existing service technician
func (c *ServiceTechnicianController) UpdateServiceTechnician(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var technician models.ServiceTechnician
	if err := ctx.ShouldBindJSON(&technician); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	technician.Id = uint(id)
	technician.ClientId = clientId

	updatedTechnician, err := c.serviceTechnicianService.UpdateServiceTechnician(technician)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": updatedTechnician})
}

// DeleteServiceTechnician deletes a service technician
func (c *ServiceTechnicianController) DeleteServiceTechnician(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	err = c.serviceTechnicianService.DeleteServiceTechnician(uint(id), clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"message": "Service technician deleted successfully"})
}

// GetAvailableTechnicians returns available service technicians
func (c *ServiceTechnicianController) GetAvailableTechnicians(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	technicians, err := c.serviceTechnicianService.GetAvailableTechnicians(clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": technicians})
}

// GetServiceTechnicianStats returns service technician statistics
func (c *ServiceTechnicianController) GetServiceTechnicianStats(ctx *gin.Context) {
	clientId := ctx.GetUint("client_id")
	if clientId == 0 {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Client ID not found"})
		return
	}

	stats, err := c.serviceTechnicianService.GetServiceTechnicianStats(clientId)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{"data": stats})
}
