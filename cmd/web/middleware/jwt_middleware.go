package middleware

import (
	"net/http"
	"strings"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"

	"github.com/gin-gonic/gin"
)

func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.Query("token")
		if token == "" {
			token = strings.ReplaceAll(c.<PERSON>("Authorization"), "Bearer ", "")
		}
		claims, err := services.VerifyToken(token, "access")
		if err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			return
		}
		var user models.User
		if err := config.DB.Preload("Client").First(&user, claims.UserId).Error; err != nil {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Unauthorized"})
			return
		}

		// For frontend users, ensure they have a ClientId
		if user.UserType == "client" && user.ClientId == nil {
			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "User not associated with any client"})
			return
		}

		c.Set("user", user)
		c.Set("client_id", user.ClientId)
		c.Set("userID", claims.UserId)
		c.Set("userType", claims.UserType)
		c.Next()
	}
}

func PublicDeviceShareMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.Query("token")
		if token == "" {
			token = strings.ReplaceAll(c.GetHeader("Authorization"), "Bearer ", "")
		}
		if token == "" {
			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Access token is required"})
			return
		}
		claims, err := services.VerifyDeviceShareToken(token)
		if err != nil {

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "Invalid or expired token"})
			return
		}
		c.Set("device_share_claims", claims)
		c.Set("client_device_id", claims.ClientDeviceId)
		c.Next()
	}
}
