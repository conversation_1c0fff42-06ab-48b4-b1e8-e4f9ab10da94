package middleware

import (
	"yotracker/internal/models"

	"github.com/gin-gonic/gin"
)

func CheckForClient() gin.HandlerFunc {
	return func(c *gin.Context) {
		user, exists := c.Get("user")
		if !exists {
			c.AbortWithStatusJSON(401, gin.H{"error": "Unauthorized"})
			return
		}

		userModel := user.(models.User)
		if userModel.ClientId == nil {
			c.AbortWithStatusJSON(401, gin.H{"error": "Unauthorized"})
			return
		}

		// Set client_id in context for use by controllers
		c.Set("client_id", *userModel.ClientId)
		c.Next()
	}
}
