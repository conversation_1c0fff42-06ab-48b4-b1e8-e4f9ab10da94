package main

import (
	"fmt"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
)

func main() {
	// Initialize database
	config.InitDB()

	fmt.Println("🔍 YoTracker Trip Detection Diagnostic Tool")
	fmt.Println("===========================================")

	// Check real-time service status
	checkRealtimeServiceStatus()

	// Check database state
	checkDatabaseState()

	// Check recent GPS data
	checkRecentGPSData()

	// Check trip detection settings
	checkTripDetectionSettings()

	// Check device configurations
	checkDeviceConfigurations()

	// Provide recommendations
	provideRecommendations()
}

func checkRealtimeServiceStatus() {
	fmt.Println("\n📡 Real-Time Service Status:")
	fmt.Println("----------------------------")

	manager := services.GetRealtimeServiceManager()
	if manager == nil {
		fmt.Println("❌ Real-time service manager is nil")
		return
	}

	if manager.IsStarted() {
		fmt.Println("✅ Real-time trip detection service is STARTED")

		service := manager.GetService()
		if service != nil {
			fmt.Printf("   - Service instance: %p\n", service)
			fmt.Println("   - Service is running (internal details not accessible)")
		} else {
			fmt.Println("❌ Service instance is nil")
		}
	} else {
		fmt.Println("❌ Real-time trip detection service is NOT STARTED")
		fmt.Println("   This is likely the cause of missing trip_id assignments!")
	}
}

func checkDatabaseState() {
	fmt.Println("\n🗄️  Database State:")
	fmt.Println("------------------")

	// Check recent GPS data
	var recentGPSCount int64
	config.DB.Model(&models.GPSData{}).
		Where("created_at > ?", time.Now().Add(-1*time.Hour)).
		Count(&recentGPSCount)
	fmt.Printf("📍 Recent GPS records (last hour): %d\n", recentGPSCount)

	// Check GPS data with trip_id
	var gpsWithTripCount int64
	config.DB.Model(&models.GPSData{}).
		Where("trip_id IS NOT NULL AND created_at > ?", time.Now().Add(-1*time.Hour)).
		Count(&gpsWithTripCount)
	fmt.Printf("📍 GPS records with trip_id (last hour): %d\n", gpsWithTripCount)

	// Check GPS data without trip_id
	var gpsWithoutTripCount int64
	config.DB.Model(&models.GPSData{}).
		Where("trip_id IS NULL AND created_at > ?", time.Now().Add(-1*time.Hour)).
		Count(&gpsWithoutTripCount)
	fmt.Printf("📍 GPS records WITHOUT trip_id (last hour): %d\n", gpsWithoutTripCount)

	// Check recent trips
	var recentTripsCount int64
	config.DB.Model(&models.Trip{}).
		Where("created_at > ?", time.Now().Add(-1*time.Hour)).
		Count(&recentTripsCount)
	fmt.Printf("🚗 Recent trips created (last hour): %d\n", recentTripsCount)

	// Check recent alerts
	var recentAlertsCount int64
	config.DB.Model(&models.Alert{}).
		Where("created_at > ?", time.Now().Add(-1*time.Hour)).
		Count(&recentAlertsCount)
	fmt.Printf("🚨 Recent alerts created (last hour): %d\n", recentAlertsCount)

	// Calculate trip assignment rate
	if recentGPSCount > 0 {
		assignmentRate := float64(gpsWithTripCount) / float64(recentGPSCount) * 100
		fmt.Printf("📊 Trip assignment rate: %.1f%%\n", assignmentRate)

		if assignmentRate < 50 {
			fmt.Println("⚠️  Low trip assignment rate - this indicates an issue!")
		}
	}
}

func checkRecentGPSData() {
	fmt.Println("\n📍 Recent GPS Data Analysis:")
	fmt.Println("----------------------------")

	// Get recent GPS data without trip_id
	var gpsWithoutTrip []models.GPSData
	err := config.DB.Where("trip_id IS NULL AND created_at > ?", time.Now().Add(-1*time.Hour)).
		Order("created_at DESC").Limit(10).Find(&gpsWithoutTrip).Error
	if err != nil {
		fmt.Printf("❌ Error querying GPS data: %v\n", err)
		return
	}

	if len(gpsWithoutTrip) == 0 {
		fmt.Println("✅ No recent GPS data without trip_id found")
		return
	}

	fmt.Printf("📋 Found %d recent GPS records without trip_id:\n", len(gpsWithoutTrip))
	for i, gps := range gpsWithoutTrip {
		ignitionStatus := "unknown"
		if gps.IgnitionStatus != nil {
			ignitionStatus = fmt.Sprintf("%v", *gps.IgnitionStatus)
		}
		speed := "unknown"
		if gps.Speed != nil {
			speed = fmt.Sprintf("%.1f km/h", *gps.Speed)
		}

		fmt.Printf("  %d. Device %d: %v, Lat=%.6f, Lon=%.6f, Speed=%s, Ignition=%s\n",
			i+1, *gps.ClientDeviceId, gps.CreatedAt, gps.Latitude, gps.Longitude, speed, ignitionStatus)
	}
}

func checkTripDetectionSettings() {
	fmt.Println("\n⚙️  Trip Detection Settings:")
	fmt.Println("----------------------------")

	// Check settings from database
	settings := map[string]string{
		"trip_detection_idle_timeout_minutes":  "5",
		"trip_detection_min_speed_threshold":   "1.0",
		"trip_detection_min_distance_meters":   "100.0",
		"realtime_trip_flush_interval_seconds": "30",
		"realtime_trip_worker_count":           "4",
		"realtime_trip_queue_size":             "10000",
	}

	for key, defaultValue := range settings {
		value := models.GetSetting(key)
		if value == "" {
			value = defaultValue + " (default)"
		}
		fmt.Printf("   %s: %s\n", key, value)
	}
}

func checkDeviceConfigurations() {
	fmt.Println("\n📱 Device Configurations:")
	fmt.Println("-------------------------")

	// Check devices with ignition status support
	var devicesWithIgnition []models.ClientDevice
	err := config.DB.Preload("DeviceType").
		Where("device_type_id IN (SELECT id FROM device_types WHERE ignition_status = true)").
		Find(&devicesWithIgnition).Error
	if err != nil {
		fmt.Printf("❌ Error querying devices: %v\n", err)
		return
	}

	fmt.Printf("🔑 Devices with ignition status support: %d\n", len(devicesWithIgnition))
	for i, device := range devicesWithIgnition {
		if i < 5 { // Show first 5
			ignitionStatus := "unknown"
			if device.IgnitionStatus != nil {
				ignitionStatus = fmt.Sprintf("%v", *device.IgnitionStatus)
			}
			fmt.Printf("   %d. %s (ID: %d, IMEI: %s, Ignition: %s)\n",
				i+1, *device.Name, device.Id, device.DeviceId, ignitionStatus)
		}
	}
	if len(devicesWithIgnition) > 5 {
		fmt.Printf("   ... and %d more devices\n", len(devicesWithIgnition)-5)
	}

	// Check total devices
	var totalDevices int64
	config.DB.Model(&models.ClientDevice{}).Count(&totalDevices)
	fmt.Printf("📱 Total devices: %d\n", totalDevices)
}

func provideRecommendations() {
	fmt.Println("\n💡 Recommendations:")
	fmt.Println("-------------------")

	manager := services.GetRealtimeServiceManager()
	if !manager.IsStarted() {
		fmt.Println("🚨 CRITICAL: Real-time trip detection service is not started!")
		fmt.Println("   - This is the primary cause of missing trip_id assignments")
		fmt.Println("   - The service should start automatically with the main server")
		fmt.Println("   - Check server logs for startup errors")
		fmt.Println("   - Verify the startup service is being called")
		return
	}

	// Check if there are recent GPS records without trip_id
	var gpsWithoutTripCount int64
	config.DB.Model(&models.GPSData{}).
		Where("trip_id IS NULL AND created_at > ?", time.Now().Add(-1*time.Hour)).
		Count(&gpsWithoutTripCount)

	if gpsWithoutTripCount > 0 {
		fmt.Println("⚠️  Found GPS records without trip_id:")
		fmt.Println("   - This suggests the real-time service may not be processing all GPS data")
		fmt.Println("   - Check if GPS data is being sent to the real-time service")
		fmt.Println("   - Verify the ProcessGPSData function is being called")
		fmt.Println("   - Check for errors in the real-time service logs")
		fmt.Println("   - Consider running the realistic trip detection test")
	} else {
		fmt.Println("✅ No recent GPS records without trip_id found")
		fmt.Println("   - Trip detection appears to be working correctly")
		fmt.Println("   - If you're still seeing issues, check older data or specific devices")
	}

	fmt.Println("\n🧪 Testing Recommendations:")
	fmt.Println("   - Run: go run cmd/test-realistic-trip-detection/main.go")
	fmt.Println("   - Run: ./test-realistic-trip-detection.sh")
	fmt.Println("   - Check server logs for real-time service activity")
	fmt.Println("   - Monitor the real-time service statistics")
}
