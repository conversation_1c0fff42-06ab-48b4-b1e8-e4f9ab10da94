package main

import (
	"fmt"
	"log"
	"time"

	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
)

func main() {
	// Initialize database
	config.InitDB()

	// Initialize real-time trip detection service
	manager := services.GetRealtimeServiceManager()
	if err := manager.StartService(); err != nil {
		log.Fatalf("Failed to start real-time trip detection service: %v", err)
	}
	realtimeService := services.GetRealtimeTripDetectionService()

	if realtimeService == nil {
		log.Fatal("Failed to initialize real-time trip detection service")
	}

	fmt.Println("🚀 Manual Real-Time Trip Detection Test")
	fmt.Println("======================================")
	fmt.Println("This test simulates GPS data with ignition changes")
	fmt.Println("")

	// Get device ID from user
	var deviceId uint
	fmt.Print("Enter device ID to test (1-10): ")
	fmt.Scanf("%d", &deviceId)

	if deviceId < 1 || deviceId > 10 {
		deviceId = 1
		fmt.Printf("Using default device ID: %d\n", deviceId)
	}

	fmt.Printf("\n📱 Testing with Device ID: %d\n", deviceId)
	fmt.Println("")

	// Test scenario selection
	var scenario int
	fmt.Println("Select test scenario:")
	fmt.Println("1. Key On/Off Events")
	fmt.Println("2. Trip Start/End")
	fmt.Println("3. Speed Alert")
	fmt.Println("4. Towing Event")
	fmt.Println("5. Complete Journey")
	fmt.Print("Enter scenario (1-5): ")
	fmt.Scanf("%d", &scenario)

	baseTime := time.Now()

	switch scenario {
	case 1:
		testKeyEvents(realtimeService, deviceId, baseTime)
	case 2:
		testTripDetection(realtimeService, deviceId, baseTime)
	case 3:
		testSpeedAlert(realtimeService, deviceId, baseTime)
	case 4:
		testTowingEvent(realtimeService, deviceId, baseTime)
	case 5:
		testCompleteJourney(realtimeService, deviceId, baseTime)
	default:
		fmt.Println("Invalid scenario, running complete journey test...")
		testCompleteJourney(realtimeService, deviceId, baseTime)
	}

	fmt.Println("\n✅ Test completed!")
	fmt.Println("Check the database for results:")
	fmt.Println("  - Trips: SELECT * FROM trips ORDER BY start_time DESC LIMIT 5;")
	fmt.Println("  - Alerts: SELECT * FROM alerts ORDER BY alert_timestamp DESC LIMIT 10;")
}

func testKeyEvents(realtimeService *services.RealtimeTripDetectionService, deviceId uint, baseTime time.Time) {
	fmt.Println("🔑 Testing Key On/Off Events")
	fmt.Println("----------------------------")

	// Key On
	fmt.Println("📱 Sending GPS data with Key ON...")
	gpsData1 := createGPSData(deviceId, baseTime, -17.8252, 31.0335, 0.0, true)
	processGPSData(gpsData1, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// Key Off
	fmt.Println("📱 Sending GPS data with Key OFF...")
	gpsData2 := createGPSData(deviceId, baseTime.Add(30*time.Second), -17.8252, 31.0335, 0.0, false)
	processGPSData(gpsData2, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// Key On again
	fmt.Println("📱 Sending GPS data with Key ON again...")
	gpsData3 := createGPSData(deviceId, baseTime.Add(60*time.Second), -17.8252, 31.0335, 0.0, true)
	processGPSData(gpsData3, realtimeService)
}

func testTripDetection(realtimeService *services.RealtimeTripDetectionService, deviceId uint, baseTime time.Time) {
	fmt.Println("🚗 Testing Trip Detection")
	fmt.Println("-------------------------")

	// Trip Start
	fmt.Println("🚀 Starting trip...")
	gpsData1 := createGPSData(deviceId, baseTime, -17.8252, 31.0335, 0.0, true)
	processGPSData(gpsData1, realtimeService)
	time.Sleep(500 * time.Millisecond)

	gpsData2 := createGPSData(deviceId, baseTime.Add(30*time.Second), -17.8253, 31.0336, 25.0, true)
	processGPSData(gpsData2, realtimeService)
	time.Sleep(500 * time.Millisecond)

	gpsData3 := createGPSData(deviceId, baseTime.Add(60*time.Second), -17.8254, 31.0337, 30.0, true)
	processGPSData(gpsData3, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// Trip End
	fmt.Println("🛑 Ending trip...")
	gpsData4 := createGPSData(deviceId, baseTime.Add(90*time.Second), -17.8255, 31.0338, 0.0, false)
	processGPSData(gpsData4, realtimeService)
}

func testSpeedAlert(realtimeService *services.RealtimeTripDetectionService, deviceId uint, baseTime time.Time) {
	fmt.Println("⚡ Testing Speed Alert")
	fmt.Println("---------------------")

	// Normal speed
	fmt.Println("📱 Sending GPS data with normal speed...")
	gpsData1 := createGPSData(deviceId, baseTime, -17.8252, 31.0335, 50.0, true)
	processGPSData(gpsData1, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// Speed violation
	fmt.Println("📱 Sending GPS data with speed violation...")
	gpsData2 := createGPSData(deviceId, baseTime.Add(30*time.Second), -17.8253, 31.0336, 85.0, true)
	processGPSData(gpsData2, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// Another violation
	fmt.Println("📱 Sending GPS data with another speed violation...")
	gpsData3 := createGPSData(deviceId, baseTime.Add(60*time.Second), -17.8254, 31.0337, 95.0, true)
	processGPSData(gpsData3, realtimeService)
}

func testTowingEvent(realtimeService *services.RealtimeTripDetectionService, deviceId uint, baseTime time.Time) {
	fmt.Println("🚛 Testing Towing Event")
	fmt.Println("-----------------------")

	// Initial position
	fmt.Println("📱 Sending GPS data at initial position...")
	gpsData1 := createGPSData(deviceId, baseTime, -17.8252, 31.0335, 0.0, false)
	processGPSData(gpsData1, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// Moved without ignition
	fmt.Println("📱 Sending GPS data showing movement without ignition...")
	gpsData2 := createGPSData(deviceId, baseTime.Add(2*time.Minute), -17.8255, 31.0340, 0.0, false)
	processGPSData(gpsData2, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// Continue moving
	fmt.Println("📱 Sending GPS data showing continued movement...")
	gpsData3 := createGPSData(deviceId, baseTime.Add(4*time.Minute), -17.8258, 31.0345, 0.0, false)
	processGPSData(gpsData3, realtimeService)
}

func testCompleteJourney(realtimeService *services.RealtimeTripDetectionService, deviceId uint, baseTime time.Time) {
	fmt.Println("🚗 Testing Complete Journey")
	fmt.Println("--------------------------")

	// 1. Key On
	fmt.Println("1️⃣ Key ON event...")
	gpsData1 := createGPSData(deviceId, baseTime, -17.8252, 31.0335, 0.0, true)
	processGPSData(gpsData1, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// 2. Start moving (trip start)
	fmt.Println("2️⃣ Starting trip with movement...")
	gpsData2 := createGPSData(deviceId, baseTime.Add(30*time.Second), -17.8253, 31.0336, 15.0, true)
	processGPSData(gpsData2, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// 3. Continue driving
	fmt.Println("3️⃣ Continuing drive...")
	gpsData3 := createGPSData(deviceId, baseTime.Add(60*time.Second), -17.8254, 31.0337, 25.0, true)
	processGPSData(gpsData3, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// 4. Speed violation
	fmt.Println("4️⃣ Speed violation...")
	gpsData4 := createGPSData(deviceId, baseTime.Add(90*time.Second), -17.8255, 31.0338, 85.0, true)
	processGPSData(gpsData4, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// 5. Continue driving
	fmt.Println("5️⃣ Continue driving...")
	gpsData5 := createGPSData(deviceId, baseTime.Add(120*time.Second), -17.8256, 31.0339, 30.0, true)
	processGPSData(gpsData5, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// 6. Stop and Key Off (trip end)
	fmt.Println("6️⃣ Stop and Key OFF (trip end)...")
	gpsData6 := createGPSData(deviceId, baseTime.Add(150*time.Second), -17.8257, 31.0340, 0.0, false)
	processGPSData(gpsData6, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// 7. Towing event (movement without ignition)
	fmt.Println("7️⃣ Towing event (movement without ignition)...")
	gpsData7 := createGPSData(deviceId, baseTime.Add(5*time.Minute), -17.8260, 31.0345, 0.0, false)
	processGPSData(gpsData7, realtimeService)
	time.Sleep(500 * time.Millisecond)

	// 8. Key On again
	fmt.Println("8️⃣ Key ON again...")
	gpsData8 := createGPSData(deviceId, baseTime.Add(6*time.Minute), -17.8260, 31.0345, 0.0, true)
	processGPSData(gpsData8, realtimeService)
}

func createGPSData(deviceId uint, timestamp time.Time, lat, lng, speed float64, ignitionOn bool) *models.GPSData {
	direction := "N"
	mcc := "716"
	mnc := "01"
	lac := "1234"
	cellId := "5678"

	return &models.GPSData{
		ClientDeviceId: &deviceId,
		DeviceId:       fmt.Sprintf("TEST_DEVICE_%d", deviceId),
		GPSTimestamp:   &timestamp,
		Latitude:       lat,
		Longitude:      lng,
		Speed:          &speed,
		IgnitionStatus: &ignitionOn,
		Direction:      &direction,
		Mcc:            &mcc,
		Mnc:            &mnc,
		Lac:            &lac,
		CellId:         &cellId,
	}
}

func processGPSData(gpsData *models.GPSData, realtimeService *services.RealtimeTripDetectionService) {
	fmt.Printf("  📡 GPS: Lat=%.6f, Lon=%.6f, Speed=%.1f km/h, Ignition=%v\n",
		gpsData.Latitude, gpsData.Longitude, *gpsData.Speed, *gpsData.IgnitionStatus)

	// Save GPS data to database
	if err := config.DB.Create(gpsData).Error; err != nil {
		fmt.Printf("❌ Error saving GPS data: %v\n", err)
		return
	}

	// Send to real-time trip detection service
	if err := realtimeService.ProcessGPSData(gpsData); err != nil {
		fmt.Printf("❌ Error processing GPS data: %v\n", err)
	}
}
