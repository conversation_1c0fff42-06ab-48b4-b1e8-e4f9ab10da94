package main

import (
	"fmt"
	"log"
	"yotracker/config"
	"yotracker/internal/models"
	"yotracker/internal/services"
)

func main() {
	// Initialize database
	config.InitDB()

	// Test trip detection service
	tripService := services.NewTripDetectionService()

	// Check current state of GPS data
	var unassignedCount int64
	var assignedCount int64
	var totalTrips int64

	config.DB.Model(&models.GPSData{}).Where("trip_id IS NULL").Count(&unassignedCount)
	config.DB.Model(&models.GPSData{}).Where("trip_id IS NOT NULL").Count(&assignedCount)
	config.DB.Model(&models.Trip{}).Count(&totalTrips)

	fmt.Printf("Current GPS Data Status:\n")
	fmt.Printf("- Unassigned GPS points: %d\n", unassignedCount)
	fmt.Printf("- Assigned GPS points: %d\n", assignedCount)
	fmt.Printf("- Total trips: %d\n", totalTrips)

	// Check if there are any devices with GPS data
	var deviceCount int64
	config.DB.Model(&models.GPSData{}).Distinct("client_device_id").Count(&deviceCount)
	fmt.Printf("- Devices with GPS data: %d\n", deviceCount)

	// Get sample of recent GPS data
	var recentGPS []models.GPSData
	err := config.DB.Where("trip_id IS NULL").
		Order("gps_timestamp DESC").
		Limit(5).
		Find(&recentGPS).Error

	if err != nil {
		log.Printf("Error fetching recent GPS data: %v", err)
		return
	}

	fmt.Printf("\nRecent unassigned GPS data:\n")
	for _, gps := range recentGPS {
		if gps.ClientDeviceId != nil && gps.GPSTimestamp != nil && gps.Speed != nil {
			fmt.Printf("- Device %d: %s at (%.6f, %.6f) speed: %.1f km/h\n",
				*gps.ClientDeviceId, gps.GPSTimestamp.Format("2006-01-02 15:04:05"),
				gps.Latitude, gps.Longitude, *gps.Speed)
		}
	}

	// Run trip detection
	fmt.Printf("\nRunning trip detection...\n")
	err = tripService.ProcessUnassignedGPSData()
	if err != nil {
		log.Printf("Error in trip detection: %v", err)
		return
	}

	// Check results after processing
	var newUnassignedCount int64
	var newAssignedCount int64
	var newTotalTrips int64

	config.DB.Model(&models.GPSData{}).Where("trip_id IS NULL").Count(&newUnassignedCount)
	config.DB.Model(&models.GPSData{}).Where("trip_id IS NOT NULL").Count(&newAssignedCount)
	config.DB.Model(&models.Trip{}).Count(&newTotalTrips)

	fmt.Printf("\nAfter Trip Detection:\n")
	fmt.Printf("- Unassigned GPS points: %d (change: %d)\n", newUnassignedCount, newUnassignedCount-unassignedCount)
	fmt.Printf("- Assigned GPS points: %d (change: %d)\n", newAssignedCount, newAssignedCount-assignedCount)
	fmt.Printf("- Total trips: %d (change: %d)\n", newTotalTrips, newTotalTrips-totalTrips)

	// Show recent trips
	var recentTrips []models.Trip
	err = config.DB.Order("created_at DESC").Limit(5).Find(&recentTrips).Error
	if err != nil {
		log.Printf("Error fetching recent trips: %v", err)
		return
	}

	fmt.Printf("\nRecent trips:\n")
	for _, trip := range recentTrips {
		duration := "N/A"
		if trip.Duration != nil {
			duration = fmt.Sprintf("%ds", *trip.Duration)
		}
		fmt.Printf("- Trip %d: Device %d, %.2fkm, %s, %s\n",
			trip.Id, trip.ClientDeviceId, trip.Distance, duration, trip.StartTime.Format("2006-01-02 15:04:05"))
	}
}
