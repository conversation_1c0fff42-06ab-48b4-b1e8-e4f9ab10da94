# YoTracker - Fleet Management & GPS Tracking System

YoTracker is a comprehensive fleet management and GPS tracking platform designed for businesses that need to monitor and manage their vehicle fleets, drivers, and assets in real-time. The system provides advanced tracking capabilities, fleet analytics, driver management, and operational insights.

## 🚀 What YoTracker Does

YoTracker is a full-stack fleet management solution that helps businesses:

- **📍 Real-time GPS Tracking** - Monitor vehicle locations, routes, and movements in real-time
- **🚗 Fleet Management** - Manage vehicles, drivers, and fleet operations efficiently
- **👥 Driver Management** - Track driver assignments, training, and compliance
- **🚨 Alert System** - Get notified of geofence violations, speed alerts, and maintenance reminders
- **📊 Analytics & Reporting** - Generate comprehensive reports on fleet performance and costs
- **💰 Financial Management** - Handle invoicing, payments, and cost tracking
- **🎫 Support System** - Manage customer support tickets and technical issues
- **🔧 Maintenance Scheduling** - Track vehicle maintenance and service schedules
- **📱 Mobile-Friendly** - Access the system from any device with responsive design

## ✨ Key Features

### 🗺️ GPS Tracking & Location Services
- Real-time vehicle location tracking
- Historical route playback and analysis
- Geofence creation and monitoring
- Speed and location alerts
- Trip recording and optimization

### 🏢 Fleet Management
- Vehicle registration and management
- Driver assignment and scheduling
- Fleet performance analytics
- Fuel consumption tracking
- Maintenance scheduling and reminders

### 👥 Driver Management
- Driver profiles and licensing
- Training record management
- Compliance tracking
- Performance monitoring
- RFID-based driver identification

### 🚨 Monitoring & Alerts
- Geofence entry/exit notifications
- Speed limit violations
- Maintenance due alerts
- Driver behavior monitoring
- Custom alert configurations

### 📊 Reporting & Analytics
- Comprehensive fleet reports
- Cost analysis and optimization
- Performance metrics
- Custom report generation
- Scheduled report delivery

### 💰 Financial Management
- Invoice generation and management
- Payment processing and tracking
- Cost allocation and analysis
- Tax rate management
- Financial reporting

### 🎫 Customer Support
- Ticket-based support system
- Multi-department support (technical, billing, general)
- Internal notes and communication
- Email and Slack notifications
- Support analytics and metrics

## 🏗️ System Architecture

### Backend API (Admin/Staff Operations)
- **Base URL**: `/api/v1/backend`
- **Port**: 9000
- **Purpose**: REST API for administrative operations by fleet managers, support staff, and system administrators
- **Features**: Full CRUD operations, advanced analytics, system configuration, admin-level data access

### Frontend API (Client Operations)
- **Base URL**: `/api/v1/frontend`
- **Port**: 9001
- **Purpose**: REST API for client-facing operations by fleet owners and drivers
- **Features**: Limited access based on client permissions, focused on operational tasks, client-specific data filtering

### GPS Protocol Support
- **Port 5022**: GT06 protocol support
- **Port 5010**: H02 protocol support
- **Real-time**: WebSocket connections for live updates

## 🛠️ Technology Stack

- **Backend APIs**: Go (Golang) with Gin framework
- **Frontend Application**: Vue.js (separate repository)
- **Database**: MySQL with GORM ORM
- **Authentication**: JWT-based authentication
- **Real-time**: WebSocket connections
- **Notifications**: Email (SendGrid) and Slack integration
- **GPS Protocols**: GT06, H02, and extensible protocol system
- **API**: RESTful API with consistent naming conventions

## 📁 Project Structure

```
yotracker/
├── cmd/web/                    # Application entry points
│   ├── backend/               # Backend API server
│   └── frontend/              # Frontend API server
├── internal/                  # Internal packages
│   ├── models/               # Database models
│   ├── services/             # Business logic
│   └── utils/                # Utility functions
├── migrations/               # Database migrations
├── docs/                     # Documentation
│   ├── api_routes.md         # Complete API documentation
│   ├── api_routes_quick_reference.md  # Quick reference
│   └── support_ticket_system.md       # Support system docs
├── scripts/                  # Utility scripts
└── README.md                 # This file
```

## 🚀 Getting Started

### Prerequisites

#### Option 1: Local Development with Docker Dev Containers
- VS Code with Dev Containers extension
- Docker Desktop
- Git
- Node.js 16+ (for Vue.js frontend - separate repository)

#### Option 2: Docker Development (Recommended)
- Docker and Docker Compose
- Git

### Installation

#### Option 1: Local Development with Docker Dev Containers

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd yotracker
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your database and API credentials
   ```

3. **Open in VS Code with Dev Containers**
   ```bash
   code .
   # VS Code will prompt to reopen in container
   # Or use: Dev Containers: Reopen in Container
   ```

4. **Backend Development Container**
   - Open the backend folder in a dev container
   - Container includes Go, Git, and development tools
   - Hot reload enabled for development
   - Run the backend API server:
     ```bash
     go run cmd/web/backend/backend.go
     ```

5. **Frontend Development Container**
   - Open the frontend folder in a dev container
   - Container includes Go, Git, and development tools
   - Hot reload enabled for development
   - Run the frontend API server:
     ```bash
     go run cmd/web/frontend/frontend.go
     ```

6. **Start the Vue.js frontend application** (separate repository)
   ```bash
   # Navigate to Vue.js frontend repository
   cd ../yotracker-frontend
   npm install
   npm run serve
   ```

**Note**: Docker dev containers provide isolated development environments with all necessary tools pre-installed, ensuring consistent development experience across different machines.

#### Option 2: Docker Development (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd yotracker
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your database and API credentials
   ```

3. **Start all services with Docker Compose**
   ```bash
   docker-compose up --build
   ```

4. **Run database migrations**
   ```bash
   docker-compose exec backend go run cmd/web/backend/backend.go
   ```

5. **Access the services**
   - Backend API: http://localhost:9000
   - Frontend API: http://localhost:9001
   - Database: localhost:3306
   - GPS Protocols: 
     - GT06: localhost:5022
     - H02: localhost:5010

### Access the Application
- **Backend API**: http://localhost:9000 (REST API for admin/staff operations)
- **Frontend API**: http://localhost:9001 (REST API for client operations)
- **Vue.js Frontend**: Separate Vue.js application that consumes these APIs
- **GPS Protocols**: 
  - GT06: localhost:5022
  - H02: localhost:5010

### Docker Commands

```bash
# Start all services
docker-compose up --build

# Start in background
docker-compose up -d

# View logs
docker-compose logs -f backend
docker-compose logs -f frontend

# Run tests in Docker
docker-compose exec backend go test ./...

# Access database
docker-compose exec db mysql -u root -p yotracker

# Stop all services
docker-compose down

# Clean up volumes
docker-compose down -v
```

## 📚 Documentation

- **[Complete API Documentation](docs/api_routes.md)** - Comprehensive list of all API endpoints
- **[API Quick Reference](docs/api_routes_quick_reference.md)** - Quick reference for common routes
- **[Support System Documentation](docs/support_ticket_system.md)** - Support ticket system guide

## 🧪 Testing

Run the comprehensive test suite:

### Local Testing (Dev Containers)
```bash
# Run all support ticket tests
./scripts/run-support-ticket-tests.sh

# Run individual test suites
go test ./internal/services -run TestSupportTicketService -v
go test ./cmd/web/backend/controllers -run TestBackendSupportTicketControllers -v
go test ./cmd/web/frontend/controllers -run TestFrontendSupportTicketControllers -v
```

### Docker Testing (Recommended)
```bash
# Run all tests in Docker
docker-compose exec backend go test ./...

# Run specific test suites in Docker
docker-compose exec backend go test ./internal/services -run TestSupportTicketService -v
docker-compose exec backend go test ./cmd/web/backend/controllers -run TestBackendSupportTicketControllers -v
docker-compose exec frontend go test ./cmd/web/frontend/controllers -run TestFrontendSupportTicketControllers -v
```

## 🔧 Configuration

### Environment Variables
- `DB_HOST` - Database host
- `DB_PORT` - Database port
- `DB_NAME` - Database name
- `DB_USER` - Database username
- `DB_PASSWORD` - Database password
- `JWT_SECRET` - JWT signing secret
- `SENDGRID_API_KEY` - Email service API key
- `SLACK_WEBHOOK_URL` - Slack notification webhook

### GPS Device Configuration
- **GT06 Protocol**: Configure devices to send data to port 5022
- **H02 Protocol**: Configure devices to send data to port 5010
- **Data Format**: System supports standard GPS protocol formats

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For technical support and questions:
- Create a support ticket through the system
- Check the [Support System Documentation](docs/support_ticket_system.md)
- Review the [API Documentation](docs/api_routes.md)

## 🔄 Version History

- **v1.0** (December 2024) - Initial release with comprehensive fleet management features
  - GPS tracking and geofencing
  - Fleet and driver management
  - Support ticket system
  - Financial management
  - Reporting and analytics

---

**YoTracker** - Empowering businesses with intelligent fleet management solutions.