-- Add real-time trip detection settings
INSERT INTO settings (name, setting_key, setting_value, category) VALUES
('Realtime Trip Flush Interval Seconds', 'realtime_trip_flush_interval_seconds', '30', 'realtime_trip_detection'),
('Realtime Trip Worker Count', 'realtime_trip_worker_count', '4', 'realtime_trip_detection'),
('Realtime Trip Queue Buffer Size', 'realtime_trip_queue_size', '10000', 'realtime_trip_detection'),
('Realtime Trip Detection Enabled', 'realtime_trip_detection_enabled', 'true', 'realtime_trip_detection')
ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);
