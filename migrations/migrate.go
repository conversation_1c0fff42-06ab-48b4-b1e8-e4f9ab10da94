package migrations

import (
	"yotracker/config"
	"yotracker/internal/models"
)

func Migrate() {
	// Disable foreign key checks during migration to handle circular dependencies
	config.DB.Exec("SET FOREIGN_KEY_CHECKS = 0")
	defer config.DB.Exec("SET FOREIGN_KEY_CHECKS = 1")

	// Migrate the schema
	config.DB.AutoMigrate(
		&models.Country{},
		&models.TaxRate{},
		&models.Currency{},
		&models.Setting{},
		&models.Alert{},
		&models.PaymentType{},
		&models.Client{},
		&models.Fleet{},
		&models.Driver{}, // Move Driver before User to fix foreign key constraint
		&models.User{},
		&models.ClientDevice{},
		&models.CommandLog{},
		&models.DeviceType{},

		// Alert preference models - moved here right after their dependencies
		&models.AlertPreference{},
		&models.FleetAlertPreference{},
		&models.DeviceAlertPreference{},

		&models.GPSData{},
		&models.Protocol{},

		&models.Role{},
		&models.Permission{},
		&models.RolePermission{},
		&models.ClientRole{},
		&models.Invoice{},
		&models.InvoiceItem{},
		&models.InvoicePayment{},
		&models.ClientDeviceDailyStat{},
		&models.ChatbotSession{},
		&models.Geofence{},
		&models.GeofenceEvent{},
		&models.CommunicationCampaignLog{},
		&models.DriverDeviceAssignment{},
		&models.Trip{},
		&models.DrivingBehaviorEvent{},
		&models.Report{},
		&models.ScheduledReport{},

		// New report-related models
		&models.CustomerVisit{},
		&models.MaintenanceSchedule{},
		&models.VehicleHealthMetric{},
		&models.OperatingCost{},
		&models.EmissionsData{},
		&models.DriverTraining{},
		&models.AssetPerformance{},
		&models.RouteOptimization{},
		&models.SafetyCompliance{},
		&models.VehicleLifecycle{},
		&models.ServiceTechnician{},
		&models.ConstructionEquipment{},
		&models.SalesTerritory{},
		&models.DeliveryPerformance{},

		// Support ticket models
		&models.SupportTicket{},
		&models.SupportTicketReply{},

		// File upload models
		&models.FileUpload{},

		// Notification models
		&models.NotificationLog{},
	)

}
