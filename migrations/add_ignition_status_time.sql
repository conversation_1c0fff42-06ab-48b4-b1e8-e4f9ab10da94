-- Add ignition_status_time field to track when ignition status last changed
ALTER TABLE client_devices 
ADD COLUMN ignition_status_time TIMESTAMP NULL 
COMMENT 'Timestamp when ignition status last changed';

-- Add index for efficient queries on ignition status time
CREATE INDEX idx_client_devices_ignition_status_time 
ON client_devices (ignition_status_time);

-- Update existing records to set ignition_status_time to last_status_update if ignition_status is set
UPDATE client_devices 
SET ignition_status_time = last_status_update 
WHERE ignition_status IS NOT NULL 
AND last_status_update IS NOT NULL;
