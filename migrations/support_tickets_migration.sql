-- Support Tickets Migration
-- This migration creates the support ticket system tables

-- Create support_tickets table
CREATE TABLE IF NOT EXISTS `support_tickets` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `client_id` bigint unsigned NOT NULL,
  `created_by_id` bigint unsigned NOT NULL,
  `assigned_to_id` bigint unsigned DEFAULT NULL,
  `client_device_id` bigint unsigned DEFAULT NULL,
  `department` enum('billing','technical','general','feature_request','bug_report') NOT NULL DEFAULT 'general',
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `status` enum('open','assigned','in_progress','waiting_for_customer','resolved','closed') NOT NULL DEFAULT 'open',
  `subject` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `resolution` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `closed_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_support_tickets_client_id` (`client_id`),
  KEY `idx_support_tickets_created_by_id` (`created_by_id`),
  KEY `idx_support_tickets_assigned_to_id` (`assigned_to_id`),
  KEY `idx_support_tickets_client_device_id` (`client_device_id`),
  KEY `idx_support_tickets_department` (`department`),
  KEY `idx_support_tickets_priority` (`priority`),
  KEY `idx_support_tickets_status` (`status`),
  KEY `idx_support_tickets_created_at` (`created_at`),
  CONSTRAINT `fk_support_tickets_client_id` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_support_tickets_created_by_id` FOREIGN KEY (`created_by_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_support_tickets_assigned_to_id` FOREIGN KEY (`assigned_to_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_support_tickets_client_device_id` FOREIGN KEY (`client_device_id`) REFERENCES `client_devices` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create support_ticket_replies table
CREATE TABLE IF NOT EXISTS `support_ticket_replies` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `ticket_id` bigint unsigned NOT NULL,
  `created_by_id` bigint unsigned NOT NULL,
  `is_internal` tinyint(1) NOT NULL DEFAULT '0',
  `message` text NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_support_ticket_replies_ticket_id` (`ticket_id`),
  KEY `idx_support_ticket_replies_created_by_id` (`created_by_id`),
  KEY `idx_support_ticket_replies_created_at` (`created_at`),
  CONSTRAINT `fk_support_ticket_replies_ticket_id` FOREIGN KEY (`ticket_id`) REFERENCES `support_tickets` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_support_ticket_replies_created_by_id` FOREIGN KEY (`created_by_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default settings for support tickets
INSERT IGNORE INTO `settings` (`name`, `setting_key`, `setting_value`, `category`, `created_at`, `updated_at`) VALUES
('Support Ticket Admin Email', 'support_ticket_admin_email', '<EMAIL>', 'support', NOW(), NOW()),
('Support Ticket Slack Channel', 'support_ticket_slack_channel', '#yopractice-support', 'support', NOW(), NOW()),
('Support Ticket Email Notifications', 'support_ticket_email_notifications', 'true', 'support', NOW(), NOW()),
('Support Ticket Slack Notifications', 'support_ticket_slack_notifications', 'true', 'support', NOW(), NOW());
