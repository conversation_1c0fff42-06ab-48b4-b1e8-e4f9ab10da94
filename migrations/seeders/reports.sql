-- YoTracker Reports Seeder
-- Powerful Client-Focused Reports that deliver real business value
-- These reports are designed to capture clients and provide actionable insights

-- Disable foreign key checks for this operation
SET FOREIGN_KEY_CHECKS = 0;

-- Clear existing reports and insert powerful ones
TRUNCATE TABLE reports;

-- Add report_order column if it doesn't exist

INSERT INTO reports (name, description, category, report_type, status, default_filters, report_order, created_at, updated_at) VALUES

-- === CORE GPS & TRACKING REPORTS ===
('Position Log Report', 'Raw GPS location data with timestamps, coordinates, speed, and device information for detailed tracking analysis.', 'Detail', 'position_log', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "fleet_ids": [], "per_page": 1000, "page": 1}', 1, NOW(), NOW()),

-- === TRIP & MOVEMENT REPORTS ===
('Trip Detail Report', 'Complete trip analysis with start/end locations, distance, duration, fuel consumption, and driver info.', 'Detail', 'trip_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "fleet_ids": [], "min_distance": null, "max_distance": null, "min_duration": null, "max_duration": null, "per_page": 100, "page": 1}', 2, NOW(), NOW()),

('Daily Trip Summary', 'Daily breakdown of trips per vehicle showing total distance, time, fuel usage, and efficiency metrics.', 'Summary', 'trip_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "fleet_ids": [], "group_by": ["date"], "per_page": 100, "page": 1}', 3, NOW(), NOW()),

('Monthly Mileage Report', 'Monthly distance traveled per vehicle with cost analysis and maintenance scheduling recommendations.', 'Summary', 'mileage_month_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "fleet_ids": [], "group_by": ["month", "device"], "per_page": 100, "page": 1}', 4, NOW(), NOW()),

('Vehicle Utilization Report', 'Shows how effectively vehicles are being used - idle time vs active time, utilization percentage.', 'Summary', 'vehicle_utilization', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "fleet_ids": [], "group_by": ["device"], "per_page": 100, "page": 1}', 5, NOW(), NOW()),

('Route Efficiency Analysis', 'Analyzes common routes for optimization opportunities, identifies inefficient paths and suggests improvements.', 'Analysis', 'route_deviation_report', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_distance": 5.0, "per_page": 100, "page": 1}', 6, NOW(), NOW()),

-- === DRIVER BEHAVIOR & SAFETY REPORTS ===
('Driver Safety Scorecard', 'Comprehensive driver safety scoring based on speeding, harsh braking, acceleration, cornering events.', 'Behavior', 'driver_safety_scorecard', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_severity": null, "max_severity": null, "per_page": 100, "page": 1}', 7, NOW(), NOW()),

('Speeding Violations Report', 'Detailed speeding events with location, speed exceeded, duration, and safety risk assessment.', 'Behavior', 'speeding_violations', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "event_types": ["overspeed"], "min_severity": 1.0, "per_page": 100, "page": 1}', 8, NOW(), NOW()),

('Harsh Driving Events', 'All harsh acceleration, braking, and cornering events with severity levels and coaching recommendations.', 'Behavior', 'rash_drive_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "event_types": ["harsh_acceleration", "harsh_braking", "harsh_cornering"], "min_severity": 1.0, "per_page": 100, "page": 1}', 9, NOW(), NOW()),

('Driver Performance Ranking', 'Ranks drivers by safety score, fuel efficiency, and overall performance with improvement suggestions.', 'Behavior', 'driver_performance_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver"], "order_by": "safety_score", "order_dir": "desc", "per_page": 100, "page": 1}', 10, NOW(), NOW()),

('Fatigue & Overtime Alert', 'Identifies drivers exceeding safe driving hours, potential fatigue risks, and compliance violations.', 'Behavior', 'fatigue_report', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_duration": 480, "per_page": 100, "page": 1}', 11, NOW(), NOW()),

-- === GEOFENCE & LOCATION REPORTS ===
('Geofence Activity Report', 'Detailed entry/exit times for all geofences with dwell time analysis and unauthorized access alerts.', 'Detail', 'geofence_activity', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "geofence_ids": [], "per_page": 100, "page": 1}', 12, NOW(), NOW()),

('Customer Visit Report', 'Tracks visits to customer locations with arrival/departure times, duration, and service efficiency metrics.', 'Detail', 'destination_arrival', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "location_name": null, "per_page": 100, "page": 1}', 13, NOW(), NOW()),

('Unauthorized Location Alerts', 'Vehicles detected in restricted areas or outside authorized zones with time and duration details.', 'Security', 'exception_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "event_types": ["geofence_violation", "unauthorized_area"], "per_page": 100, "page": 1}', 14, NOW(), NOW()),

('Job Site Productivity', 'Time spent at job sites, productivity metrics, and billing-ready timesheet data.', 'Productivity', 'stop_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_duration": 300, "per_page": 100, "page": 1}', 15, NOW(), NOW()),

-- === COST & EFFICIENCY REPORTS ===
('Fuel Consumption Analysis', 'Detailed fuel usage per vehicle with cost breakdown, efficiency trends, and savings opportunities.', 'Cost', 'fuel_consumption_analysis', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device", "date"], "per_page": 100, "page": 1}', 16, NOW(), NOW()),

('Operating Cost Report', 'Complete cost analysis including fuel, maintenance, insurance, and depreciation per vehicle/mile.', 'Cost', 'fuel_estimation_month_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month", "device"], "per_page": 100, "page": 1}', 17, NOW(), NOW()),

('Idle Time Cost Analysis', 'Calculates cost of excessive idling with fuel waste, engine wear, and environmental impact.', 'Cost', 'idle_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_duration": 300, "per_page": 100, "page": 1}', 18, NOW(), NOW()),

('Fleet ROI Dashboard', 'Return on investment analysis showing cost savings, efficiency gains, and fleet optimization results.', 'Financial', 'fleet_roi_dashboard', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month"], "per_page": 100, "page": 1}', 19, NOW(), NOW()),

-- === MAINTENANCE & VEHICLE HEALTH ===
('Maintenance Due Report', 'Upcoming maintenance schedules based on mileage, engine hours, and time intervals with cost estimates.', 'Maintenance', 'maintenance_schedule', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 20, NOW(), NOW()),

('Vehicle Health Dashboard', 'Real-time vehicle diagnostics, fault codes, battery status, and preventive maintenance alerts.', 'Maintenance', 'vehicle_health_dashboard', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 21, NOW(), NOW()),

('Breakdown & Repair History', 'Complete maintenance history with costs, downtime analysis, and reliability trends per vehicle.', 'Maintenance', 'maintenance_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 22, NOW(), NOW()),

-- === COMPLIANCE & SECURITY REPORTS ===
('Hours of Service Compliance', 'Driver hours tracking for DOT compliance with violation alerts and logbook integration.', 'Compliance', 'working_time_day_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver", "date"], "per_page": 100, "page": 1}', 23, NOW(), NOW()),

('Vehicle Security Report', 'After-hours usage, unauthorized access, theft alerts, and security breach notifications.', 'Security', 'after_hour_month_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 24, NOW(), NOW()),

('Speed Limit Compliance', 'Compliance with posted speed limits by location with violation frequency and risk assessment.', 'Compliance', 'geofence_speeding_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "event_types": ["overspeed"], "speed_limit": null, "per_page": 100, "page": 1}', 25, NOW(), NOW()),

-- === EXECUTIVE & MANAGEMENT REPORTS ===
('Executive Fleet Summary', 'High-level KPIs for management: total miles, costs, safety scores, efficiency metrics, and trends.', 'Executive', 'executive_fleet_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month"], "per_page": 100, "page": 1}', 26, NOW(), NOW()),

('Fleet Performance Trends', 'Month-over-month performance analysis with predictive insights and optimization recommendations.', 'Executive', 'fleet_performance_trends', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month"], "order_by": "date", "order_dir": "desc", "per_page": 100, "page": 1}', 27, NOW(), NOW()),

('Cost Center Analysis', 'Cost breakdown by department, project, or cost center with budget variance and allocation insights.', 'Financial', 'cost_center_analysis', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["cost_center", "month"], "per_page": 100, "page": 1}', 28, NOW(), NOW()),

('Environmental Impact Report', 'Carbon footprint analysis, fuel efficiency trends, and sustainability metrics for ESG reporting.', 'Environmental', 'environmental_impact', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month"], "per_page": 100, "page": 1}', 29, NOW(), NOW()),

-- === OPERATIONAL REPORTS ===
('Daily Operations Dashboard', 'Real-time operational overview: active vehicles, current locations, alerts, and daily performance.', 'Operations', 'daily_operations_dashboard', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["date"], "per_page": 100, "page": 1}', 30, NOW(), NOW()),

('Vehicle Availability Report', 'Shows which vehicles are available, in use, under maintenance, or out of service with scheduling.', 'Operations', 'vehicle_availability', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 31, NOW(), NOW()),

('Emergency Response Report', 'Panic button activations, emergency stops, accident detection, and response time analysis.', 'Emergency', 'emergency_response', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "event_types": ["panic_button", "emergency_stop", "accident_detection"], "per_page": 100, "page": 1}', 32, NOW(), NOW()),

('Asset Tracking Report', 'Location and status of all tracked assets with last known position and movement history.', 'Operations', 'last_location', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 33, NOW(), NOW()),

-- === CUSTOM BUSINESS REPORTS ===
('Delivery Performance Report', 'On-time delivery rates, route optimization, customer satisfaction metrics, and delivery efficiency.', 'Business', 'delivery_performance', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver", "date"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Service Technician Report', 'Field service efficiency: jobs completed, travel time, customer locations, and productivity metrics.', 'Business', 'service_technician', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver", "date"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Construction Equipment Report', 'Equipment utilization, job site time, productivity metrics, and equipment ROI analysis.', 'Business', 'construction_equipment', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device", "date"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Sales Territory Analysis', 'Sales team territory coverage, customer visit frequency, and territory optimization insights.', 'Business', 'sales_territory_analysis', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver", "territory"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

-- === ADDITIONAL SPECIALIZED REPORTS ===
('Driver Behavior Analysis', 'Comprehensive analysis of driver behavior patterns, trends, and improvement recommendations.', 'Behavior', 'driver_behavior_analysis', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver", "month"], "per_page": 100, "page": 1}', 38, NOW(), NOW()),

('Fuel Efficiency Trends', 'Long-term fuel efficiency analysis with cost projections and optimization recommendations.', 'Cost', 'fuel_efficiency_trends', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month"], "order_by": "date", "order_dir": "desc", "per_page": 100, "page": 1}', 39, NOW(), NOW()),

('Maintenance Cost Analysis', 'Detailed breakdown of maintenance costs with predictive maintenance recommendations.', 'Maintenance', 'maintenance_cost_analysis', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device", "month"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Fleet Productivity Report', 'Overall fleet productivity metrics with utilization and efficiency insights.', 'Operations', 'fleet_productivity', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month"], "per_page": 100, "page": 1}', 40, NOW(), NOW()),

('Safety Compliance Report', 'Comprehensive safety compliance tracking with regulatory requirement monitoring.', 'Compliance', 'safety_compliance', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Asset Performance Report', 'Individual asset performance tracking with ROI and replacement recommendations.', 'Financial', 'asset_performance', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device", "month"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Route Optimization Report', 'Advanced route optimization analysis with time and fuel savings recommendations.', 'Analysis', 'route_optimization', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_distance": 5.0, "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Driver Training Report', 'Driver training needs analysis based on performance and safety metrics.', 'Behavior', 'driver_training', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver"], "order_by": "safety_score", "order_dir": "desc", "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Vehicle Lifecycle Report', 'Complete vehicle lifecycle analysis with depreciation and replacement planning.', 'Financial', 'vehicle_lifecycle', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Operational Efficiency Report', 'Overall operational efficiency metrics with improvement recommendations.', 'Operations', 'operational_efficiency', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

-- === ADDITIONAL IMPLEMENTED REPORTS ===
('Speeding Detail Report', 'Detailed speeding events with location, speed exceeded, duration, and safety risk assessment.', 'Behavior', 'speeding_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "event_types": ["overspeed"], "min_severity": 1.0, "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Mileage Day Summary', 'Daily mileage breakdown per vehicle with efficiency metrics and cost analysis.', 'Summary', 'mileage_day_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["date", "device"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Trip Day Summary', 'Daily trip summary with total trips, distance, and duration per vehicle.', 'Summary', 'trip_day_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["date", "device"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Working Time Day Summary', 'Daily working hours per driver with overtime analysis and compliance tracking.', 'Summary', 'working_time_day_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["date", "driver"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Fleet Productive Day Summary', 'Daily fleet productivity metrics with utilization and efficiency insights.', 'Summary', 'fleet_productive_day_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["date"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('After Hour Month Summary', 'Monthly after-hours usage analysis with security and cost implications.', 'Summary', 'after_hour_month_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Mileage Achieving Summary', 'Mileage achievement analysis with targets vs actual performance.', 'Summary', 'mileage_achieving_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month", "device"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Fuel Estimation Month Summary', 'Monthly fuel consumption estimation with cost analysis and trends.', 'Summary', 'fuel_estimation_month_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month", "device"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Fuel Estimation Hour Summary', 'Hourly fuel consumption analysis with efficiency patterns and optimization opportunities.', 'Summary', 'fuel_estimation_hour_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["hour", "device"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Vehicle Usage Summary', 'Vehicle utilization analysis with usage patterns and efficiency metrics.', 'Summary', 'vehicle_usage_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device", "month"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Exception Summary', 'Summary of all exception events with frequency and severity analysis.', 'Summary', 'exception_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["event_type", "month"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Driver Exception Summary', 'Driver-specific exception analysis with safety and compliance insights.', 'Summary', 'driver_exception_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver", "event_type"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Speeding Exception Summary', 'Speeding violation summary with frequency and severity analysis.', 'Summary', 'speeding_exception_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver", "month"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Vehicle Performance Summary', 'Vehicle performance metrics with efficiency and reliability analysis.', 'Summary', 'vehicle_performance_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device", "month"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Vehicle Online Summary', 'Vehicle online status analysis with connectivity and uptime metrics.', 'Summary', 'vehicle_online_summary', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device", "date"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Cargo Day Summary Fuel', 'Daily cargo and fuel consumption analysis for logistics optimization.', 'Summary', 'cargo_day_summary_fuel', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["date", "device"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('OBD Report', 'On-board diagnostics data analysis with vehicle health and performance insights.', 'Summary', 'obd_report', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Fuel Estimation', 'Fuel consumption estimation with cost analysis and efficiency metrics.', 'Maintenance', 'fuel_estimation', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device", "month"], "per_page": 100, "page": 1}', 37, NOW(), NOW()),

('Fuel Estimation Hour', 'Hourly fuel consumption estimation with detailed analysis.', 'Maintenance', 'fuel_estimation_hour', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["hour", "device"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Maintenance Schedule Hour', 'Hourly maintenance schedule with detailed timing and cost analysis.', 'Maintenance', 'maintenance_schedule_hour', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Maintenance Summary', 'Maintenance summary with cost analysis and scheduling insights.', 'Maintenance', 'maintenance_summary', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device", "month"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Violation Stay Report', 'Violation duration analysis with compliance and safety insights.', 'Behavior', 'violation_stay_report', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_duration": 300, "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Vehicle Alive Report', 'Vehicle status and connectivity analysis with uptime metrics.', 'Management', 'vehicle_alive_report', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device", "date"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),



('Login History Detail', 'Detailed user login history with security and access analysis.', 'Management', 'login_history_detail', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Login History Summary', 'User login summary with access patterns and security insights.', 'Management', 'login_history_summary', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["user", "date"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

-- === ADDITIONAL DETAIL REPORTS ===
('Position Log Driver', 'Driver-specific GPS position data with enhanced driver information.', 'Detail', 'position_log_driver', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 1000, "page": 1}', 999, NOW(), NOW()),

('Trip Detail Delta', 'Trip detail with delta analysis showing changes and improvements over time.', 'Detail', 'trip_detail_delta', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Trip Mileage', 'Trip mileage analysis with distance tracking and efficiency metrics.', 'Detail', 'trip_mileage', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_distance": 1.0, "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Stop Detail', 'Detailed stop analysis with location, duration, and purpose tracking.', 'Detail', 'stop_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_duration": 60, "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Idle Detail', 'Idle time analysis with fuel consumption and cost implications.', 'Detail', 'idle_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_duration": 300, "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Door Detail', 'Door open/close events with security and access analysis.', 'Detail', 'door_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "event_types": ["door_open", "door_close"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Exception Detail', 'Detailed exception events with severity and resolution tracking.', 'Detail', 'exception_detail', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_severity": 1.0, "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Destination Zone In', 'Destination zone entry analysis with arrival patterns and efficiency metrics.', 'Detail', 'destination_zone_in', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Last Location History', 'Historical location tracking with movement patterns and route analysis.', 'Detail', 'last_location_history', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Cargo Detail', 'Cargo tracking and management with delivery status and efficiency analysis.', 'Detail', 'cargo_detail', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Cargo Fuel Consumption', 'Cargo-specific fuel consumption analysis with cost allocation.', 'Detail', 'cargo_fuel_consumption', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["cargo", "date"], "per_page": 100, "page": 1}', 999, NOW(), NOW()),

('Rash Drive Detail', 'Harsh driving event details with severity and coaching recommendations.', 'Detail', 'rash_drive_detail', 'inactive', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "event_types": ["harsh_acceleration", "harsh_braking", "harsh_cornering"], "min_severity": 1.0, "per_page": 100, "page": 1}', 999, NOW(), NOW()),

-- === CRUCIAL MISSING REPORTS ===
('Real-time Fleet Dashboard', 'Live fleet status with current locations, active trips, alerts, and operational overview.', 'Operations', 'real_time_dashboard', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 50, "page": 1}', 34, NOW(), NOW()),

('Driver Scorecard', 'Comprehensive driver performance scoring with safety, efficiency, and compliance metrics.', 'Behavior', 'driver_scorecard', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver"], "order_by": "score", "order_dir": "desc", "per_page": 100, "page": 1}', 35, NOW(), NOW()),

('Fleet Health Monitor', 'Overall fleet health status with maintenance alerts, vehicle availability, and operational readiness.', 'Maintenance', 'fleet_health_monitor', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 36, NOW(), NOW()),

('Cost Analysis Dashboard', 'Comprehensive cost analysis with fuel, maintenance, insurance, and operational costs per vehicle.', 'Financial', 'cost_analysis_dashboard', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device", "month"], "per_page": 100, "page": 1}', 37, NOW(), NOW()),

('Compliance Dashboard', 'Regulatory compliance overview with hours of service, safety violations, and audit readiness.', 'Compliance', 'compliance_dashboard', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver", "month"], "per_page": 100, "page": 1}', 38, NOW(), NOW()),

('Route Analysis Report', 'Detailed route analysis with optimization opportunities, traffic patterns, and efficiency metrics.', 'Analysis', 'route_analysis', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "min_distance": 5.0, "per_page": 100, "page": 1}', 39, NOW(), NOW()),

('Predictive Maintenance Report', 'Predictive maintenance recommendations based on vehicle usage, mileage, and health indicators.', 'Maintenance', 'predictive_maintenance', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "per_page": 100, "page": 1}', 40, NOW(), NOW()),

('Fleet Optimization Report', 'Fleet optimization recommendations with utilization analysis, right-sizing suggestions, and cost savings.', 'Financial', 'fleet_optimization', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["device"], "per_page": 100, "page": 1}', 41, NOW(), NOW()),

('Driver Training Needs Report', 'Driver training recommendations based on safety violations, performance metrics, and skill gaps.', 'Behavior', 'driver_training_needs', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["driver"], "order_by": "training_priority", "order_dir": "desc", "per_page": 100, "page": 1}', 42, NOW(), NOW()),

('Environmental Impact Dashboard', 'Environmental impact analysis with carbon footprint, fuel efficiency trends, and sustainability metrics.', 'Environmental', 'environmental_dashboard', 'active', 
'{"start_date": null, "end_date": null, "client_device_ids": [], "driver_ids": [], "group_by": ["month"], "per_page": 100, "page": 1}', 43, NOW(), NOW());

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;
