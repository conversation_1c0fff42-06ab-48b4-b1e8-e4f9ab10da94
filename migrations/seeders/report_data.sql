-- Sample data for comprehensive reports
-- This will make all reports return real, meaningful data

-- Sample customer visits data
INSERT INTO customer_visits (client_id, client_device_id, driver_id, customer_name, customer_address, visit_date, arrival_time, departure_time, visit_duration, visit_type, status) VALUES
(1, 1, 1, 'ABC Manufacturing', '123 Industrial Blvd, City Center', '2025-01-15', '2025-01-15 09:00:00', '2025-01-15 10:30:00', 5400, 'delivery', 'completed'),
(1, 2, 2, 'XYZ Logistics', '456 Warehouse St, Business District', '2025-01-15', '2025-01-15 11:00:00', '2025-01-15 12:15:00', 4500, 'pickup', 'completed'),
(1, 1, 1, 'Tech Solutions Inc', '789 Innovation Ave, Tech Park', '2025-01-16', '2025-01-16 08:30:00', '2025-01-16 09:45:00', 4500, 'service', 'completed'),
(1, 3, 3, 'Global Retail', '321 Commerce Rd, Mall Area', '2025-01-16', '2025-01-16 14:00:00', '2025-01-16 15:30:00', 5400, 'sales', 'completed'),
(1, 2, 2, 'Metro Distribution', '654 Supply Chain Way, Port Area', '2025-01-17', '2025-01-17 10:00:00', '2025-01-17 11:20:00', 4800, 'delivery', 'completed');

-- Sample maintenance schedule data
INSERT INTO maintenance_schedule (client_id, client_device_id, maintenance_type, service_type, scheduled_date, due_date, cost, service_provider, description, status, priority) VALUES
(1, 1, 'scheduled', 'Oil Change & Filter', '2025-01-20', '2025-01-20', 150.00, 'Quick Lube Pro', 'Regular oil change and filter replacement', 'scheduled', 'medium'),
(1, 2, 'preventive', 'Brake Inspection', '2025-01-22', '2025-01-22', 200.00, 'Brake Masters', 'Complete brake system inspection and adjustment', 'scheduled', 'high'),
(1, 3, 'corrective', 'Engine Repair', '2025-01-18', '2025-01-18', 850.00, 'Engine Experts', 'Engine overheating issue repair', 'completed', 'critical'),
(1, 1, 'scheduled', 'Tire Rotation', '2025-01-25', '2025-01-25', 80.00, 'Tire World', 'Tire rotation and balance', 'scheduled', 'low'),
(1, 2, 'preventive', 'Battery Replacement', '2025-01-30', '2025-01-30', 120.00, 'Battery Plus', 'Battery replacement and testing', 'scheduled', 'medium');

-- Sample vehicle health metrics data
INSERT INTO vehicle_health_metrics (client_id, client_device_id, metric_date, engine_hours, fuel_efficiency, engine_temperature, oil_pressure, battery_voltage, tire_pressure_front, tire_pressure_rear, brake_condition, overall_health_score, alerts_count) VALUES
(1, 1, '2025-01-15', 8, 12.5, 85.2, 45.0, 12.8, 32.0, 30.0, 'good', 92.5, 0),
(1, 2, '2025-01-15', 7, 11.8, 82.1, 42.5, 12.6, 31.5, 29.5, 'good', 88.0, 1),
(1, 3, '2025-01-15', 9, 10.2, 88.5, 38.0, 12.2, 28.0, 27.0, 'fair', 75.5, 3),
(1, 1, '2025-01-16', 6, 12.8, 83.0, 46.0, 12.9, 32.5, 30.5, 'good', 94.0, 0),
(1, 2, '2025-01-16', 8, 11.5, 84.2, 43.0, 12.7, 31.0, 29.0, 'good', 89.5, 1);

-- Sample operating costs data
INSERT INTO operating_costs (client_id, client_device_id, cost_date, cost_type, cost_category, amount, description, vendor) VALUES
(1, 1, '2025-01-15', 'fuel', 'diesel', 450.00, 'Fuel purchase for daily operations', 'Shell Station'),
(1, 2, '2025-01-15', 'fuel', 'diesel', 380.00, 'Fuel purchase for daily operations', 'Exxon Station'),
(1, 3, '2025-01-15', 'fuel', 'diesel', 520.00, 'Fuel purchase for daily operations', 'BP Station'),
(1, 1, '2025-01-16', 'maintenance', 'repair', 150.00, 'Oil change and filter replacement', 'Quick Lube Pro'),
(1, 2, '2025-01-16', 'labor', 'driver_salary', 120.00, 'Driver daily salary', 'Payroll'),
(1, 3, '2025-01-16', 'insurance', 'vehicle_insurance', 25.00, 'Daily vehicle insurance cost', 'Insurance Co'),
(1, 1, '2025-01-17', 'depreciation', 'vehicle_depreciation', 35.00, 'Daily vehicle depreciation', 'Accounting'),
(1, 2, '2025-01-17', 'fuel', 'diesel', 420.00, 'Fuel purchase for daily operations', 'Shell Station');

-- Sample emissions data
INSERT INTO emissions_data (client_id, client_device_id, date, fuel_consumed, distance_traveled, co2_emissions, nox_emissions, particulate_matter, fuel_efficiency, carbon_footprint) VALUES
(1, 1, '2025-01-15', 45.5, 568.75, 120.58, 2.85, 0.15, 12.5, 0.212),
(1, 2, '2025-01-15', 38.2, 450.76, 101.23, 2.45, 0.12, 11.8, 0.225),
(1, 3, '2025-01-15', 52.0, 530.40, 137.80, 3.12, 0.18, 10.2, 0.260),
(1, 1, '2025-01-16', 42.8, 547.84, 113.42, 2.68, 0.14, 12.8, 0.207),
(1, 2, '2025-01-16', 44.5, 511.75, 117.93, 2.78, 0.15, 11.5, 0.230);

-- Sample driver training data
INSERT INTO driver_training (client_id, driver_id, training_type, training_date, completion_date, score, status, trainer, training_provider, cost, next_refresh_date) VALUES
(1, 1, 'Defensive Driving', '2025-01-10', '2025-01-10', 95.5, 'completed', 'John Smith', 'Safe Driving Academy', 250.00, '2026-01-10'),
(1, 2, 'Hazardous Materials', '2025-01-12', '2025-01-12', 88.0, 'completed', 'Sarah Johnson', 'Safety First Training', 300.00, '2026-01-12'),
(1, 3, 'Advanced Vehicle Operation', '2025-01-08', '2025-01-08', 92.5, 'completed', 'Mike Wilson', 'Professional Drivers Institute', 400.00, '2026-01-08'),
(1, 1, 'Emergency Response', '2025-01-20', NULL, NULL, 'scheduled', 'Emergency Training Co', 'Emergency Response Training', 180.00, NULL),
(1, 2, 'Fuel Efficiency Training', '2025-01-25', NULL, NULL, 'scheduled', 'Eco Driving School', 'Eco Driving Academy', 150.00, NULL);

-- Sample asset performance data
INSERT INTO asset_performance (client_id, client_device_id, performance_date, utilization_rate, availability_rate, reliability_rate, efficiency_score, downtime_hours, productive_hours, total_hours, revenue_generated, cost_per_hour, roi_percentage) VALUES
(1, 1, '2025-01-15', 85.5, 95.0, 92.0, 88.5, 1.2, 7.8, 9.0, 780.00, 45.50, 15.2),
(1, 2, '2025-01-15', 78.0, 92.5, 89.0, 84.2, 1.8, 7.2, 9.0, 720.00, 42.20, 12.8),
(1, 3, '2025-01-15', 72.5, 88.0, 85.5, 78.8, 2.5, 6.5, 9.0, 650.00, 48.50, 8.5),
(1, 1, '2025-01-16', 88.0, 96.0, 94.0, 91.0, 0.8, 8.2, 9.0, 820.00, 44.80, 18.5),
(1, 2, '2025-01-16', 82.5, 94.0, 91.0, 87.8, 1.2, 7.8, 9.0, 780.00, 43.10, 14.2);

-- Sample route optimization data
INSERT INTO route_optimization (client_id, route_name, start_location, end_location, optimized_distance, original_distance, time_saved, fuel_saved, cost_saved, optimization_date, status) VALUES
(1, 'Downtown Delivery Route', 'Warehouse A', 'City Center', 45.2, 52.8, 25, 3.8, 45.60, '2025-01-15', 'implemented'),
(1, 'Industrial Zone Circuit', 'Distribution Center', 'Industrial Park', 38.5, 42.1, 18, 2.9, 34.80, '2025-01-16', 'implemented'),
(1, 'Suburban Service Route', 'Service Depot', 'Suburban Area', 62.3, 68.9, 32, 4.2, 50.40, '2025-01-17', 'proposed'),
(1, 'Port Logistics Route', 'Port Terminal', 'Storage Facility', 28.7, 31.5, 15, 2.1, 25.20, '2025-01-18', 'implemented'),
(1, 'Airport Express Route', 'Cargo Terminal', 'Business District', 41.8, 46.2, 22, 3.3, 39.60, '2025-01-19', 'proposed');

-- Sample safety compliance data
INSERT INTO safety_compliance (client_id, driver_id, compliance_date, compliance_type, status, expiry_date, last_check_date, next_check_date) VALUES
(1, 1, '2025-01-15', 'license', 'compliant', '2026-01-15', '2025-01-15', '2025-07-15'),
(1, 2, '2025-01-15', 'medical', 'compliant', '2025-12-15', '2025-01-15', '2025-07-15'),
(1, 3, '2025-01-15', 'training', 'compliant', '2026-01-08', '2025-01-08', '2025-07-08'),
(1, 1, '2025-01-16', 'vehicle_inspection', 'compliant', '2025-07-16', '2025-01-16', '2025-04-16'),
(1, 2, '2025-01-16', 'drug_test', 'compliant', '2025-07-16', '2025-01-16', '2025-04-16'),
(1, 3, '2025-01-17', 'license', 'expiring_soon', '2025-02-15', '2025-01-15', '2025-01-30');

-- Sample vehicle lifecycle data
INSERT INTO vehicle_lifecycle (client_id, client_device_id, lifecycle_stage, stage_date, stage_duration, cost, description, next_stage_date) VALUES
(1, 1, 'operation', '2025-01-15', 365, 0.00, 'Vehicle in active operation phase', '2026-01-15'),
(1, 2, 'operation', '2025-01-15', 730, 0.00, 'Vehicle in active operation phase', '2026-01-15'),
(1, 3, 'maintenance', '2025-01-15', 30, 850.00, 'Vehicle under maintenance for engine repair', '2025-02-15'),
(1, 1, 'upgrade', '2025-01-20', 7, 150.00, 'Scheduled oil change and filter upgrade', '2025-01-27'),
(1, 2, 'operation', '2025-01-16', 365, 0.00, 'Vehicle in active operation phase', '2026-01-16');

-- Sample service technicians data
INSERT INTO service_technicians (client_id, technician_name, specialization, contact_number, email, availability_status, current_location, rating, total_assignments, successful_repairs) VALUES
(1, 'John Smith', 'Engine Specialist', '+1-555-0101', '<EMAIL>', 'available', 'Central Garage', 4.8, 156, 148),
(1, 'Sarah Johnson', 'Electrical Systems', '+1-555-0102', '<EMAIL>', 'busy', 'North Service Center', 4.6, 142, 135),
(1, 'Mike Wilson', 'Brake Systems', '+1-555-0103', '<EMAIL>', 'available', 'South Garage', 4.9, 178, 172),
(1, 'Lisa Brown', 'Transmission Expert', '+1-555-0104', '<EMAIL>', 'off_duty', 'East Service Center', 4.7, 134, 128),
(1, 'David Lee', 'General Maintenance', '+1-555-0105', '<EMAIL>', 'available', 'West Garage', 4.5, 198, 185);

-- Sample construction equipment data
INSERT INTO construction_equipment (client_id, client_device_id, equipment_type, model, capacity, fuel_type, operating_hours, maintenance_hours, idle_hours, efficiency_rating, project_assignments) VALUES
(1, 1, 'Excavator', 'CAT 320', '20 tons', 'diesel', 8, 1, 1, 0.85, 3),
(1, 2, 'Bulldozer', 'CAT D6', '15 tons', 'diesel', 7, 2, 1, 0.78, 2),
(1, 3, 'Crane', 'Liebherr LTM', '50 tons', 'diesel', 6, 3, 1, 0.72, 1),
(1, 4, 'Loader', 'CAT 950', '5 tons', 'diesel', 8, 1, 1, 0.88, 4),
(1, 5, 'Compactor', 'CAT CS533E', '12 tons', 'diesel', 7, 1, 2, 0.82, 2);

-- Sample sales territories data
INSERT INTO sales_territories (client_id, territory_name, territory_code, region, assigned_driver_id, total_customers, active_customers, monthly_revenue, visit_frequency, territory_size) VALUES
(1, 'Downtown District', 'DT001', 'Central Business District', 1, 45, 38, 125000.00, 12, 25.5),
(1, 'Industrial Zone', 'IZ002', 'Industrial Park Area', 2, 32, 28, 98000.00, 8, 45.2),
(1, 'Suburban Market', 'SM003', 'Suburban Residential', 3, 58, 52, 156000.00, 15, 38.7),
(1, 'Port Logistics', 'PL004', 'Port and Logistics Area', 1, 28, 25, 89000.00, 6, 52.1),
(1, 'Airport District', 'AD005', 'Airport and Cargo Area', 2, 35, 31, 112000.00, 10, 31.8);

-- Sample delivery performance data
INSERT INTO delivery_performance (client_id, delivery_id, client_device_id, driver_id, customer_id, scheduled_delivery_time, actual_delivery_time, delivery_status, on_time_delivery, delivery_rating, customer_feedback) VALUES
(1, 'DEL001', 1, 1, 'CUST001', '2025-01-15 10:00:00', '2025-01-15 09:45:00', 'delivered', TRUE, 5.0, 'Excellent service, delivered early'),
(1, 'DEL002', 2, 2, 'CUST002', '2025-01-15 14:00:00', '2025-01-15 14:15:00', 'delivered', FALSE, 4.0, 'Good service, slightly delayed'),
(1, 'DEL003', 3, 3, 'CUST003', '2025-01-16 09:00:00', '2025-01-16 08:55:00', 'delivered', TRUE, 5.0, 'Perfect timing, professional driver'),
(1, 'DEL004', 1, 1, 'CUST004', '2025-01-16 16:00:00', '2025-01-16 16:30:00', 'delivered', FALSE, 3.5, 'Delayed but driver was apologetic'),
(1, 'DEL005', 2, 2, 'CUST005', '2025-01-17 11:00:00', '2025-01-17 10:45:00', 'delivered', TRUE, 4.5, 'Good service, would recommend');
