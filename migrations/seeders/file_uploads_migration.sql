-- Create file_uploads table
CREATE TABLE IF NOT EXISTS `file_uploads` (
    `id` bigint unsigned AUTO_INCREMENT PRIMARY KEY,
    `client_id` bigint unsigned NULL,
    `created_by_id` bigint unsigned NOT NULL,
    `file_name` varchar(255) NOT NULL,
    `original_name` varchar(255) NOT NULL,
    `file_size` bigint NOT NULL,
    `mime_type` varchar(100) NOT NULL,
    `file_url` varchar(500) NOT NULL,
    `file_path` varchar(500) NOT NULL,
    `file_type` enum('image','document','video','audio','other') DEFAULT 'other',
    `description` text NULL,
    `created_at` datetime(3) NULL,
    `updated_at` datetime(3) NULL,
    INDEX `idx_file_uploads_client_id` (`client_id`),
    INDEX `idx_file_uploads_created_by_id` (`created_by_id`),
    INDEX `idx_file_uploads_file_type` (`file_type`),
    CONSTRAINT `fk_file_uploads_client` FOREIGN KEY (`client_id`) REFERENCES `clients`(`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_file_uploads_created_by` FOREIGN KEY (`created_by_id`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


