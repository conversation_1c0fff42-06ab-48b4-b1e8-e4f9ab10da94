-- Migration to create missing tables for comprehensive reports
-- Date: 2025-01-01

-- Customer visits tracking
CREATE TABLE IF NOT EXISTS customer_visits (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    client_device_id BIGINT UNSIGNED,
    driver_id BIGINT UNSIGNED,
    customer_name VARCHAR(255) NOT NULL,
    customer_address TEXT,
    visit_date DATE NOT NULL,
    arrival_time DATETIME,
    departure_time DATETIME,
    visit_duration INT, -- in seconds
    visit_type ENUM('delivery', 'pickup', 'service', 'sales', 'other') DEFAULT 'other',
    status ENUM('scheduled', 'in_progress', 'completed', 'cancelled') DEFAULT 'scheduled',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_client_device (client_device_id),
    INDEX idx_driver (driver_id),
    INDEX idx_visit_date (visit_date),
    INDEX idx_customer (customer_name)
);

-- Maintenance schedule and history
CREATE TABLE IF NOT EXISTS maintenance_schedule (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    client_device_id BIGINT UNSIGNED NOT NULL,
    maintenance_type ENUM('scheduled', 'preventive', 'corrective', 'emergency') DEFAULT 'scheduled',
    service_type VARCHAR(255) NOT NULL,
    scheduled_date DATE NOT NULL,
    due_date DATE NOT NULL,
    completed_date DATE,
    cost DECIMAL(10,2) DEFAULT 0.00,
    service_provider VARCHAR(255),
    description TEXT,
    status ENUM('scheduled', 'in_progress', 'completed', 'overdue', 'cancelled') DEFAULT 'scheduled',
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_client_device (client_device_id),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status)
);

-- Vehicle health metrics
CREATE TABLE IF NOT EXISTS vehicle_health_metrics (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    client_device_id BIGINT UNSIGNED NOT NULL,
    metric_date DATE NOT NULL,
    engine_hours INT DEFAULT 0,
    fuel_efficiency DECIMAL(5,2), -- km/L
    engine_temperature DECIMAL(5,2),
    oil_pressure DECIMAL(5,2),
    battery_voltage DECIMAL(4,2),
    tire_pressure_front DECIMAL(4,2),
    tire_pressure_rear DECIMAL(4,2),
    brake_condition ENUM('good', 'fair', 'poor', 'critical') DEFAULT 'good',
    overall_health_score DECIMAL(3,1) DEFAULT 100.0, -- 0-100 scale
    alerts_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_client_device (client_device_id),
    INDEX idx_metric_date (metric_date),
    INDEX idx_health_score (overall_health_score)
);

-- Operating costs tracking
CREATE TABLE IF NOT EXISTS operating_costs (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    client_device_id BIGINT UNSIGNED,
    cost_date DATE NOT NULL,
    cost_type ENUM('fuel', 'maintenance', 'labor', 'insurance', 'depreciation', 'other') NOT NULL,
    cost_category VARCHAR(255),
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    invoice_number VARCHAR(255),
    vendor VARCHAR(255),
    trip_id BIGINT UNSIGNED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_client_device (client_device_id),
    INDEX idx_cost_date (cost_date),
    INDEX idx_cost_type (cost_type)
);

-- Environmental impact data
CREATE TABLE IF NOT EXISTS emissions_data (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    client_device_id BIGINT UNSIGNED,
    date DATE NOT NULL,
    fuel_consumed DECIMAL(8,2), -- liters
    distance_traveled DECIMAL(8,2), -- km
    co2_emissions DECIMAL(8,2), -- kg CO2
    nox_emissions DECIMAL(6,2), -- g NOx
    particulate_matter DECIMAL(6,2), -- g PM
    fuel_efficiency DECIMAL(5,2), -- km/L
    carbon_footprint DECIMAL(8,2), -- kg CO2/km
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_client_device (client_device_id),
    INDEX idx_date (date)
);

-- Driver training records
CREATE TABLE IF NOT EXISTS driver_training (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    driver_id BIGINT UNSIGNED NOT NULL,
    training_type VARCHAR(255) NOT NULL,
    training_date DATE NOT NULL,
    completion_date DATE,
    score DECIMAL(5,2), -- percentage
    status ENUM('scheduled', 'in_progress', 'completed', 'failed', 'expired') DEFAULT 'scheduled',
    trainer VARCHAR(255),
    training_provider VARCHAR(255),
    cost DECIMAL(8,2) DEFAULT 0.00,
    notes TEXT,
    next_refresh_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_driver (driver_id),
    INDEX idx_training_date (training_date),
    INDEX idx_status (status)
);

-- Asset performance tracking
CREATE TABLE IF NOT EXISTS asset_performance (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    client_device_id BIGINT UNSIGNED NOT NULL,
    performance_date DATE NOT NULL,
    utilization_rate DECIMAL(5,2), -- percentage
    availability_rate DECIMAL(5,2), -- percentage
    reliability_rate DECIMAL(5,2), -- percentage
    efficiency_score DECIMAL(5,2), -- 0-100 scale
    downtime_hours INT DEFAULT 0,
    productive_hours INT DEFAULT 0,
    total_hours INT DEFAULT 0,
    revenue_generated DECIMAL(10,2) DEFAULT 0.00,
    cost_per_hour DECIMAL(8,2) DEFAULT 0.00,
    roi_percentage DECIMAL(5,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_client_device (client_device_id),
    INDEX idx_performance_date (performance_date)
);

-- Route optimization data
CREATE TABLE IF NOT EXISTS route_optimization (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    route_name VARCHAR(255) NOT NULL,
    start_location VARCHAR(255),
    end_location VARCHAR(255),
    optimized_distance DECIMAL(8,2),
    original_distance DECIMAL(8,2),
    time_saved INT, -- in minutes
    fuel_saved DECIMAL(6,2), -- liters
    cost_saved DECIMAL(8,2),
    optimization_date DATE NOT NULL,
    status ENUM('proposed', 'implemented', 'rejected') DEFAULT 'proposed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_optimization_date (optimization_date),
    INDEX idx_status (status)
);

-- Safety compliance tracking
CREATE TABLE IF NOT EXISTS safety_compliance (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    driver_id BIGINT UNSIGNED,
    compliance_date DATE NOT NULL,
    compliance_type ENUM('license', 'medical', 'training', 'vehicle_inspection', 'drug_test') NOT NULL,
    status ENUM('compliant', 'non_compliant', 'expiring_soon', 'expired') DEFAULT 'compliant',
    expiry_date DATE,
    last_check_date DATE,
    next_check_date DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_driver (driver_id),
    INDEX idx_compliance_date (compliance_date),
    INDEX idx_status (status)
);

-- Vehicle lifecycle stages
CREATE TABLE IF NOT EXISTS vehicle_lifecycle (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    client_device_id BIGINT UNSIGNED NOT NULL,
    lifecycle_stage ENUM('acquisition', 'operation', 'maintenance', 'upgrade', 'disposal') NOT NULL,
    stage_date DATE NOT NULL,
    stage_duration INT, -- in days
    cost DECIMAL(10,2) DEFAULT 0.00,
    description TEXT,
    next_stage_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_client_device (client_device_id),
    INDEX idx_lifecycle_stage (lifecycle_stage),
    INDEX idx_stage_date (stage_date)
);

-- Service technician assignments
CREATE TABLE IF NOT EXISTS service_technicians (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    technician_name VARCHAR(255) NOT NULL,
    specialization VARCHAR(255),
    contact_number VARCHAR(50),
    email VARCHAR(255),
    availability_status ENUM('available', 'busy', 'off_duty', 'on_leave') DEFAULT 'available',
    current_location VARCHAR(255),
    rating DECIMAL(3,2) DEFAULT 5.00, -- 0-5 scale
    total_assignments INT DEFAULT 0,
    successful_repairs INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_availability (availability_status),
    INDEX idx_rating (rating)
);

-- Construction equipment specific data
CREATE TABLE IF NOT EXISTS construction_equipment (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    client_device_id BIGINT UNSIGNED NOT NULL,
    equipment_type VARCHAR(255) NOT NULL,
    model VARCHAR(255),
    capacity VARCHAR(100),
    fuel_type ENUM('diesel', 'gasoline', 'electric', 'hybrid', 'other') DEFAULT 'diesel',
    operating_hours INT DEFAULT 0,
    maintenance_hours INT DEFAULT 0,
    idle_hours INT DEFAULT 0,
    efficiency_rating DECIMAL(3,2) DEFAULT 1.00, -- 0-1 scale
    project_assignments INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_equipment_type (equipment_type),
    INDEX idx_efficiency (efficiency_rating)
);

-- Sales territory data
CREATE TABLE IF NOT EXISTS sales_territories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    territory_name VARCHAR(255) NOT NULL,
    territory_code VARCHAR(50),
    region VARCHAR(255),
    assigned_driver_id BIGINT UNSIGNED,
    total_customers INT DEFAULT 0,
    active_customers INT DEFAULT 0,
    monthly_revenue DECIMAL(12,2) DEFAULT 0.00,
    visit_frequency INT DEFAULT 0, -- visits per month
    territory_size DECIMAL(8,2), -- km²
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_territory_name (territory_name),
    INDEX idx_assigned_driver (assigned_driver_id)
);

-- Delivery performance tracking
CREATE TABLE IF NOT EXISTS delivery_performance (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    client_id BIGINT UNSIGNED NOT NULL,
    delivery_id VARCHAR(255) NOT NULL,
    client_device_id BIGINT UNSIGNED,
    driver_id BIGINT UNSIGNED,
    customer_id VARCHAR(255),
    scheduled_delivery_time DATETIME,
    actual_delivery_time DATETIME,
    delivery_status ENUM('scheduled', 'in_transit', 'delivered', 'failed', 'returned') DEFAULT 'scheduled',
    on_time_delivery BOOLEAN DEFAULT TRUE,
    delivery_rating DECIMAL(3,2) DEFAULT 5.00, -- 0-5 scale
    customer_feedback TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_delivery_id (delivery_id),
    INDEX idx_delivery_status (delivery_status),
    INDEX idx_scheduled_time (scheduled_delivery_time)
);
